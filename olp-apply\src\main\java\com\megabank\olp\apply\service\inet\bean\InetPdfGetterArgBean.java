package com.megabank.olp.apply.service.inet.bean;

import java.util.ArrayList;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.client.sender.inet.bean.InetReportDataBean;

public class InetPdfGetterArgBean extends BaseBean
{
	private String rptTemplate;

	private String returnType;

	private List<InetReportDataBean> inetReportDataBeanList = new ArrayList<>();

	private List<Object> parameters = new ArrayList<>();

	public List<InetReportDataBean> getInetReportDataBeanList()
	{
		return inetReportDataBeanList;
	}

	public List<Object> getParameters()
	{
		return parameters;
	}

	public String getReturnType()
	{
		return returnType;
	}

	public String getRptTemplate()
	{
		return rptTemplate;
	}

	public void setInetReportDataBeanList( List<InetReportDataBean> inetReportDataBeanList )
	{
		this.inetReportDataBeanList = inetReportDataBeanList;
	}

	public void setParameters( List<Object> parameters )
	{
		this.parameters = parameters;
	}

	public void setReturnType( String returnType )
	{
		this.returnType = returnType;
	}

	public void setRptTemplate( String rptTemplate )
	{
		this.rptTemplate = rptTemplate;
	}
}
