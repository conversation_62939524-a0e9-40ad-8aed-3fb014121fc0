package com.megabank.olp.apply.service.mail;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.utility.BaseApplyService;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Transactional( propagation = Propagation.REQUIRES_NEW )
@Profile( { "dev" } )
public class MailServerServiceMockImp extends BaseApplyService implements MailServerService
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Override
	public boolean send( List<String> to, String subject, String text )
	{
		String from = propertyBean.getMailServerFrom();
		String host = propertyBean.getMailServerUrl();

		return send( to, from, host, subject, text );
	}

	@Override
	public boolean send( List<String> to, String from, String host, String subject, String text )
	{
		if( to.isEmpty() )
			return false;

		logger.info( "mocked send mail success." );

		return true;
	}

	@Override
	public boolean send( String to, String subject, String text )
	{
		String from = propertyBean.getMailServerFrom();
		String host = propertyBean.getMailServerUrl();

		return send( to, from, host, subject, text );
	}

	@Override
	public boolean send( String to, String from, String host, String subject, String text )
	{
		logger.info( "mocked send mail success." );

		return true;
	}

}
