package com.megabank.olp.apply.controller.loan.bean.apply;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class LoanContentBean extends BaseBean
{
	@NotNull
	private Integer loanRequestAmt;

	@NotBlank
	private String loanPurposeCode;

	private String otherPurpose;

	@NotBlank
	private String loanPeriodCode;

	private String collateralAddressTownCode;

	private String collateralAddressVillage;

	private String collateralAddressNeighborhood;

	private String collateralAddressStreet;

	private Integer collateralAddressSection;

	private String collateralAddressLane;

	private String collateralAddressAlley;

	private String collateralAddressNo;

	private String collateralAddressFloor;

	private Integer collateralAddressRoom;

	private String gracePeriodCode;

	@NotBlank
	private String notificationCode;

	private String mortgageType;

	private String nonPrivateUsageType;

	private String nonPrivateUsageSubType;

	private String privateUsageType;

	private Boolean isIncreasingLoan;

	private String appnBankCode;

	private String appnDpAcct;

	private String caseSourceCode;

	private String urlToIdentifyFraud;

	public LoanContentBean()
	{}

	public String getAppnBankCode()
	{
		return appnBankCode;
	}

	public String getAppnDpAcct()
	{
		return appnDpAcct;
	}

	public String getCollateralAddressAlley()
	{
		return collateralAddressAlley;
	}

	public String getCollateralAddressFloor()
	{
		return collateralAddressFloor;
	}

	public String getCollateralAddressLane()
	{
		return collateralAddressLane;
	}

	public String getCollateralAddressNeighborhood()
	{
		return collateralAddressNeighborhood;
	}

	public String getCollateralAddressNo()
	{
		return collateralAddressNo;
	}

	public Integer getCollateralAddressRoom()
	{
		return collateralAddressRoom;
	}

	public Integer getCollateralAddressSection()
	{
		return collateralAddressSection;
	}

	public String getCollateralAddressStreet()
	{
		return collateralAddressStreet;
	}

	public String getCollateralAddressTownCode()
	{
		return collateralAddressTownCode;
	}

	public String getCollateralAddressVillage()
	{
		return collateralAddressVillage;
	}

	public String getGracePeriodCode()
	{
		return gracePeriodCode;
	}

	public Boolean getIsIncreasingLoan()
	{
		return isIncreasingLoan;
	}

	public String getLoanPeriodCode()
	{
		return loanPeriodCode;
	}

	public String getLoanPurposeCode()
	{
		return loanPurposeCode;
	}

	public Integer getLoanRequestAmt()
	{
		return loanRequestAmt;
	}

	public String getMortgageType()
	{
		return mortgageType;
	}

	public String getNonPrivateUsageSubType()
	{
		return nonPrivateUsageSubType;
	}

	public String getNonPrivateUsageType()
	{
		return nonPrivateUsageType;
	}

	public String getNotificationCode()
	{
		return notificationCode;
	}

	public String getOtherPurpose()
	{
		return otherPurpose;
	}

	public String getPrivateUsageType()
	{
		return privateUsageType;
	}

	public void setAppnBankCode( String appnBankCode )
	{
		this.appnBankCode = appnBankCode;
	}

	public void setAppnDpAcct( String appnDpAcct )
	{
		this.appnDpAcct = appnDpAcct;
	}

	public void setCollateralAddressAlley( String collateralAddressAlley )
	{
		this.collateralAddressAlley = collateralAddressAlley;
	}

	public void setCollateralAddressFloor( String collateralAddressFloor )
	{
		this.collateralAddressFloor = collateralAddressFloor;
	}

	public void setCollateralAddressLane( String collateralAddressLane )
	{
		this.collateralAddressLane = collateralAddressLane;
	}

	public void setCollateralAddressNeighborhood( String collateralAddressNeighborhood )
	{
		this.collateralAddressNeighborhood = collateralAddressNeighborhood;
	}

	public void setCollateralAddressNo( String collateralAddressNo )
	{
		this.collateralAddressNo = collateralAddressNo;
	}

	public void setCollateralAddressRoom( Integer collateralAddressRoom )
	{
		this.collateralAddressRoom = collateralAddressRoom;
	}

	public void setCollateralAddressSection( Integer collateralAddressSection )
	{
		this.collateralAddressSection = collateralAddressSection;
	}

	public void setCollateralAddressStreet( String collateralAddressStreet )
	{
		this.collateralAddressStreet = collateralAddressStreet;
	}

	public void setCollateralAddressTownCode( String collateralAddressTownCode )
	{
		this.collateralAddressTownCode = collateralAddressTownCode;
	}

	public void setCollateralAddressVillage( String collateralAddressVillage )
	{
		this.collateralAddressVillage = collateralAddressVillage;
	}

	public void setGracePeriodCode( String gracePeriodCode )
	{
		this.gracePeriodCode = gracePeriodCode;
	}

	public void setIsIncreasingLoan( Boolean isIncreasingLoan )
	{
		this.isIncreasingLoan = isIncreasingLoan;
	}

	public void setLoanPeriodCode( String loanPeriodCode )
	{
		this.loanPeriodCode = loanPeriodCode;
	}

	public void setLoanPurposeCode( String loanPurposeCode )
	{
		this.loanPurposeCode = loanPurposeCode;
	}

	public void setLoanRequestAmt( Integer loanRequestAmt )
	{
		this.loanRequestAmt = loanRequestAmt;
	}

	public void setMortgageType( String mortgageType )
	{
		this.mortgageType = mortgageType;
	}

	public void setNonPrivateUsageSubType( String nonPrivateUsageSubType )
	{
		this.nonPrivateUsageSubType = nonPrivateUsageSubType;
	}

	public void setNonPrivateUsageType( String nonPrivateUsageType )
	{
		this.nonPrivateUsageType = nonPrivateUsageType;
	}

	public void setNotificationCode( String notificationCode )
	{
		this.notificationCode = notificationCode;
	}

	public void setOtherPurpose( String otherPurpose )
	{
		this.otherPurpose = otherPurpose;
	}

	public void setPrivateUsageType( String privateUsageType )
	{
		this.privateUsageType = privateUsageType;
	}

	public String getCaseSourceCode()
	{
		return caseSourceCode;
	}

	public void setCaseSourceCode( String caseSourceCode )
	{
		this.caseSourceCode = caseSourceCode;
	}

	public String getUrlToIdentifyFraud()
	{
		return urlToIdentifyFraud;
	}

	public void setUrlToIdentifyFraud( String urlToIdentifyFraud )
	{
		this.urlToIdentifyFraud = urlToIdentifyFraud;
	}
}
