package com.megabank.olp.apply.persistence.dao.generated.code;

import com.megabank.olp.apply.persistence.pojo.code.CodeCaseSource;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

@Repository
public class CodeCaseSourceDAO extends BasePojoDAO<CodeCaseSource, String>
{
	public CodeCaseSource read( String caseSourceCode )
	{
		Validate.notBlank( caseSourceCode );

		return getPojoByPK( caseSourceCode, CodeCaseSource.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeCaseSource> getPojoClass()
	{
		return CodeCaseSource.class;
	}
}
