package com.megabank.olp.apply.persistence.dao.generated.temp;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.persistence.bean.generated.temp.TempUploadFileCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.youthStartUp.ApplyYouthStartUpDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeAttachmentTypeDAO;
import com.megabank.olp.apply.persistence.pojo.temp.TempUploadFile;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The TempUploadFileDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class TempUploadFileDAO extends BasePojoDAO<TempUploadFile, Long>
{
	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private ApplyYouthStartUpDAO applyYouthStartUpDAO;

	@Autowired
	private CodeAttachmentTypeDAO codeAttachmentTypeDAO;

	@Transactional( propagation = Propagation.REQUIRES_NEW )
	public Long create( TempUploadFileCreatedParamBean paramBean, String loanType )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notNull( paramBean.getFileSize() );
		Validate.notNull( paramBean.getFileContent() );
		Validate.notNull( paramBean.getProcessed() );
		Validate.notBlank( paramBean.getAttachmentType() );
		Validate.notBlank( paramBean.getFileName() );

		TempUploadFile pojo = new TempUploadFile();
		if( LoanTypeEnum.YOUTH_START_UP.getContext().equals( loanType ) )
			pojo.setApplyYouthStartUp( applyYouthStartUpDAO.read( paramBean.getLoanId() ) );
		else
			pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );

		pojo.setCodeAttachmentType( codeAttachmentTypeDAO.read( paramBean.getAttachmentType() ) );
		pojo.setCompressFileContent( paramBean.getCompressFileContent() );
		pojo.setFileName( paramBean.getFileName() );
		pojo.setFileSize( paramBean.getFileSize() );
		pojo.setFileContent( paramBean.getFileContent() );
		pojo.setProcessed( paramBean.getProcessed() );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public void delete( Long fileId )
	{
		TempUploadFile pojo = read( fileId );

		deletePojo( pojo );
	}

	public void delete( Long uploadFileId, Long loanId )
	{
		TempUploadFile pojo = getPojoById( uploadFileId, loanId );

		deletePojo( pojo );
	}

	public void deleteByYouthStartUpId( Long uploadFileId, Long loanId )
	{
		TempUploadFile pojo = getPojoByYouthStartUpId( uploadFileId, loanId );

		deletePojo( pojo );
	}

	public void deletePojosByLoanId( Long loanId, boolean processed )
	{
		List<TempUploadFile> pojos = getPojosByLoanId( loanId, processed );

		super.deletePojos( pojos );
	}

	public void deletePojosByYouthStartUpId( Long youthStartUpId, boolean processed )
	{
		List<TempUploadFile> pojos = getPojosByYouthStartUpId( youthStartUpId, processed );

		super.deletePojos( pojos );
	}

	public TempUploadFile getPojoById( Long uploadFileId, Long loanId )
	{
		Validate.notNull( uploadFileId );
		Validate.notNull( loanId );

		NameValueBean pk = new NameValueBean( "uploadFileId", uploadFileId );
		NameValueBean applyLoan = new NameValueBean( "applyLoan", applyLoanDAO.read( loanId ) );
		NameValueBean[] conditions = new NameValueBean[]{ pk, applyLoan };

		return getUniquePojoByProperties( conditions, TempUploadFile.TABLENAME_CONSTANT );
	}

	public TempUploadFile getPojoByYouthStartUpId( Long uploadFileId, Long youthStartUpId )
	{
		Validate.notNull( uploadFileId );
		Validate.notNull( youthStartUpId );

		NameValueBean pk = new NameValueBean( "uploadFileId", uploadFileId );
		NameValueBean applyYouthStartUp = new NameValueBean( "applyYouthStartUp", applyYouthStartUpDAO.read( youthStartUpId ) );
		NameValueBean[] conditions = new NameValueBean[]{ pk, applyYouthStartUp };

		return getUniquePojoByProperties( conditions, TempUploadFile.TABLENAME_CONSTANT );
	}

	public List<TempUploadFile> getPojosByLoanId( Long loanId, boolean processed )
	{
		Validate.notNull( loanId );

		NameValueBean applyLoan = new NameValueBean( TempUploadFile.APPLY_LOAN_CONSTANT, applyLoanDAO.read( loanId ) );
		NameValueBean isProcessed = new NameValueBean( TempUploadFile.PROCESSED_CONSTANT, processed );
		NameValueBean[] conditions = new NameValueBean[]{ applyLoan, isProcessed };

		return getPojosByProperties( conditions );
	}

	public List<TempUploadFile> getPojosByYouthStartUpId( Long youthStartUpId, boolean processed )
	{
		Validate.notNull( youthStartUpId );

		NameValueBean applyYouthStartUp = new NameValueBean( TempUploadFile.APPLY_YOUTH_START_UP_CONSTANT,
															 applyYouthStartUpDAO.read( youthStartUpId ) );
		NameValueBean isProcessed = new NameValueBean( TempUploadFile.PROCESSED_CONSTANT, processed );
		NameValueBean[] conditions = new NameValueBean[]{ applyYouthStartUp, isProcessed };

		return getPojosByProperties( conditions );
	}

	public TempUploadFile read( Long uploadFileId )
	{
		Validate.notNull( uploadFileId );

		return getPojoByPK( uploadFileId, TempUploadFile.TABLENAME_CONSTANT );
	}

	public Long updateCompressFileContent( Long uploadFileId, byte[] compressFileContent )
	{
		Validate.notNull( uploadFileId );

		TempUploadFile pojo = read( uploadFileId );
		pojo.setCompressFileContent( new ImmutableByteArray( compressFileContent ) );
		pojo.setProcessed( true );

		return pojo.getUploadFileId();
	}

	@Override
	protected Class<TempUploadFile> getPojoClass()
	{
		return TempUploadFile.class;
	}
}
