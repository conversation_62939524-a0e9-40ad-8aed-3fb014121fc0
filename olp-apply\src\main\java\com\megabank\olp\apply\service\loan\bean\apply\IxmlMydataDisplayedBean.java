package com.megabank.olp.apply.service.loan.bean.apply;

import com.megabank.olp.base.bean.BaseBean;

public class IxmlMydataDisplayedBean extends BaseBean
{
	private boolean ixmlDisplayed;
	private boolean mydataDisplayed;

	public IxmlMydataDisplayedBean()
	{
		// default constructor
	}

	public boolean isIxmlDisplayed() {
		return ixmlDisplayed;
	}

	public void setIxmlDisplayed(boolean ixmlDisplayed) {
		this.ixmlDisplayed = ixmlDisplayed;
	}

	public boolean isMydataDisplayed() {
		return mydataDisplayed;
	}

	public void setMydataDisplayed(boolean mydataDisplayed) {
		this.mydataDisplayed = mydataDisplayed;
	}
}
