/**
 *
 */
package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractListDTO extends BaseBean
{
	private Long signingContractId;

	private String contractNo;

	private String signingContractType;

	private String signingContractTypeName;

	private Date expiredDate;

	private Date createdDate;

	private String loanType;

	private Long validatedIdentityId;

	private String name;

	private Integer discard;

	private Date firstPaymentDate;

	private Date appropriationDate;

	private Integer userCount;

	private Integer isAppropiration;

	private Integer inetResponseStatus;

	private String email;

	private String verifiedEmail;

	public SigningContractListDTO()
	{}

	public Date getAppropriationDate()
	{
		return appropriationDate;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public Integer getDiscard()
	{
		return discard;
	}

	public String getEmail()
	{
		return email;
	}

	public Date getExpiredDate()
	{
		return expiredDate;
	}

	public Date getFirstPaymentDate()
	{
		return firstPaymentDate;
	}

	public Integer getInetResponseStatus()
	{
		return inetResponseStatus;
	}

	public Integer getIsAppropiration()
	{
		return isAppropiration;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getName()
	{
		return name;
	}

	public Long getSigningContractId()
	{
		return signingContractId;
	}

	public String getSigningContractType()
	{
		return signingContractType;
	}

	public String getSigningContractTypeName()
	{
		return signingContractTypeName;
	}

	public Integer getUserCount()
	{
		return userCount;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public String getVerifiedEmail()
	{
		return verifiedEmail;
	}

	public void setAppropriationDate( Date appropriationDate )
	{
		this.appropriationDate = appropriationDate;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setDiscard( Integer discard )
	{
		this.discard = discard;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setExpiredDate( Date expiredDate )
	{
		this.expiredDate = expiredDate;
	}

	public void setFirstPaymentDate( Date firstPaymentDate )
	{
		this.firstPaymentDate = firstPaymentDate;
	}

	public void setInetResponseStatus( Integer inetResponseStatus )
	{
		this.inetResponseStatus = inetResponseStatus;
	}

	public void setIsAppropiration( Integer isAppropiration )
	{
		this.isAppropiration = isAppropiration;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setSigningContractType( String signingContractType )
	{
		this.signingContractType = signingContractType;
	}

	public void setSigningContractTypeName( String signingContractTypeName )
	{
		this.signingContractTypeName = signingContractTypeName;
	}

	public void setUserCount( Integer userCount )
	{
		this.userCount = userCount;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

	public void setVerifiedEmail( String verifiedEmail )
	{
		this.verifiedEmail = verifiedEmail;
	}
}
