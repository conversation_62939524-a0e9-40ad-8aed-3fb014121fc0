package com.megabank.olp.apply.persistence.pojo.house;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseContactBasicInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_contact_basic_info" )
public class HouseContactBasicInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_contact_basic_info";

	public static final String CONTACT_INFO_ID_CONSTANT = "contactInfoId";

	public static final String HOUSE_CONTACT_INFO_CONSTANT = "houseContactInfo";

	public static final String _C_NAME_CONSTANT = "CName";

	public static final String CALL_BACK_TIME_CONSTANT = "callBackTime";

	private long contactInfoId;

	private transient HouseContactInfo houseContactInfo;

	private String CName;

	private String callBackTime;

	public HouseContactBasicInfo()
	{}

	public HouseContactBasicInfo( Long contactInfoId )
	{
		this.contactInfoId = contactInfoId;
	}

	@Column( name = "call_back_time", nullable = false, length = 20 )
	public String getCallBackTime()
	{
		return callBackTime;
	}

	@Column( name = "c_name", nullable = false, length = 20 )
	public String getCName()
	{
		return CName;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "houseContactInfo" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "contact_info_id", unique = true, nullable = false )
	public long getContactInfoId()
	{
		return contactInfoId;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public HouseContactInfo getHouseContactInfo()
	{
		return houseContactInfo;
	}

	public void setCallBackTime( String callBackTime )
	{
		this.callBackTime = callBackTime;
	}

	public void setCName( String CName )
	{
		this.CName = CName;
	}

	public void setContactInfoId( long contactInfoId )
	{
		this.contactInfoId = contactInfoId;
	}

	public void setHouseContactInfo( HouseContactInfo houseContactInfo )
	{
		this.houseContactInfo = houseContactInfo;
	}
}