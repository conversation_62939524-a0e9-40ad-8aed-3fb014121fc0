package com.megabank.olp.apply.persistence.pojo.house;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseEstimateInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_estimate_info" )
public class HouseEstimateInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_estimate_info";

	public static final String PRICING_INFO_ID_CONSTANT = "pricingInfoId";

	public static final String HOUSE_PRICING_INFO_CONSTANT = "housePricingInfo";

	public static final String AV750_CONSTANT = "av750";

	private long pricingInfoId;

	private transient HousePricingInfo housePricingInfo;

	private BigDecimal av750;

	public HouseEstimateInfo()
	{}

	public HouseEstimateInfo( HousePricingInfo housePricingInfo )
	{
		this.housePricingInfo = housePricingInfo;
	}

	public HouseEstimateInfo( Long pricingInfoId )
	{
		this.pricingInfoId = pricingInfoId;
	}

	@Column( name = "av750", precision = 10 )
	public BigDecimal getAv750()
	{
		return av750;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public HousePricingInfo getHousePricingInfo()
	{
		return housePricingInfo;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "housePricingInfo" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "pricing_info_id", unique = true, nullable = false )
	public long getPricingInfoId()
	{
		return pricingInfoId;
	}

	public void setAv750( BigDecimal av750 )
	{
		this.av750 = av750;
	}

	public void setHousePricingInfo( HousePricingInfo housePricingInfo )
	{
		this.housePricingInfo = housePricingInfo;
	}

	public void setPricingInfoId( long pricingInfoId )
	{
		this.pricingInfoId = pricingInfoId;
	}
}