package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class RepaymentBean extends BaseBean
{
	private String bankCode;

	private String bankName;

	private String repaymentProductType;

	private BigDecimal originalAmt;

	public RepaymentBean()
	{}

	public String getBankCode()
	{
		return bankCode;
	}

	public String getBankName()
	{
		return bankName;
	}

	public BigDecimal getOriginalAmt()
	{
		return originalAmt;
	}

	public String getRepaymentProductType()
	{
		return repaymentProductType;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setBankName( String bankName )
	{
		this.bankName = bankName;
	}

	public void setOriginalAmt( BigDecimal originalAmt )
	{
		this.originalAmt = originalAmt;
	}

	public void setRepaymentProductType( String repaymentProductType )
	{
		this.repaymentProductType = repaymentProductType;
	}
}
