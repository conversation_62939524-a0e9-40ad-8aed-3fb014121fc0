# C001 中鋼總公司消費性貸款測試指南

## 📋 概述
本指南提供C001中鋼總公司消費性貸款專案的完整測試資料創建和測試流程。

## 🎯 測試目標
- 驗證C001中鋼總公司申請流程
- 測試EIP系統整合驗證
- 確認重複申請檢查機制
- 驗證時間限制控制

## 🔧 環境準備

### 1. 資料庫設定
執行 `C001_test_data_setup.sql` 腳本來建立基礎測試資料：

```bash
sqlcmd -S your_server -d PLOANDB -i C001_test_data_setup.sql
```

### 2. 應用程式配置
確認以下配置正確：

```yaml
# application.yml
service:
  pdf:
    template:
      personalloan:
        chinaSteel:
          parent: pdf/apply/personalloan-chinaSteel-parent-info-page.html
```

## 🧪 測試案例

### 測試案例1：正常申請流程

#### 前置條件
- 用戶在中鋼控管名單中 (grpCntrNo: 918111000325)
- 申請時間在有效期間內 (2022/10/01 ~ 2025/12/31)
- 6個月內無重複申請記錄

#### 測試步驟
1. **創建申請**
   ```json
   POST /apply/create
   {
     "loanType": "personalloan",
     "plan": "C001",
     "introduceBrNo": "002"
   }
   ```

2. **確認同意事項**
   ```json
   POST /apply/confirmAgreement
   {
     "loanType": "personalloan",
     "itemIds": [1, 2, 3, 5],
     "notUsTaxpayer": true,
     "notOuttwTaxpayer": true,
     "rateAdjNotify": "Y",
     "crossMarketing": false
   }
   ```

3. **提交申請資料**
   ```json
   POST /apply/submitLoanApplyInfo
   {
     "loanType": "personalloan",
     "basicBean": {
       "name": "王大明",
       "idNo": "A123456789",
       "birthDate": "1985-05-15",
       "childrenCount": 0,
       "email": "<EMAIL>"
     },
     "loanContentBean": {
       "loanRequestAmt": 500000,
       "loanPeriodCode": "7"
     }
   }
   ```

4. **送出申請**
   ```json
   POST /apply/deliverLoanApply
   ```

#### 預期結果
- 所有步驟成功執行
- 案件派送到002分行
- 貸款期間固定為7年

### 測試案例2：EIP系統驗證失敗

#### 前置條件
- 用戶不在中鋼控管名單中
- 或 grpCntrNo 為空

#### 測試步驟
執行創建申請步驟

#### 預期結果
- 拋出 `FREETEXT_01019_DESCRIPTION` 錯誤
- 錯誤訊息包含EIP系統申請時間提示

### 測試案例3：重複申請檢查

#### 前置條件
- 6個月內已有成功申請記錄

#### 測試步驟
再次執行創建申請

#### 預期結果
- 拋出 `ALREADY_FINISH_APPLY` 錯誤
- 顯示上次申請日期

### 測試案例4：申請時間限制

#### 前置條件
- 當前時間超出方案有效期間

#### 測試步驟
執行創建申請步驟

#### 預期結果
- 拋出 `LOAN_PLAN_EXCEED_ENDTS` 錯誤
- 顯示中鋼專用錯誤訊息

## 🔍 關鍵驗證點

### 1. 中鋼特殊邏輯
- ✅ 不派送到229分行
- ✅ 固定派送到002分行
- ✅ 貸款期間固定7年
- ✅ Email為必填欄位
- ✅ 居住狀況不填寫
- ✅ 子女數不填寫

### 2. EIP系統整合
- ✅ grpCntrNo驗證
- ✅ 控管名單檢查
- ✅ 手機號碼比對

### 3. 業務規則
- ✅ 6個月重複申請限制
- ✅ 申請時間範圍檢查
- ✅ 資金用途限制

## 🚨 常見問題

### Q1: EIP驗證一直失敗
**A:** 檢查 code_list 表中是否有正確的 ChinaSteel_GrpCntrNo 資料

### Q2: 時間驗證錯誤
**A:** 確認 code_loan_plan 表中 C001 的 beg_ts 和 end_ts 設定正確

### Q3: 分行派送錯誤
**A:** 確認 ApplyLoanUtils.get_ChinaSteelGroup_BatchPersonalLoan_brNo() 回傳 "002"

## 📊 測試資料範本

### 有效測試用戶
```json
{
  "idNo": "A123456789",
  "name": "王大明",
  "birthDate": "1985-05-15",
  "mobileNumber": "0912345678",
  "email": "<EMAIL>",
  "grpCntrNo": "918111000325"
}
```

### 無效測試用戶
```json
{
  "idNo": "B987654321",
  "name": "李小華",
  "birthDate": "1990-01-01",
  "mobileNumber": "0987654321",
  "email": "<EMAIL>",
  "grpCntrNo": null
}
```

## 🔧 Mock設定

### EloanCustInfoResultBean Mock
```java
@MockBean
private EloanSenderService eloanSenderService;

@Before
public void setup() {
    EloanCustInfoResultBean mockBean = new EloanCustInfoResultBean();
    mockBean.setGrpCntrNo("918111000325");
    mockBean.setMobileNumber("0912345678");
    mockBean.setEmail("<EMAIL>");
    
    when(eloanSenderService.getEloanCustInfo(anyString(), any(Date.class)))
        .thenReturn(mockBean);
}
```

## 📝 測試報告範本

### 測試執行結果
- [ ] 正常申請流程
- [ ] EIP驗證失敗
- [ ] 重複申請檢查
- [ ] 時間限制驗證
- [ ] 分行派送邏輯
- [ ] 特殊欄位處理

### 發現問題
1. 問題描述
2. 重現步驟
3. 預期結果 vs 實際結果
4. 影響程度

---

**注意事項：**
- 測試前請備份資料庫
- 確認測試環境與生產環境隔離
- 測試完成後清理測試資料
