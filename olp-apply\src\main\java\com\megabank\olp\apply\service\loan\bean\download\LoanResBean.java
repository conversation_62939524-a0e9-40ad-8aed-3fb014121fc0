package com.megabank.olp.apply.service.loan.bean.download;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class LoanResBean extends BaseBean
{
	private Long loanId;

	private String name;

	private String status;

	private String loanType;

	private Date applyDate;

	private String branchBank;

	private Boolean ixmlApprovedFlag;

	public LoanResBean()
	{
		// default constructor
	}

	public Date getApplyDate()
	{
		return applyDate;
	}

	public String getBranchBank()
	{
		return branchBank;
	}

	public Boolean getIxmlApprovedFlag()
	{
		return ixmlApprovedFlag;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getName()
	{
		return name;
	}

	public String getStatus()
	{
		return status;
	}

	public void setApplyDate( Date applyDate )
	{
		this.applyDate = applyDate;
	}

	public void setBranchBank( String branchBank )
	{
		this.branchBank = branchBank;
	}

	public void setIxmlApprovedFlag( Boolean ixmlApprovedFlag )
	{
		this.ixmlApprovedFlag = ixmlApprovedFlag;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setStatus( String status )
	{
		this.status = status;
	}
}
