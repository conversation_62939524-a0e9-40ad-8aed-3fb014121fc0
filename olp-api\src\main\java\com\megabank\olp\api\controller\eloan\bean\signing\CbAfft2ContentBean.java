package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class CbAfft2ContentBean extends BaseBean
{
	private String cbAfft2_1;

	private String cbAfft2_2;

	private String cbAfft2_3_year;

	private String cbAfft2_3_mth;

	private String cbAfft2_3_day;

	private Integer cbAfft2_4;

	private Integer cbAfft2_5;

	private Integer cbAfft2_6;

	private Integer cbAfft2_7;

	private Integer cbAfft2_8;

	private Integer cbAfft2_9;

	private BigDecimal cbAfft2_10;

	public CbAfft2ContentBean()
	{}

	public String getCbAfft2_1()
	{
		return cbAfft2_1;
	}

	public void setCbAfft2_1( String cbAfft2_1 )
	{
		this.cbAfft2_1 = cbAfft2_1;
	}

	public String getCbAfft2_2()
	{
		return cbAfft2_2;
	}

	public void setCbAfft2_2( String cbAfft2_2 )
	{
		this.cbAfft2_2 = cbAfft2_2;
	}

	public String getCbAfft2_3_year()
	{
		return cbAfft2_3_year;
	}

	public void setCbAfft2_3_year( String cbAfft2_3_year )
	{
		this.cbAfft2_3_year = cbAfft2_3_year;
	}

	public String getCbAfft2_3_mth()
	{
		return cbAfft2_3_mth;
	}

	public void setCbAfft2_3_mth( String cbAfft2_3_mth )
	{
		this.cbAfft2_3_mth = cbAfft2_3_mth;
	}

	public String getCbAfft2_3_day()
	{
		return cbAfft2_3_day;
	}

	public void setCbAfft2_3_day( String cbAfft2_3_day )
	{
		this.cbAfft2_3_day = cbAfft2_3_day;
	}

	public Integer getCbAfft2_4()
	{
		return cbAfft2_4;
	}

	public void setCbAfft2_4( Integer cbAfft2_4 )
	{
		this.cbAfft2_4 = cbAfft2_4;
	}

	public Integer getCbAfft2_5()
	{
		return cbAfft2_5;
	}

	public void setCbAfft2_5( Integer cbAfft2_5 )
	{
		this.cbAfft2_5 = cbAfft2_5;
	}

	public Integer getCbAfft2_6()
	{
		return cbAfft2_6;
	}

	public void setCbAfft2_6( Integer cbAfft2_6 )
	{
		this.cbAfft2_6 = cbAfft2_6;
	}

	public Integer getCbAfft2_7()
	{
		return cbAfft2_7;
	}

	public void setCbAfft2_7( Integer cbAfft2_7 )
	{
		this.cbAfft2_7 = cbAfft2_7;
	}

	public Integer getCbAfft2_8()
	{
		return cbAfft2_8;
	}

	public void setCbAfft2_8( Integer cbAfft2_8 )
	{
		this.cbAfft2_8 = cbAfft2_8;
	}

	public Integer getCbAfft2_9()
	{
		return cbAfft2_9;
	}

	public void setCbAfft2_9( Integer cbAfft2_9 )
	{
		this.cbAfft2_9 = cbAfft2_9;
	}

	public BigDecimal getCbAfft2_10()
	{
		return cbAfft2_10;
	}

	public void setCbAfft2_10( BigDecimal cbAfft2_10 )
	{
		this.cbAfft2_10 = cbAfft2_10;
	}
}
