package com.megabank.olp.apply.persistence.dao.generated.code;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeOtherBank;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeOtherBankDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeOtherBankDAO extends BasePojoDAO<CodeOtherBank, String>
{

	public CodeOtherBank getPojoByOtherBankCode( String other_bank_code )
	{
		NameValueBean bank_code = new NameValueBean( CodeOtherBank.OTHER_BANK_CODE_CONSTANT, other_bank_code );
		NameValueBean[] conditions = new NameValueBean[]{ bank_code };

		return getUniquePojoByProperties( conditions );
	}

	@Override
	protected Class<CodeOtherBank> getPojoClass()
	{
		return CodeOtherBank.class;
	}
}
