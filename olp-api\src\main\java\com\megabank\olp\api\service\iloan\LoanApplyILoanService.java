package com.megabank.olp.api.service.iloan;

import com.megabank.olp.client.sender.micro.apply.management.apply.ApplyTransferCaseClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyTransferCaseArgBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.management.apply.ApplyBranchBankUpdatedClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.ApplyLoanDiscardedClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyBranchBankUpdatedArgBean;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyLoanDiscardedArgBean;

@Service
public class LoanApplyILoanService extends BaseApiService
{

	private static final String ILOAN_CONSTANT = "iloan";

	@Autowired
	private ApplyBranchBankUpdatedClient applyBranchBankUpdatedClient;

	@Autowired
	private ApplyLoanDiscardedClient applyLoanDiscardedClient;

	@Autowired
	private ApplyTransferCaseClient applyTransferCaseClient;

	public void discardLoan( String caseNo )
	{
		String employeeId = ILOAN_CONSTANT;
		String employeeName = ILOAN_CONSTANT;

		ApplyLoanDiscardedArgBean argBean = new ApplyLoanDiscardedArgBean();
		argBean.setCaseNo( caseNo );
		argBean.setEmployeeId( employeeId );
		argBean.setEmployeeName( employeeName );

		applyLoanDiscardedClient.send( argBean, new JwtArgBean() );

	}

	public void updateBranch( String caseNo, String branchBankCode )
	{
		String employeeId = ILOAN_CONSTANT;
		String employeeName = ILOAN_CONSTANT;

		ApplyBranchBankUpdatedArgBean argBean = new ApplyBranchBankUpdatedArgBean();
		argBean.setCaseNo( caseNo );
		argBean.setBranchBankCode( branchBankCode );
		argBean.setEmployeeId( employeeId );
		argBean.setEmployeeName( employeeName );

		applyBranchBankUpdatedClient.send( argBean, new JwtArgBean() );
	}

	public void transferCase( String caseNo )
	{
		ApplyTransferCaseArgBean argBean = new ApplyTransferCaseArgBean();
		argBean.setCaseNo( caseNo );

		applyTransferCaseClient.send( argBean, new JwtArgBean() );
	}

}
