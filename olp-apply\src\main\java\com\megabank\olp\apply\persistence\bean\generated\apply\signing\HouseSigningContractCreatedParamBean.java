package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;

import java.math.BigDecimal;

public class HouseSigningContractCreatedParamBean
{
	private Long signingContractId;

	private BigDecimal guaranteeType;

	private String witness;

	private BigDecimal payeeInfoType;

	private String payeeInfoAccountType;

	private BigDecimal expireInfoType;

	private BigDecimal startYear1;

	private BigDecimal startMonth1;

	private BigDecimal startDay1;

	private BigDecimal expireYear1;

	private BigDecimal expireMonth1;

	private BigDecimal expireDay1;

	private BigDecimal duration1;

	private BigDecimal startYear2;

	private BigDecimal startMonth2;

	private BigDecimal startDay2;

	private BigDecimal expireYear2;

	private BigDecimal expireMonth2;

	private BigDecimal expireDay2;

	private BigDecimal duration2;

	private BigDecimal startYear3;

	private BigDecimal startMonth3;

	private BigDecimal startDay3;

	private BigDecimal expireYear3;

	private BigDecimal expireMonth3;

	private BigDecimal expireDay3;

	private BigDecimal duration3;

	private BigDecimal duration4;

	private BigDecimal maxYear4;

	private BigDecimal expireYear4;

	private BigDecimal expireMonth4;

	private BigDecimal expireDay4;

	private String other5;

	private BigDecimal repaymentInfoType;

	private BigDecimal limtedYear4;

	private BigDecimal limtedMonth4;

	private BigDecimal year4;

	private BigDecimal month4;

	private BigDecimal limtedYear5;

	private BigDecimal limtedMonth5;

	private BigDecimal year5;

	private BigDecimal month5;

	private BigDecimal period6;

	private BigDecimal year6;

	private String other7;

	private BigDecimal houseRedemption;

	private Double firstRate;

	private Double secondRate;

	private BigDecimal interestInfoType;

	private String formula1;

	private BigDecimal firstPeriodFrom1;

	private BigDecimal firstPeriodTo1;

	private Double firstPeriodRate1;

	private BigDecimal secondPeriodFrom1;

	private BigDecimal secondPeriodTo1;

	private Double secondPeriodRate1;

	private BigDecimal thirdPeriodFrom1;

	private BigDecimal thirdPeriodTo1;

	private Double thirdPeriodRate1;

	private Double rate1;

	private String other1;

	private String formula2;

	private Double rate2_1;

	private Double rate2_2;

	private Double rate2_3_1;

	private Double rate2_3_2;

	private String other2;

	private String other3;

	private String brNoTel;

	private String brNoFax;

	private transient ApplySigningContract applySigningContract;

	public HouseSigningContractCreatedParamBean()
	{
	}

	public Long getSigningContractId()
	{
		return signingContractId;
	}

	public BigDecimal getGuaranteeType()
	{
		return guaranteeType;
	}

	public String getWitness()
	{
		return witness;
	}

	public BigDecimal getPayeeInfoType()
	{
		return payeeInfoType;
	}

	public String getPayeeInfoAccountType()
	{
		return payeeInfoAccountType;
	}

	public BigDecimal getExpireInfoType()
	{
		return expireInfoType;
	}

	public BigDecimal getStartYear1()
	{
		return startYear1;
	}

	public BigDecimal getStartMonth1()
	{
		return startMonth1;
	}

	public BigDecimal getStartDay1()
	{
		return startDay1;
	}

	public BigDecimal getExpireYear1()
	{
		return expireYear1;
	}

	public BigDecimal getExpireMonth1()
	{
		return expireMonth1;
	}

	public BigDecimal getExpireDay1()
	{
		return expireDay1;
	}

	public BigDecimal getDuration1()
	{
		return duration1;
	}

	public BigDecimal getStartYear2()
	{
		return startYear2;
	}

	public BigDecimal getStartMonth2()
	{
		return startMonth2;
	}

	public BigDecimal getStartDay2()
	{
		return startDay2;
	}

	public BigDecimal getExpireYear2()
	{
		return expireYear2;
	}

	public BigDecimal getExpireMonth2()
	{
		return expireMonth2;
	}

	public BigDecimal getExpireDay2()
	{
		return expireDay2;
	}

	public BigDecimal getDuration2()
	{
		return duration2;
	}

	public BigDecimal getStartYear3()
	{
		return startYear3;
	}

	public BigDecimal getStartMonth3()
	{
		return startMonth3;
	}

	public BigDecimal getStartDay3()
	{
		return startDay3;
	}

	public BigDecimal getExpireYear3()
	{
		return expireYear3;
	}

	public BigDecimal getExpireMonth3()
	{
		return expireMonth3;
	}

	public BigDecimal getExpireDay3()
	{
		return expireDay3;
	}

	public BigDecimal getDuration3()
	{
		return duration3;
	}

	public BigDecimal getDuration4()
	{
		return duration4;
	}

	public BigDecimal getMaxYear4()
	{
		return maxYear4;
	}

	public BigDecimal getExpireYear4()
	{
		return expireYear4;
	}

	public BigDecimal getExpireMonth4()
	{
		return expireMonth4;
	}

	public BigDecimal getExpireDay4()
	{
		return expireDay4;
	}

	public String getOther5()
	{
		return other5;
	}

	public BigDecimal getRepaymentInfoType()
	{
		return repaymentInfoType;
	}

	public BigDecimal getLimtedYear4()
	{
		return limtedYear4;
	}

	public BigDecimal getLimtedMonth4()
	{
		return limtedMonth4;
	}

	public BigDecimal getYear4()
	{
		return year4;
	}

	public BigDecimal getMonth4()
	{
		return month4;
	}

	public BigDecimal getLimtedYear5()
	{
		return limtedYear5;
	}

	public BigDecimal getLimtedMonth5()
	{
		return limtedMonth5;
	}

	public BigDecimal getYear5()
	{
		return year5;
	}

	public BigDecimal getMonth5()
	{
		return month5;
	}

	public BigDecimal getPeriod6()
	{
		return period6;
	}

	public BigDecimal getYear6()
	{
		return year6;
	}

	public String getOther7()
	{
		return other7;
	}

	public BigDecimal getHouseRedemption()
	{
		return houseRedemption;
	}

	public Double getFirstRate()
	{
		return firstRate;
	}

	public Double getSecondRate()
	{
		return secondRate;
	}

	public BigDecimal getInterestInfoType()
	{
		return interestInfoType;
	}

	public String getFormula1()
	{
		return formula1;
	}

	public BigDecimal getFirstPeriodFrom1()
	{
		return firstPeriodFrom1;
	}

	public BigDecimal getFirstPeriodTo1()
	{
		return firstPeriodTo1;
	}

	public Double getFirstPeriodRate1()
	{
		return firstPeriodRate1;
	}

	public BigDecimal getSecondPeriodFrom1()
	{
		return secondPeriodFrom1;
	}

	public BigDecimal getSecondPeriodTo1()
	{
		return secondPeriodTo1;
	}

	public Double getSecondPeriodRate1()
	{
		return secondPeriodRate1;
	}

	public BigDecimal getThirdPeriodFrom1()
	{
		return thirdPeriodFrom1;
	}

	public BigDecimal getThirdPeriodTo1()
	{
		return thirdPeriodTo1;
	}

	public Double getThirdPeriodRate1()
	{
		return thirdPeriodRate1;
	}

	public Double getRate1()
	{
		return rate1;
	}

	public String getOther1()
	{
		return other1;
	}

	public String getFormula2()
	{
		return formula2;
	}

	public Double getRate2_1()
	{
		return rate2_1;
	}

	public Double getRate2_2()
	{
		return rate2_2;
	}

	public Double getRate2_3_1()
	{
		return rate2_3_1;
	}

	public Double getRate2_3_2()
	{
		return rate2_3_2;
	}

	public String getOther2()
	{
		return other2;
	}

	public String getOther3()
	{
		return other3;
	}

	public String getBrNoTel()
	{
		return brNoTel;
	}

	public String getBrNoFax()
	{
		return brNoFax;
	}

	public ApplySigningContract getApplySigningContract()
	{
		return applySigningContract;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setGuaranteeType( BigDecimal guaranteeType )
	{
		this.guaranteeType = guaranteeType;
	}

	public void setWitness( String witness )
	{
		this.witness = witness;
	}

	public void setPayeeInfoType( BigDecimal payeeInfoType )
	{
		this.payeeInfoType = payeeInfoType;
	}

	public void setPayeeInfoAccountType( String payeeInfoAccountType )
	{
		this.payeeInfoAccountType = payeeInfoAccountType;
	}

	public void setExpireInfoType( BigDecimal expireInfoType )
	{
		this.expireInfoType = expireInfoType;
	}

	public void setStartYear1( BigDecimal startYear1 )
	{
		this.startYear1 = startYear1;
	}

	public void setStartMonth1( BigDecimal startMonth1 )
	{
		this.startMonth1 = startMonth1;
	}

	public void setStartDay1( BigDecimal startDay1 )
	{
		this.startDay1 = startDay1;
	}

	public void setExpireYear1( BigDecimal expireYear1 )
	{
		this.expireYear1 = expireYear1;
	}

	public void setExpireMonth1( BigDecimal expireMonth1 )
	{
		this.expireMonth1 = expireMonth1;
	}

	public void setExpireDay1( BigDecimal expireDay1 )
	{
		this.expireDay1 = expireDay1;
	}

	public void setDuration1( BigDecimal duration1 )
	{
		this.duration1 = duration1;
	}

	public void setStartYear2( BigDecimal startYear2 )
	{
		this.startYear2 = startYear2;
	}

	public void setStartMonth2( BigDecimal startMonth2 )
	{
		this.startMonth2 = startMonth2;
	}

	public void setStartDay2( BigDecimal startDay2 )
	{
		this.startDay2 = startDay2;
	}

	public void setExpireYear2( BigDecimal expireYear2 )
	{
		this.expireYear2 = expireYear2;
	}

	public void setExpireMonth2( BigDecimal expireMonth2 )
	{
		this.expireMonth2 = expireMonth2;
	}

	public void setExpireDay2( BigDecimal expireDay2 )
	{
		this.expireDay2 = expireDay2;
	}

	public void setDuration2( BigDecimal duration2 )
	{
		this.duration2 = duration2;
	}

	public void setStartYear3( BigDecimal startYear3 )
	{
		this.startYear3 = startYear3;
	}

	public void setStartMonth3( BigDecimal startMonth3 )
	{
		this.startMonth3 = startMonth3;
	}

	public void setStartDay3( BigDecimal startDay3 )
	{
		this.startDay3 = startDay3;
	}

	public void setExpireYear3( BigDecimal expireYear3 )
	{
		this.expireYear3 = expireYear3;
	}

	public void setExpireMonth3( BigDecimal expireMonth3 )
	{
		this.expireMonth3 = expireMonth3;
	}

	public void setExpireDay3( BigDecimal expireDay3 )
	{
		this.expireDay3 = expireDay3;
	}

	public void setDuration3( BigDecimal duration3 )
	{
		this.duration3 = duration3;
	}

	public void setDuration4( BigDecimal duration4 )
	{
		this.duration4 = duration4;
	}

	public void setMaxYear4( BigDecimal maxYear4 )
	{
		this.maxYear4 = maxYear4;
	}

	public void setExpireYear4( BigDecimal expireYear4 )
	{
		this.expireYear4 = expireYear4;
	}

	public void setExpireMonth4( BigDecimal expireMonth4 )
	{
		this.expireMonth4 = expireMonth4;
	}

	public void setExpireDay4( BigDecimal expireDay4 )
	{
		this.expireDay4 = expireDay4;
	}

	public void setOther5( String other5 )
	{
		this.other5 = other5;
	}

	public void setRepaymentInfoType( BigDecimal repaymentInfoType )
	{
		this.repaymentInfoType = repaymentInfoType;
	}

	public void setLimtedYear4( BigDecimal limtedYear4 )
	{
		this.limtedYear4 = limtedYear4;
	}

	public void setLimtedMonth4( BigDecimal limtedMonth4 )
	{
		this.limtedMonth4 = limtedMonth4;
	}

	public void setYear4( BigDecimal year4 )
	{
		this.year4 = year4;
	}

	public void setMonth4( BigDecimal month4 )
	{
		this.month4 = month4;
	}

	public void setLimtedYear5( BigDecimal limtedYear5 )
	{
		this.limtedYear5 = limtedYear5;
	}

	public void setLimtedMonth5( BigDecimal limtedMonth5 )
	{
		this.limtedMonth5 = limtedMonth5;
	}

	public void setYear5( BigDecimal year5 )
	{
		this.year5 = year5;
	}

	public void setMonth5( BigDecimal month5 )
	{
		this.month5 = month5;
	}

	public void setPeriod6( BigDecimal period6 )
	{
		this.period6 = period6;
	}

	public void setYear6( BigDecimal year6 )
	{
		this.year6 = year6;
	}

	public void setOther7( String other7 )
	{
		this.other7 = other7;
	}

	public void setHouseRedemption( BigDecimal houseRedemption )
	{
		this.houseRedemption = houseRedemption;
	}

	public void setFirstRate( Double firstRate )
	{
		this.firstRate = firstRate;
	}

	public void setSecondRate( Double secondRate )
	{
		this.secondRate = secondRate;
	}

	public void setInterestInfoType( BigDecimal interestInfoType )
	{
		this.interestInfoType = interestInfoType;
	}

	public void setFormula1( String formula1 )
	{
		this.formula1 = formula1;
	}

	public void setFirstPeriodFrom1( BigDecimal firstPeriodFrom1 )
	{
		this.firstPeriodFrom1 = firstPeriodFrom1;
	}

	public void setFirstPeriodTo1( BigDecimal firstPeriodTo1 )
	{
		this.firstPeriodTo1 = firstPeriodTo1;
	}

	public void setFirstPeriodRate1( Double firstPeriodRate1 )
	{
		this.firstPeriodRate1 = firstPeriodRate1;
	}

	public void setSecondPeriodFrom1( BigDecimal secondPeriodFrom1 )
	{
		this.secondPeriodFrom1 = secondPeriodFrom1;
	}

	public void setSecondPeriodTo1( BigDecimal secondPeriodTo1 )
	{
		this.secondPeriodTo1 = secondPeriodTo1;
	}

	public void setSecondPeriodRate1( Double secondPeriodRate1 )
	{
		this.secondPeriodRate1 = secondPeriodRate1;
	}

	public void setThirdPeriodFrom1( BigDecimal thirdPeriodFrom1 )
	{
		this.thirdPeriodFrom1 = thirdPeriodFrom1;
	}

	public void setThirdPeriodTo1( BigDecimal thirdPeriodTo1 )
	{
		this.thirdPeriodTo1 = thirdPeriodTo1;
	}

	public void setThirdPeriodRate1( Double thirdPeriodRate1 )
	{
		this.thirdPeriodRate1 = thirdPeriodRate1;
	}

	public void setRate1( Double rate1 )
	{
		this.rate1 = rate1;
	}

	public void setOther1( String other1 )
	{
		this.other1 = other1;
	}

	public void setFormula2( String formula2 )
	{
		this.formula2 = formula2;
	}

	public void setRate2_1( Double rate2_1 )
	{
		this.rate2_1 = rate2_1;
	}

	public void setRate2_2( Double rate2_2 )
	{
		this.rate2_2 = rate2_2;
	}

	public void setRate2_3_1( Double rate2_3_1 )
	{
		this.rate2_3_1 = rate2_3_1;
	}

	public void setRate2_3_2( Double rate2_3_2 )
	{
		this.rate2_3_2 = rate2_3_2;
	}

	public void setOther2( String other2 )
	{
		this.other2 = other2;
	}

	public void setOther3( String other3 )
	{
		this.other3 = other3;
	}

	public void setApplySigningContract( ApplySigningContract applySigningContract )
	{
		this.applySigningContract = applySigningContract;
	}

	public void setBrNoTel( String brNoTel )
	{
		this.brNoTel = brNoTel;
	}

	public void setBrNoFax( String brNoFax )
	{
		this.brNoFax = brNoFax;
	}
}
