package com.megabank.olp.apply.controller.management.iloan;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.signing.LoanConditionInfoBean;
import com.megabank.olp.apply.controller.management.bean.signing.LoanPurposeInfoBean;
import com.megabank.olp.apply.controller.management.bean.signing.PaymentInfoCreateArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.SigningContractDiscardArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.SigningContractSendArgBean;
import com.megabank.olp.apply.service.management.ContractService;
import com.megabank.olp.apply.service.management.bean.signing.ContractCreatedParamBean;
import com.megabank.olp.apply.service.management.bean.signing.LoanConditionDataBean;
import com.megabank.olp.apply.service.management.bean.signing.LoanPurposeDataBean;
import com.megabank.olp.apply.service.management.bean.signing.PaymentInfoCreateParamBean;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.utility.web.CommonAppUtils;

@RestController
@RequestMapping( "management/signingcontract/iloan" )
public class ContractILoanController extends BaseController
{
	@Autowired
	private ContractService service;

	@PostMapping( "discardSigningContract" )
	public Map<String, Object> iloanDiscardSigningContract( @RequestBody @Validated SigningContractDiscardArgBean argBean )
	{
		return getResponseMap( service.setDiscard( argBean.getContractNo() ) );
	}

	@PostMapping( "sendPaymentInfo" )
	public Map<String, Object> sendPaymentInfo( @RequestBody @Validated PaymentInfoCreateArgBean argBean ) throws Exception
	{
		PaymentInfoCreateParamBean paramBean = mapPaymentInfo( argBean );

		Long contractId = service.setPaymentInfo( paramBean );

		service.saveSigningContractPdf( paramBean );

		service.informCustomerAppropirated( paramBean );

		return getResponseMap( contractId );
	}

	@PostMapping( "sendSigningContract" )
	public Map<String, Object> sendSigningContract( @RequestBody @Validated SigningContractSendArgBean argBean ) throws MyRuntimeException
	{
		String clientAddress = CommonAppUtils.getClientAddress( request );

		return getResponseMap( service.createSigningContract( mapCreatedParamBean( argBean ), clientAddress ) );
	}

	private ContractCreatedParamBean mapCreatedParamBean( SigningContractSendArgBean argBean )
	{
		ContractCreatedParamBean paramBean = new ContractCreatedParamBean();
		paramBean.setLoanType( argBean.getLoanType() );
		paramBean.setBranchCode( argBean.getBranchCode() );
		paramBean.setBorrowerBirthDate( argBean.getBorrowerBirthDate() );
		paramBean.setBorrowerId( argBean.getBorrowerId() );
		paramBean.setBorrowerMobileNumber( argBean.getBorrowerMobileNumber() );
		paramBean.setBorrowerName( argBean.getBorrowerName() );
		paramBean.setBorrowerEmail( argBean.getBorrowerEmail() );
		paramBean.setIsBorrowerYouth( argBean.getIsBorrowerYouth() );
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setContractVersion( argBean.getContractVersion() );
		paramBean.setCourtName( argBean.getCourtName() );
		paramBean.setExpiredDate( argBean.getExpiredDate() );
		paramBean.setGuaranteeAmt( argBean.getGuaranteeAmt() );
		paramBean.setGeneralGuaranteePlan( argBean.getGeneralGuaranteePlan() );
		paramBean.setGeneralGuaranteePlanInfo( argBean.getGeneralGuaranteePlanInfo() );
		paramBean.setJointGuaranteePlan( argBean.getJointGuaranteePlan() );
		paramBean.setJointGuaranteePlanInfo( argBean.getJointGuaranteePlanInfo() );
		paramBean.setProductCode( argBean.getProductCode() );
		paramBean.setRelatedPersonBirthDate( argBean.getRelatedPersonBirthDate() );
		paramBean.setRelatedPersonId( argBean.getRelatedPersonId() );
		paramBean.setRelatedPersonMobileNumber( argBean.getRelatedPersonMobileNumber() );
		paramBean.setRelatedPersonName( argBean.getRelatedPersonName() );
		paramBean.setRelatedPersonType( argBean.getRelatedPersonType() );
		paramBean.setRelatedPersonEmail( argBean.getRelatedPersonEmail() );
		paramBean.setLoanAccts( argBean.getLoanAccts() );
		paramBean.setLoanConditionDataBean( mapLoanConditionDataBean( argBean.getLoanConditionInfoBean() ) );
		paramBean.setLoanPlan( argBean.getLoanPlan() );
		paramBean.setGrpCntrNo( argBean.getGrpCntrNo() );
		paramBean.setGivenApprBegDate( argBean.getGivenApprBegDate() );
		paramBean.setGivenApprEndDate( argBean.getGivenApprEndDate() );
		paramBean.setPayeeBankCode( argBean.getPayeeBankCode() );
		paramBean.setPayeeBankAccountNo( argBean.getPayeeBankAccountNo() );
		paramBean.setPayeeBankAccountName( argBean.getPayeeBankAccountName() );
		paramBean.setPayeeTotalAmt( argBean.getPayeeTotalAmt() );
		paramBean.setPayeeRemittance( argBean.getPayeeRemittance() );
		paramBean.setPayeeSelfProvide( argBean.getPayeeSelfProvide() );
		paramBean.setBaseRate( argBean.getBaseRate() );
		paramBean.setRateList( argBean.getRateList() );
		paramBean.setIsRepayment( argBean.getIsRepayment() );
		paramBean.setRepamentList( argBean.getRepaymentList() );
		paramBean.setRefSystemId( argBean.getRefSystemId() );

		return paramBean;
	}

	private LoanConditionDataBean mapLoanConditionDataBean( LoanConditionInfoBean infoBean )
	{
		LoanConditionDataBean dataBean = new LoanConditionDataBean();
		dataBean.setCreditCheckFee( infoBean.getCreditCheckFee() );
		dataBean.setDrawDownType( infoBean.getDrawDownType() );
		dataBean.setLendingPlan( infoBean.getLendingPlan() );
		dataBean.setLoanAmt( infoBean.getLoanAmt() );
		dataBean.setLoanPeriod( infoBean.getLoanPeriod() );
		dataBean.setLoanPurposeDataBeans( mapLoanPurposeDataBeans( infoBean.getLoanPurposeInfoBeans() ) );
		dataBean.setOneTimeFee( infoBean.getOneTimeFee() );
		dataBean.setPreliminaryFee( infoBean.getPreliminaryFee() );
		dataBean.setRepaymentMethod( infoBean.getRepaymentMethod() );
		dataBean.setAdvancedRedemptionTitle( infoBean.getAdvancedRedemptionTitle() );
		dataBean.setAdvancedRedemptionDesc( infoBean.getAdvancedRedemptionDesc() );
		dataBean.setAdvancedRateTitle( infoBean.getAdvancedRateTitle() );
		dataBean.setAdvancedRateDesc( infoBean.getAdvancedRateDesc() );
		dataBean.setAdvancedApr( infoBean.getAdvancedApr() );
		dataBean.setLimitedRedemptionTitle( infoBean.getLimitedRedemptionTitle() );
		dataBean.setLimitedRedemptionDesc( infoBean.getLimitedRedemptionDesc() );
		dataBean.setLimitedRateTitle( infoBean.getLimitedRateTitle() );
		dataBean.setLimitedRateDesc( infoBean.getLimitedRateDesc() );
		dataBean.setLimitedApr( infoBean.getLimitedApr() );
		dataBean.setOtherInfoTitle( infoBean.getOtherInfoTitle() );
		dataBean.setOtherInfoDesc( infoBean.getOtherInfoDesc() );
		dataBean.setShowOption( infoBean.getShowOption() );

		return dataBean;
	}

	private List<LoanPurposeDataBean> mapLoanPurposeDataBeans( List<LoanPurposeInfoBean> loanPurposeInfoBeans )
	{
		List<LoanPurposeDataBean> dataBeans = new ArrayList<>();

		for( LoanPurposeInfoBean loanPurposeInfoBean : loanPurposeInfoBeans )
		{
			LoanPurposeDataBean dataBean = new LoanPurposeDataBean();
			dataBean.setLoanPurpose( loanPurposeInfoBean.getLoanPurpose() );
			dataBean.setIsChecked( loanPurposeInfoBean.getIsChecked() );

			dataBeans.add( dataBean );

		}

		return dataBeans;
	}

	private PaymentInfoCreateParamBean mapPaymentInfo( PaymentInfoCreateArgBean argBean )
	{
		PaymentInfoCreateParamBean paramBean = new PaymentInfoCreateParamBean();
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setPreliminaryFee( argBean.getPreliminaryFee() );
		paramBean.setCrChkFee( argBean.getCrChkFee() );
		paramBean.setPaymentInfoList( argBean.getPaymentInfoList() );

		return paramBean;
	}

}
