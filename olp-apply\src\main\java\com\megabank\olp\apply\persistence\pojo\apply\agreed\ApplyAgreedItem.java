package com.megabank.olp.apply.persistence.pojo.apply.agreed;

import static jakarta.persistence.GenerationType.IDENTITY;

import com.megabank.olp.base.bean.BaseBean;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

/**
 * The ApplyAgreedItem is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_agreed_item" )
public class ApplyAgreedItem extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_agreed_item";

	public static final String AGREED_ITEM_ID_CONSTANT = "agreedItemId";

	public static final String APPLY_AGREED_CONSTANT = "applyAgreed";

	public static final String CONTENT_CONSTANT = "content";

	public static final String ITEM_VALUE_CONSTANT = "itemValue";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	public static final String NEED_TO_CHECK_CONSTANT = "needToCheck";

	public static final String AGREED_GROUP_CONSTANT = "agreedGroup";

	private Long agreedItemId;

	private transient ApplyAgreed applyAgreed;

	private String content;

	private String itemValue;

	private int displayOrder;

	private boolean needToCheck;

	private String agreedGroup;

	private String itemType;

	public ApplyAgreedItem()
	{}

	public ApplyAgreedItem( ApplyAgreed applyAgreed, String content, int displayOrder, boolean needToCheck )
	{
		this.applyAgreed = applyAgreed;
		this.content = content;
		this.displayOrder = displayOrder;
		this.needToCheck = needToCheck;
	}

	public ApplyAgreedItem( Long agreedItemId )
	{
		this.agreedItemId = agreedItemId;
	}

	@Column( name = "agreed_group" )
	public String getAgreedGroup()
	{
		return agreedGroup;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "agreed_item_id", unique = true, nullable = false )
	public Long getAgreedItemId()
	{
		return agreedItemId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "agreed_id", nullable = false )
	public ApplyAgreed getApplyAgreed()
	{
		return applyAgreed;
	}

	@Column( name = "content", nullable = false )
	public String getContent()
	{
		return content;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "item_type", length = 50 )
	public String getItemType()
	{
		return itemType;
	}

	@Column( name = "item_value", length = 50 )
	public String getItemValue()
	{
		return itemValue;
	}

	@Column( name = "need_to_check", nullable = false, precision = 1, scale = 0 )
	public boolean isNeedToCheck()
	{
		return needToCheck;
	}

	public void setAgreedGroup( String agreedGroup )
	{
		this.agreedGroup = agreedGroup;
	}

	public void setAgreedItemId( Long agreedItemId )
	{
		this.agreedItemId = agreedItemId;
	}

	public void setApplyAgreed( ApplyAgreed applyAgreed )
	{
		this.applyAgreed = applyAgreed;
	}

	public void setContent( String content )
	{
		this.content = content;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setItemType( String itemType )
	{
		this.itemType = itemType;
	}

	public void setItemValue( String itemValue )
	{
		this.itemValue = itemValue;
	}

	public void setNeedToCheck( boolean needToCheck )
	{
		this.needToCheck = needToCheck;
	}
}