package com.megabank.olp.apply.persistence.pojo.apply.loan;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.megabank.olp.apply.persistence.pojo.apply.attachment.ApplyAttachment;
import com.megabank.olp.apply.persistence.pojo.apply.mydata.ApplyMyData;
import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.apply.persistence.pojo.code.CodeApplyStatus;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeLoanType;
import com.megabank.olp.apply.persistence.pojo.code.CodeProcess;
import com.megabank.olp.apply.persistence.pojo.code.CodeRecipientSystem;
import com.megabank.olp.apply.persistence.pojo.code.CodeTransmissionStatus;
import com.megabank.olp.apply.persistence.pojo.temp.TempUploadFile;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;

/**
 * The ApplyLoan is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan", uniqueConstraints = @UniqueConstraint( columnNames = "case_no" ) )
public class ApplyLoan extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan";

	public static final String LOAN_ID_CONSTANT = "loanId";

	public static final String REF_BORROWER_APPLY_LOAN_CONSTANT = "refBorrowerApplyLoan";

	public static final String CODE_APPLY_STATUS_CONSTANT = "codeApplyStatus";

	public static final String CODE_BRANCH_BANK_CONSTANT = "codeBranchBank";

	public static final String CODE_LOAN_TYPE_CONSTANT = "codeLoanType";

	public static final String CODE_PROCESS_CONSTANT = "codeProcess";

	public static final String CODE_TRANSMISSION_STATUS_CONSTANT = "codeTransmissionStatus";

	public static final String CASE_NO_CONSTANT = "caseNo";

	public static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	public static final String AGREED_DATE_CONSTANT = "agreedDate";

	public static final String APPLY_COMPLETED_DATE_CONSTANT = "applyCompletedDate";

	public static final String PDF_CONTENT_CONSTANT = "pdfContent";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String NOT_COMPLETED_UPDATED_DATE_CONSTANT = "notCompletedUpdatedDate";

	public static final String LOAN_VERSION_CONSTANT = "loanVersion";

	public static final String RESEND_COUNT_CONSTANT = "resendCount";

	public static final String IDENTITY_FLAG_CONSTANT = "identityFlag";

	public static final String DISCARD_CONSTANT = "discard";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String LOAN_PLAN_CODE_CONSTANT = "loanPlanCode"; // J-110-0373 中鋼消貸線上申請暨對保作業

	public static final String INTRODUCE_BR_NO_CONSTANT = "introduceBrNo"; // J-110-0543 引介分行

	public static final String INTRODUCE_BR1ST_CONSTANT = "introduceBr1st";

	public static final String APPLY_LOAN_GUARANTEE_INFO_CONSTANT = "applyLoanGuaranteeInfo";

	public static final String APPLY_NOTES_CONSTANT = "applyNotes";

	public static final String APPLY_LOAN_CONTENT_CONSTANT = "applyLoanContent";

	public static final String APPLY_ATTACHMENTS_CONSTANT = "applyAttachments";

	public static final String APPLY_MY_DATAS_CONSTANT = "applyMyDatas";

	public static final String TEMP_UPLOAD_FILES_CONSTANT = "tempUploadFiles";

	public static final String APPLY_LOAN_CONTACT_INFO_CONSTANT = "applyLoanContactInfo";

	public static final String APPLY_LOAN_OCCUPATION_CONSTANT = "applyLoanOccupation";

	public static final String APPLY_LOAN_BASIC_CONSTANT = "applyLoanBasic";

	private Long loanId;

	private transient ApplyLoan refBorrowerApplyLoan;

	private transient CodeApplyStatus codeApplyStatus;

	private transient CodeBranchBank codeBranchBank;

	private transient CodeLoanType codeLoanType;

	private transient CodeProcess codeProcess;

	private transient CodeTransmissionStatus codeTransmissionStatus;

	private String caseNo;

	private long validatedIdentityId;

	private Date agreedDate;

	private Date applyCompletedDate;

	private transient ImmutableByteArray pdfContent;

	private boolean notified;

	private Date notCompletedUpdatedDate;

	private String loanVersion;

	private int resendCount;

	/* 參考 IdentityFlagEnum ，組合出來的值域 {0：無, 1：行員身分, 2：貸款戶, 3：行員身分且貸款戶} */
	private int identityFlag;

	private boolean discard;

	private Date updatedDate;

	private Date createdDate;

	private String loanPlanCode;

	private String introduceBrNo;

	private String introduceBr1st;

	private transient ApplyLoanGuaranteeInfo applyLoanGuaranteeInfo;

	private transient Set<ApplyNote> applyNotes = new HashSet<>( 0 );

	private transient ApplyLoanContent applyLoanContent;

	private transient Set<ApplyAttachment> applyAttachments = new HashSet<>( 0 );

	private transient Set<ApplyMyData> applyMyDatas = new HashSet<>( 0 );

	private transient Set<TempUploadFile> tempUploadFiles = new HashSet<>( 0 );

	private transient ApplyLoanContactInfo applyLoanContactInfo;

	private transient ApplyLoanOccupation applyLoanOccupation;

	private transient ApplyLoanBasic applyLoanBasic;

	private transient CodeRecipientSystem codeRecipientSystem;

	public ApplyLoan()
	{}

	public ApplyLoan( CodeApplyStatus codeApplyStatus, CodeLoanType codeLoanType, CodeProcess codeProcess,
					  CodeTransmissionStatus codeTransmissionStatus, String caseNo, long validatedIdentityId, boolean notified, int resendCount,
					  int identityFlag, boolean discard, Date updatedDate, Date createdDate, String loanPlanCode )
	{
		this.codeApplyStatus = codeApplyStatus;
		this.codeLoanType = codeLoanType;
		this.codeProcess = codeProcess;
		this.codeTransmissionStatus = codeTransmissionStatus;
		this.caseNo = caseNo;
		this.validatedIdentityId = validatedIdentityId;
		this.notified = notified;
		this.resendCount = resendCount;
		this.identityFlag = identityFlag;
		this.discard = discard;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
		this.loanPlanCode = loanPlanCode;
	}

	public ApplyLoan( Long loanId )
	{
		this.loanId = loanId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "agreed_date", length = 23 )
	public Date getAgreedDate()
	{
		return agreedDate;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public Set<ApplyAttachment> getApplyAttachments()
	{
		return applyAttachments;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "apply_completed_date", length = 23 )
	public Date getApplyCompletedDate()
	{
		return applyCompletedDate;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public ApplyLoanBasic getApplyLoanBasic()
	{
		return applyLoanBasic;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public ApplyLoanContactInfo getApplyLoanContactInfo()
	{
		return applyLoanContactInfo;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public ApplyLoanContent getApplyLoanContent()
	{
		return applyLoanContent;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public ApplyLoanGuaranteeInfo getApplyLoanGuaranteeInfo()
	{
		return applyLoanGuaranteeInfo;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public ApplyLoanOccupation getApplyLoanOccupation()
	{
		return applyLoanOccupation;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public Set<ApplyMyData> getApplyMyDatas()
	{
		return applyMyDatas;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public Set<ApplyNote> getApplyNotes()
	{
		return applyNotes;
	}

	@Column( name = "case_no", unique = true, nullable = false, length = 30 )
	public String getCaseNo()
	{
		return caseNo;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "apply_status_code", nullable = false )
	public CodeApplyStatus getCodeApplyStatus()
	{
		return codeApplyStatus;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "final_branch_bank_id" )
	public CodeBranchBank getCodeBranchBank()
	{
		return codeBranchBank;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_type", nullable = false )
	public CodeLoanType getCodeLoanType()
	{
		return codeLoanType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "process_code", nullable = false )
	public CodeProcess getCodeProcess()
	{
		return codeProcess;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_recipient_id" )
	public CodeRecipientSystem getCodeRecipientSystem()
	{
		return codeRecipientSystem;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "transmission_status_code", nullable = false )
	public CodeTransmissionStatus getCodeTransmissionStatus()
	{
		return codeTransmissionStatus;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "identity_flag", nullable = false, precision = 5, scale = 0 )
	public int getIdentityFlag()
	{
		return identityFlag;
	}

	@Column( name = "introduce_br_1st", length = 3 )
	public String getIntroduceBr1st()
	{
		return introduceBr1st;
	}

	@Column( name = "introduce_br_no", length = 3 )
	public String getIntroduceBrNo()
	{
		return introduceBrNo;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "loan_id", unique = true, nullable = false )
	public Long getLoanId()
	{
		return loanId;
	}

	@Column( name = "loan_plan_code", nullable = false, length = 20 )
	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	@Column( name = "loan_version", length = 30 )
	public String getLoanVersion()
	{
		return loanVersion;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "not_completed_updated_date", length = 23 )
	public Date getNotCompletedUpdatedDate()
	{
		return notCompletedUpdatedDate;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "pdf_content" ) ) } )
	public ImmutableByteArray getPdfContent()
	{
		return pdfContent;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "ref_borrower_apply_loan" )
	public ApplyLoan getRefBorrowerApplyLoan()
	{
		return refBorrowerApplyLoan;
	}

	@Column( name = "resend_count", nullable = false, precision = 5, scale = 0 )
	public int getResendCount()
	{
		return resendCount;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyLoan" )
	public Set<TempUploadFile> getTempUploadFiles()
	{
		return tempUploadFiles;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "validated_identity_id", nullable = false )
	public long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	@Column( name = "discard", nullable = false, precision = 1, scale = 0 )
	public boolean isDiscard()
	{
		return discard;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	public void setAgreedDate( Date agreedDate )
	{
		this.agreedDate = agreedDate;
	}

	public void setApplyAttachments( Set<ApplyAttachment> applyAttachments )
	{
		this.applyAttachments = applyAttachments;
	}

	public void setApplyCompletedDate( Date applyCompletedDate )
	{
		this.applyCompletedDate = applyCompletedDate;
	}

	public void setApplyLoanBasic( ApplyLoanBasic applyLoanBasic )
	{
		this.applyLoanBasic = applyLoanBasic;
	}

	public void setApplyLoanContactInfo( ApplyLoanContactInfo applyLoanContactInfo )
	{
		this.applyLoanContactInfo = applyLoanContactInfo;
	}

	public void setApplyLoanContent( ApplyLoanContent applyLoanContent )
	{
		this.applyLoanContent = applyLoanContent;
	}

	public void setApplyLoanGuaranteeInfo( ApplyLoanGuaranteeInfo applyLoanGuaranteeInfo )
	{
		this.applyLoanGuaranteeInfo = applyLoanGuaranteeInfo;
	}

	public void setApplyLoanOccupation( ApplyLoanOccupation applyLoanOccupation )
	{
		this.applyLoanOccupation = applyLoanOccupation;
	}

	public void setApplyMyDatas( Set<ApplyMyData> applyMyDatas )
	{
		this.applyMyDatas = applyMyDatas;
	}

	public void setApplyNotes( Set<ApplyNote> applyNotes )
	{
		this.applyNotes = applyNotes;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCodeApplyStatus( CodeApplyStatus codeApplyStatus )
	{
		this.codeApplyStatus = codeApplyStatus;
	}

	public void setCodeBranchBank( CodeBranchBank codeBranchBank )
	{
		this.codeBranchBank = codeBranchBank;
	}

	public void setCodeLoanType( CodeLoanType codeLoanType )
	{
		this.codeLoanType = codeLoanType;
	}

	public void setCodeProcess( CodeProcess codeProcess )
	{
		this.codeProcess = codeProcess;
	}

	public void setCodeRecipientSystem( CodeRecipientSystem codeRecipientSystem )
	{
		this.codeRecipientSystem = codeRecipientSystem;
	}

	public void setCodeTransmissionStatus( CodeTransmissionStatus codeTransmissionStatus )
	{
		this.codeTransmissionStatus = codeTransmissionStatus;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setDiscard( boolean discard )
	{
		this.discard = discard;
	}

	public void setIdentityFlag( int identityFlag )
	{
		this.identityFlag = identityFlag;
	}

	public void setIntroduceBr1st( String introduceBr1st )
	{
		this.introduceBr1st = introduceBr1st;
	}

	public void setIntroduceBrNo( String introduceBrNo )
	{
		this.introduceBrNo = introduceBrNo;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanRecipient( CodeRecipientSystem recipientId )
	{
		codeRecipientSystem = recipientId;
	}

	public void setLoanVersion( String loanVersion )
	{
		this.loanVersion = loanVersion;
	}

	public void setNotCompletedUpdatedDate( Date notCompletedUpdatedDate )
	{
		this.notCompletedUpdatedDate = notCompletedUpdatedDate;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setPdfContent( ImmutableByteArray pdfContent )
	{
		this.pdfContent = pdfContent;
	}

	public void setRefBorrowerApplyLoan( ApplyLoan refBorrowerApplyLoan )
	{
		this.refBorrowerApplyLoan = refBorrowerApplyLoan;
	}

	public void setResendCount( int resendCount )
	{
		this.resendCount = resendCount;
	}

	public void setTempUploadFiles( Set<TempUploadFile> tempUploadFiles )
	{
		this.tempUploadFiles = tempUploadFiles;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

	public void setValidatedIdentityId( long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}
}