/**
 *
 */
package com.megabank.olp.api.service.eloan;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoCreateParamBean;
import com.megabank.olp.api.controller.eloan.bean.signing.RateDataBean;
import com.megabank.olp.api.controller.eloan.bean.signing.RepaymentBean;
import com.megabank.olp.api.service.eloan.bean.BankAccountDataBean;
import com.megabank.olp.api.service.eloan.bean.ContractCtrTypeCCreatedAPIParamBean;
import com.megabank.olp.api.service.eloan.bean.GuaranteeDataBean;
import com.megabank.olp.api.service.eloan.bean.LendingPlanDataBean;
import com.megabank.olp.api.service.eloan.bean.LoanConditionDataBean;
import com.megabank.olp.api.service.eloan.bean.LoanPurposeDataBean;
import com.megabank.olp.api.service.eloan.bean.SigningContractCreatedParamBean;
import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.api.utility.ObjectConversionUtils;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.ProductCodeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.ContractCtrTypeCClient;
import com.megabank.olp.client.sender.micro.apply.management.signing.ContractPaymentInfoClient;
import com.megabank.olp.client.sender.micro.apply.management.signing.SigningContractCreatedClient;
import com.megabank.olp.client.sender.micro.apply.management.signing.SigningContractInvalidClient;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft1ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft2ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft3ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft4ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft5ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.ContractCtrTypeCApplyClientArgBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.ExpireInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.InterestInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.LoanConditionInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.LoanPurposeInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.PayeeInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoCreateArgBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.RateInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.SigningContractCreatedArgBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.SigningContractDiscardArgBean;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
public class SigningContractService extends BaseApiService
{
	@Autowired
	private ContractCtrTypeCClient contractCtrTypeCClient;

	@Autowired
	private SigningContractCreatedClient contractCreatedClient;

	@Autowired
	private SigningContractInvalidClient contractInvalidClient;

	@Autowired
	private ContractPaymentInfoClient contractPaymentInfoClient;

	/**
	 * 建立對保契約資料
	 *
	 * @param paramBean
	 * @return
	 */
	public String createContract( SigningContractCreatedParamBean paramBean )
	{
		String result = "";

		contractCreatedClient.send( mapSigningContractCreatedArgBean( paramBean ), new JwtArgBean() );

		return result;
	}

	public String createCtrTypeCContract( ContractCtrTypeCCreatedAPIParamBean paramBean )
	{
		String result = "";

		contractCtrTypeCClient.send( mapContractCtrTypeCApplyClientArgBean( paramBean ), new JwtArgBean() );

		return result;
	}

	/**
	 * 建立撥款資料
	 *
	 * @return
	 */
	public String createPaymentInfo( PaymentInfoCreateParamBean paramBean )
	{
		String result = "";

		contractPaymentInfoClient.send( mapPaymentInfoClientArgBean( paramBean ), new JwtArgBean() );

		return result;
	}

	public String discardContract( String contractNo )
	{
		String result = "";

		SigningContractDiscardArgBean argBean = new SigningContractDiscardArgBean();
		argBean.setContractNo( contractNo );

		contractInvalidClient.send( argBean, new JwtArgBean() );

		return result;
	}

	private ContractCtrTypeCApplyClientArgBean mapContractCtrTypeCApplyClientArgBean( ContractCtrTypeCCreatedAPIParamBean paramBean )
	{
		ContractCtrTypeCApplyClientArgBean argBean = new ContractCtrTypeCApplyClientArgBean();
		if( true )
			argBean.setLoanType( LoanTypeEnum.PERSONAL_LOAN.getContext() );

		argBean.setContractNo( paramBean.getContractNo() );
		argBean.setBankAcctCode( paramBean.getBankAcctCode() );
		argBean.setBankAcctNo( paramBean.getBankAcctNo() );
		argBean.setBorrowerIPAddr( paramBean.getBorrowerIPAddr() );
		argBean.setBorrowerIdentityType( paramBean.getBorrowerIdentityType() );
		argBean.setLoanBeginDate( paramBean.getLoanBeginDate() );
		argBean.setLoanEndDate( paramBean.getLoanEndDate() );
		argBean.setRateAdjustInformMethod( paramBean.getRateAdjustInformMethod() );
		argBean.setBorrowerSingingDate( paramBean.getBorrowerSingingDate() );
		argBean.setContractCheckDate( paramBean.getContractCheckDate() );
		argBean.setBorrowerAgreeCrossSelling( paramBean.getBorrowerAgreeCrossSelling() );
		argBean.setBranchCode( paramBean.getBranchCode() );
		argBean.setBorrowerId( paramBean.getBorrowerId() );
		argBean.setBorrowerBirthDate( paramBean.getBorrowerBirthDate() );
		argBean.setBorrowerName( paramBean.getBorrowerName() );
		argBean.setBorrowerMobileNumber( paramBean.getBorrowerMobileNumber() );
		argBean.setBorrowerEmail( paramBean.getBorrowerEmail() );
		argBean.setProductCode( paramBean.getProductCode() );
		argBean.setContractVersion( paramBean.getContractVersion() );
		argBean.setLoanAmt( paramBean.getLoanAmt() );
		argBean.setLoanPeriod( paramBean.getLoanPeriod() );
		argBean.setDrawDownType( paramBean.getDrawDownType() );
		argBean.setOneTimeFee( paramBean.getOneTimeFee() );
		argBean.setPreliminaryFee( paramBean.getPreliminaryFee() );
		argBean.setCreditCheckFee( paramBean.getCreditCheckFee() );
		argBean.setRepaymentMethod( paramBean.getRepaymentMethod() );
		argBean.setAdvancedRateDesc( paramBean.getAdvancedRateDesc() );
		argBean.setAdvancedAPR( paramBean.getAdvancedAPR() );
		argBean.setLimitedRateDesc( paramBean.getLimitedRateDesc() );
		argBean.setLimitedAPR( paramBean.getLimitedAPR() );
		argBean.setShowOption( paramBean.getShowOption() );
		argBean.setCourtName( paramBean.getCourtName() );
		argBean.setFile( paramBean.getFile() );

		return argBean;
	}

	private ExpireInfoBean mapExpireInfo( com.megabank.olp.api.controller.eloan.bean.signing.ExpireInfoBean argBean )
	{
		ExpireInfoBean paramBean = new ExpireInfoBean();

		paramBean.setExpireInfoType( argBean.getExpireInfoType() );
		paramBean.setStartYear1( argBean.getStartYear1() );
		paramBean.setStartMonth1( argBean.getStartMonth1() );
		paramBean.setStartDay1( argBean.getStartDay1() );
		paramBean.setExpireYear1( argBean.getExpireYear1() );
		paramBean.setExpireMonth1( argBean.getExpireMonth1() );
		paramBean.setExpireDay1( argBean.getExpireDay1() );
		paramBean.setDuration1( argBean.getDuration1() );
		paramBean.setStartYear2( argBean.getStartYear2() );
		paramBean.setStartMonth2( argBean.getStartMonth2() );
		paramBean.setStartDay2( argBean.getStartDay2() );
		paramBean.setExpireYear2( argBean.getExpireYear2() );
		paramBean.setExpireMonth2( argBean.getExpireMonth2() );
		paramBean.setExpireDay2( argBean.getExpireDay2() );
		paramBean.setDuration2( argBean.getDuration2() );
		paramBean.setStartYear3( argBean.getStartYear3() );
		paramBean.setStartMonth3( argBean.getStartMonth3() );
		paramBean.setStartDay3( argBean.getStartDay3() );
		paramBean.setExpireYear3( argBean.getExpireYear3() );
		paramBean.setExpireMonth3( argBean.getExpireMonth3() );
		paramBean.setExpireDay3( argBean.getExpireDay3() );
		paramBean.setDuration3( argBean.getDuration3() );
		paramBean.setDuration4( argBean.getDuration4() );
		paramBean.setMaxYear4( argBean.getMaxYear4() );
		paramBean.setExpireYear4( argBean.getExpireYear4() );
		paramBean.setExpireMonth4( argBean.getExpireMonth4() );
		paramBean.setExpireDay4( argBean.getExpireDay4() );
		paramBean.setOther5( argBean.getOther5() );

		return paramBean;
	}

	private InterestInfoBean mapInterestInfo( com.megabank.olp.api.controller.eloan.bean.signing.InterestInfoBean argBean )
	{
		InterestInfoBean paramBean = new InterestInfoBean();

		paramBean.setInterestInfoType( argBean.getInterestInfoType() );
		paramBean.setFormula1( argBean.getFormula1() );
		paramBean.setFormula2( argBean.getFormula2() );
		paramBean.setFirstPeriodFrom1( argBean.getFirstPeriodFrom1() );
		paramBean.setFirstPeriodTo1( argBean.getFirstPeriodTo1() );
		paramBean.setFirstPeriodRate1( argBean.getFirstPeriodRate1() );
		paramBean.setSecondPeriodFrom1( argBean.getSecondPeriodFrom1() );
		paramBean.setSecondPeriodTo1( argBean.getSecondPeriodTo1() );
		paramBean.setSecondPeriodRate1( argBean.getSecondPeriodRate1() );
		paramBean.setThirdPeriodFrom1( argBean.getThirdPeriodFrom1() );
		paramBean.setThirdPeriodTo1( argBean.getThirdPeriodTo1() );
		paramBean.setThirdPeriodRate1( argBean.getThirdPeriodRate1() );
		paramBean.setOther1( argBean.getOther1() );
		paramBean.setOther2( argBean.getOther2() );
		paramBean.setOther3( argBean.getOther3() );
		paramBean.setRate1( argBean.getRate1() );
		paramBean.setRate2_1( argBean.getRate2_1() );
		paramBean.setRate2_2( argBean.getRate2_2() );
		paramBean.setRate2_3_1( argBean.getRate2_3_1() );
		paramBean.setRate2_3_2( argBean.getRate2_3_2() );

		return paramBean;
	}

	private List<String> mapLoanAccts( List<BankAccountDataBean> bankAccountDataBeans )
	{
		List<String> acctDatas = new ArrayList<>();

		for( BankAccountDataBean bankAccountDataBean : bankAccountDataBeans )
			acctDatas.add( bankAccountDataBean.getAccount() );

		return acctDatas;
	}

	private LoanConditionInfoBean mapLoanConditionInfoBean( LoanConditionDataBean data )
	{
		LoanConditionInfoBean infoBean = new LoanConditionInfoBean();
		infoBean.setCreditCheckFee( data.getCreditCheckFee() );
		infoBean.setRenewFee( data.getRenewFee() );
		infoBean.setChangeFee( data.getChangeFee() );
		infoBean.setCertFee( data.getCertFee() );
		infoBean.setReissueFee( data.getReissueFee() );
		infoBean.setDrawDownType( data.getDrawDownType() );
		infoBean.setLendingPlan( data.getLendingPlan() );
		infoBean.setLoanAmt( data.getLoanAmt() );
		infoBean.setLoanPeriod( data.getLoanPeriod() );
		infoBean.setLoanPurposeInfoBeans( mapLoanPurposeInfoBeans( data.getLoanPurposeDataBeans() ) );
		infoBean.setOneTimeFee( data.getOneTimeFee() );
		infoBean.setPreliminaryFee( data.getPreliminaryFee() );
		infoBean.setRepaymentMethod( data.getRepaymentMethod() );

		LendingPlanDataBean lendingPlanDataBean = data.getLendingPlanDataBean();
		infoBean.setAdvancedRedemptionTitle( lendingPlanDataBean.getAdvancedRedemptionTitle() );
		infoBean.setAdvancedRedemptionDesc( setDescHtml( lendingPlanDataBean.getAdvancedRedemptionDesc() ) );
		infoBean.setAdvancedRateTitle( lendingPlanDataBean.getAdvancedRateTitle() );
		infoBean.setAdvancedRateDesc( setDescHtml( lendingPlanDataBean.getAdvancedRateDesc() ) );
		infoBean.setAdvancedApr( lendingPlanDataBean.getAdvancedApr() );
		infoBean.setLimitedRedemptionTitle( lendingPlanDataBean.getLimitedRedemptionTitle() );
		infoBean.setLimitedRedemptionDesc( setDescHtml( lendingPlanDataBean.getLimitedRedemptionDesc() ) );
		infoBean.setLimitedRateTitle( lendingPlanDataBean.getLimitedRateTitle() );
		infoBean.setLimitedRateDesc( setDescHtml( lendingPlanDataBean.getLimitedRateDesc() ) );
		infoBean.setLimitedApr( lendingPlanDataBean.getLimitedApr() );
		infoBean.setOtherInfoDesc( setDescHtml( lendingPlanDataBean.getOtherInfoDesc() ) );
		infoBean.setOtherInfoTitle( lendingPlanDataBean.getOtherInfoTitle() );
		infoBean.setShowOption( lendingPlanDataBean.getShowOption() );

		return infoBean;
	}

	private List<LoanPurposeInfoBean> mapLoanPurposeInfoBeans( List<LoanPurposeDataBean> loanPurposeDataBeans )
	{
		List<LoanPurposeInfoBean> infoBeans = new ArrayList<>();

		for( LoanPurposeDataBean loanPurposeDataBean : loanPurposeDataBeans )
		{
			LoanPurposeInfoBean infoBean = new LoanPurposeInfoBean();
			infoBean.setLoanPurpose( loanPurposeDataBean.getLoanPurposeName() );
			infoBean.setIsChecked( "Y".equals( loanPurposeDataBean.getIsChecked() ) );

			infoBeans.add( infoBean );
		}

		return infoBeans;
	}

	private PayeeInfoBean mapPayeeInfo( com.megabank.olp.api.controller.eloan.bean.signing.PayeeInfoBean argBean )
	{
		PayeeInfoBean paramBean = new PayeeInfoBean();

		paramBean.setPayeeInfoType( argBean.getPayeeInfoType() );
		paramBean.setPayeeInfoAccountType( argBean.getPayeeInfoAccountType() );

		return paramBean;
	}

	private PaymentInfoCreateArgBean mapPaymentInfoClientArgBean( PaymentInfoCreateParamBean paramBean )
	{
		PaymentInfoCreateArgBean argBean = new PaymentInfoCreateArgBean();
		argBean.setContractNo( paramBean.getContractNo() );
		argBean.setPreliminaryFee( paramBean.getPreliminaryFee() );
		argBean.setCrChkFee( paramBean.getCrChkFee() );
		argBean.setPaymentInfoList( paramBean.getPaymentInfoList().size() > 0 ? mapPaymentList( paramBean.getPaymentInfoList() )
																			  : new ArrayList<>() );

		return argBean;
	}

	private List<com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean> mapPaymentList( List<PaymentInfoBean> paymentInfoBeans )
	{
		List<com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean> resBean = new ArrayList<>();

		for( PaymentInfoBean bean : paymentInfoBeans )
		{
			com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean infoBean =
																										new com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean();
			infoBean.setBankCode( bean.getBankCode() );
			infoBean.setBankName( bean.getBankName() );
			infoBean.setRepaymentProductType( bean.getRepaymentProductType() );
			infoBean.setRepaymentProduct( bean.getRepaymentProduct() );
			infoBean.setBankAcctNo( bean.getBankAcctNo() );
			infoBean.setRepaymentAmt( bean.getRepaymentAmt() );
			infoBean.setAccountName( bean.getAccountName() );
			resBean.add( infoBean );
		}

		return resBean;
	}

	private List<RateInfoBean> mapRateList( List<RateDataBean> rateInfoBeans )
	{
		List<RateInfoBean> rateLists = new ArrayList<>();

		for( RateDataBean rateInfoBean : rateInfoBeans )
		{
			RateInfoBean infoBean = new RateInfoBean();
			infoBean.setRate( rateInfoBean.getRate() );
			infoBean.setRate_Type( rateInfoBean.getRate_Type() );
			infoBean.setRate_Bgn( rateInfoBean.getRate_Bgn() );
			infoBean.setRate_End( rateInfoBean.getRate_End() );
			rateLists.add( infoBean );
		}

		return rateLists;
	}

	private RepaymentInfoBean mapRepaymentInfo( com.megabank.olp.api.controller.eloan.bean.signing.RepaymentInfoBean argBean )
	{
		RepaymentInfoBean paramBean = new RepaymentInfoBean();

		paramBean.setRepaymentInfoType( argBean.getRepaymentInfoType() );
		paramBean.setLimtedYear4( argBean.getLimtedYear4() );
		paramBean.setLimtedMonth4( argBean.getLimtedMonth4() );
		paramBean.setYear4( argBean.getYear4() );
		paramBean.setMonth4( argBean.getMonth4() );
		paramBean.setLimtedYear5( argBean.getLimtedYear5() );
		paramBean.setLimtedMonth5( argBean.getLimtedMonth5() );
		paramBean.setYear5( argBean.getYear5() );
		paramBean.setMonth5( argBean.getMonth5() );
		paramBean.setPeriod6( argBean.getPeriod6() );
		paramBean.setYear6( argBean.getYear6() );
		paramBean.setOther7( argBean.getOther7() );
		paramBean.setHouseRedemption( argBean.getHouseRedemption() );
		paramBean.setFirstRate( argBean.getFirstRate() );
		paramBean.setSecondRate( argBean.getSecondRate() );

		return paramBean;
	}

	private List<com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean> mapRepaymentList( List<RepaymentBean> repaymentList )
	{
		List<com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean> resBean = new ArrayList<>();

		for( RepaymentBean repaymentBean : repaymentList )
		{
			com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean bean =
																								  new com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean();
			bean.setBankCode( repaymentBean.getBankCode() );
			bean.setBankName( repaymentBean.getBankName() );
			bean.setRepaymentProductType( repaymentBean.getRepaymentProductType() );
			bean.setOriginalAmt( repaymentBean.getOriginalAmt() );
			resBean.add( bean );
		}

		return resBean;
	}

	private SigningContractCreatedArgBean mapSigningContractCreatedArgBean( SigningContractCreatedParamBean paramBean )
	{
		SigningContractCreatedArgBean argBean = new SigningContractCreatedArgBean();
		argBean.setBranchCode( paramBean.getBranchCode() );
		argBean.setBorrowerBirthDate( paramBean.getBorrowerBirthDate() );
		argBean.setBorrowerId( paramBean.getBorrowerId() );
		argBean.setBorrowerMobileNumber( paramBean.getBorrowerMobileNumber() );
		argBean.setBorrowerName( paramBean.getBorrowerName() );
		argBean.setBorrowerEmail( paramBean.getBorrowerEmail() );
		argBean.setIsBorrowerYouth( paramBean.getIsBorrowerYouth() == null ? null : "Y".equals( paramBean.getIsBorrowerYouth() ) );
		argBean.setContractNo( paramBean.getContractNo() );
		argBean.setContractVersion( paramBean.getContractVersion() );
		argBean.setCourtName( paramBean.getCourtName() );
		argBean.setExpiredDate( paramBean.getExpiredDate() );
		argBean.setProductCode( paramBean.getProductCode() );
		if( ProductCodeEnum.PERSONAL_LOAN.getContext().equals( paramBean.getProductCode() ) )
			argBean.setLoanType( LoanTypeEnum.PERSONAL_LOAN.getContext() );
		else if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( paramBean.getProductCode() ) )
			argBean.setLoanType( LoanTypeEnum.HOUSE_LOAN.getContext() );
		argBean.setLoanConditionInfoBean( mapLoanConditionInfoBean( paramBean.getLoanConditionDataBean() ) );

		GuaranteeDataBean guaranteeDataBean = paramBean.getGuaranteeDataBean();
		argBean.setGuaranteeAmt( guaranteeDataBean.getGuaranteeAmt() );
		argBean.setGeneralGuaranteePlan( guaranteeDataBean.getGeneralGuaranteePlan() );
		argBean.setGeneralGuaranteePlanInfo( setDescHtml( guaranteeDataBean.getGeneralGuaranteePlanInfo() ) );
		argBean.setJointGuaranteePlan( guaranteeDataBean.getJointGuaranteePlan() );
		argBean.setJointGuaranteePlanInfo( setDescHtml( guaranteeDataBean.getJointGuaranteePlanInfo() ) );

		if( StringUtils.isNotBlank( paramBean.getRelatedPersonType() ) )
		{
			argBean.setRelatedPersonBirthDate( paramBean.getRelatedPersonBirthDate() );
			argBean.setRelatedPersonId( paramBean.getRelatedPersonId() );
			argBean.setRelatedPersonMobileNumber( paramBean.getRelatedPersonMobileNumber() );
			argBean.setRelatedPersonName( paramBean.getRelatedPersonName() );
			argBean.setRelatedPersonType( paramBean.getRelatedPersonType() );
			argBean.setRelatedPersonEmail( paramBean.getRelatedPersonEmail() );
		}

		argBean.setLoanAccts( mapLoanAccts( paramBean.getBankAccountDataBeans() ) );
		argBean.setLoanPlan( paramBean.getLoanPlan() );
		argBean.setGrpCntrNo( paramBean.getGrpCntrNo() );
		argBean.setGivenApprBegDate( paramBean.getGivenApprBegDate() );
		argBean.setGivenApprEndDate( paramBean.getGivenApprEndDate() );
		argBean.setPayeeBankCode( paramBean.getPayeeBankCode() );
		argBean.setPayeeBankAccountNo( paramBean.getPayeeBankAccountNo() );
		argBean.setPayeeBankAccountName( paramBean.getPayeeBankAccountName() );
		argBean.setPayeeTotalAmt( paramBean.getPayeeTotalAmt() );
		argBean.setPayeeRemittance( paramBean.getPayeeRemittance() );
		argBean.setPayeeSelfProvide( paramBean.getPayeeSelfProvide() );
		argBean.setBaseRate( paramBean.getBaseRate() );
		argBean.setRateList( mapRateList( paramBean.getRateList() ) );
		argBean.setIsRepayment( paramBean.getIsRepayment() == null ? null : "Y".equals( paramBean.getIsRepayment() ) );
		argBean.setRepaymentList( mapRepaymentList( paramBean.getRepaymentList() ) );
		argBean.setStaffRule( paramBean.getStaffRule() );
		argBean.setPayeeInfo( paramBean.getPayeeInfo() == null ? new PayeeInfoBean() : mapPayeeInfo( paramBean.getPayeeInfo() ) );
		argBean.setExpireInfo( paramBean.getExpireInfo() == null ? new ExpireInfoBean() : mapExpireInfo( paramBean.getExpireInfo() ) );
		argBean.setRepaymentInfo( paramBean.getRepaymentInfo() == null ? new RepaymentInfoBean() : mapRepaymentInfo( paramBean.getRepaymentInfo() ) );
		argBean.setInterestInfo( paramBean.getInterestInfo() == null ? new InterestInfoBean() : mapInterestInfo( paramBean.getInterestInfo() ) );
		argBean.setGuaranteeType( paramBean.getGuaranteeType() );
		argBean.setWitness( paramBean.getWitness() );
		argBean.setBrNoTel( paramBean.getBrNoTel() );
		argBean.setBrNoFax( paramBean.getBrNoFax() );
		argBean.setRefSystemId( paramBean.getRefSystemId() );
		argBean.setProdKind( paramBean.getProdKind() );
		argBean.setLnDate( paramBean.getLnDate() );
		argBean.setConsentVer( paramBean.getConsentVer() );
		argBean.setCollateralBuildingAddr1( paramBean.getCollateralBuildingAddr1() );
		argBean.setCollateralBuildingAddr2( paramBean.getCollateralBuildingAddr2() );
		argBean.setMortgageMaxAmt1( paramBean.getMortgageMaxAmt1() );
		argBean.setMortgageMaxAmt2( paramBean.getMortgageMaxAmt2() );
		argBean.setFirstLoanDateYear( paramBean.getFirstLoanDateYear() );
		argBean.setFirstLoanDateMth( paramBean.getFirstLoanDateMth() );
		argBean.setFirstLoanDateDay( paramBean.getFirstLoanDateDay() );
		argBean.setFirstLoanAmt1( paramBean.getFirstLoanAmt1() );
		argBean.setFirstLoanAmt2( paramBean.getFirstLoanAmt2() );
		argBean.setCollateralContractTerms( paramBean.getCollateralContractTerms() );
		argBean.setUnregisteredBuildingDesc( paramBean.getUnregisteredBuildingDesc() );
		argBean.setHouseLoanContractNo( paramBean.getHouseLoanContractNo() );
		argBean.setCoTarget( paramBean.getCoTarget() );
		argBean.setCbAfftTerms( paramBean.getCbAfftTerms() );
		argBean.setCbAfftVersion( paramBean.getCbAfftVersion() );

		try
		{
			argBean.setCbAfft1ContentBean( paramBean.getCbAfft1ContentBean() == null ? new CbAfft1ContentBean()
																					 : ObjectConversionUtils
																								 .convert( paramBean.getCbAfft1ContentBean(),
																										   CbAfft1ContentBean.class ) );
			argBean.setCbAfft2ContentBean( paramBean.getCbAfft2ContentBean() == null ? new CbAfft2ContentBean()
																					 : ObjectConversionUtils
																								 .convert( paramBean.getCbAfft2ContentBean(),
																										   CbAfft2ContentBean.class ) );
			argBean.setCbAfft3ContentBean( paramBean.getCbAfft3ContentBean() == null ? new CbAfft3ContentBean()
																					 : ObjectConversionUtils
																								 .convert( paramBean.getCbAfft3ContentBean(),
																										   CbAfft3ContentBean.class ) );
			argBean.setCbAfft4ContentBean( paramBean.getCbAfft4ContentBean() == null ? new CbAfft4ContentBean()
																					 : ObjectConversionUtils
																								 .convert( paramBean.getCbAfft4ContentBean(),
																										   CbAfft4ContentBean.class ) );
			argBean.setCbAfft5ContentBean( paramBean.getCbAfft5ContentBean() == null ? new CbAfft5ContentBean()
																					 : ObjectConversionUtils
																								 .convert( paramBean.getCbAfft5ContentBean(),
																										   CbAfft5ContentBean.class ) );
		}
		catch( Exception ex )
		{
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
										  new String[]{ "cbAfft1Content ~ cbAfft5Content parameters mapping error" } );
		}

		return argBean;
	}

	private String setDescHtml( List<String> descList )
	{
		if( descList == null || descList.isEmpty() )
			return "";

		StringBuilder builder = new StringBuilder();
		for( String desc : descList )
		{
			if( builder.length() > 0 )
				builder.append( "<br>" );

			builder.append( StringUtils.replaceAll( desc, "\n", "<br>" ) );
		}

		return builder.toString();
	}

}
