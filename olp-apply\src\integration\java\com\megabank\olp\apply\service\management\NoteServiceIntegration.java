/**
 *
 */
package com.megabank.olp.apply.service.management;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.management.bean.note.NoteCreatedParamBean;
import com.megabank.olp.apply.service.management.bean.note.NoteListResBean;
import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
class NoteServiceIntegration
{
	@Autowired
	private NoteService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void createNote()
	{
		NoteCreatedParamBean paramBean = getNoteCreatedParamBean();

		Long id = service.createNote( paramBean );

		logger.info( "id:{}", id );
	}

	@Test
	public void getNoteList()
	{
		Long id = 1L;
		String type = "houseloan";

		NoteListResBean resBean = service.getNoteList( id, type );

		logger.info( "resBean:{}", resBean );
	}

	private NoteCreatedParamBean getNoteCreatedParamBean()
	{
		Long id = 1L;
		String type = "houseloan";
		String employeeName = "developer";
		String employeeId = "developer-01";
		String note = "integration";
		String action = "comment";

		NoteCreatedParamBean paramBean = new NoteCreatedParamBean();
		paramBean.setId( id );
		paramBean.setType( type );
		paramBean.setEmployeeName( employeeName );
		paramBean.setEmployeeId( employeeId );
		paramBean.setText( note );
		paramBean.setAction( action );

		return paramBean;
	}

}
