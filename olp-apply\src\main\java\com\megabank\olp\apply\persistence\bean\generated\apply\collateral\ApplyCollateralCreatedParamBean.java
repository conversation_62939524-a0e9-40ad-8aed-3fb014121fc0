package com.megabank.olp.apply.persistence.bean.generated.apply.collateral;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyCollateralCreatedParamBean extends BaseBean
{
	private Long validatedIdentityId;

	private String contractNo;

	private String providerName;

	private String borrowerName;

	private String transmissionStatusCode;

	private String branchBankCode;

	public ApplyCollateralCreatedParamBean()
	{
		// default constructor
	}

	public String getBorrowerName()
	{
		return borrowerName;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getProviderName()
	{
		return providerName;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setBorrowerName( String borrowerName )
	{
		this.borrowerName = borrowerName;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setProviderName( String providerName )
	{
		this.providerName = providerName;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

}
