package com.megabank.olp.apply.service.inet;

import java.util.List;

import com.megabank.olp.apply.service.inet.bean.InetPdfGetterArgBean;
import com.megabank.olp.apply.service.inet.bean.PackagedInetResBean;

public interface InetService
{
	public PackagedInetResBean getPdf( String rptTemplate, String returnType, List<Object> parameters );

	public PackagedInetResBean getPdfByPostReq( InetPdfGetterArgBean argBean ) throws Exception;

	public void send( String rptTemplate, String returnType, List<Object> parameters );
}
