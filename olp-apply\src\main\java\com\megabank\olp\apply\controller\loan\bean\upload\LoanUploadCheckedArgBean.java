package com.megabank.olp.apply.controller.loan.bean.upload;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class LoanUploadCheckedArgBean extends BaseBean
{
	@NotBlank
	private String idNo;

	@NotNull
	private Date birthDate;

	@NotBlank
	private String loanType;

	public LoanUploadCheckedArgBean()
	{
		// default constructor
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

}
