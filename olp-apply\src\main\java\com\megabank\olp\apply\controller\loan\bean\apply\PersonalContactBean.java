package com.megabank.olp.apply.controller.loan.bean.apply;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class PersonalContactBean extends BaseBean
{
	@NotBlank
	private String homeAddressTownCode;

	private String homeAddressVillage;

	private String homeAddressNeighborhood;

	@NotBlank
	private String homeAddressStreet;

	private Integer homeAddressSection;

	private String homeAddressLane;

	private String homeAddressAlley;

	@NotBlank
	private String homeAddressNo;

	private String homeAddressFloor;

	private Integer homeAddressRoom;

	@NotBlank
	private String mailingAddressTownCode;

	private String mailingAddressVillage;

	private String mailingAddressNeighborhood;

	@NotBlank
	private String mailingAddressStreet;

	private Integer mailingAddressSection;

	private String mailingAddressLane;

	private String mailingAddressAlley;

	@NotBlank
	private String mailingAddressNo;

	private String mailingAddressFloor;

	private Integer mailingAddressRoom;

	@NotBlank
	private String residenceStatusCode;

	private String houseStatusCode;

	private String homePhoneCode;

	private String homePhoneNumber;

	@NotBlank
	private String email;

	@NotBlank
	private String mobileNumber;

	@NotBlank
	private String branchBankCode;

	private String serviceAssociateDeptCode;

	private String serviceAssociate;
	
	private String serviceAssociateBranchCode;

	private Integer rent;

	public PersonalContactBean()
	{
		// default constructor
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getEmail()
	{
		return email;
	}

	public String getHomeAddressAlley()
	{
		return homeAddressAlley;
	}

	public String getHomeAddressFloor()
	{
		return homeAddressFloor;
	}

	public String getHomeAddressLane()
	{
		return homeAddressLane;
	}

	public String getHomeAddressNeighborhood()
	{
		return homeAddressNeighborhood;
	}

	public String getHomeAddressNo()
	{
		return homeAddressNo;
	}

	public Integer getHomeAddressRoom()
	{
		return homeAddressRoom;
	}

	public Integer getHomeAddressSection()
	{
		return homeAddressSection;
	}

	public String getHomeAddressStreet()
	{
		return homeAddressStreet;
	}

	public String getHomeAddressTownCode()
	{
		return homeAddressTownCode;
	}

	public String getHomeAddressVillage()
	{
		return homeAddressVillage;
	}

	public String getHomePhoneCode()
	{
		return homePhoneCode;
	}

	public String getHomePhoneNumber()
	{
		return homePhoneNumber;
	}

	public String getHouseStatusCode()
	{
		return houseStatusCode;
	}

	public String getMailingAddressAlley()
	{
		return mailingAddressAlley;
	}

	public String getMailingAddressFloor()
	{
		return mailingAddressFloor;
	}

	public String getMailingAddressLane()
	{
		return mailingAddressLane;
	}

	public String getMailingAddressNeighborhood()
	{
		return mailingAddressNeighborhood;
	}

	public String getMailingAddressNo()
	{
		return mailingAddressNo;
	}

	public Integer getMailingAddressRoom()
	{
		return mailingAddressRoom;
	}

	public Integer getMailingAddressSection()
	{
		return mailingAddressSection;
	}

	public String getMailingAddressStreet()
	{
		return mailingAddressStreet;
	}

	public String getMailingAddressTownCode()
	{
		return mailingAddressTownCode;
	}

	public String getMailingAddressVillage()
	{
		return mailingAddressVillage;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public Integer getRent()
	{
		return rent;
	}

	public String getResidenceStatusCode()
	{
		return residenceStatusCode;
	}

	public String getServiceAssociate()
	{
		return serviceAssociate;
	}

	public String getServiceAssociateDeptCode()
	{
		return serviceAssociateDeptCode;
	}

	public String getServiceAssociateBranchCode()
	{
		return serviceAssociateBranchCode;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setHomeAddressAlley( String homeAddressAlley )
	{
		this.homeAddressAlley = homeAddressAlley;
	}

	public void setHomeAddressFloor( String homeAddressFloor )
	{
		this.homeAddressFloor = homeAddressFloor;
	}

	public void setHomeAddressLane( String homeAddressLane )
	{
		this.homeAddressLane = homeAddressLane;
	}

	public void setHomeAddressNeighborhood( String homeAddressNeighborhood )
	{
		this.homeAddressNeighborhood = homeAddressNeighborhood;
	}

	public void setHomeAddressNo( String homeAddressNo )
	{
		this.homeAddressNo = homeAddressNo;
	}

	public void setHomeAddressRoom( Integer homeAddressRoom )
	{
		this.homeAddressRoom = homeAddressRoom;
	}

	public void setHomeAddressSection( Integer homeAddressSection )
	{
		this.homeAddressSection = homeAddressSection;
	}

	public void setHomeAddressStreet( String homeAddressStreet )
	{
		this.homeAddressStreet = homeAddressStreet;
	}

	public void setHomeAddressTownCode( String homeAddressTownCode )
	{
		this.homeAddressTownCode = homeAddressTownCode;
	}

	public void setHomeAddressVillage( String homeAddressVillage )
	{
		this.homeAddressVillage = homeAddressVillage;
	}

	public void setHomePhoneCode( String homePhoneCode )
	{
		this.homePhoneCode = homePhoneCode;
	}

	public void setHomePhoneNumber( String homePhoneNumber )
	{
		this.homePhoneNumber = homePhoneNumber;
	}

	public void setHouseStatusCode( String houseStatusCode )
	{
		this.houseStatusCode = houseStatusCode;
	}

	public void setMailingAddressAlley( String mailingAddressAlley )
	{
		this.mailingAddressAlley = mailingAddressAlley;
	}

	public void setMailingAddressFloor( String mailingAddressFloor )
	{
		this.mailingAddressFloor = mailingAddressFloor;
	}

	public void setMailingAddressLane( String mailingAddressLane )
	{
		this.mailingAddressLane = mailingAddressLane;
	}

	public void setMailingAddressNeighborhood( String mailingAddressNeighborhood )
	{
		this.mailingAddressNeighborhood = mailingAddressNeighborhood;
	}

	public void setMailingAddressNo( String mailingAddressNo )
	{
		this.mailingAddressNo = mailingAddressNo;
	}

	public void setMailingAddressRoom( Integer mailingAddressRoom )
	{
		this.mailingAddressRoom = mailingAddressRoom;
	}

	public void setMailingAddressSection( Integer mailingAddressSection )
	{
		this.mailingAddressSection = mailingAddressSection;
	}

	public void setMailingAddressStreet( String mailingAddressStreet )
	{
		this.mailingAddressStreet = mailingAddressStreet;
	}

	public void setMailingAddressTownCode( String mailingAddressTownCode )
	{
		this.mailingAddressTownCode = mailingAddressTownCode;
	}

	public void setMailingAddressVillage( String mailingAddressVillage )
	{
		this.mailingAddressVillage = mailingAddressVillage;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setRent( Integer rent )
	{
		this.rent = rent;
	}

	public void setResidenceStatusCode( String residenceStatusCode )
	{
		this.residenceStatusCode = residenceStatusCode;
	}

	public void setServiceAssociate( String serviceAssociate )
	{
		this.serviceAssociate = serviceAssociate;
	}

	public void setServiceAssociateBranchCode( String serviceAssociateBranchCode )
	{
		this.serviceAssociateBranchCode = serviceAssociateBranchCode;
	}

	public void setServiceAssociateDeptCode( String serviceAssociateDeptCode )
	{
		this.serviceAssociateDeptCode = serviceAssociateDeptCode;
	}

}
