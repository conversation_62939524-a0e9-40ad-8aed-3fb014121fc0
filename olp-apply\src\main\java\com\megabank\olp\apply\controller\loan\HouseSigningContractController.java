package com.megabank.olp.apply.controller.loan;

import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractArgBean;
import com.megabank.olp.apply.service.loan.DownloadService;
import com.megabank.olp.apply.service.loan.SigningContractService;
import com.megabank.olp.base.layer.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping( "loan/housesigningcontract" )
public class HouseSigningContractController extends BaseController
{
	@Autowired
	private SigningContractService signingContractService;

	@Autowired
	private DownloadService downloadService;

	/**
	 * 取得對保完成感謝頁內容
	 */
	@PostMapping( "getThankyouMessage" )
	public Map<String, Object> getThankyouMessage( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();

		return getResponseMap( signingContractService.getThankyouMessage( contractNo ) );
	}
}