package com.megabank.olp.apply.persistence.pojo.apply.mydata;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyMyDataNotification is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_my_data_notification" )
public class ApplyMyDataNotification extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_my_data_notification";

	public static final String MY_DATA_NOTIFICATION_ID_CONSTANT = "myDataNotificationId";

	public static final String APPLY_MY_DATA_CONSTANT = "applyMyData";

	public static final String WAIT_SEC_CONSTANT = "waitSec";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	private Long myDataNotificationId;

	private transient ApplyMyData applyMyData;

	private int waitSec;

	private Date createdDate;

	public ApplyMyDataNotification()
	{}

	public ApplyMyDataNotification( Long myDataNotificationId )
	{
		this.myDataNotificationId = myDataNotificationId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "my_data_id", nullable = false )
	public ApplyMyData getApplyMyData()
	{
		return applyMyData;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "my_data_notification_id", unique = true, nullable = false )
	public Long getMyDataNotificationId()
	{
		return myDataNotificationId;
	}

	@Column( name = "wait_sec", nullable = false, precision = 5, scale = 0 )
	public int getWaitSec()
	{
		return waitSec;
	}

	public void setApplyMyData( ApplyMyData applyMyData )
	{
		this.applyMyData = applyMyData;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setMyDataNotificationId( Long myDataNotificationId )
	{
		this.myDataNotificationId = myDataNotificationId;
	}

	public void setWaitSec( int waitSec )
	{
		this.waitSec = waitSec;
	}
}