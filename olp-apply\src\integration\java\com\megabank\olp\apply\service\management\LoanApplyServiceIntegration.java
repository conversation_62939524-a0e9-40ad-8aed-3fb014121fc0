package com.megabank.olp.apply.service.management;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.download.FileDownloadedResBean;
import com.megabank.olp.apply.service.management.bean.loan.CustInfoResBean;
import com.megabank.olp.apply.service.management.bean.loan.LoanApplyExportedParamBean;
import com.megabank.olp.apply.service.management.bean.loan.LoanApplyListedParamBean;
import com.megabank.olp.apply.service.management.bean.loan.LoanApplyListedResBean;
import com.megabank.olp.apply.service.management.bean.loan.LoanAttachmentResBean;
import com.megabank.olp.apply.service.management.bean.loan.LoanDetailResBean;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class LoanApplyServiceIntegration
{
	@Autowired
	private LoanApplyService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void discardLoan()
	{
		String caseNo = "PA000001";
		String employeeName = "developer";
		String employeeId = "developer-01";

		service.discardLoan( caseNo, employeeId, employeeName );
	}

	@Test
	public void exportHouseLoanCompleted()
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();

		FileDownloadedResBean resBean = service.exportHouseLoanCompleted( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void exportHouseLoanInterrupted()
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();

		FileDownloadedResBean resBean = service.exportHouseLoanInterrupted( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void exportPersonalLoanCompleted()
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();

		FileDownloadedResBean resBean = service.exportPersonalLoanCompletedEloan( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void exportPersonalLoanInterrupted()
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();

		FileDownloadedResBean resBean = service.exportPersonalLoanInterrupted( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getCustInfo()
	{
		String caseNo = "PA000001";

		CustInfoResBean resBean = service.getCustInfo( caseNo );

		// logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getHouseLoanDetail()
	{
		Long loanId = 1L;

		LoanDetailResBean resBean = service.getHouseLoanDetail( loanId );

		// logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getLoanAttachment()
	{
		Long loanId = 1L;

		LoanAttachmentResBean resBean = service.getLoanAttachment( loanId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getPersonalLoanDetail()
	{
		Long loanId = 1L;

		LoanDetailResBean resBean = service.getPersonalLoanDetail( loanId );

		// logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listHouselLoanInterrupted()
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setPage( 1 );

		LoanApplyListedResBean resBean = service.listHouselLoanInterrupted( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listHouseLoanCompleted()
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setPage( 1 );

		LoanApplyListedResBean resBean = service.listHouseLoanCompleted( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listPersonalLoanCompleted()
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setPage( 1 );

		LoanApplyListedResBean resBean = service.listPersonalLoanCompletedEloan( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listPersonalLoanInterrupted()
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setPage( 1 );

		LoanApplyListedResBean resBean = service.listPersonalLoanInterrupted( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void updateBranchBank()
	{
		String caseNo = "PA000015";
		String branchBankCode = "017";
		String employeeId = "eloan";
		String employeeName = "eloan";

		service.updateLoanCompletedBranchBank( caseNo, branchBankCode, employeeId, employeeName );
	}

	@Test
	public void updateProcessStatus()
	{
		Long loanId = 1L;
		String processCode = "processing";
		String employeeName = "developer";
		String employeeId = "developer-01";

		Long id = service.updateProcessStatus( loanId, processCode, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	@Test
	public void updateTransmissionStatus()
	{
		Long loanId = 1L;
		String transmissionStatus = "completed";
		String employeeName = "developer";
		String employeeId = "developer-01";

		Long id = service.updateTransmissionStatus( loanId, transmissionStatus, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	private LoanApplyListedParamBean getPersonalLoanListParamBean()
	{
		String transmissionStatus = "no";
		String applyStatus = "complete";
		String idNo = "A123456789";
		String name = "王大明";
		String mobileNumber = "**********";
		Date applyDateStart = CommonDateUtils.getDate( 2020, 1, 1 );
		Date applyDateEnd = CommonDateUtils.getDate( 2020, 12, 31 );
		String caseNo = "112533654";
		String sortColumn = "applyDate";
		String sortDirection = "desc";
		int page = 1;
		int length = 10;

		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setTransmissionStatusCode( transmissionStatus );
		paramBean.setDateStart( applyDateStart );
		paramBean.setDateEnd( applyDateEnd );
		paramBean.setIdNo( idNo );
		paramBean.setName( name );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setPage( page );
		paramBean.setLength( length );
		paramBean.setSortColumn( sortColumn );
		paramBean.setSortDirection( sortDirection );

		return paramBean;
	}
}
