package com.megabank.olp.apply.persistence.dao.generated.apply.youthStartUp;

import com.megabank.olp.apply.persistence.bean.generated.apply.youthStartUp.ApplyYouthStartUpCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.*;
import com.megabank.olp.apply.persistence.pojo.apply.youthStartUp.ApplyYouthStartUp;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public class ApplyYouthStartUpDAO extends BasePojoDAO<ApplyYouthStartUp, Long>
{
	private static final String DISCARD_CONSTANT = "discard";

	private static final String YOUTH_START_UP_ID_CONSTANT = "youthStartUpId";

	private static final String TRANSMISSION_STATUS_CONSTANT = "transmissionStatusCode";

	private static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	public Long create( ApplyYouthStartUpCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notBlank( paramBean.getCaseNo() );

		ApplyYouthStartUp pojo = new ApplyYouthStartUp();
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setCaseNo( paramBean.getCaseNo() );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( TransmissionStatusEnum.NO.getContext() ) );
		pojo.setDiscard( false );

		return super.createPojo( pojo );
	}

	public ApplyYouthStartUp getPojoByCaseNo( String caseNo )
	{
		Validate.notBlank( caseNo );

		NameValueBean condition = new NameValueBean( ApplyYouthStartUp.CASE_NO_CONSTANT, caseNo );

		return getUniquePojoByProperty( condition, ApplyYouthStartUp.TABLENAME_CONSTANT );
	}

	public ApplyYouthStartUp read( Long youthStartUpId )
	{
		Validate.notNull( youthStartUpId );

		return getPojoByPK( youthStartUpId, ApplyYouthStartUp.TABLENAME_CONSTANT );
	}

	public ApplyYouthStartUp readToNull( String caseNo )
	{
		Validate.notNull( caseNo );

		NameValueBean condition = new NameValueBean( ApplyYouthStartUp.CASE_NO_CONSTANT, caseNo );
		NameValueBean[] conditions = new NameValueBean[]{ condition };

		return getUniquePojoByProperties( conditions );
	}

	public Long updateAgreedDate( Long youthStartUpId, Date agreedDate )
	{
		Validate.notNull( youthStartUpId );

		ApplyYouthStartUp pojo = read( youthStartUpId );
		pojo.setAgreedDate( agreedDate );
		pojo.setUpdatedDate( new Date() );

		return pojo.getYouthStartUpId();
	}

	public Long updateFinalBranchBankId( Long youthStartUpId, Long finalBranchBankId )
	{
		Validate.notNull( finalBranchBankId );

		ApplyYouthStartUp pojo = read( youthStartUpId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( finalBranchBankId ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getYouthStartUpId();
	}

	public Long updateDiscard( Long youthStartUpId )
	{
		Validate.notNull( youthStartUpId );

		ApplyYouthStartUp pojo = read( youthStartUpId );
		pojo.setDiscard( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getYouthStartUpId();
	}

	public Long updateTransmissionStatus( Long youthStartUpId, String transmissionStatusCode )
	{
		Validate.notNull( youthStartUpId );

		ApplyYouthStartUp pojo = read( youthStartUpId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getYouthStartUpId();
	}

	@Override
	protected Class<ApplyYouthStartUp> getPojoClass()
	{
		return ApplyYouthStartUp.class;
	}
}
