package com.megabank.olp.apply.controller.loan.bean.survey;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class SurveyJobBean extends BaseBean
{
	@NotBlank
	private String jobSubType;

	@NotNull
	private Integer annualIncome;

	public SurveyJobBean()
	{
		// default constructor
	}

	public Integer getAnnualIncome()
	{
		return annualIncome;
	}

	public String getJobSubType()
	{
		return jobSubType;
	}

	public void setAnnualIncome( Integer annualIncome )
	{
		this.annualIncome = annualIncome;
	}

	public void setJobSubType( String jobSubType )
	{
		this.jobSubType = jobSubType;
	}
}
