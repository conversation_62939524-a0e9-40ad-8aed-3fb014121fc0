package com.megabank.olp.apply.persistence.dao.generated.apply.collateral;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.collateral.ApplyCollateralAgreementCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTownDAO;
import com.megabank.olp.apply.persistence.pojo.apply.collateral.ApplyCollateralAgreement;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyCollateralAgreementDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyCollateralAgreementDAO extends BasePojoDAO<ApplyCollateralAgreement, Long>
{
	@Autowired
	private ApplyCollateralDAO applyCollateralDAO;

	@Autowired
	private CodeTownDAO codeTownDAO;

	public ApplyCollateralAgreement create( ApplyCollateralAgreementCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getCollateralId() );
		Validate.notNull( paramBean.getCollateralAmt() );
		Validate.notNull( paramBean.getLoanAmt() );
		Validate.notNull( paramBean.getIsCollateralFullPayment() );
		Validate.notNull( paramBean.getBorrowerSignDate() );
		Validate.notBlank( paramBean.getEmail() );
		Validate.notBlank( paramBean.getCollateralAddressTownCode() );
		Validate.notBlank( paramBean.getCollateralAddressStreet() );
		Validate.notBlank( paramBean.getWarrantee1() );
		Validate.notBlank( paramBean.getWarrantee2() );
		Validate.notBlank( paramBean.getWarrantee3() );
		Validate.notBlank( paramBean.getLoanProduct1() );

		ApplyCollateralAgreement pojo = new ApplyCollateralAgreement();
		pojo.setApplyCollateral( applyCollateralDAO.read( paramBean.getCollateralId() ) );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setFullPayment( paramBean.getIsCollateralFullPayment() );
		pojo.setCodeTown( codeTownDAO.read( paramBean.getCollateralAddressTownCode() ) );
		pojo.setCollateralStreet( paramBean.getCollateralAddressStreet() );
		pojo.setCollateralAmt( paramBean.getCollateralAmt() );
		pojo.setLoanAmt( paramBean.getLoanAmt() );
		pojo.setWarrantee1( paramBean.getWarrantee1() );
		pojo.setWarrantee2( paramBean.getWarrantee2() );
		pojo.setWarrantee3( paramBean.getWarrantee3() );
		pojo.setGuranteeAmt( paramBean.getGuranteeAmt() );
		pojo.setSignatory( paramBean.getSignatory() );
		pojo.setTermNo( paramBean.getTermNo() );
		pojo.setBorrowerSignDate( paramBean.getBorrowerSignDate() );
		pojo.setLoanProduct1( paramBean.getLoanProduct1() );
		pojo.setLoanProduct2( paramBean.getLoanProduct2() );
		pojo.setLoanProduct3( paramBean.getLoanProduct3() );
		pojo.setLoanProduct4( paramBean.getLoanProduct4() );
		pojo.setLoanProduct5( paramBean.getLoanProduct5() );

		super.createPojo( pojo );

		return pojo;
	}

	public ApplyCollateralAgreement read( Long collateralId )
	{
		Validate.notNull( collateralId );

		return getPojoByPK( collateralId, ApplyCollateralAgreement.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<ApplyCollateralAgreement> getPojoClass()
	{
		return ApplyCollateralAgreement.class;
	}
}
