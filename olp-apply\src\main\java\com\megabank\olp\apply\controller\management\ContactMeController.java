package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.contact.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.contact.ContactMeDetailGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.contact.ContactMeExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.contact.ContactMeListedArgBean;
import com.megabank.olp.apply.controller.management.bean.contact.ProcessStatusUpdatedArgBean;
import com.megabank.olp.apply.service.management.ContactMeService;
import com.megabank.olp.apply.service.management.bean.contact.ContactMeExportedParamBean;
import com.megabank.olp.apply.service.management.bean.contact.ContactMeListedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/contactme" )
public class ContactMeController extends BaseController
{
	@Autowired
	private ContactMeService contactMeService;

	/**
	 * 信貸專人聯絡案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportList" )
	public Map<String, Object> exportList( @RequestBody @Validated ContactMeExportedArgBean argBean )
	{
		ContactMeExportedParamBean paramBean = new ContactMeExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setContactTimeCode( argBean.getContactTimeCode() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setIntroducerBranchBankId( argBean.getIntroducerBranchBankId() );
		paramBean.setIntroducerEmpId( argBean.getIntroducerEmpId() );
		paramBean.setPermissionBranchBankId( argBean.getPermissionBranchBankId() );
		paramBean.setPermissionEmpId( argBean.getPermissionEmpId() );

		return getResponseMap( contactMeService.exportList( paramBean, argBean.getHeadOffice() ) );
	}

	/**
	 * 取得信貸專人聯絡案件詳細內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getDetail" )
	public Map<String, Object> getDetail( @RequestBody @Validated ContactMeDetailGetterArgBean argBean )
	{
		Long contactMeId = argBean.getContactMeId();

		return getResponseMap( contactMeService.getDetail( contactMeId ) );
	}

	/**
	 * 取得改派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getReassignBranchBank" )
	public Map<String, Object> getReassignBranchBank( @RequestBody @Validated ContactMeDetailGetterArgBean argBean )
	{
		Long contactMeId = argBean.getContactMeId();

		return getResponseMap( contactMeService.getReassignBranchBank( contactMeId ) );
	}

	/**
	 * 取得信貸專人聯絡案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "list" )
	public Map<String, Object> listContactMe( @RequestBody @Validated ContactMeListedArgBean argBean )
	{
		ContactMeListedParamBean paramBean = new ContactMeListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setContactTimeCode( argBean.getContactTimeCode() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setIntroducerBranchBankId( argBean.getIntroducerBranchBankId() );
		paramBean.setIntroducerEmpId( argBean.getIntroducerEmpId() );
		paramBean.setPermissionBranchBankId( argBean.getPermissionBranchBankId() );
		paramBean.setPermissionEmpId( argBean.getPermissionEmpId() );

		return getResponseMap( contactMeService.listContactMe( paramBean ) );

	}

	/**
	 * 更改案件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		Long contactMeId = argBean.getContactMeId();
		Long branchBankId = argBean.getBranchBankId();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( contactMeService.updateBranchBank( contactMeId, branchBankId, employeeId, employeeName ) );
	}

	/**
	 * 更新案件處理狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateProcessStatus" )
	public Map<String, Object> updateProcessStatus( @RequestBody @Validated ProcessStatusUpdatedArgBean argBean )
	{
		Long contactMeId = argBean.getContactMeId();
		String processStatus = argBean.getProcessStatus();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( contactMeService.updateProcessStatus( contactMeId, processStatus, employeeId, employeeName ) );
	}

}
