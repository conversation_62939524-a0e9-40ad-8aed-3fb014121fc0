package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.Arrays;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.dto.SigningContractCountDTO;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class SigningContractDAOIntegration
{
	@Autowired
	private SigningContractDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void checkUserOwnContracts()
	{
		List<Long> validatedIdentityIds = Arrays.asList( 11L );
		String productCode = "personalloan";

		List<Long> dtos = dao.checkUserOwnContracts( validatedIdentityIds, productCode );

		logger.info( "dtos:" + dtos );

		logger.info( "result:" + dtos.isEmpty() );
	}
	
	@Test
	public void getContractCountByBranch()
	{
		List<SigningContractCountDTO> dtos = dao.getContractCountByBranch( null, null );

		logger.info( "dtos:" + dtos.size() );
	}

}
