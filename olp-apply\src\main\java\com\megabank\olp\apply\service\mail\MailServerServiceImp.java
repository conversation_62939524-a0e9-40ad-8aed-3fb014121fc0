package com.megabank.olp.apply.service.mail;

import java.util.List;
import java.util.Properties;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.utility.BaseApplyService;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.Transport;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;


/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Transactional( propagation = Propagation.REQUIRES_NEW )
@Profile( { "sit", "uat", "prod", "stress" } )
public class MailServerServiceImp extends BaseApplyService implements MailServerService
{
	@Autowired
	private SystemService systemService;

	@Override
	public boolean send( List<String> to, String subject, String text )
	{
		String from = propertyBean.getMailServerFrom();
		String host = propertyBean.getMailServerUrl();

		return send( to, from, host, subject, text );
	}

	@Override
	public boolean send( List<String> to, String from, String host, String subject, String text )
	{
		try
		{
			if( to.isEmpty() )
			    return false;

			InternetAddress[] toArray = new InternetAddress[ to.size() ];
			for( int i = 0; i < to.size(); i++ )
			    toArray[ i ] = new InternetAddress( to.get( i ) );

			Properties properties = new Properties();
			properties.setProperty( "mail.smtp.host", host );
			Session session = Session.getDefaultInstance( properties );

			// compose the message
			MimeMessage message = new MimeMessage( session );
			message.setFrom( from );
			message.addRecipients( Message.RecipientType.TO, toArray );
			message.setSubject( subject );
			message.setText( text );

			// Send message
			Transport.send( message );

			return true;
		}
		catch( MessagingException ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-apply", "連結 mail server 系統錯誤" );

			return false;
		}
	}

	@Override
	public boolean send( String to, String subject, String text )
	{
		String from = propertyBean.getMailServerFrom();
		String host = propertyBean.getMailServerUrl();

		return send( to, from, host, subject, text );
	}

	@Override
	public boolean send( String to, String from, String host, String subject, String text )
	{
		try
		{
			Properties properties = new Properties();
			properties.setProperty( "mail.smtp.host", host );
			Session session = Session.getDefaultInstance( properties );

			// compose the message
			MimeMessage message = new MimeMessage( session );
			message.setFrom( from );
			message.addRecipient( Message.RecipientType.TO, new InternetAddress( to ) );
			message.setSubject( subject );
			message.setText( text );

			// Send message
			Transport.send( message );

			return true;
		}
		catch( MessagingException ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-apply", "連結 mail server 系統錯誤" );

			return false;
		}
	}
}
