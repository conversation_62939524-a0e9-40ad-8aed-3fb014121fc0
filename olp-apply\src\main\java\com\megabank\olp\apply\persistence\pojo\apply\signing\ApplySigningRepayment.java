package com.megabank.olp.apply.persistence.pojo.apply.signing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

@Entity
@Table( name = "apply_signing_repayment" )
public class ApplySigningRepayment extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_signing_repayment";

	public static final String SIGNING_CONTRACT_ID_CONSTANT = "signingContractId";

	private Long signingRepaymentId;

	private Long signingContractId;

	private String bankCode;

	private String bankName;

	private String repaymentProductType;

	private BigDecimal originalAmt;

	public ApplySigningRepayment()
	{}

	@Column( name = "bank_code" )
	public String getBankCode()
	{
		return bankCode;
	}

	@Column( name = "bank_name" )
	public String getBankName()
	{
		return bankName;
	}

	@Column( name = "original_amt", precision = 10 )
	public BigDecimal getOriginalAmt()
	{
		return originalAmt;
	}

	@Column( name = "repayment_product_type" )
	public String getRepaymentProductType()
	{
		return repaymentProductType;
	}

	@Column( name = "signing_contract_id" )
	public Long getSigningContractId()
	{
		return signingContractId;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "signing_repayment_id", unique = true, nullable = false )
	public Long getSigningRepaymentId()
	{
		return signingRepaymentId;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setBankName( String bankName )
	{
		this.bankName = bankName;
	}

	public void setOriginalAmt( BigDecimal originalAmt )
	{
		this.originalAmt = originalAmt;
	}

	public void setRepaymentProductType( String repaymentProductType )
	{
		this.repaymentProductType = repaymentProductType;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setSigningRepaymentId( Long signingRepaymentId )
	{
		this.signingRepaymentId = signingRepaymentId;
	}
}
