package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class CodeBranchBankDAOIntegration
{
	@Autowired
	private CodeBranchBankDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getPojoByHeadOffice()
	{
		boolean headOffice = true;

		CodeBranchBank pojo = dao.getPojoByHeadOffice( headOffice );

		logger.info( "pojo:{}", pojo );
	}

}
