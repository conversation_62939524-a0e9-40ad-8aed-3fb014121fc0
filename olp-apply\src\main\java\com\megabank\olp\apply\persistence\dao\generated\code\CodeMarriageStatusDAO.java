package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeMarriageStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeMarriageStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeMarriageStatusDAO extends BasePojoDAO<CodeMarriageStatus, String>
{
	public List<CodeMarriageStatus> getList()
	{
		return getAllPojos();
	}

	public CodeMarriageStatus read( String marriageStatusCode )
	{
		Validate.notBlank( marriageStatusCode );

		return getPojoByPK( marriageStatusCode, CodeMarriageStatus.TABLENAME_CONSTANT );
	}

	public CodeMarriageStatus readToNull( String marriageStatusCode )
	{
		Validate.notBlank( marriageStatusCode );

		return getPojoByPK( marriageStatusCode );
	}

	@Override
	protected Class<CodeMarriageStatus> getPojoClass()
	{
		return CodeMarriageStatus.class;
	}
}
