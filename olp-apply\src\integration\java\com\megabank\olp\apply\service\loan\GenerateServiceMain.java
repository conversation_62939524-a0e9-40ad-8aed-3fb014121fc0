package com.megabank.olp.apply.service.loan;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.base.config.BaseMicroWebConfig;

@EnableAutoConfiguration( exclude = { HibernateJpaAutoConfiguration.class } )
@Import( { ApplyConfig.class, BaseMicroWebConfig.class } )
public class GenerateServiceMain
{
	private final static Logger logger = LogManager.getLogger( GenerateServiceMain.class );

	public static void main( String[] args )
	{
		ApplicationContext context = SpringApplication.run( GenerateServiceMain.class, args );

		GenerateService service = context.getBean( GenerateService.class );

		GenerateServiceThread thread1 = new GenerateServiceThread( service );
		GenerateServiceThread thread2 = new GenerateServiceThread( service );
		GenerateServiceThread thread3 = new GenerateServiceThread( service );
		GenerateServiceThread thread4 = new GenerateServiceThread( service );
		GenerateServiceThread thread5 = new GenerateServiceThread( service );
		GenerateServiceThread thread6 = new GenerateServiceThread( service );
		GenerateServiceThread thread7 = new GenerateServiceThread( service );
		GenerateServiceThread thread8 = new GenerateServiceThread( service );
		GenerateServiceThread thread9 = new GenerateServiceThread( service );
		GenerateServiceThread thread10 = new GenerateServiceThread( service );

		thread1.start();
		thread2.start();
		thread3.start();
		thread4.start();
		thread5.start();
		thread6.start();
		thread7.start();
		thread8.start();
		thread9.start();
		thread10.start();

		logger.info( "generate service done." );
	}
}
