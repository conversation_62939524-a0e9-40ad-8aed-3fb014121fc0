/**
 *
 */
package com.megabank.olp.api.controller.btt;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.controller.btt.bean.ApplyCustInfoGetterArgBean;
import com.megabank.olp.api.service.btt.ApplyService;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "btt/apply" )
public class ApplyController extends BaseController
{
	@Autowired
	private ApplyService service;

	@PostMapping( "getCustInfo" )
	public Map<String, Object> getApplyCustInfo( @RequestBody @Validated ApplyCustInfoGetterArgBean argBean )
	{
		return getResponseMap( service.getApplyCustInfo( argBean.getCaseNo() ) );
	}

}
