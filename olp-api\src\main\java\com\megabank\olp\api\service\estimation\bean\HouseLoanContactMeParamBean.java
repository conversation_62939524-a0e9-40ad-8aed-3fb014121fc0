/**
 *
 */
package com.megabank.olp.api.service.estimation.bean;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanContactMeParamBean extends BaseBean
{
	private String caseID;

	private String mobileNumber;

	private Date createdDate;

	private String branchBankCode;

	private ContactMeBasicDataBean basicInfo;

	private ContactMeLoanDataBean loanInfo;

	private String otherMsg;

	public ContactMeBasicDataBean getBasicInfo()
	{
		return basicInfo;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseID()
	{
		return caseID;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public ContactMeLoanDataBean getLoanInfo()
	{
		return loanInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public void setBasicInfo(ContactMeBasicDataBean basicInfo )
	{
		this.basicInfo = basicInfo;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseID( String caseID )
	{
		this.caseID = caseID;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setLoanInfo( ContactMeLoanDataBean loanInfo )
	{
		this.loanInfo = loanInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}
}
