package com.megabank.olp.api.service.ixml;

import java.io.IOException;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.client.sender.ixml.doCallback.IxmlDoCallbackClient;
import com.megabank.olp.client.sender.ixml.doCallback.bean.IxmlDoCallbackArgBean;
import com.megabank.olp.client.sender.micro.JwtArgBean;

@Service
public class IxmlService extends BaseApiService
{
	@Autowired
	private IxmlDoCallbackClient ixmlDoCallbackClient;

	public String doCallback( HttpServletRequest request ) throws IOException
	{
		IxmlDoCallbackArgBean argBean = new IxmlDoCallbackArgBean();
		argBean.setUrl( request.getRequestURL().toString() );
		argBean.setParams( request.getReader().lines().collect( Collectors.joining( System.lineSeparator() ) ) );

		return ixmlDoCallbackClient.send( argBean, new JwtArgBean() );
	}
}
