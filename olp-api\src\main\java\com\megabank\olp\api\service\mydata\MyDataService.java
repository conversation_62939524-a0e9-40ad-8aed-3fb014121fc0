/**
 *
 */
package com.megabank.olp.api.service.mydata;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.controller.open.bean.DataTransferArgBean;
import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.mydata.MyDataNotifiedClient;
import com.megabank.olp.client.sender.micro.apply.mydata.MyDataTransferClient;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MyDataNotifiedArgBean;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MyDataTransferArgBean;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MydataResultBean;

@Service
public class MyDataService extends BaseApiService
{
	@Autowired
	private MyDataNotifiedClient myDataNotifiedClient;
	
	@Autowired
	private MyDataTransferClient myDataTransferClient;

	public void notifyServlet( String txId, String idNo, int waitSec )
	{
		MyDataNotifiedArgBean argBean = new MyDataNotifiedArgBean();
		argBean.setIdNo( idNo );
		argBean.setTxId( txId );
		argBean.setWaitSec( waitSec );

		myDataNotifiedClient.send( argBean, new JwtArgBean() );
	}
	
	public MydataResultBean dataTransfer( DataTransferArgBean dataTransferArgBean )
	{
		MyDataTransferArgBean argBean = new MyDataTransferArgBean();
		argBean.setTransactionId( dataTransferArgBean.getTransactionId() );
		argBean.setTx_id( dataTransferArgBean.getTx_id() );
		argBean.setFile( dataTransferArgBean.getFile() );
		argBean.setStatus( dataTransferArgBean.getStatus() );

		return myDataTransferClient.send( argBean, new JwtArgBean() );
	}

}
