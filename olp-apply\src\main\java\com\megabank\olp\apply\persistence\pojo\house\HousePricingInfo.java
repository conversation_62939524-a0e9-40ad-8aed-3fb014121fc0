package com.megabank.olp.apply.persistence.pojo.house;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;

import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeProcess;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The HousePricingInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_pricing_info", uniqueConstraints = @UniqueConstraint( columnNames = "case_no" ) )
public class HousePricingInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_pricing_info";

	public static final String PRICING_INFO_ID_CONSTANT = "pricingInfoId";

	public static final String CODE_BRANCH_BANK_CONSTANT = "codeBranchBank";

	public static final String CODE_PROCESS_CONSTANT = "codeProcess";

	public static final String CASE_NO_CONSTANT = "caseNo";

	public static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	public static final String EMAIL_CONSTANT = "email";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String APPLY_NOTES_CONSTANT = "applyNotes";

	public static final String HOUSE_ESTIMATE_INFO_CONSTANT = "houseEstimateInfo";

	public static final String HOUSE_INFO_CONSTANT = "houseInfo";

	private Long pricingInfoId;

	private transient CodeBranchBank codeBranchBank;

	private transient CodeProcess codeProcess;

	private String caseNo;

	private String mobileNumber;

	private String email;

	private boolean notified;

	private Date updatedDate;

	private Date createdDate;

	private transient Set<ApplyNote> applyNotes = new HashSet<>( 0 );

	private transient HouseEstimateInfo houseEstimateInfo;

	private transient HouseInfo houseInfo;

	public HousePricingInfo()
	{}

	public HousePricingInfo( CodeBranchBank codeBranchBank, CodeProcess codeProcess, String caseNo, boolean notified, Date updatedDate,
							 Date createdDate )
	{
		this.codeBranchBank = codeBranchBank;
		this.codeProcess = codeProcess;
		this.caseNo = caseNo;
		this.notified = notified;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
	}

	public HousePricingInfo( Long pricingInfoId )
	{
		this.pricingInfoId = pricingInfoId;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "housePricingInfo" )
	public Set<ApplyNote> getApplyNotes()
	{
		return applyNotes;
	}

	@Column( name = "case_no", unique = true, nullable = false, length = 20 )
	public String getCaseNo()
	{
		return caseNo;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "branch_bank_id", nullable = false )
	public CodeBranchBank getCodeBranchBank()
	{
		return codeBranchBank;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "process_code", nullable = false )
	public CodeProcess getCodeProcess()
	{
		return codeProcess;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "email", length = 50 )
	public String getEmail()
	{
		return email;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "housePricingInfo" )
	public HouseEstimateInfo getHouseEstimateInfo()
	{
		return houseEstimateInfo;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "housePricingInfo" )
	public HouseInfo getHouseInfo()
	{
		return houseInfo;
	}

	@Column( name = "mobile_number", length = 10 )
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "pricing_info_id", unique = true, nullable = false )
	public Long getPricingInfoId()
	{
		return pricingInfoId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	public void setApplyNotes( Set<ApplyNote> applyNotes )
	{
		this.applyNotes = applyNotes;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCodeBranchBank( CodeBranchBank codeBranchBank )
	{
		this.codeBranchBank = codeBranchBank;
	}

	public void setCodeProcess( CodeProcess codeProcess )
	{
		this.codeProcess = codeProcess;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setHouseEstimateInfo( HouseEstimateInfo houseEstimateInfo )
	{
		this.houseEstimateInfo = houseEstimateInfo;
	}

	public void setHouseInfo( HouseInfo houseInfo )
	{
		this.houseInfo = houseInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setPricingInfoId( Long pricingInfoId )
	{
		this.pricingInfoId = pricingInfoId;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}
}