package com.megabank.olp.apply.persistence.dao.generated.house;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.house.ContactBasicCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.house.HouseContactBasicInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseContactBasicInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseContactBasicInfoDAO extends BasePojoDAO<HouseContactBasicInfo, Long>
{
	@Autowired
	private HouseContactInfoDAO contactInfoDAO;

	public Long create( ContactBasicCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getContactId() );
		Validate.notBlank( paramBean.getcName() );
		Validate.notNull( paramBean.getCallBackTime() );

		HouseContactBasicInfo pojo = new HouseContactBasicInfo();
		pojo.setHouseContactInfo( contactInfoDAO.read( paramBean.getContactId() ) );
		pojo.setCName( paramBean.getcName() );
		pojo.setCallBackTime( paramBean.getCallBackTime() );

		return super.createPojo( pojo );
	}

	public HouseContactBasicInfo read( Long houseContactId )
	{
		Validate.notNull( houseContactId );

		return getPojoByPK( houseContactId, HouseContactBasicInfo.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<HouseContactBasicInfo> getPojoClass()
	{
		return HouseContactBasicInfo.class;
	}
}
