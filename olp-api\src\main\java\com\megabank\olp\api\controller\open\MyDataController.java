package com.megabank.olp.api.controller.open;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.controller.open.bean.ServletNotifiedArgBean;
import com.megabank.olp.api.service.mydata.MyDataService;
import com.megabank.olp.base.layer.BaseController;

@RestController
@RequestMapping( "open/mydata" )
public class MyDataController extends BaseController
{
	@Autowired
	private MyDataService service;

	@PostMapping( "notifyServlet" )
	public Map<String, Object> notifyServlet( @RequestBody @Validated ServletNotifiedArgBean argBean )
	{
		String txId = argBean.getTxId();
		String idNo = argBean.getIdNo();
		int waitSec = argBean.getWaitSec();

		/*
		 * curl -X POST -H "Content-type: application/json" -d
		 * "{\"txId\":\"abcdefgh\",\"userId\":\"wxyz\",\"waitSec\":3600}"
		 * http://127.0.0.1:9005/open/mydata/notifyServlet
		 * http://192.168.70.21/service/api/open/mydata/notifyServlet
		 */
		service.notifyServlet( txId, idNo, waitSec );

		return getResponseMap();
	}

}
