package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningEddaCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningEddaUpdateParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningEdda;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.enums.OrderEnum;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderArgBean;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderResultBean;

/**
 * The ApplySigningEddaDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySigningEddaDAO extends BasePojoDAO<ApplySigningEdda, Long>
{
	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	@Qualifier( "clientObjectMapper" )
	protected ObjectMapper mapper;

	public Long create( SigningEddaCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getSigningContractId() );
		Validate.notBlank( paramBean.getAdMark() );
		Validate.notBlank( paramBean.getaID() );
		Validate.notBlank( paramBean.getCertType() );
		Validate.notBlank( paramBean.getMegaMessageId() );
		Validate.notBlank( paramBean.getMegaSystemId() );
		Validate.notBlank( paramBean.getpBank() );
		Validate.notBlank( paramBean.getrBank() );
		Validate.notBlank( paramBean.getRclNo() );
		Validate.notBlank( paramBean.getReservedField() );
		Validate.notBlank( paramBean.getrID() );
		Validate.notBlank( paramBean.getTaskId() );
		Validate.notBlank( paramBean.getTix() );
		Validate.notBlank( paramBean.getUserNo() );

		ApplySigningEdda pojo = new ApplySigningEdda();
		pojo.setAdMark( paramBean.getAdMark() );
		pojo.setAid( paramBean.getaID() );
		pojo.setCertType( paramBean.getCertType() );
		pojo.setExpiryDate( paramBean.getExpiryDate() );
		pojo.setMegaMessageId( paramBean.getMegaMessageId() );
		pojo.setMegaSystemId( paramBean.getMegaSystemId() );
		pojo.setPbank( paramBean.getpBank() );
		pojo.setRbank( paramBean.getrBank() );
		pojo.setRclNo( paramBean.getRclNo() );
		pojo.setReservedField( paramBean.getReservedField() );
		pojo.setRid( paramBean.getrID() );
		pojo.setTaskId( paramBean.getTaskId() );
		pojo.setTix( paramBean.getTix() );
		pojo.setUserNo( paramBean.getUserNo() );
		pojo.setApplySigningContract( applySigningContractDAO.read( paramBean.getSigningContractId() ) );
		pojo.setCreateTime( new Date() );
		pojo.setDiscard( false );
		pojo.setPbankNote( paramBean.getpBankNote() );

		return super.createPojo( pojo );
	}

	public Long discard( Long eddaId, Long signingContractId ) throws ParseException
	{
		ApplySigningEdda pojo = read( eddaId, signingContractId );
		pojo.setDiscard( true );
		pojo.setUpdateTime( new Date() );
		return pojo.getEddaId();
	}

	public SigningEddaCreatedParamBean mapCreate( EddaSenderArgBean paramBean )
	{
		return mapper.convertValue( paramBean, SigningEddaCreatedParamBean.class );
	}

	public SigningEddaUpdateParamBean mapUpdate( EddaSenderResultBean paramBean )
	{
		return mapper.convertValue( paramBean, SigningEddaUpdateParamBean.class );
	}

	public ApplySigningEdda read( Long eddaId, Long contractId )
	{
		Validate.notNull( contractId );

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( ApplySigningEdda.EDDA_ID_CONSTANT, eddaId ),
														  new NameValueBean( ApplySigningEdda.APPLY_SIGNING_CONTRACT_CONSTANT,
																			 applySigningContractDAO.read( contractId ) ) };

		return getUniquePojoByProperties( conditions, ApplySigningEdda.TABLENAME_CONSTANT );
	}

	public ApplySigningEdda readCntrNoLastest( String userNo )
	{
		Validate.notNull( userNo );

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( ApplySigningEdda.RC_CONSTANT, "A0" ),
														  new NameValueBean( ApplySigningEdda.USER_NO_CONSTANT, userNo ),
														  new NameValueBean( ApplySigningEdda.DISCARD_CONSTANT, false ) };

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplySigningEdda.EDDA_ID_CONSTANT, OrderEnum.DESCEND ) };

		List<ApplySigningEdda> eddas = getPojosByPropertiesOrderBy( conditions, 1, orderBeans );

		if( eddas.size() > 0 )
			return eddas.get( 0 );
		else
			return null;
	}

	public ApplySigningEdda readLastest( Long contractId )
	{
		Validate.notNull( contractId );

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( ApplySigningEdda.RC_CONSTANT, "A0" ),
														  new NameValueBean( ApplySigningEdda.APPLY_SIGNING_CONTRACT_CONSTANT,
																			 applySigningContractDAO.read( contractId ) ),
														  new NameValueBean( ApplySigningEdda.DISCARD_CONSTANT, false ) };

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplySigningEdda.EDDA_ID_CONSTANT, OrderEnum.DESCEND ) };

		List<ApplySigningEdda> eddas = getPojosByPropertiesOrderBy( conditions, 1, orderBeans );

		if( eddas.size() > 0 )
			return eddas.get( 0 );
		else
			return null;
	}

	public Long update( SigningEddaUpdateParamBean paramBean ) throws ParseException
	{
		Validate.notNull( paramBean.getEddaId() );

		ApplySigningEdda pojo = read( paramBean.getEddaId(), paramBean.getSigningContractId() );
		pojo.setRcAdate( transString2Date( paramBean.getaDate(), "yyyyMMddHHmmss" ) );
		pojo.setRc( paramBean.getRc() );
		pojo.setRcSeq( paramBean.getSeq() );
		pojo.setRcCno( paramBean.getcNo() );
		pojo.setRcReservedField( paramBean.getReservedField() );
		pojo.setRcDesc( paramBean.getRcDesc() );
		pojo.setUpdateTime( new Date() );
		pojo.setRcPbankNote( paramBean.getpBankNote() );
		return pojo.getEddaId();
	}

	private Date transString2Date( String value, String format )
	{
		Date rocDate = CommonDateStringUtils.transString2Date( value, format );
		Calendar Calendar = DateUtils.toCalendar( rocDate );
		Calendar.set( java.util.Calendar.YEAR, Calendar.get( java.util.Calendar.YEAR ) );

		return Calendar.getTime();
	}

	@Override
	protected Class<ApplySigningEdda> getPojoClass()
	{
		return ApplySigningEdda.class;
	}
}
