package com.megabank.olp.apply.persistence.pojo.apply.signing;

import com.megabank.olp.base.bean.BaseBean;
import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import static jakarta.persistence.GenerationType.IDENTITY;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The ApplySigningEdda is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_signing_edda" )
public class ApplySigningEdda extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_signing_edda";

	public static final String EDDA_ID_CONSTANT = "eddaId";

	public static final String APPLY_SIGNING_CONTRACT_CONSTANT = "applySigningContract";

	public static final String AD_MARK_CONSTANT = "adMark";

	public static final String AID_CONSTANT = "aid";

	public static final String CARD_NUM_CONSTANT = "cardNum";

	public static final String CERT_TYPE_CONSTANT = "certType";

	public static final String CHIP_CARD_REMARKS_CONSTANT = "chipCardRemarks";

	public static final String CUR_CONSTANT = "cur";

	public static final String DN_CONSTANT = "dn";

	public static final String ENC_DATA_CONSTANT = "encData";

	public static final String ENC_SEQ_CONSTANT = "encSeq";

	public static final String EXPIRY_DATE_CONSTANT = "expiryDate";

	public static final String LIMIT_DAMT_CONSTANT = "limitDamt";

	public static final String LIMIT_MAMT_CONSTANT = "limitMamt";

	public static final String LIMIT_SAMT_CONSTANT = "limitSamt";

	public static final String MEGA_MESSAGE_ID_CONSTANT = "megaMessageId";

	public static final String MEGA_SYSTEM_ID_CONSTANT = "megaSystemId";

	public static final String NOTE_CONSTANT = "note";

	public static final String PBANK_CONSTANT = "pbank";

	public static final String PBANK_NOTE_CONSTANT = "pbankNote";

	public static final String RBANK_CONSTANT = "rbank";

	public static final String RCL_NO_CONSTANT = "rclNo";

	public static final String RESERVED_FIELD_CONSTANT = "reservedField";

	public static final String RID_CONSTANT = "rid";

	public static final String SUPER_MARKET_CONSTANT = "superMarket";

	public static final String TASK_ID_CONSTANT = "taskId";

	public static final String TIX_CONSTANT = "tix";

	public static final String UNPREDICTABLE_NUM_CONSTANT = "unpredictableNum";

	public static final String USER_NO_CONSTANT = "userNo";

	public static final String RC_ADATE_CONSTANT = "rcAdate";

	public static final String RC_CONSTANT = "rc";

	public static final String RC_NOTE_CONSTANT = "rcNote";

	public static final String RC_CNO_CONSTANT = "rcCno";

	public static final String RC_SEQ_CONSTANT = "rcSeq";

	public static final String RC_RESERVED_FIELD_CONSTANT = "rcReservedField";

	public static final String RC_PBANK_NOTE_CONSTANT = "rcPbankNote";

	public static final String RC_DESC_CONSTANT = "rcDesc";

	public static final String CREATE_TIME_CONSTANT = "createTime";

	public static final String UPDATE_TIME_CONSTANT = "updateTime";
	
	public static final String DISCARD_CONSTANT = "discard";

	private Long eddaId;

	private transient ApplySigningContract applySigningContract;

	private String adMark;

	private String aid;

	private String cardNum;

	private String certType;

	private String chipCardRemarks;

	private String cur;

	private String dn;

	private String encData;

	private String encSeq;

	private String expiryDate;

	private String limitDamt;

	private String limitMamt;

	private String limitSamt;

	private String megaMessageId;

	private String megaSystemId;

	private String note;

	private String pbank;

	private String pbankNote;

	private String rbank;

	private String rclNo;

	private String reservedField;

	private String rid;

	private String superMarket;

	private String taskId;

	private String tix;

	private String unpredictableNum;

	private String userNo;

	private Date rcAdate;

	private String rc;

	private String rcNote;

	private String rcCno;

	private String rcSeq;

	private String rcReservedField;

	private String rcPbankNote;

	private String rcDesc;

	private Date createTime;

	private Date updateTime;
	
	private Boolean discard;

	public ApplySigningEdda()
	{}

	public ApplySigningEdda( Long eddaId )
	{
		this.eddaId = eddaId;
	}

	public ApplySigningEdda( ApplySigningContract applySigningContract, String adMark, String aid )
	{
		this.applySigningContract = applySigningContract;
		this.adMark = adMark;
		this.aid = aid;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "edda_id", unique = true, nullable = false )
	public Long getEddaId()
	{
		return this.eddaId;
	}

	public void setEddaId( Long eddaId )
	{
		this.eddaId = eddaId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "signing_contract_id", nullable = false )
	public ApplySigningContract getApplySigningContract()
	{
		return applySigningContract;
	}

	public void setApplySigningContract( ApplySigningContract applySigningContract )
	{
		this.applySigningContract = applySigningContract;
	}

	@Column( name = "adMark", nullable = false, length = 1 )
	public String getAdMark()
	{
		return this.adMark;
	}

	public void setAdMark( String adMark )
	{
		this.adMark = adMark;
	}

	@Column( name = "aID", nullable = false, length = 10 )
	public String getAid()
	{
		return this.aid;
	}

	public void setAid( String aid )
	{
		this.aid = aid;
	}

	@Column( name = "cardNum", length = 20 )
	public String getCardNum()
	{
		return this.cardNum;
	}

	public void setCardNum( String cardNum )
	{
		this.cardNum = cardNum;
	}

	@Column( name = "certType", length = 10 )
	public String getCertType()
	{
		return this.certType;
	}

	public void setCertType( String certType )
	{
		this.certType = certType;
	}

	@Column( name = "chipCardRemarks", length = 10 )
	public String getChipCardRemarks()
	{
		return this.chipCardRemarks;
	}

	public void setChipCardRemarks( String chipCardRemarks )
	{
		this.chipCardRemarks = chipCardRemarks;
	}

	@Column( name = "cur", length = 10 )
	public String getCur()
	{
		return this.cur;
	}

	public void setCur( String cur )
	{
		this.cur = cur;
	}

	@Column( name = "dn", length = 10 )
	public String getDn()
	{
		return this.dn;
	}

	public void setDn( String dn )
	{
		this.dn = dn;
	}

	@Column( name = "encData", length = 10 )
	public String getEncData()
	{
		return this.encData;
	}

	public void setEncData( String encData )
	{
		this.encData = encData;
	}

	@Column( name = "encSeq", length = 10 )
	public String getEncSeq()
	{
		return this.encSeq;
	}

	public void setEncSeq( String encSeq )
	{
		this.encSeq = encSeq;
	}

	@Column( name = "expiryDate", length = 10 )
	public String getExpiryDate()
	{
		return this.expiryDate;
	}

	public void setExpiryDate( String expiryDate )
	{
		this.expiryDate = expiryDate;
	}

	@Column( name = "limitDamt", length = 10 )
	public String getLimitDamt()
	{
		return this.limitDamt;
	}

	public void setLimitDamt( String limitDamt )
	{
		this.limitDamt = limitDamt;
	}

	@Column( name = "limitMamt", length = 10 )
	public String getLimitMamt()
	{
		return this.limitMamt;
	}

	public void setLimitMamt( String limitMamt )
	{
		this.limitMamt = limitMamt;
	}

	@Column( name = "limitSamt", length = 10 )
	public String getLimitSamt()
	{
		return this.limitSamt;
	}

	public void setLimitSamt( String limitSamt )
	{
		this.limitSamt = limitSamt;
	}

	@Column( name = "megaMessageId", length = 100 )
	public String getMegaMessageId()
	{
		return this.megaMessageId;
	}

	public void setMegaMessageId( String megaMessageId )
	{
		this.megaMessageId = megaMessageId;
	}

	@Column( name = "megaSystemId", length = 10 )
	public String getMegaSystemId()
	{
		return this.megaSystemId;
	}

	public void setMegaSystemId( String megaSystemId )
	{
		this.megaSystemId = megaSystemId;
	}

	@Column( name = "note", length = 100 )
	public String getNote()
	{
		return this.note;
	}

	public void setNote( String note )
	{
		this.note = note;
	}

	@Column( name = "pBank", length = 7 )
	public String getPbank()
	{
		return this.pbank;
	}

	public void setPbank( String pbank )
	{
		this.pbank = pbank;
	}

	@Column( name = "pBankNote", length = 100 )
	public String getPbankNote()
	{
		return this.pbankNote;
	}

	public void setPbankNote( String pbankNote )
	{
		this.pbankNote = pbankNote;
	}

	@Column( name = "rBank", length = 7 )
	public String getRbank()
	{
		return this.rbank;
	}

	public void setRbank( String rbank )
	{
		this.rbank = rbank;
	}

	@Column( name = "rclNo", length = 20 )
	public String getRclNo()
	{
		return this.rclNo;
	}

	public void setRclNo( String rclNo )
	{
		this.rclNo = rclNo;
	}

	@Column( name = "reservedField", length = 10 )
	public String getReservedField()
	{
		return this.reservedField;
	}

	public void setReservedField( String reservedField )
	{
		this.reservedField = reservedField;
	}

	@Column( name = "rID", length = 10 )
	public String getRid()
	{
		return this.rid;
	}

	public void setRid( String rid )
	{
		this.rid = rid;
	}

	@Column( name = "superMarket", length = 100 )
	public String getSuperMarket()
	{
		return this.superMarket;
	}

	public void setSuperMarket( String superMarket )
	{
		this.superMarket = superMarket;
	}

	@Column( name = "taskId", length = 4 )
	public String getTaskId()
	{
		return this.taskId;
	}

	public void setTaskId( String taskId )
	{
		this.taskId = taskId;
	}

	@Column( name = "tix", length = 3 )
	public String getTix()
	{
		return this.tix;
	}

	public void setTix( String tix )
	{
		this.tix = tix;
	}

	@Column( name = "unpredictableNum", length = 100 )
	public String getUnpredictableNum()
	{
		return this.unpredictableNum;
	}

	public void setUnpredictableNum( String unpredictableNum )
	{
		this.unpredictableNum = unpredictableNum;
	}

	@Column( name = "userNo", length = 20 )
	public String getUserNo()
	{
		return this.userNo;
	}

	public void setUserNo( String userNo )
	{
		this.userNo = userNo;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "rcADate", length = 23 )
	public Date getRcAdate()
	{
		return this.rcAdate;
	}

	public void setRcAdate( Date rcAdate )
	{
		this.rcAdate = rcAdate;
	}

	@Column( name = "rc", length = 3 )
	public String getRc()
	{
		return this.rc;
	}

	public void setRc( String rc )
	{
		this.rc = rc;
	}

	@Column( name = "rcNote", length = 100 )
	public String getRcNote()
	{
		return this.rcNote;
	}

	public void setRcNote( String rcNote )
	{
		this.rcNote = rcNote;
	}

	@Column( name = "rcCNo", length = 20 )
	public String getRcCno()
	{
		return this.rcCno;
	}

	public void setRcCno( String rcCno )
	{
		this.rcCno = rcCno;
	}

	@Column( name = "rcSeq", length = 10 )
	public String getRcSeq()
	{
		return this.rcSeq;
	}

	public void setRcSeq( String rcSeq )
	{
		this.rcSeq = rcSeq;
	}

	@Column( name = "rcReservedField", length = 10 )
	public String getRcReservedField()
	{
		return this.rcReservedField;
	}

	public void setRcReservedField( String rcReservedField )
	{
		this.rcReservedField = rcReservedField;
	}

	@Column( name = "rcPBankNote", length = 100 )
	public String getRcPbankNote()
	{
		return this.rcPbankNote;
	}

	public void setRcPbankNote( String rcPbankNote )
	{
		this.rcPbankNote = rcPbankNote;
	}

	@Column( name = "rcDesc", length = 100 )
	public String getRcDesc()
	{
		return this.rcDesc;
	}

	public void setRcDesc( String rcDesc )
	{
		this.rcDesc = rcDesc;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "createTime", length = 23 )
	public Date getCreateTime()
	{
		return this.createTime;
	}

	public void setCreateTime( Date createTime )
	{
		this.createTime = createTime;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updateTime", length = 23 )
	public Date getUpdateTime()
	{
		return this.updateTime;
	}

	public void setUpdateTime( Date updateTime )
	{
		this.updateTime = updateTime;
	}
	
	@Column( name = "discard", nullable = false, precision = 1, scale = 0 )
	public Boolean getDiscard()
	{
		return discard;
	}
	
	public void setDiscard( Boolean discard )
	{
		this.discard = discard;
	}
}