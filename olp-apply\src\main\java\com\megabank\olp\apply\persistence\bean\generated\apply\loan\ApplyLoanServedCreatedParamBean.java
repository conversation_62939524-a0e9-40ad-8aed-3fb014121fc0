package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanServedCreatedParamBean extends BaseBean
{
	private Long loanId;

	private String companyName;

	private String taxNo;

	private String servedTitle;

	private String comment;

	private String representativeType;

	public ApplyLoanServedCreatedParamBean()
	{}

	public String getComment()
	{
		return comment;
	}

	public String getCompanyName()
	{
		return companyName;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getRepresentativeType()
	{
		return representativeType;
	}

	public String getServedTitle()
	{
		return servedTitle;
	}

	public String getTaxNo()
	{
		return taxNo;
	}

	public void setComment( String comment )
	{
		this.comment = comment;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setRepresentativeType( String representativeType )
	{
		this.representativeType = representativeType;
	}

	public void setServedTitle( String servedTitle )
	{
		this.servedTitle = servedTitle;
	}

	public void setTaxNo( String taxNo )
	{
		this.taxNo = taxNo;
	}
}
