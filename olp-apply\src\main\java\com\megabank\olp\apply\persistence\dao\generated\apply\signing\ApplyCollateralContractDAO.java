package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.signing.ApplyCollateralContractCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyCollateralContract;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

@Repository
public class ApplyCollateralContractDAO extends BasePojoDAO<ApplyCollateralContract, Long>
{
	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	public Long create( Long contractId, ApplyCollateralContractCreatedParamBean paramBean )
	{
		ApplyCollateralContract pojo = new ApplyCollateralContract();
		pojo.setApplySigningContract( applySigningContractDAO.read( contractId ) );
		pojo.setConsentVer( paramBean.getConsentVer() );
		pojo.setCollateralBuildingAddr1( paramBean.getCollateralBuildingAddr1() );
		pojo.setCollateralBuildingAddr2( paramBean.getCollateralBuildingAddr2() );
		pojo.setMortgageMaxAmt1( paramBean.getMortgageMaxAmt1() );
		pojo.setMortgageMaxAmt2( paramBean.getMortgageMaxAmt2() );
		pojo.setFirstLoanDateYear( paramBean.getFirstLoanDateYear() );
		pojo.setFirstLoanDateMth( paramBean.getFirstLoanDateMth() );
		pojo.setFirstLoanDateDay( paramBean.getFirstLoanDateDay() );
		pojo.setFirstLoanAmt1( paramBean.getFirstLoanAmt1() );
		pojo.setFirstLoanAmt2( paramBean.getFirstLoanAmt2() );
		pojo.setCollateralContractTerms( paramBean.getCollateralContractTerms() );
		pojo.setUnregisteredBuildingDesc( paramBean.getUnregisteredBuildingDesc() );
		pojo.setHouseLoanContractNo( paramBean.getHouseLoanContractNo() );
		pojo.setCoTarget( paramBean.getCoTarget() );
		pojo.setCbAfftTerms( paramBean.getCbAfftTerms() );
		pojo.setCbAfftVersion( paramBean.getCbAfftVersion() );
		pojo.setCbAfft1_1( paramBean.getCbAfft1_1() );
		pojo.setCbAfft1_2( paramBean.getCbAfft1_2() );
		pojo.setCbAfft1_3_year( paramBean.getCbAfft1_3_year() );
		pojo.setCbAfft1_3_mth( paramBean.getCbAfft1_3_mth() );
		pojo.setCbAfft1_3_day( paramBean.getCbAfft1_3_day() );
		pojo.setCbAfft1_4( paramBean.getCbAfft1_4() );
		pojo.setCbAfft1_5( paramBean.getCbAfft1_5() );
		pojo.setCbAfft1_6( paramBean.getCbAfft1_6() );
		pojo.setCbAfft1_7( paramBean.getCbAfft1_7() );
		pojo.setCbAfft1_8( paramBean.getCbAfft1_8() );
		pojo.setCbAfft1_9_year( paramBean.getCbAfft1_9_year() );
		pojo.setCbAfft1_9_mth( paramBean.getCbAfft1_9_mth() );
		pojo.setCbAfft1_9_day( paramBean.getCbAfft1_9_day() );
		pojo.setCbAfft1_10_year( paramBean.getCbAfft1_10_year() );
		pojo.setCbAfft1_10_mth( paramBean.getCbAfft1_10_mth() );
		pojo.setCbAfft1_10_day( paramBean.getCbAfft1_10_day() );
		pojo.setCbAfft1_10_no( paramBean.getCbAfft1_10_no() );
		pojo.setCbAfft1_11( paramBean.getCbAfft1_11() );
		pojo.setCbAfft1_12( paramBean.getCbAfft1_12() );
		pojo.setCbAfft1_13( paramBean.getCbAfft1_13() );
		pojo.setCbAfft1_14( paramBean.getCbAfft1_14() );
		pojo.setCbAfft1_15( paramBean.getCbAfft1_15() );
		pojo.setCbAfft1_16( paramBean.getCbAfft1_16() );
		pojo.setCbAfft2_1( paramBean.getCbAfft2_1() );
		pojo.setCbAfft2_2( paramBean.getCbAfft2_2() );
		pojo.setCbAfft2_3_year( paramBean.getCbAfft2_3_year() );
		pojo.setCbAfft2_3_mth( paramBean.getCbAfft2_3_mth() );
		pojo.setCbAfft2_3_day( paramBean.getCbAfft2_3_day() );
		pojo.setCbAfft2_4( paramBean.getCbAfft2_4() );
		pojo.setCbAfft2_5( paramBean.getCbAfft2_5() );
		pojo.setCbAfft2_6( paramBean.getCbAfft2_6() );
		pojo.setCbAfft2_7( paramBean.getCbAfft2_7() );
		pojo.setCbAfft2_8( paramBean.getCbAfft2_8() );
		pojo.setCbAfft2_9( paramBean.getCbAfft2_9() );
		pojo.setCbAfft2_10( paramBean.getCbAfft2_10() );
		pojo.setCbAfft3_1( paramBean.getCbAfft3_1() );
		pojo.setCbAfft3_2( paramBean.getCbAfft3_2() );
		pojo.setCbAfft3_3_year( paramBean.getCbAfft3_3_year() );
		pojo.setCbAfft3_3_mth( paramBean.getCbAfft3_3_mth() );
		pojo.setCbAfft3_3_day( paramBean.getCbAfft3_3_day() );
		pojo.setCbAfft3_4( paramBean.getCbAfft3_4() );
		pojo.setCbAfft3_5( paramBean.getCbAfft3_5() );
		pojo.setCbAfft4_1( paramBean.getCbAfft4_1() );
		pojo.setCbAfft4_2_year( paramBean.getCbAfft4_2_year() );
		pojo.setCbAfft4_2_mth( paramBean.getCbAfft4_2_mth() );
		pojo.setCbAfft4_2_day( paramBean.getCbAfft4_2_day() );
		pojo.setCbAfft4_3( paramBean.getCbAfft4_3() );
		pojo.setCbAfft4_4( paramBean.getCbAfft4_4() );
		pojo.setCbAfft4_5( paramBean.getCbAfft4_5() );
		pojo.setCbAfft4_6( paramBean.getCbAfft4_6() );
		pojo.setCbAfft4_7( paramBean.getCbAfft4_7() );
		pojo.setCbAfft5_1( paramBean.getCbAfft5_1() );
		pojo.setCbAfft5_2( paramBean.getCbAfft5_2() );
		pojo.setCbAfft5_3_year( paramBean.getCbAfft5_3_year() );
		pojo.setCbAfft5_3_mth( paramBean.getCbAfft5_3_mth() );
		pojo.setCbAfft5_3_day( paramBean.getCbAfft5_3_day() );
		pojo.setCbAfft5_4( paramBean.getCbAfft5_4() );
		pojo.setCbAfft5_5( paramBean.getCbAfft5_5() );
		pojo.setCbAfft5_6( paramBean.getCbAfft5_6() );
		pojo.setCbAfft5_7( paramBean.getCbAfft5_7() );
		pojo.setCbAfft5_8( paramBean.getCbAfft5_8() );
		pojo.setCbAfft5_9( paramBean.getCbAfft5_9() );
		pojo.setCbAfft5_10( paramBean.getCbAfft5_10() );
		pojo.setCbAfft5_11( paramBean.getCbAfft5_11() );
		pojo.setCbAfft5_12( paramBean.getCbAfft5_12() );
		pojo.setCbAfft5_13( paramBean.getCbAfft5_13() );
		pojo.setCbAfft5_14( paramBean.getCbAfft5_14() );
		pojo.setCbAfft5_15( paramBean.getCbAfft5_15() );
		return super.createPojo( pojo );
	}

	public ApplyCollateralContract getPojoBySigningContractId( Long signingContractId )
	{
		Validate.notNull( signingContractId );

		NameValueBean condition = new NameValueBean( ApplyCollateralContract.SIGNING_CONTRACT_ID_CONSTANT, signingContractId );

		return getUniquePojoByProperty( condition, ApplyCollateralContract.TABLENAME_CONSTANT );
	}

	public Long updateMortgageSettingDesc( Long signingContractId, String mortgageSettingDesc )
	{
		Validate.notNull( signingContractId );

		ApplyCollateralContract pojo = getPojoBySigningContractId( signingContractId );
		pojo.setMortgageSettingDesc( mortgageSettingDesc );

		return pojo.getSigningContractId();
	}

	@Override
	protected Class<ApplyCollateralContract> getPojoClass()
	{
		return ApplyCollateralContract.class;
	}
}
