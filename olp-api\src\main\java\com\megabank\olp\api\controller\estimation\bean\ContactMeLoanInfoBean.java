/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class ContactMeLoanInfoBean extends BaseBean
{
	private String loanPurpose;

	@Digits( integer = 11, fraction = 2 )
	private BigDecimal loanBalance;

	private String county;

	private String district;

	private String address;

	public String getAddress()
	{
		return address;
	}

	public String getCounty()
	{
		return county;
	}

	public String getDistrict()
	{
		return district;
	}

	public BigDecimal getLoanBalance()
	{
		return loanBalance;
	}

	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public void setAddress( String address )
	{
		this.address = address;
	}

	public void setCounty( String county )
	{
		this.county = county;
	}

	public void setDistrict( String district )
	{
		this.district = district;
	}

	public void setLoanBalance( BigDecimal loanBalance )
	{
		this.loanBalance = loanBalance;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}
}
