package com.megabank.olp.api.controller.eloan.bean.signing;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class LoanConditionInfoBean extends BaseBean
{
	@NotNull
	private Integer loanAmt;

	@NotNull
	private Integer loanPeriod;

	@JsonProperty( "loanPurposeInfo" )
	@NotEmpty
	@Valid
	private List<LoanPurposeInfoBean> loanPurposeInfoBeans;

	@NotBlank
	private String drawDownType;

	@NotNull
	private Integer oneTimeFee;

	@NotNull
	private Integer preliminaryFee;

	@NotNull
	private Integer creditCheckFee;

	private Integer renewFee;

	private Integer changeFee;

	private Integer certFee;

	private Integer reissueFee;

	private String repaymentMethod;

	@NotBlank
	private String lendingPlan;

	@JsonProperty( "lendingPlanInfo" )
	@NotNull
	@Valid
	private LendingPlanInfoBean lendingPlanInfoBean;

	public LoanConditionInfoBean()
	{
	}

	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	public Integer getRenewFee()
	{
		return renewFee;
	}

	public void setRenewFee( Integer renewFee )
	{
		this.renewFee = renewFee;
	}

	public Integer getChangeFee()
	{
		return changeFee;
	}

	public void setChangeFee( Integer changeFee )
	{
		this.changeFee = changeFee;
	}

	public String getDrawDownType()
	{
		return drawDownType;
	}

	public String getLendingPlan()
	{
		return lendingPlan;
	}

	public LendingPlanInfoBean getLendingPlanInfoBean()
	{
		return lendingPlanInfoBean;
	}

	public Integer getLoanAmt()
	{
		return loanAmt;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public List<LoanPurposeInfoBean> getLoanPurposeInfoBeans()
	{
		return loanPurposeInfoBeans;
	}

	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public Integer getCertFee()
	{
		return certFee;
	}

	public void setCertFee( Integer certFee )
	{
		this.certFee = certFee;
	}

	public Integer getReissueFee()
	{
		return reissueFee;
	}

	public void setReissueFee( Integer reissueFee )
	{
		this.reissueFee = reissueFee;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public void setLendingPlan( String lendingPlan )
	{
		this.lendingPlan = lendingPlan;
	}

	public void setLendingPlanInfoBean( LendingPlanInfoBean lendingPlanInfoBean )
	{
		this.lendingPlanInfoBean = lendingPlanInfoBean;
	}

	public void setLoanAmt( Integer loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanPurposeInfoBeans( List<LoanPurposeInfoBean> loanPurposeInfoBeans )
	{
		this.loanPurposeInfoBeans = loanPurposeInfoBeans;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}

}
