package com.megabank.olp.apply.controller.management.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

import java.math.BigDecimal;

public class PayeeInfoCreateArgBean extends BaseBean
{
	private BigDecimal payeeInfoType;

	private String payeeInfoAccountType;

	public PayeeInfoCreateArgBean()
	{

	}

	public BigDecimal getPayeeInfoType()
	{
		return payeeInfoType;
	}

	public String getPayeeInfoAccountType()
	{
		return payeeInfoAccountType;
	}

	public void setPayeeInfoType( BigDecimal payeeInfoType )
	{
		this.payeeInfoType = payeeInfoType;
	}

	public void setPayeeInfoAccountType( String payeeInfoAccountType )
	{
		this.payeeInfoAccountType = payeeInfoAccountType;
	}
}
