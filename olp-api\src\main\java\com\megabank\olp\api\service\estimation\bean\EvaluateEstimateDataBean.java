/**
 *
 */
package com.megabank.olp.api.service.estimation.bean;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class EvaluateEstimateDataBean extends BaseBean
{
	/**
	 * 預估每坪單價(萬)
	 */
	private BigDecimal av750;

	public BigDecimal getAv750()
	{
		return av750;
	}

	public void setAv750( BigDecimal av750 )
	{
		this.av750 = av750;
	}

}
