package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanContactInfoUpdatedParamBean extends BaseBean
{
	private Long loanId;

	private Long homeAddressId;

	private Long mailingAddressId;

	private String residenceStatusCode;

	private String houseStatusCode;

	private String homePhoneCode;

	private String homePhoneNumber;

	private String email;

	private String mobileNumber;

	private Long branchBankId;

	private String serviceAssociateDeptCode;

	private String serviceAssociate;

	private Integer rent;

	public ApplyLoanContactInfoUpdatedParamBean()
	{
		// default constructor
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public String getEmail()
	{
		return email;
	}

	public Long getHomeAddressId()
	{
		return homeAddressId;
	}

	public String getHomePhoneCode()
	{
		return homePhoneCode;
	}

	public String getHomePhoneNumber()
	{
		return homePhoneNumber;
	}

	public String getHouseStatusCode()
	{
		return houseStatusCode;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public Long getMailingAddressId()
	{
		return mailingAddressId;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public Integer getRent()
	{
		return rent;
	}

	public String getResidenceStatusCode()
	{
		return residenceStatusCode;
	}

	public String getServiceAssociate()
	{
		return serviceAssociate;
	}

	public String getServiceAssociateDeptCode()
	{
		return serviceAssociateDeptCode;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setHomeAddressId( Long homeAddressId )
	{
		this.homeAddressId = homeAddressId;
	}

	public void setHomePhoneCode( String homePhoneCode )
	{
		this.homePhoneCode = homePhoneCode;
	}

	public void setHomePhoneNumber( String homePhoneNumber )
	{
		this.homePhoneNumber = homePhoneNumber;
	}

	public void setHouseStatusCode( String houseStatusCode )
	{
		this.houseStatusCode = houseStatusCode;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setMailingAddressId( Long mailingAddressId )
	{
		this.mailingAddressId = mailingAddressId;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setRent( Integer rent )
	{
		this.rent = rent;
	}

	public void setResidenceStatusCode( String residenceStatusCode )
	{
		this.residenceStatusCode = residenceStatusCode;
	}

	public void setServiceAssociate( String serviceAssociate )
	{
		this.serviceAssociate = serviceAssociate;
	}

	public void setServiceAssociateDeptCode( String serviceAssociateDeptCode )
	{
		this.serviceAssociateDeptCode = serviceAssociateDeptCode;
	}

}
