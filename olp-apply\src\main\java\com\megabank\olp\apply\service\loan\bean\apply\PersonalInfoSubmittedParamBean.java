package com.megabank.olp.apply.service.loan.bean.apply;

import com.megabank.olp.base.bean.BaseBean;

public class PersonalInfoSubmittedParamBean extends BaseBean
{
	private String loanType;

	private PersonalBasicParamBean personalBasicParamBean;

	private PersonalContactParamBean personalContactParamBean;

	private PersonalJobParamBean personalJobParamBean;

	private PersonalGuaranteeParamBean personalGuaranteeParamBean;

	public PersonalInfoSubmittedParamBean()
	{
		// default constructor
	}

	public String getLoanType()
	{
		return loanType;
	}

	public PersonalBasicParamBean getPersonalBasicParamBean()
	{
		return personalBasicParamBean;
	}

	public PersonalContactParamBean getPersonalContactParamBean()
	{
		return personalContactParamBean;
	}

	public PersonalGuaranteeParamBean getPersonalGuaranteeParamBean()
	{
		return personalGuaranteeParamBean;
	}

	public PersonalJobParamBean getPersonalJobParamBean()
	{
		return personalJobParamBean;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setPersonalBasicParamBean( PersonalBasicParamBean personalBasicParamBean )
	{
		this.personalBasicParamBean = personalBasicParamBean;
	}

	public void setPersonalContactParamBean( PersonalContactParamBean personalContactParamBean )
	{
		this.personalContactParamBean = personalContactParamBean;
	}

	public void setPersonalGuaranteeParamBean( PersonalGuaranteeParamBean personalGuaranteeParamBean )
	{
		this.personalGuaranteeParamBean = personalGuaranteeParamBean;
	}

	public void setPersonalJobParamBean( PersonalJobParamBean personalJobParamBean )
	{
		this.personalJobParamBean = personalJobParamBean;
	}

}
