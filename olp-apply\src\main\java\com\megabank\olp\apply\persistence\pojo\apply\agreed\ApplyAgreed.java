package com.megabank.olp.apply.persistence.pojo.apply.agreed;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.megabank.olp.apply.persistence.pojo.code.CodeContractType;
import com.megabank.olp.apply.persistence.pojo.code.CodeIdentityType;
import com.megabank.olp.apply.persistence.pojo.code.CodeServiceType;
import com.megabank.olp.apply.persistence.pojo.code.CodeUserType;
import com.megabank.olp.base.bean.BaseBean;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The ApplyAgreed is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_agreed" )
public class ApplyAgreed extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_agreed";

	public static final String AGREED_ID_CONSTANT = "agreedId";

	public static final String CODE_CONTRACT_TYPE_CONSTANT = "codeContractType";

	public static final String CODE_IDENTITY_TYPE_CONSTANT = "codeIdentityType";

	public static final String CODE_SERVICE_TYPE_CONSTANT = "codeServiceType";

	public static final String CODE_USER_TYPE_CONSTANT = "codeUserType";

	public static final String TITLE_CONSTANT = "title";

	public static final String CONTENT_CONSTANT = "content";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	public static final String UPDATED_BY_EMPOLYEE_ID_CONSTANT = "updatedByEmpolyeeId";

	public static final String UPDATED_BY_EMPLOYEE_NAME_CONSTANT = "updatedByEmployeeName";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String APPLY_AGREED_ITEMS_CONSTANT = "applyAgreedItems";

	private Long agreedId;

	private transient CodeContractType codeContractType;

	private transient CodeIdentityType codeIdentityType;

	private transient CodeServiceType codeServiceType;

	private transient CodeUserType codeUserType;

	private String agreedType;

	private String title;

	private String content;

	private int displayOrder;

	private String updatedByEmpolyeeId;

	private String updatedByEmployeeName;

	private Date updatedDate;

	private Date createdDate;

	private transient Set<ApplyAgreedItem> applyAgreedItems = new HashSet<>( 0 );

	public ApplyAgreed()
	{}

	public ApplyAgreed( CodeServiceType codeServiceType, String title, int displayOrder, String updatedByEmpolyeeId, String updatedByEmployeeName,
						Date updatedDate, Date createdDate )
	{
		this.codeServiceType = codeServiceType;
		this.title = title;
		this.displayOrder = displayOrder;
		this.updatedByEmpolyeeId = updatedByEmpolyeeId;
		this.updatedByEmployeeName = updatedByEmployeeName;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
	}

	public ApplyAgreed( Long agreedId )
	{
		this.agreedId = agreedId;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "agreed_id", unique = true, nullable = false )
	public Long getAgreedId()
	{
		return agreedId;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyAgreed" )
	public Set<ApplyAgreedItem> getApplyAgreedItems()
	{
		return applyAgreedItems;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "contract_type" )
	public CodeContractType getCodeContractType()
	{
		return codeContractType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "identity_type" )
	public CodeIdentityType getCodeIdentityType()
	{
		return codeIdentityType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "service_type", nullable = false )
	public CodeServiceType getCodeServiceType()
	{
		return codeServiceType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "user_type" )
	public CodeUserType getCodeUserType()
	{
		return codeUserType;
	}

	@Column( name = "content" )
	public String getContent()
	{
		return content;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "title", nullable = false )
	public String getTitle()
	{
		return title;
	}

	@Column( name = "agreed_type", length = 30 )
	public String getAgreedType()
	{
		return agreedType;
	}

	@Column( name = "updated_by_employee_name", nullable = false )
	public String getUpdatedByEmployeeName()
	{
		return updatedByEmployeeName;
	}

	@Column( name = "updated_by_empolyee_id", nullable = false, length = 30 )
	public String getUpdatedByEmpolyeeId()
	{
		return updatedByEmpolyeeId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	public void setAgreedId( Long agreedId )
	{
		this.agreedId = agreedId;
	}

	public void setApplyAgreedItems( Set<ApplyAgreedItem> applyAgreedItems )
	{
		this.applyAgreedItems = applyAgreedItems;
	}

	public void setCodeContractType( CodeContractType codeContractType )
	{
		this.codeContractType = codeContractType;
	}

	public void setCodeIdentityType( CodeIdentityType codeIdentityType )
	{
		this.codeIdentityType = codeIdentityType;
	}

	public void setCodeServiceType( CodeServiceType codeServiceType )
	{
		this.codeServiceType = codeServiceType;
	}

	public void setCodeUserType( CodeUserType codeUserType )
	{
		this.codeUserType = codeUserType;
	}

	public void setContent( String content )
	{
		this.content = content;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setTitle( String title )
	{
		this.title = title;
	}

	public void setUpdatedByEmployeeName( String updatedByEmployeeName )
	{
		this.updatedByEmployeeName = updatedByEmployeeName;
	}

	public void setUpdatedByEmpolyeeId( String updatedByEmpolyeeId )
	{
		this.updatedByEmpolyeeId = updatedByEmpolyeeId;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

	public void setAgreedType( String agreedType )
	{
		this.agreedType = agreedType;
	}
}