package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeTitleType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeTitleTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeTitleTypeDAO extends BasePojoDAO<CodeTitleType, String>
{
	public List<CodeTitleType> getList()
	{
		return getAllPojos();
	}

	public CodeTitleType read( String titleType )
	{
		Validate.notBlank( titleType );

		return getPojoByPK( titleType, CodeTitleType.TABLENAME_CONSTANT );
	}

	public CodeTitleType readToNull( String titleType )
	{
		Validate.notBlank( titleType );

		return getPojoByPK( titleType );
	}

	@Override
	protected Class<CodeTitleType> getPojoClass()
	{
		return CodeTitleType.class;
	}
}
