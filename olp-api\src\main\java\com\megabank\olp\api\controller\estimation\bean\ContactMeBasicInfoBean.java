/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class ContactMeBasicInfoBean extends BaseBean
{
	@NotBlank
	private String cName;

	@NotBlank
	private String callBackTime;

	public ContactMeBasicInfoBean()
	{}

	public String getCallBackTime()
	{
		return callBackTime;
	}

	public String getcName()
	{
		return cName;
	}

	public void setCallBackTime( String callBackTime )
	{
		this.callBackTime = callBackTime;
	}

	public void setcName( String cName )
	{
		this.cName = cName;
	}

}
