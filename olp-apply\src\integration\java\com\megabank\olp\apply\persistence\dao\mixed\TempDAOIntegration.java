package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.dto.UploadFileDTO;
import com.megabank.olp.base.config.BasePersistenceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class TempDAOIntegration
{
	@Autowired
	private TempDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getUploadFiles()
	{
		Long loanId = 3L;

		List<UploadFileDTO> dtos = dao.getUploadFiles( loanId );

		logger.info( "dtos:{}", dtos );
	}

}
