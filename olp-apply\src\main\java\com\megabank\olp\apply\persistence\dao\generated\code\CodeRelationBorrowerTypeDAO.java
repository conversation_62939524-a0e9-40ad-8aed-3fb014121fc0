package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeRelationBorrowerType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeRelationBorrowerTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeRelationBorrowerTypeDAO extends BasePojoDAO<CodeRelationBorrowerType, String>
{
	public CodeRelationBorrowerType read( String relationBorrowerType )
	{
		Validate.notBlank( relationBorrowerType );

		return getPojoByPK( relationBorrowerType, CodeRelationBorrowerType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeRelationBorrowerType> getPojoClass()
	{
		return CodeRelationBorrowerType.class;
	}
}
