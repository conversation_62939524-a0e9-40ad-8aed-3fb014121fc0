/**
 *
 */
package com.megabank.olp.api.controller.open.bean;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class ServletNotifiedArgBean extends BaseBean
{
	@NotBlank
	private String txId;

	@JsonProperty( "userId" )
	@NotBlank
	private String idNo;

	@NotNull
	private Integer waitSec;

	public ServletNotifiedArgBean()
	{
		// default constructor
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getTxId()
	{
		return txId;
	}

	public Integer getWaitSec()
	{
		return waitSec;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setTxId( String txId )
	{
		this.txId = txId;
	}

	public void setWaitSec( Integer waitSec )
	{
		this.waitSec = waitSec;
	}

}
