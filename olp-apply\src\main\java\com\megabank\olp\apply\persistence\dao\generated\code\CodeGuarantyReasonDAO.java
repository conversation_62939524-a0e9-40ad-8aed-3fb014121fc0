package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeGuarantyReason;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeGuarantyReasonDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeGuarantyReasonDAO extends BasePojoDAO<CodeGuarantyReason, String>
{
	public CodeGuarantyReason read( String guarantyReasonCode )
	{
		Validate.notBlank( guarantyReasonCode );

		return getPojoByPK( guarantyReasonCode, CodeGuarantyReason.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeGuarantyReason> getPojoClass()
	{
		return CodeGuarantyReason.class;
	}
}
