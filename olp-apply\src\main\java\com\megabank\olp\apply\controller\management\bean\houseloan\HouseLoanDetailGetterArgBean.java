package com.megabank.olp.apply.controller.management.bean.houseloan;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class HouseLoanDetailGetterArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long loanId;

	public HouseLoanDetailGetterArgBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return loanId
	 */
	public Long getLoanId()
	{
		return loanId;
	}

	/**
	 *
	 * @param loanId
	 */
	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}
}
