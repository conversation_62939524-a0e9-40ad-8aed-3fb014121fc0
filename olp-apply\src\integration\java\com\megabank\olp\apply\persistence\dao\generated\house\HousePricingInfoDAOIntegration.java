package com.megabank.olp.apply.persistence.dao.generated.house;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.house.ContactLoanCreatedParamBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HousePricingInfoDAOIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );
	
	@Autowired
	private HousePricingInfoDAO dao;

	@Test
	public void updateNotified()
	{
		List<Long> l = new ArrayList<>();

		int result = dao.updateNotified( l );

		logger.info( "result:{}", result );
	}
}
