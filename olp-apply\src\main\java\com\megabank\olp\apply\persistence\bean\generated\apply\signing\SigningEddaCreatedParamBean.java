/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningEddaCreatedParamBean extends BaseBean
{
	private Long signingContractId;

	public SigningEddaCreatedParamBean()
	{}

	private String adMark;

	private String aID;

	private String cardNum;

	private String certType;

	private String chipCardRemarks;

	private String cur;

	private String dn;

	private String encData;

	private String encSeq;

	private String expiryDate;

	private String limitDamt;

	private String limitMamt;

	private String limitSamt;

	private String megaMessageId;

	private String megaSystemId;

	private String note;

	private String pBank;

	private String pBankNote;

	private String rBank;

	private String rclNo;

	private String reservedField;

	private String rID;

	private String superMarket;

	private String taskId;

	private String tix;

	private String unpredictableNum;

	private String userNo;

	private String contractNo;

	public String getAdMark()
	{
		return adMark;
	}

	public String getaID()
	{
		return aID;
	}

	public String getCardNum()
	{
		return cardNum;
	}

	public String getCertType()
	{
		return certType;
	}

	public String getChipCardRemarks()
	{
		return chipCardRemarks;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getCur()
	{
		return cur;
	}

	public String getDn()
	{
		return dn;
	}

	public String getEncData()
	{
		return encData;
	}

	public String getEncSeq()
	{
		return encSeq;
	}

	public String getExpiryDate()
	{
		return expiryDate;
	}

	public String getLimitDamt()
	{
		return limitDamt;
	}

	public String getLimitMamt()
	{
		return limitMamt;
	}

	public String getLimitSamt()
	{
		return limitSamt;
	}

	public String getMegaMessageId()
	{
		return megaMessageId;
	}

	public String getMegaSystemId()
	{
		return megaSystemId;
	}

	public String getNote()
	{
		return note;
	}

	public String getpBank()
	{
		return pBank;
	}

	public String getpBankNote()
	{
		return pBankNote;
	}

	public String getrBank()
	{
		return rBank;
	}

	public String getRclNo()
	{
		return rclNo;
	}

	public String getReservedField()
	{
		return reservedField;
	}

	public String getrID()
	{
		return rID;
	}

	public Long getSigningContractId()
	{
		return signingContractId;
	}

	public String getSuperMarket()
	{
		return superMarket;
	}

	public String getTaskId()
	{
		return taskId;
	}

	public String getTix()
	{
		return tix;
	}

	public String getUnpredictableNum()
	{
		return unpredictableNum;
	}

	public String getUserNo()
	{
		return userNo;
	}

	public void setAdMark( String adMark )
	{
		this.adMark = adMark;
	}

	public void setaID( String aID )
	{
		this.aID = aID;
	}

	public void setCardNum( String cardNum )
	{
		this.cardNum = cardNum;
	}

	public void setCertType( String certType )
	{
		this.certType = certType;
	}

	public void setChipCardRemarks( String chipCardRemarks )
	{
		this.chipCardRemarks = chipCardRemarks;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setCur( String cur )
	{
		this.cur = cur;
	}

	public void setDn( String dn )
	{
		this.dn = dn;
	}

	public void setEncData( String encData )
	{
		this.encData = encData;
	}

	public void setEncSeq( String encSeq )
	{
		this.encSeq = encSeq;
	}

	public void setExpiryDate( String expiryDate )
	{
		this.expiryDate = expiryDate;
	}

	public void setLimitDamt( String limitDamt )
	{
		this.limitDamt = limitDamt;
	}

	public void setLimitMamt( String limitMamt )
	{
		this.limitMamt = limitMamt;
	}

	public void setLimitSamt( String limitSamt )
	{
		this.limitSamt = limitSamt;
	}

	public void setMegaMessageId( String megaMessageId )
	{
		this.megaMessageId = megaMessageId;
	}

	public void setMegaSystemId( String megaSystemId )
	{
		this.megaSystemId = megaSystemId;
	}

	public void setNote( String note )
	{
		this.note = note;
	}

	public void setpBank( String pBank )
	{
		this.pBank = pBank;
	}

	public void setpBankNote( String pBankNote )
	{
		this.pBankNote = pBankNote;
	}

	public void setrBank( String rBank )
	{
		this.rBank = rBank;
	}

	public void setRclNo( String rclNo )
	{
		this.rclNo = rclNo;
	}

	public void setReservedField( String reservedField )
	{
		this.reservedField = reservedField;
	}

	public void setrID( String rID )
	{
		this.rID = rID;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setSuperMarket( String superMarket )
	{
		this.superMarket = superMarket;
	}

	public void setTaskId( String taskId )
	{
		this.taskId = taskId;
	}

	public void setTix( String tix )
	{
		this.tix = tix;
	}

	public void setUnpredictableNum( String unpredictableNum )
	{
		this.unpredictableNum = unpredictableNum;
	}

	public void setUserNo( String userNo )
	{
		this.userNo = userNo;
	}

}
