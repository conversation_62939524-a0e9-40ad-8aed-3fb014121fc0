package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeLoanType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeLoanTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeLoanTypeDAO extends BasePojoDAO<CodeLoanType, String>
{

	public CodeLoanType read( String loanType )
	{
		Validate.notBlank( loanType );

		return getPojoByPK( loanType, CodeLoanType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeLoanType> getPojoClass()
	{
		return CodeLoanType.class;
	}
}
