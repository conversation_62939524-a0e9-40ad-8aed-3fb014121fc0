package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import java.math.BigDecimal;

public class ApplyCollateralContractCreatedParamBean
{
	private String consentVer;

	private String collateralBuildingAddr1;

	private String collateralBuildingAddr2;

	private Integer mortgageMaxAmt1;

	private Integer mortgageMaxAmt2;

	private String firstLoanDateYear;

	private String firstLoanDateMth;

	private String firstLoanDateDay;

	private Integer firstLoanAmt1;

	private Integer firstLoanAmt2;

	private String collateralContractTerms;

	private String unregisteredBuildingDesc;

	private String houseLoanContractNo;

	private String coTarget;

	private String cbAfftTerms;

	private String cbAfftVersion;

	// cbAfft1Content
	private String cbAfft1_1;

	private String cbAfft1_2;

	private String cbAfft1_3_year;

	private String cbAfft1_3_mth;

	private String cbAfft1_3_day;

	private Integer cbAfft1_4;

	private String cbAfft1_5;

	private String cbAfft1_6;

	private BigDecimal cbAfft1_7;

	private Integer cbAfft1_8;

	private String cbAfft1_9_year;

	private String cbAfft1_9_mth;

	private String cbAfft1_9_day;

	private String cbAfft1_10_year;

	private String cbAfft1_10_mth;

	private String cbAfft1_10_day;

	private String cbAfft1_10_no;

	private Integer cbAfft1_11;

	private Integer cbAfft1_12;

	private Integer cbAfft1_13;

	private Integer cbAfft1_14;

	private Integer cbAfft1_15;

	private String cbAfft1_16;

	// cbAfft2Content
	private String cbAfft2_1;

	private String cbAfft2_2;

	private String cbAfft2_3_year;

	private String cbAfft2_3_mth;

	private String cbAfft2_3_day;

	private Integer cbAfft2_4;

	private Integer cbAfft2_5;

	private Integer cbAfft2_6;

	private Integer cbAfft2_7;

	private Integer cbAfft2_8;

	private Integer cbAfft2_9;

	private BigDecimal cbAfft2_10;

	// cbAfft3Content
	private String cbAfft3_1;

	private String cbAfft3_2;

	private String cbAfft3_3_year;

	private String cbAfft3_3_mth;

	private String cbAfft3_3_day;

	private Integer cbAfft3_4;

	private BigDecimal cbAfft3_5;

	// cbAfft4Content
	private String cbAfft4_1;

	private String cbAfft4_2_year;

	private String cbAfft4_2_mth;

	private String cbAfft4_2_day;

	private Integer cbAfft4_3;

	private String cbAfft4_4;

	private BigDecimal cbAfft4_5;

	private BigDecimal cbAfft4_6;

	private BigDecimal cbAfft4_7;

	// cbAfft5Content
	private String cbAfft5_1;

	private String cbAfft5_2;

	private String cbAfft5_3_year;

	private String cbAfft5_3_mth;

	private String cbAfft5_3_day;

	private Integer cbAfft5_4;

	private String cbAfft5_5;

	private BigDecimal cbAfft5_6;

	private Integer cbAfft5_7;

	private Integer cbAfft5_8;

	private Integer cbAfft5_9;

	private Integer cbAfft5_10;

	private Integer cbAfft5_11;

	private String cbAfft5_12;

	private BigDecimal cbAfft5_13;

	private BigDecimal cbAfft5_14;

	private BigDecimal cbAfft5_15;

	public ApplyCollateralContractCreatedParamBean()
	{}

	public String getCbAfft1_1()
	{
		return cbAfft1_1;
	}

	public String getCbAfft1_10_day()
	{
		return cbAfft1_10_day;
	}

	public String getCbAfft1_10_mth()
	{
		return cbAfft1_10_mth;
	}

	public String getCbAfft1_10_no()
	{
		return cbAfft1_10_no;
	}

	public String getCbAfft1_10_year()
	{
		return cbAfft1_10_year;
	}

	public Integer getCbAfft1_11()
	{
		return cbAfft1_11;
	}

	public Integer getCbAfft1_12()
	{
		return cbAfft1_12;
	}

	public Integer getCbAfft1_13()
	{
		return cbAfft1_13;
	}

	public Integer getCbAfft1_14()
	{
		return cbAfft1_14;
	}

	public Integer getCbAfft1_15()
	{
		return cbAfft1_15;
	}

	public String getCbAfft1_16()
	{
		return cbAfft1_16;
	}

	public String getCbAfft1_2()
	{
		return cbAfft1_2;
	}

	public String getCbAfft1_3_day()
	{
		return cbAfft1_3_day;
	}

	public String getCbAfft1_3_mth()
	{
		return cbAfft1_3_mth;
	}

	public String getCbAfft1_3_year()
	{
		return cbAfft1_3_year;
	}

	public Integer getCbAfft1_4()
	{
		return cbAfft1_4;
	}

	public String getCbAfft1_5()
	{
		return cbAfft1_5;
	}

	public String getCbAfft1_6()
	{
		return cbAfft1_6;
	}

	public BigDecimal getCbAfft1_7()
	{
		return cbAfft1_7;
	}

	public Integer getCbAfft1_8()
	{
		return cbAfft1_8;
	}

	public String getCbAfft1_9_day()
	{
		return cbAfft1_9_day;
	}

	public String getCbAfft1_9_mth()
	{
		return cbAfft1_9_mth;
	}

	public String getCbAfft1_9_year()
	{
		return cbAfft1_9_year;
	}

	public String getCbAfft2_1()
	{
		return cbAfft2_1;
	}

	public BigDecimal getCbAfft2_10()
	{
		return cbAfft2_10;
	}

	public String getCbAfft2_2()
	{
		return cbAfft2_2;
	}

	public String getCbAfft2_3_day()
	{
		return cbAfft2_3_day;
	}

	public String getCbAfft2_3_mth()
	{
		return cbAfft2_3_mth;
	}

	public String getCbAfft2_3_year()
	{
		return cbAfft2_3_year;
	}

	public Integer getCbAfft2_4()
	{
		return cbAfft2_4;
	}

	public Integer getCbAfft2_5()
	{
		return cbAfft2_5;
	}

	public Integer getCbAfft2_6()
	{
		return cbAfft2_6;
	}

	public Integer getCbAfft2_7()
	{
		return cbAfft2_7;
	}

	public Integer getCbAfft2_8()
	{
		return cbAfft2_8;
	}

	public Integer getCbAfft2_9()
	{
		return cbAfft2_9;
	}

	public String getCbAfft3_1()
	{
		return cbAfft3_1;
	}

	public String getCbAfft3_2()
	{
		return cbAfft3_2;
	}

	public String getCbAfft3_3_day()
	{
		return cbAfft3_3_day;
	}

	public String getCbAfft3_3_mth()
	{
		return cbAfft3_3_mth;
	}

	public String getCbAfft3_3_year()
	{
		return cbAfft3_3_year;
	}

	public Integer getCbAfft3_4()
	{
		return cbAfft3_4;
	}

	public BigDecimal getCbAfft3_5()
	{
		return cbAfft3_5;
	}

	public String getCbAfft4_1()
	{
		return cbAfft4_1;
	}

	public String getCbAfft4_2_day()
	{
		return cbAfft4_2_day;
	}

	public String getCbAfft4_2_mth()
	{
		return cbAfft4_2_mth;
	}

	public String getCbAfft4_2_year()
	{
		return cbAfft4_2_year;
	}

	public Integer getCbAfft4_3()
	{
		return cbAfft4_3;
	}

	public String getCbAfft4_4()
	{
		return cbAfft4_4;
	}

	public BigDecimal getCbAfft4_5()
	{
		return cbAfft4_5;
	}

	public BigDecimal getCbAfft4_6()
	{
		return cbAfft4_6;
	}

	public BigDecimal getCbAfft4_7()
	{
		return cbAfft4_7;
	}

	public String getCbAfft5_1()
	{
		return cbAfft5_1;
	}

	public Integer getCbAfft5_10()
	{
		return cbAfft5_10;
	}

	public Integer getCbAfft5_11()
	{
		return cbAfft5_11;
	}

	public String getCbAfft5_12()
	{
		return cbAfft5_12;
	}

	public BigDecimal getCbAfft5_13()
	{
		return cbAfft5_13;
	}

	public BigDecimal getCbAfft5_14()
	{
		return cbAfft5_14;
	}

	public BigDecimal getCbAfft5_15()
	{
		return cbAfft5_15;
	}

	public String getCbAfft5_2()
	{
		return cbAfft5_2;
	}

	public String getCbAfft5_3_day()
	{
		return cbAfft5_3_day;
	}

	public String getCbAfft5_3_mth()
	{
		return cbAfft5_3_mth;
	}

	public String getCbAfft5_3_year()
	{
		return cbAfft5_3_year;
	}

	public Integer getCbAfft5_4()
	{
		return cbAfft5_4;
	}

	public String getCbAfft5_5()
	{
		return cbAfft5_5;
	}

	public BigDecimal getCbAfft5_6()
	{
		return cbAfft5_6;
	}

	public Integer getCbAfft5_7()
	{
		return cbAfft5_7;
	}

	public Integer getCbAfft5_8()
	{
		return cbAfft5_8;
	}

	public Integer getCbAfft5_9()
	{
		return cbAfft5_9;
	}

	public String getCbAfftTerms()
	{
		return cbAfftTerms;
	}

	public String getCbAfftVersion()
	{
		return cbAfftVersion;
	}

	public String getCollateralBuildingAddr1()
	{
		return collateralBuildingAddr1;
	}

	public String getCollateralBuildingAddr2()
	{
		return collateralBuildingAddr2;
	}

	public String getCollateralContractTerms()
	{
		return collateralContractTerms;
	}

	public String getConsentVer()
	{
		return consentVer;
	}

	public String getCoTarget()
	{
		return coTarget;
	}

	public Integer getFirstLoanAmt1()
	{
		return firstLoanAmt1;
	}

	public Integer getFirstLoanAmt2()
	{
		return firstLoanAmt2;
	}

	public String getFirstLoanDateDay()
	{
		return firstLoanDateDay;
	}

	public String getFirstLoanDateMth()
	{
		return firstLoanDateMth;
	}

	public String getFirstLoanDateYear()
	{
		return firstLoanDateYear;
	}

	public String getHouseLoanContractNo()
	{
		return houseLoanContractNo;
	}

	public Integer getMortgageMaxAmt1()
	{
		return mortgageMaxAmt1;
	}

	public Integer getMortgageMaxAmt2()
	{
		return mortgageMaxAmt2;
	}

	public String getUnregisteredBuildingDesc()
	{
		return unregisteredBuildingDesc;
	}

	public void setCbAfft1_1( String cbAfft1_1 )
	{
		this.cbAfft1_1 = cbAfft1_1;
	}

	public void setCbAfft1_10_day( String cbAfft1_10_day )
	{
		this.cbAfft1_10_day = cbAfft1_10_day;
	}

	public void setCbAfft1_10_mth( String cbAfft1_10_mth )
	{
		this.cbAfft1_10_mth = cbAfft1_10_mth;
	}

	public void setCbAfft1_10_no( String cbAfft1_10_no )
	{
		this.cbAfft1_10_no = cbAfft1_10_no;
	}

	public void setCbAfft1_10_year( String cbAfft1_10_year )
	{
		this.cbAfft1_10_year = cbAfft1_10_year;
	}

	public void setCbAfft1_11( Integer cbAfft1_11 )
	{
		this.cbAfft1_11 = cbAfft1_11;
	}

	public void setCbAfft1_12( Integer cbAfft1_12 )
	{
		this.cbAfft1_12 = cbAfft1_12;
	}

	public void setCbAfft1_13( Integer cbAfft1_13 )
	{
		this.cbAfft1_13 = cbAfft1_13;
	}

	public void setCbAfft1_14( Integer cbAfft1_14 )
	{
		this.cbAfft1_14 = cbAfft1_14;
	}

	public void setCbAfft1_15( Integer cbAfft1_15 )
	{
		this.cbAfft1_15 = cbAfft1_15;
	}

	public void setCbAfft1_16( String cbAfft1_16 )
	{
		this.cbAfft1_16 = cbAfft1_16;
	}

	public void setCbAfft1_2( String cbAfft1_2 )
	{
		this.cbAfft1_2 = cbAfft1_2;
	}

	public void setCbAfft1_3_day( String cbAfft1_3_day )
	{
		this.cbAfft1_3_day = cbAfft1_3_day;
	}

	public void setCbAfft1_3_mth( String cbAfft1_3_mth )
	{
		this.cbAfft1_3_mth = cbAfft1_3_mth;
	}

	public void setCbAfft1_3_year( String cbAfft1_3_year )
	{
		this.cbAfft1_3_year = cbAfft1_3_year;
	}

	public void setCbAfft1_4( Integer cbAfft1_4 )
	{
		this.cbAfft1_4 = cbAfft1_4;
	}

	public void setCbAfft1_5( String cbAfft1_5 )
	{
		this.cbAfft1_5 = cbAfft1_5;
	}

	public void setCbAfft1_6( String cbAfft1_6 )
	{
		this.cbAfft1_6 = cbAfft1_6;
	}

	public void setCbAfft1_7( BigDecimal cbAfft1_7 )
	{
		this.cbAfft1_7 = cbAfft1_7;
	}

	public void setCbAfft1_8( Integer cbAfft1_8 )
	{
		this.cbAfft1_8 = cbAfft1_8;
	}

	public void setCbAfft1_9_day( String cbAfft1_9_day )
	{
		this.cbAfft1_9_day = cbAfft1_9_day;
	}

	public void setCbAfft1_9_mth( String cbAfft1_9_mth )
	{
		this.cbAfft1_9_mth = cbAfft1_9_mth;
	}

	public void setCbAfft1_9_year( String cbAfft1_9_year )
	{
		this.cbAfft1_9_year = cbAfft1_9_year;
	}

	public void setCbAfft2_1( String cbAfft2_1 )
	{
		this.cbAfft2_1 = cbAfft2_1;
	}

	public void setCbAfft2_10( BigDecimal cbAfft2_10 )
	{
		this.cbAfft2_10 = cbAfft2_10;
	}

	public void setCbAfft2_2( String cbAfft2_2 )
	{
		this.cbAfft2_2 = cbAfft2_2;
	}

	public void setCbAfft2_3_day( String cbAfft2_3_day )
	{
		this.cbAfft2_3_day = cbAfft2_3_day;
	}

	public void setCbAfft2_3_mth( String cbAfft2_3_mth )
	{
		this.cbAfft2_3_mth = cbAfft2_3_mth;
	}

	public void setCbAfft2_3_year( String cbAfft2_3_year )
	{
		this.cbAfft2_3_year = cbAfft2_3_year;
	}

	public void setCbAfft2_4( Integer cbAfft2_4 )
	{
		this.cbAfft2_4 = cbAfft2_4;
	}

	public void setCbAfft2_5( Integer cbAfft2_5 )
	{
		this.cbAfft2_5 = cbAfft2_5;
	}

	public void setCbAfft2_6( Integer cbAfft2_6 )
	{
		this.cbAfft2_6 = cbAfft2_6;
	}

	public void setCbAfft2_7( Integer cbAfft2_7 )
	{
		this.cbAfft2_7 = cbAfft2_7;
	}

	public void setCbAfft2_8( Integer cbAfft2_8 )
	{
		this.cbAfft2_8 = cbAfft2_8;
	}

	public void setCbAfft2_9( Integer cbAfft2_9 )
	{
		this.cbAfft2_9 = cbAfft2_9;
	}

	public void setCbAfft3_1( String cbAfft3_1 )
	{
		this.cbAfft3_1 = cbAfft3_1;
	}

	public void setCbAfft3_2( String cbAfft3_2 )
	{
		this.cbAfft3_2 = cbAfft3_2;
	}

	public void setCbAfft3_3_day( String cbAfft3_3_day )
	{
		this.cbAfft3_3_day = cbAfft3_3_day;
	}

	public void setCbAfft3_3_mth( String cbAfft3_3_mth )
	{
		this.cbAfft3_3_mth = cbAfft3_3_mth;
	}

	public void setCbAfft3_3_year( String cbAfft3_3_year )
	{
		this.cbAfft3_3_year = cbAfft3_3_year;
	}

	public void setCbAfft3_4( Integer cbAfft3_4 )
	{
		this.cbAfft3_4 = cbAfft3_4;
	}

	public void setCbAfft3_5( BigDecimal cbAfft3_5 )
	{
		this.cbAfft3_5 = cbAfft3_5;
	}

	public void setCbAfft4_1( String cbAfft4_1 )
	{
		this.cbAfft4_1 = cbAfft4_1;
	}

	public void setCbAfft4_2_day( String cbAfft4_2_day )
	{
		this.cbAfft4_2_day = cbAfft4_2_day;
	}

	public void setCbAfft4_2_mth( String cbAfft4_2_mth )
	{
		this.cbAfft4_2_mth = cbAfft4_2_mth;
	}

	public void setCbAfft4_2_year( String cbAfft4_2_year )
	{
		this.cbAfft4_2_year = cbAfft4_2_year;
	}

	public void setCbAfft4_3( Integer cbAfft4_3 )
	{
		this.cbAfft4_3 = cbAfft4_3;
	}

	public void setCbAfft4_4( String cbAfft4_4 )
	{
		this.cbAfft4_4 = cbAfft4_4;
	}

	public void setCbAfft4_5( BigDecimal cbAfft4_5 )
	{
		this.cbAfft4_5 = cbAfft4_5;
	}

	public void setCbAfft4_6( BigDecimal cbAfft4_6 )
	{
		this.cbAfft4_6 = cbAfft4_6;
	}

	public void setCbAfft4_7( BigDecimal cbAfft4_7 )
	{
		this.cbAfft4_7 = cbAfft4_7;
	}

	public void setCbAfft5_1( String cbAfft5_1 )
	{
		this.cbAfft5_1 = cbAfft5_1;
	}

	public void setCbAfft5_10( Integer cbAfft5_10 )
	{
		this.cbAfft5_10 = cbAfft5_10;
	}

	public void setCbAfft5_11( Integer cbAfft5_11 )
	{
		this.cbAfft5_11 = cbAfft5_11;
	}

	public void setCbAfft5_12( String cbAfft5_12 )
	{
		this.cbAfft5_12 = cbAfft5_12;
	}

	public void setCbAfft5_13( BigDecimal cbAfft5_13 )
	{
		this.cbAfft5_13 = cbAfft5_13;
	}

	public void setCbAfft5_14( BigDecimal cbAfft5_14 )
	{
		this.cbAfft5_14 = cbAfft5_14;
	}

	public void setCbAfft5_15( BigDecimal cbAfft5_15 )
	{
		this.cbAfft5_15 = cbAfft5_15;
	}

	public void setCbAfft5_2( String cbAfft5_2 )
	{
		this.cbAfft5_2 = cbAfft5_2;
	}

	public void setCbAfft5_3_day( String cbAfft5_3_day )
	{
		this.cbAfft5_3_day = cbAfft5_3_day;
	}

	public void setCbAfft5_3_mth( String cbAfft5_3_mth )
	{
		this.cbAfft5_3_mth = cbAfft5_3_mth;
	}

	public void setCbAfft5_3_year( String cbAfft5_3_year )
	{
		this.cbAfft5_3_year = cbAfft5_3_year;
	}

	public void setCbAfft5_4( Integer cbAfft5_4 )
	{
		this.cbAfft5_4 = cbAfft5_4;
	}

	public void setCbAfft5_5( String cbAfft5_5 )
	{
		this.cbAfft5_5 = cbAfft5_5;
	}

	public void setCbAfft5_6( BigDecimal cbAfft5_6 )
	{
		this.cbAfft5_6 = cbAfft5_6;
	}

	public void setCbAfft5_7( Integer cbAfft5_7 )
	{
		this.cbAfft5_7 = cbAfft5_7;
	}

	public void setCbAfft5_8( Integer cbAfft5_8 )
	{
		this.cbAfft5_8 = cbAfft5_8;
	}

	public void setCbAfft5_9( Integer cbAfft5_9 )
	{
		this.cbAfft5_9 = cbAfft5_9;
	}

	public void setCbAfftTerms( String cbAfftTerms )
	{
		this.cbAfftTerms = cbAfftTerms;
	}

	public void setCbAfftVersion( String cbAfftVersion )
	{
		this.cbAfftVersion = cbAfftVersion;
	}

	public void setCollateralBuildingAddr1( String collateralBuildingAddr1 )
	{
		this.collateralBuildingAddr1 = collateralBuildingAddr1;
	}

	public void setCollateralBuildingAddr2( String collateralBuildingAddr2 )
	{
		this.collateralBuildingAddr2 = collateralBuildingAddr2;
	}

	public void setCollateralContractTerms( String collateralContractTerms )
	{
		this.collateralContractTerms = collateralContractTerms;
	}

	public void setConsentVer( String consentVer )
	{
		this.consentVer = consentVer;
	}

	public void setCoTarget( String coTarget )
	{
		this.coTarget = coTarget;
	}

	public void setFirstLoanAmt1( Integer firstLoanAmt1 )
	{
		this.firstLoanAmt1 = firstLoanAmt1;
	}

	public void setFirstLoanAmt2( Integer firstLoanAmt2 )
	{
		this.firstLoanAmt2 = firstLoanAmt2;
	}

	public void setFirstLoanDateDay( String firstLoanDateDay )
	{
		this.firstLoanDateDay = firstLoanDateDay;
	}

	public void setFirstLoanDateMth( String firstLoanDateMth )
	{
		this.firstLoanDateMth = firstLoanDateMth;
	}

	public void setFirstLoanDateYear( String firstLoanDateYear )
	{
		this.firstLoanDateYear = firstLoanDateYear;
	}

	public void setHouseLoanContractNo( String houseLoanContractNo )
	{
		this.houseLoanContractNo = houseLoanContractNo;
	}

	public void setMortgageMaxAmt1( Integer mortgageMaxAmt1 )
	{
		this.mortgageMaxAmt1 = mortgageMaxAmt1;
	}

	public void setMortgageMaxAmt2( Integer mortgageMaxAmt2 )
	{
		this.mortgageMaxAmt2 = mortgageMaxAmt2;
	}

	public void setUnregisteredBuildingDesc( String unregisteredBuildingDesc )
	{
		this.unregisteredBuildingDesc = unregisteredBuildingDesc;
	}
}
