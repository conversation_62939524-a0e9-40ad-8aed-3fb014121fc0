package com.megabank.olp.apply.service.loan;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.megabank.olp.apply.controller.loan.bean.basic.LoanApplyBasicBean;
import com.megabank.olp.apply.controller.loan.bean.contact.LoanApplyContactBean;
import com.megabank.olp.apply.controller.loan.bean.content.LoanApplyContentBean;
import com.megabank.olp.apply.controller.loan.bean.submit.LoanApplySubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.create.CreateResBean;
import com.megabank.olp.base.enums.LoanPlanEnum;
import com.megabank.olp.base.utility.CommonDateUtils;

/**
 * C001 中鋼總公司消費性貸款測試案例
 */
public class C001ChinaSteelApplyServiceTest extends BaseApplyServiceTest
{
    @Autowired
    private ApplyService applyService;

    /**
     * 測試C001中鋼總公司完整申請流程
     */
    @Test
    public void testC001FullApplyProcess()
    {
        // Step 1: 創建申請案件
        CreateResBean createResult = createC001Application();
        Long loanId = createResult.getLoanId();
        
        System.out.println("申請案件已創建，loanId: " + loanId);
        
        // Step 2: 確認同意事項
        Long agreedLoanId = confirmAgreement();
        
        System.out.println("同意事項已確認，loanId: " + agreedLoanId);
        
        // Step 3: 提交申請資料
        Long submittedLoanId = submitLoanApplication();
        
        System.out.println("申請資料已提交，loanId: " + submittedLoanId);
        
        // Step 4: 送出申請
        Long finalLoanId = deliverApplication();
        
        System.out.println("申請已送出，最終loanId: " + finalLoanId);
    }

    /**
     * 創建C001申請案件
     */
    private CreateResBean createC001Application()
    {
        String loanType = "personalloan";
        String refCaseNo = "";  // 主借人，非保證人
        String plan = LoanPlanEnum.C001.getContext();  // C001
        String introduceBrNo = "002";  // 中鋼專案分行
        
        return applyService.createLoanApply(loanType, refCaseNo, plan, introduceBrNo);
    }

    /**
     * 確認同意事項
     */
    private Long confirmAgreement()
    {
        String loanType = "personalloan";
        List<Long> itemIds = Arrays.asList(1L, 2L, 3L, 5L);  // 必要同意事項
        Boolean notUsTaxpayer = true;      // 非美國納稅人
        Boolean notOuttwTaxpayer = true;   // 非境外納稅人
        String rateAdjNotify = "Y";        // 利率調整通知
        Boolean crossMarketing = false;    // 不同意交叉行銷
        
        return applyService.confirmAgreement(loanType, itemIds, notUsTaxpayer, 
                                           notOuttwTaxpayer, rateAdjNotify, crossMarketing);
    }

    /**
     * 提交申請資料
     */
    private Long submitLoanApplication()
    {
        LoanApplySubmittedParamBean paramBean = new LoanApplySubmittedParamBean();
        paramBean.setLoanType("personalloan");
        
        // 基本資料
        LoanApplyBasicBean basicBean = new LoanApplyBasicBean();
        basicBean.setName("王大明");
        basicBean.setIdNo("A123456789");
        basicBean.setBirthDate(CommonDateUtils.getDate(1985, 5, 15));
        basicBean.setChildrenCount(0);  // 中鋼總公司不填子女數
        basicBean.setNotUsTaxpayer(true);
        basicBean.setNotOuttwTaxpayer(true);
        basicBean.setCrossMarketing(false);
        paramBean.setBasicBean(basicBean);
        
        // 聯絡資料
        LoanApplyContactBean contactBean = new LoanApplyContactBean();
        contactBean.setMobileNumber("0912345678");
        contactBean.setEmail("<EMAIL>");  // 中鋼總公司email必填
        contactBean.setResidenceStatusCode("");  // 中鋼總公司不填居住狀況
        contactBean.setHomePhoneCode("");
        contactBean.setHomePhoneNumber("");
        paramBean.setContactBean(contactBean);
        
        // 貸款內容
        LoanApplyContentBean loanContentBean = new LoanApplyContentBean();
        loanContentBean.setLoanRequestAmt(500000);  // 申貸金額50萬
        loanContentBean.setLoanPeriodCode("7");     // 固定7年期
        loanContentBean.setLoanPurposeCode("01");   // 一般消費
        loanContentBean.setCaseSourceCode("01");    // 網路申請
        loanContentBean.setIncreasingLoan(false);   // 非增貸
        paramBean.setLoanContentBean(loanContentBean);
        
        return applyService.submitLoanApplyInfo(paramBean);
    }

    /**
     * 送出申請
     */
    private Long deliverApplication()
    {
        return applyService.deliverLoanApply();
    }

    /**
     * 測試C001重複申請檢查
     */
    @Test
    public void testC001DuplicateApplicationCheck()
    {
        try {
            // 第一次申請
            CreateResBean firstResult = createC001Application();
            System.out.println("第一次申請成功，loanId: " + firstResult.getLoanId());
            
            // 模擬6個月內重複申請
            CreateResBean secondResult = createC001Application();
            System.out.println("不應該執行到這裡 - 重複申請應該被阻擋");
            
        } catch (Exception e) {
            System.out.println("預期的錯誤：" + e.getMessage());
            // 應該拋出 ALREADY_FINISH_APPLY 錯誤
        }
    }

    /**
     * 測試C001 EIP系統驗證
     */
    @Test
    public void testC001EIPValidation()
    {
        // 這個測試需要模擬eloanCustInfoResultBean
        // 在實際測試中，需要設定mock資料
        
        /*
        EloanCustInfoResultBean mockBean = new EloanCustInfoResultBean();
        mockBean.setGrpCntrNo("918111000325");  // 中鋼控管編號
        mockBean.setMobileNumber("0912345678");
        mockBean.setEmail("<EMAIL>");
        
        // 使用Mockito或類似工具來模擬這個Bean的回傳
        when(eloanSenderService.getEloanCustInfo(anyString(), any(Date.class)))
            .thenReturn(mockBean);
        */
        
        try {
            CreateResBean result = createC001Application();
            System.out.println("EIP驗證通過，loanId: " + result.getLoanId());
        } catch (Exception e) {
            System.out.println("EIP驗證失敗：" + e.getMessage());
        }
    }

    /**
     * 測試C001申請時間限制
     */
    @Test
    public void testC001TimeRestriction()
    {
        // 這個測試需要修改code_loan_plan的時間範圍來測試
        // 或者使用時間Mock來模擬不同的申請時間
        
        try {
            CreateResBean result = createC001Application();
            System.out.println("時間驗證通過，loanId: " + result.getLoanId());
        } catch (Exception e) {
            System.out.println("時間驗證失敗：" + e.getMessage());
            // 可能的錯誤：LOAN_PLAN_NOT_BEGIN 或 LOAN_PLAN_EXCEED_ENDTS
        }
    }
}
