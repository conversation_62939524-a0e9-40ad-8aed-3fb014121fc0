/**
 *
 */
package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.houseloantrial.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloantrial.HouseLoanInfoBean;
import com.megabank.olp.apply.controller.management.bean.houseloantrial.HouseLoanTrialBasicInfoBean;
import com.megabank.olp.apply.controller.management.bean.houseloantrial.HouseLoanTrialDetailGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloantrial.HouseLoanTrialExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloantrial.HouseLoanTrialListedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloantrial.HouseLoanTrialSendCaseArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloantrial.ProcessStatusUpdatedArgBean;
import com.megabank.olp.apply.service.management.HouseLoanTrialService;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanDataBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialBasicDataBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialExportedParamBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialListedParamBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialSendCaseParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/houseloantrial" )
public class HouseLoanTrialController extends BaseController
{
	@Autowired
	private HouseLoanTrialService service;

	/**
	 * 房貸試算案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportList" )
	public Map<String, Object> exportList( @RequestBody @Validated HouseLoanTrialExportedArgBean argBean )
	{
		HouseLoanTrialExportedParamBean paramBean = new HouseLoanTrialExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( service.exportList( paramBean ) );
	}

	/**
	 * 取得房貸試算案件詳細內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getDetail" )
	public Map<String, Object> getDetail( @RequestBody @Validated HouseLoanTrialDetailGetterArgBean argBean )
	{
		Long houseLoanTrialId = argBean.getHouseLoanTrialId();

		return getResponseMap( service.getDetail( houseLoanTrialId ) );
	}

	/**
	 * 取得改派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getReassignBranchBank" )
	public Map<String, Object> getReassignBranchBank( @RequestBody @Validated HouseLoanTrialDetailGetterArgBean argBean )
	{
		Long houseLoanTrialId = argBean.getHouseLoanTrialId();

		return getResponseMap( service.getReassignBranchBank( houseLoanTrialId ) );
	}

	/**
	 * 取得房貸試算案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "list" )
	public Map<String, Object> listLoanTrial( @RequestBody @Validated HouseLoanTrialListedArgBean argBean )
	{
		HouseLoanTrialListedParamBean paramBean = new HouseLoanTrialListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( service.listLoanTrial( paramBean ) );
	}

	/**
	 * 房貸試算案件進件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "sendCase" )
	public Map<String, Object> sendCase( @RequestBody @Validated HouseLoanTrialSendCaseArgBean argBean )
	{
		return getResponseMap( service.create( mapCalculateParamBean( argBean ) ) );
	}

	/**
	 * 更改案件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		Long houseLoanTrialId = argBean.getHouseLoanTrialId();
		Long branchBankId = argBean.getBranchBankId();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( service.updateBranchBank( houseLoanTrialId, branchBankId, employeeId, employeeName ) );
	}

	/**
	 * 更新案件處理狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateProcessStatus" )
	public Map<String, Object> updateProcessStatus( @RequestBody @Validated ProcessStatusUpdatedArgBean argBean )
	{
		Long houseLoanTrialId = argBean.getHouseLoanTrialId();
		String processStatus = argBean.getProcessStatus();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( service.updateProcessStatus( houseLoanTrialId, processStatus, employeeId, employeeName ) );
	}

	private HouseLoanTrialBasicDataBean mapCalculateBasicData( HouseLoanTrialBasicInfoBean argBean )
	{
		HouseLoanTrialBasicDataBean paramBean = new HouseLoanTrialBasicDataBean();
		paramBean.setBalance( argBean.getBalance() );
		paramBean.setBorrow( argBean.getBorrow() );
		paramBean.setBuyHouseFrom( argBean.getBuyHouseFrom() );
		paramBean.setCashCard( argBean.getCashCard() );
		paramBean.setCashCardBalance( argBean.getCashCardBalance() );
		paramBean.setCreditcard( argBean.getCr3ditCard() );
		paramBean.setLoanPurpose( argBean.getLoanPurpose() );
		paramBean.setPay( argBean.getPay() );
		paramBean.setTotalPrice( argBean.getTotalPrice() );
		paramBean.setUserAge( argBean.getUserAge() );
		paramBean.setUserChildren( argBean.getUserChildren() );
		paramBean.setUserIncome( argBean.getUserIncome() );
		paramBean.setJobType( argBean.getJobType() );
		paramBean.setTitleType( argBean.getTitleType() );
		return paramBean;
	}

	private HouseLoanDataBean mapCalculateLoanData( HouseLoanInfoBean argBean )
	{
		HouseLoanDataBean paramBean = new HouseLoanDataBean();
		paramBean.setRate( argBean.getRate() );
		paramBean.setTopLoanCredit( argBean.getTopLoanCredit() );
		return paramBean;
	}

	private HouseLoanTrialSendCaseParamBean mapCalculateParamBean( HouseLoanTrialSendCaseArgBean argBean )
	{
		HouseLoanTrialSendCaseParamBean paramBean = new HouseLoanTrialSendCaseParamBean();

		paramBean.setCaseNo( argBean.getCaseNo() );
		paramBean.setCreatedDate( argBean.getCreatedDate() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setBasicInfo( mapCalculateBasicData( argBean.getBasicInfo() ) );
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setLoanInfo( mapCalculateLoanData( argBean.getLoanInfo() ) );

		return paramBean;
	}
}
