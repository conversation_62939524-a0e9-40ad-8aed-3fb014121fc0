package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanBasicCreatedParamBean extends BaseBean
{
	private Long loanId;

	private String idNo;

	private String name;

	private String engFirstName;

	private String engLastName;

	private Date birthDate;

	private String marriageStatusCode;

	private String educationLevelCode;

	private Integer childrenCount;

	private String nationalityCode;

	public ApplyLoanBasicCreatedParamBean()
	{
		// default constructor
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public Integer getChildrenCount()
	{
		return childrenCount;
	}

	public String getEducationLevelCode()
	{
		return educationLevelCode;
	}

	public String getEngFirstName()
	{
		return engFirstName;
	}

	public String getEngLastName()
	{
		return engLastName;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getMarriageStatusCode()
	{
		return marriageStatusCode;
	}

	public String getName()
	{
		return name;
	}

	public String getNationalityCode()
	{
		return nationalityCode;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setChildrenCount( Integer childrenCount )
	{
		this.childrenCount = childrenCount;
	}

	public void setEducationLevelCode( String educationLevelCode )
	{
		this.educationLevelCode = educationLevelCode;
	}

	public void setEngFirstName( String engFirstName )
	{
		this.engFirstName = engFirstName;
	}

	public void setEngLastName( String engLastName )
	{
		this.engLastName = engLastName;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setMarriageStatusCode( String marriageStatusCode )
	{
		this.marriageStatusCode = marriageStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNationalityCode( String nationalityCode )
	{
		this.nationalityCode = nationalityCode;
	}

}
