package com.megabank.olp.apply.persistence.bean.generated.temp;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

public class TempUploadFileCreatedParamBean extends BaseBean
{
	private Long loanId;

	private String attachmentType;

	private String fileName;

	private Long fileSize;

	private transient ImmutableByteArray fileContent;

	private transient ImmutableByteArray compressFileContent;

	private Boolean processed;

	public TempUploadFileCreatedParamBean()
	{}

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public ImmutableByteArray getCompressFileContent()
	{
		return compressFileContent;
	}

	public ImmutableByteArray getFileContent()
	{
		return fileContent;
	}

	public String getFileName()
	{
		return fileName;
	}

	public Long getFileSize()
	{
		return fileSize;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public Boolean getProcessed()
	{
		return processed;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setCompressFileContent( ImmutableByteArray compressFileContent )
	{
		this.compressFileContent = compressFileContent;
	}

	public void setFileContent( ImmutableByteArray fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setFileSize( Long fileSize )
	{
		this.fileSize = fileSize;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setProcessed( Boolean processed )
	{
		this.processed = processed;
	}

}