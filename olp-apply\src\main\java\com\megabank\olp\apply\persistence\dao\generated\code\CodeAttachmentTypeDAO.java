package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeAttachmentType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeAttachmentTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeAttachmentTypeDAO extends BasePojoDAO<CodeAttachmentType, String>
{
	public CodeAttachmentType read( String attachmentType )
	{
		Validate.notBlank( attachmentType );

		return getPojoByPK( attachmentType, CodeAttachmentType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeAttachmentType> getPojoClass()
	{
		return CodeAttachmentType.class;
	}
}
