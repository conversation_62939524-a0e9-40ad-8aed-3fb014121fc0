package com.megabank.olp.apply.controller.loan.bean.upload;

import javax.validation.constraints.NotBlank;

public class FileUploadedArgBean extends LoanUploadArgBean
{
	@NotBlank
	private String attachmentType;

	@NotBlank
	private String fileName;

	@NotBlank
	private transient String fileContent;

	private String caseNo;

	public FileUploadedArgBean()
	{
		// default constructor
	}

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public String getFileContent()
	{
		return fileContent;
	}

	public String getFileName()
	{
		return fileName;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setFileContent( String fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
}
