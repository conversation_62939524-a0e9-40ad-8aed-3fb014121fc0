package com.megabank.olp.api.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.api.service.client.ApiClientService;
import com.megabank.olp.system.config.SystemConfig;

@SpringBootTest
@ContextConfiguration( classes = { ApiConfig.class, SystemConfig.class } )
public class ApiClientServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private ApiClientService service;

	@Test
	public void checkLegalAddress()
	{
		String requestUrl = "/eloan/*";
		String clientAddress = "*************:12345";

		boolean result = service.checkLegalAddress( requestUrl, clientAddress );

		logger.info( "result:{}", result );
	}

}
