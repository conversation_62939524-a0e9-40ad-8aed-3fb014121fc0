package com.megabank.olp.api.controller.eloan.bean.signing;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class LoanPurposeInfoBean extends BaseBean
{
	@NotBlank
	private String loanPurposeName;

	@NotBlank
	private String isChecked;

	public LoanPurposeInfoBean()
	{}

	public String getIsChecked()
	{
		return isChecked;
	}

	public String getLoanPurposeName()
	{
		return loanPurposeName;
	}

	public void setIsChecked( String isChecked )
	{
		this.isChecked = isChecked;
	}

	public void setLoanPurposeName( String loanPurposeName )
	{
		this.loanPurposeName = loanPurposeName;
	}
}
