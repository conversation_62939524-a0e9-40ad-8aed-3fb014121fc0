package com.megabank.olp.apply.controller.loan.bean.apply;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PersonalInfoSubmittedArgBean extends LoanApplyBaseArgBean
{
	@JsonProperty( "basic" )
	@Valid
	private PersonalBasicBean personalBasicBean = new PersonalBasicBean();

	@JsonProperty( "contact" )
	@Valid
	private PersonalContactBean personalContactBean = new PersonalContactBean();

	@JsonProperty( "job" )
	@Valid
	private PersonalJobBean personalJobBean = new PersonalJobBean();

	@JsonProperty( "guarantee" )
	@Valid
	private PersonalGuaranteeBean personalGuaranteeBean = new PersonalGuaranteeBean();

	public PersonalInfoSubmittedArgBean()
	{
		// default constructor
	}

	public PersonalBasicBean getPersonalBasicBean()
	{
		return personalBasicBean;
	}

	public PersonalContactBean getPersonalContactBean()
	{
		return personalContactBean;
	}

	public PersonalGuaranteeBean getPersonalGuaranteeBean()
	{
		return personalGuaranteeBean;
	}

	public PersonalJobBean getPersonalJobBean()
	{
		return personalJobBean;
	}

	public void setPersonalBasicBean( PersonalBasicBean personalBasicBean )
	{
		this.personalBasicBean = personalBasicBean;
	}

	public void setPersonalContactBean( PersonalContactBean personalContactBean )
	{
		this.personalContactBean = personalContactBean;
	}

	public void setPersonalGuaranteeBean( PersonalGuaranteeBean personalGuaranteeBean )
	{
		this.personalGuaranteeBean = personalGuaranteeBean;
	}

	public void setPersonalJobBean( PersonalJobBean personalJobBean )
	{
		this.personalJobBean = personalJobBean;
	}

}
