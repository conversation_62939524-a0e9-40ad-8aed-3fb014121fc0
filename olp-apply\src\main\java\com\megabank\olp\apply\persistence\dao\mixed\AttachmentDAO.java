package com.megabank.olp.apply.persistence.dao.mixed;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import jakarta.persistence.TemporalType;
import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.mixed.AttachmentListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.AttachmentCountDTO;
import com.megabank.olp.apply.persistence.dto.AttachmentListDTO;
import com.megabank.olp.apply.persistence.dto.LoanAttachmentDTO;
import com.megabank.olp.apply.persistence.dto.SigningContractIdentityAttachmentDTO;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BaseDAO;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Repository
public class AttachmentDAO extends BaseDAO
{
	private static final String CREATED_DATE_CONSTANT = "createdDate";

	private static final String APPLY_STATUS_CODE_CONSTANT = "applyStatusCode";

	private static final String ATTACHMENT_ID_CONSTANT = "attachmentId";

	private static final String ATTACHMENT_TYPE_CONSTANT = "attachmentType";

	private static final String BIRTH_DATE_CONSTANT = "birthDate";

	private static final String CASE_NO_CONSTANT = "caseNo";

	private static final String DATE_END_CONSTANT = "dateEnd";

	private static final String DATE_START_CONSTANT = "dateStart";

	private static final String FILE_NAME_CONSTANT = "fileName";

	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String ID_NO_CONSTANT = "idNo";

	private static final String LOAN_ID_CONSTANT = "loanId";

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String TRANSMISSION_STATUS_CODE_CONSTANT = "transmissionStatusCode";

	private static final String TRANSMISSION_STATUS_NAME_CONSTANT = "transmissionStatusName";
	
	private static final String SIGNING_CONTRACT_ID = "signingContractId";	

	@SuppressWarnings( "unchecked" )
	public List<AttachmentCountDTO> getAttachmentCountByBranch( List<String> applyStatusCode, String loanType, Long finalBranchBankId )
	{
		Validate.notEmpty( applyStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "attachment.getAttachmentCount" );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, applyStatusCode, String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, finalBranchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( AttachmentCountDTO.class ) );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<AttachmentListDTO> getList( AttachmentListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "attachment.getList" );
		nativeQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );

		if( paramBean.getBirthDate() != null ){
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, paramBean.getBirthDate(), TemporalType.DATE );
		}else{
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, paramBean.getBirthDate(), Date.class );
		}

		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( CASE_NO_CONSTANT, paramBean.getCaseNo(), String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );

		nativeQuery.addScalar( ATTACHMENT_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( ATTACHMENT_TYPE_CONSTANT, String.class );
		nativeQuery.addScalar( FILE_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( ID_NO_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( BIRTH_DATE_CONSTANT, Date.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_NAME_CONSTANT, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( AttachmentListDTO.class ) );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<LoanAttachmentDTO> getLoanAttachment( Long loanId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "attachment.getLoanAttachment" );
		nativeQuery.setParameter( LOAN_ID_CONSTANT, loanId, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( LoanAttachmentDTO.class ) );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedBankIds( List<String> applyStatusCode, String loanType )
	{
		Validate.notEmpty( applyStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "attachment.getBranchBankIds" );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, applyStatusCode, String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public PagingBean<AttachmentListDTO> getPaging( AttachmentListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "attachment.getList" );
		nativeQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );

		if( paramBean.getBirthDate() != null ){
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, paramBean.getBirthDate(), TemporalType.DATE );
		}else{
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, paramBean.getBirthDate(), Date.class );
		}

		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( CASE_NO_CONSTANT, paramBean.getCaseNo(), String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );

		nativeQuery.addScalar( ATTACHMENT_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( ATTACHMENT_TYPE_CONSTANT, String.class );
		nativeQuery.addScalar( FILE_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( ID_NO_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( BIRTH_DATE_CONSTANT, Date.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_NAME_CONSTANT, String.class );

		NativeQuery countQuery = getNamedSQLQueryByCount( "attachment.getList.count" );
		countQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );
		
		if( paramBean.getBirthDate() != null ){
			countQuery.setParameter( BIRTH_DATE_CONSTANT, paramBean.getBirthDate(), TemporalType.DATE );
		}else{
			countQuery.setParameter( BIRTH_DATE_CONSTANT, paramBean.getBirthDate(), Date.class );
		}

		countQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		countQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		countQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		countQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		countQuery.setParameter( CASE_NO_CONSTANT, paramBean.getCaseNo(), String.class );
		countQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		countQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( AttachmentListDTO.class ) );

		return processPagination( nativeQuery, countQuery );

	}
	
	public List<SigningContractIdentityAttachmentDTO> getSigningContractOtherBankIdentityAttachment( Long signing_contract_id )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "identity_attachment.getListBy_signingContractId_and_otherBank" );
		nativeQuery.setParameter( SIGNING_CONTRACT_ID, signing_contract_id, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( SigningContractIdentityAttachmentDTO.class ) );

		return nativeQuery.getResultList();
	}
}
