/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.housepricing;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseEstimateInfoBean extends BaseBean
{
	@Digits( integer = 8, fraction = 2 )
	private BigDecimal av750;

	private BigDecimal estimateTotalPrice;

	public BigDecimal getAv750()
	{
		return av750;
	}

	public BigDecimal getEstimateTotalPrice()
	{
		return estimateTotalPrice;
	}

	public void setAv750( BigDecimal av750 )
	{
		this.av750 = av750;
	}

	public void setEstimateTotalPrice( BigDecimal estimateTotalPrice )
	{
		this.estimateTotalPrice = estimateTotalPrice;
	}
}
