/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.houseloantrial;

import java.util.Date;

import javax.validation.Valid;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanTrialSendCaseArgBean extends BaseBean
{
	private String caseNo;

	private String mobileNumber;

	private String email;

	private String branchBankCode;

	private Date createdDate;

	@Valid
	private HouseLoanTrialBasicInfoBean basicInfo;

	@Valid
	private HouseLoanInfoBean loanInfo;

	public HouseLoanTrialBasicInfoBean getBasicInfo()
	{
		return basicInfo;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getEmail()
	{
		return email;
	}

	public HouseLoanInfoBean getLoanInfo()
	{
		return loanInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBasicInfo( HouseLoanTrialBasicInfoBean basicInfo )
	{
		this.basicInfo = basicInfo;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setLoanInfo( HouseLoanInfoBean loanInfo )
	{
		this.loanInfo = loanInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}
}
