package com.megabank.olp.apply.persistence.dto;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class UploadFileDTO extends BaseBean
{
	private Long uploadFileId;

	private String attachmentType;

	private String fileName;

	private transient ImmutableByteArray compressFileContent;

	public UploadFileDTO()
	{}

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public ImmutableByteArray getCompressFileContent()
	{
		return compressFileContent;
	}

	public String getFileName()
	{
		return fileName;
	}

	public Long getUploadFileId()
	{
		return uploadFileId;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setCompressFileContent( byte[] compressFileContent )
	{
		this.compressFileContent = compressFileContent == null ? null : new ImmutableByteArray( compressFileContent );
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setUploadFileId( Long uploadFileId )
	{
		this.uploadFileId = uploadFileId;
	}
}
