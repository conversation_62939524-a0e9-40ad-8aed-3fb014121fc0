package com.megabank.olp.apply.controller.management.bean.houseloantrial;

import java.util.Date;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.validator.CheckDateRange;

@CheckDateRange( dateStart = "dateStart", dateEnd = "dateEnd" )
public class HouseLoanTrialListedArgBean extends BaseBean
{
	private String branchBankCode;

	private String notificationStatusCode;

	private String processStatusCode;

	private String email;

	private String mobileNumber;

	private Date dateStart;

	private Date dateEnd;

	@NotNull
	@Min( 1 )
	private Integer page;

	private Integer length;

	private String sortColumn;

	private String sortDirection;

	public HouseLoanTrialListedArgBean()
	{
		// default constructor
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	public Date getDateStart()
	{
		return dateStart;
	}

	public String getEmail()
	{
		return email;
	}

	public Integer getLength()
	{
		return length;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getNotificationStatusCode()
	{
		return notificationStatusCode;
	}

	public Integer getPage()
	{
		return page;
	}

	public String getProcessStatusCode()
	{
		return processStatusCode;
	}

	public String getSortColumn()
	{
		return sortColumn;
	}

	public String getSortDirection()
	{
		return sortDirection;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setLength( Integer length )
	{
		this.length = length;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setNotificationStatusCode( String notificationStatusCode )
	{
		this.notificationStatusCode = notificationStatusCode;
	}

	public void setPage( Integer page )
	{
		this.page = page;
	}

	public void setProcessStatusCode( String processStatusCode )
	{
		this.processStatusCode = processStatusCode;
	}

	public void setSortColumn( String sortColumn )
	{
		this.sortColumn = sortColumn;
	}

	public void setSortDirection( String sortDirection )
	{
		this.sortDirection = sortDirection;
	}

}
