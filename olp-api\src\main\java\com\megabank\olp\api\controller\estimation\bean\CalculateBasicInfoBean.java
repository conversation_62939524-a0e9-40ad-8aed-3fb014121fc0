/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class CalculateBasicInfoBean extends BaseBean
{
	@Min( 0 )
	@Max( 99 )
	private Integer userAge;

	@Min( 0 )
	@Max( 999 )
	private Integer userChildren;

	private String userJob;

	private String userTitle;

	@Min( 0 )
	@Max( ********* )
	private Integer userIncome;

	private String loanPurpose;

	private String buyHouseFrom;

	@Digits( integer = 9, fraction = 0 )
	private BigDecimal totalPrice;

	private String creditcard;

	private String pay;

	private String borrow;

	private String cashCard;

	private String cashCardBalance;

	private String balance;

	public String getBalance()
	{
		return balance;
	}

	public String getBorrow()
	{
		return borrow;
	}

	public String getBuyHouseFrom()
	{
		return buyHouseFrom;
	}

	public String getCashCard()
	{
		return cashCard;
	}

	public String getCashCardBalance()
	{
		return cashCardBalance;
	}

	public String getCreditcard()
	{
		return creditcard;
	}

	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public String getPay()
	{
		return pay;
	}

	public BigDecimal getTotalPrice()
	{
		return totalPrice;
	}

	public Integer getUserAge()
	{
		return userAge;
	}

	public Integer getUserChildren()
	{
		return userChildren;
	}

	public Integer getUserIncome()
	{
		return userIncome;
	}

	public String getUserJob()
	{
		return userJob;
	}

	public String getUserTitle()
	{
		return userTitle;
	}

	public void setBalance( String balance )
	{
		this.balance = balance;
	}

	public void setBorrow( String borrow )
	{
		this.borrow = borrow;
	}

	public void setBuyHouseFrom( String buyHouseFrom )
	{
		this.buyHouseFrom = buyHouseFrom;
	}

	public void setCashCard( String cashCard )
	{
		this.cashCard = cashCard;
	}

	public void setCashCardBalance( String cashCardBalance )
	{
		this.cashCardBalance = cashCardBalance;
	}

	public void setCreditcard( String creditcard )
	{
		this.creditcard = creditcard;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setPay( String pay )
	{
		this.pay = pay;
	}

	public void setTotalPrice( BigDecimal totalPrice )
	{
		this.totalPrice = totalPrice;
	}

	public void setUserAge( Integer userAge )
	{
		this.userAge = userAge;
	}

	public void setUserChildren( Integer userChildren )
	{
		this.userChildren = userChildren;
	}

	public void setUserIncome( Integer userIncome )
	{
		this.userIncome = userIncome;
	}

	public void setUserJob( String userJob )
	{
		this.userJob = userJob;
	}

	public void setUserTitle( String userTitle )
	{
		this.userTitle = userTitle;
	}

}
