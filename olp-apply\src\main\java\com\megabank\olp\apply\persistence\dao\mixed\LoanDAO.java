package com.megabank.olp.apply.persistence.dao.mixed;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import jakarta.persistence.TemporalType;
import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.mixed.LoanListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.ApplyListDTO;
import com.megabank.olp.apply.persistence.dto.LoanListDTO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.utility.enums.ApplyStatusEnum;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class LoanDAO extends BaseDAO
{

	private static final String APPLY_STATUS_CODE_CONSTANT = "applyStatusCode";

	private static final String BIRTH_DATE_CONSTANT = "birthDate";

	private static final String CASE_NO_CONSTANT = "caseNo";

	private static final String CURRENT_TIME_CONSTANT = "currentTime";

	private static final String DATE_END_CONSTANT = "dateEnd";

	private static final String DATE_START_CONSTANT = "dateStart";

	private static final String DAY_DIFF_CONSTANT = "dayDiff";

	private static final String DISCARD_CONSTANT = "discard";

	private static final String ID_NO_CONSTANT = "idNo";

	private static final String LOAN_ID_CONSTANT = "loanId";

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String NAME_CONSTANT = "name";

	private static final String TRANSMISSION_STATUS_CODE_CONSTANT = "transmissionStatusCode";

	private static final String TRANSMISSION_STATUS_NAME_CONSTANT = "transmissionStatusName";

	private static final String PROCESS_CODE_CONSTANT = "processCode";

	private static final String PROCESS_STATUS_CONSTANT = "processStatus";

	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String NOT_NOTIFIED_NAME_CONSTANT = "notNotifiedName";

	private static final String NOTIFIED_NAME_CONSTANT = "notifiedName";

	private static final String CREATED_DATE_CONSTANT = "createdDate";

	private static final String BRANCH_BANK_CONSTANT = "branchBank";

	private static final String NOTIFICATION_STATUS_CONSTANT = "notificationStatus";

	private static final String SECOND_DIFF_CONSTANT = "secondDiff";

	private static final String LOAN_PLAN_CODE_CONSTANT = "loanPlanCode";

	private static final String LOAN_PLAN_NAME_CONSTANT = "loanPlanName";

	private static final String LOAN_RECIPIENT_ID_CONSTANT = "loanRecipientId";

	private static final String INTRODUCE_BR_NO_CONSTANT = "introduceBrNo";
	
	private static final String INTRODUCE_EMP_ID_CONSTANT = "introduceEmpId";
	
	private static final String INTRODUCE_BR_NAME_CONSTANT = "introduceBrName";

	private static final String EMAIL_CONSTANT = "email";
	
	private static final String COMPANY_NAME_CONSTANT = "companyName";
	
	private static final String COMPANY_PHONE_CODE_CONSTANT = "companyPhoneCode";
	
	private static final String COMPANY_PHONE_NUMBER_CONSTANT = "companyPhoneNumber";
	
	private static final String COMPANY_PHONE_EXT_CONSTANT = "companyPhoneExt";

	private static final String MARKETING_BRANCH_BANK_NAME_CONSTANT = "marketingBranchBankName";

	public long getApplyCountInDays( String idNo, Date birthDate, String loanType, Integer dayDiff )
	{
		Validate.notBlank( idNo );
		Validate.notNull( birthDate );
		Validate.notNull( dayDiff );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getApplyCountInDays" );
		nativeQuery.setParameter( ID_NO_CONSTANT, idNo, String.class );
		nativeQuery.setParameter( BIRTH_DATE_CONSTANT, birthDate, TemporalType.DATE );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext(), String.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( DAY_DIFF_CONSTANT, dayDiff, Integer.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		return ( long )nativeQuery.uniqueResult();
	}

	public List<ApplyListDTO> getApplyList( String idNo, Date birthDate, String mobileNumber )
	{
		return getApplyList( idNo, birthDate, mobileNumber, null );
	}

	public List<ApplyListDTO> getApplyList( String idNo, Date birthDate, String mobileNumber, Boolean discard )
	{
		Validate.notBlank( idNo );
		Validate.notNull( birthDate );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getApplyList" );
		nativeQuery.setParameter( ID_NO_CONSTANT, idNo, String.class );
		nativeQuery.setParameter( BIRTH_DATE_CONSTANT, birthDate, TemporalType.DATE );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, mobileNumber, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext(), String.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, discard, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( ApplyListDTO.class ) );

		return nativeQuery.getResultList();
	}

	public List<Long> getCompletedIdentityIdsIn7Days( String idNo, Date birthDate, String loanType )
	{
		Validate.notBlank( idNo );
		Validate.notBlank( loanType );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getCompletedIdentityIdsIn7Days" );
		nativeQuery.setParameter( ID_NO_CONSTANT, idNo, String.class );

		if( birthDate != null ){
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, birthDate, TemporalType.DATE );
		}else{
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, birthDate, Date.class );
		}

		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext(), String.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		return nativeQuery.getResultList();
	}

	public List<Long> getCompletedIdentityIdIn30Days( String idNo, Date birthDate, String loanType, Integer loanRecipientId )
	{
		Validate.notBlank( idNo );
		Validate.notBlank( loanType );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getCompletedIdentityIdIn30Days" );
		nativeQuery.setParameter( ID_NO_CONSTANT, idNo, String.class );

		if( birthDate != null ){
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, birthDate, TemporalType.DATE );
		}else{
			nativeQuery.setParameter( BIRTH_DATE_CONSTANT, birthDate, Date.class );
		}

		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext(), String.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date() );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );
		nativeQuery.setParameter( LOAN_RECIPIENT_ID_CONSTANT, loanRecipientId );

		return nativeQuery.getResultList();
	}

	public Long getLatestLoanIdInDays( String idNo, Date birthDate, String mobileNumber, String loanType, Integer dayDiff )
	{
		Validate.notBlank( idNo );
		Validate.notNull( birthDate );
		Validate.notBlank( loanType );
		Validate.notNull( dayDiff );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getLatestLoanIdInDays" );
		nativeQuery.setParameter( ID_NO_CONSTANT, idNo, String.class );
		nativeQuery.setParameter( BIRTH_DATE_CONSTANT, birthDate, TemporalType.DATE );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, mobileNumber, String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext(), String.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( DAY_DIFF_CONSTANT, dayDiff, Integer.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		return ( Long )nativeQuery.uniqueResult();
	}

	public Long getLatestLoanIdInDaysByOtp( String idNo, Date birthDate, String mobileNumber, String loanType, Integer dayDiff )
	{
		Validate.notBlank( mobileNumber );
		Validate.notNull( dayDiff );

		return getLatestLoanIdInDays( idNo, birthDate, mobileNumber, loanType, dayDiff );
	}

	@SuppressWarnings( "unchecked" )
	public List<LoanListDTO> getList( LoanListGetterParamBean paramBean )
	{
		Validate.notNull( paramBean.getSecondDiff() );
		Validate.notEmpty( paramBean.getApplyStatusCodes() );

		NativeQuery nativeQuery = getNamedNativeQuery( "loan.getList" );
		nativeQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, paramBean.getDiscard(), Boolean.class );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, paramBean.getApplyStatusCodes(), String.class );
		// 若 loanRecipientId 為 null，視為 eloan (有些舊 eloan 件沒有 loanRecipientId)。[loan.hbm.xml]
		nativeQuery.setParameterList( LOAN_RECIPIENT_ID_CONSTANT, paramBean.getLoanRecipientId(), Long.class );

		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( SECOND_DIFF_CONSTANT, paramBean.getSecondDiff(), Integer.class );
		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.setParameter( INTRODUCE_BR_NO_CONSTANT, paramBean.getIntroduceBrNo(), String.class );
		nativeQuery.setParameter( INTRODUCE_EMP_ID_CONSTANT, paramBean.getIntroduceEmpId(), String.class );

		nativeQuery.addScalar( LOAN_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( ID_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( EMAIL_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( BIRTH_DATE_CONSTANT, Date.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( COMPANY_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( COMPANY_PHONE_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( COMPANY_PHONE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( COMPANY_PHONE_EXT_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( DISCARD_CONSTANT, Boolean.class );

		nativeQuery.addScalar( INTRODUCE_BR_NO_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_EMP_ID_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_BR_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MARKETING_BRANCH_BANK_NAME_CONSTANT, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( LoanListDTO.class ) );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public PagingBean<LoanListDTO> getPaging( LoanListGetterParamBean paramBean )
	{
		Validate.notNull( paramBean.getSecondDiff() );
		Validate.notEmpty( paramBean.getApplyStatusCodes() );

		NativeQuery nativeQuery = getNamedNativeQuery( "loan.getList" );
		nativeQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class);
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, paramBean.getDiscard(), Boolean.class );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, paramBean.getApplyStatusCodes(), String.class );
		// 若 loanRecipientId 為 null，視為 eloan (有些舊 eloan 件沒有 loanRecipientId)。[loan.hbm.xml]
		nativeQuery.setParameterList( LOAN_RECIPIENT_ID_CONSTANT, paramBean.getLoanRecipientId(), Long.class );
		// 若 introduce_br_1st 有值，則取 introduce_br_1st，若無則取 introduce_br_no。[loan.hbm.xml]
		nativeQuery.setParameter( INTRODUCE_BR_NO_CONSTANT, paramBean.getIntroduceBrNo(), String.class );
		nativeQuery.setParameter( INTRODUCE_EMP_ID_CONSTANT, paramBean.getIntroduceEmpId(), String.class );

		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( SECOND_DIFF_CONSTANT, paramBean.getSecondDiff(), Integer.class );
		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.addScalar( LOAN_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( ID_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( BIRTH_DATE_CONSTANT, Date.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( DISCARD_CONSTANT, Boolean.class );
		nativeQuery.addScalar( INTRODUCE_BR_NO_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_EMP_ID_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_BR_NAME_CONSTANT, String.class );

		NativeQuery countQuery = getNamedSQLQueryByCount( "loan.getList.count" );
		countQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );
		countQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		countQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		countQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		countQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		countQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		countQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		countQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		countQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		countQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		countQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, paramBean.getApplyStatusCodes(), String.class );
		countQuery.setParameter( SECOND_DIFF_CONSTANT, paramBean.getSecondDiff(), Integer.class );
		countQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		countQuery.setParameter( DISCARD_CONSTANT, paramBean.getDiscard(), Boolean.class );
		// 若 loanRecipientId 為 null，視為 eloan (有些舊 eloan 件沒有 loanRecipientId)。[loan.hbm.xml]
		countQuery.setParameterList( LOAN_RECIPIENT_ID_CONSTANT, paramBean.getLoanRecipientId(), Long.class );
		// 若 introduce_br_1st 有值，則取 introduce_br_1st，若無則取 introduce_br_no。[loan.hbm.xml]
		countQuery.setParameter( INTRODUCE_BR_NO_CONSTANT, paramBean.getIntroduceBrNo(), String.class );
		countQuery.setParameter( INTRODUCE_EMP_ID_CONSTANT, paramBean.getIntroduceEmpId(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( LoanListDTO.class ) );

		return processPagination( nativeQuery, countQuery );
	}

	@SuppressWarnings( "unchecked" )
	public List<LoanListDTO> getBranchList( LoanListGetterParamBean paramBean )
	{
		Validate.notNull( paramBean.getSecondDiff() );
		Validate.notEmpty( paramBean.getApplyStatusCodes() );

		NativeQuery nativeQuery = getNamedNativeQuery( "loan.getBranchList" );
		nativeQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, paramBean.getDiscard(), Boolean.class );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, paramBean.getApplyStatusCodes(), String.class );
		// 若 loanRecipientId 為 null，視為 eloan (有些舊 eloan 件沒有 loanRecipientId)。[loan.hbm.xml]
		nativeQuery.setParameterList( LOAN_RECIPIENT_ID_CONSTANT, paramBean.getLoanRecipientId(), Long.class );

		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( SECOND_DIFF_CONSTANT, paramBean.getSecondDiff(), Integer.class );
		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );
		
		nativeQuery.setParameter( INTRODUCE_BR_NO_CONSTANT, paramBean.getIntroduceBrNo(), String.class );
		nativeQuery.setParameter( INTRODUCE_EMP_ID_CONSTANT, paramBean.getIntroduceEmpId(), String.class );

		nativeQuery.addScalar( LOAN_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( ID_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( BIRTH_DATE_CONSTANT, Date.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( DISCARD_CONSTANT, Boolean.class );
		nativeQuery.addScalar( INTRODUCE_BR_NO_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_EMP_ID_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_BR_NAME_CONSTANT, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( LoanListDTO.class ) );

		return nativeQuery.getResultList();

	}
	
	@SuppressWarnings( "unchecked" )
	public PagingBean<LoanListDTO> getBranchPaging( LoanListGetterParamBean paramBean )
	{		
		Validate.notNull( paramBean.getSecondDiff() );
		Validate.notEmpty( paramBean.getApplyStatusCodes() );

		NativeQuery nativeQuery = getNamedNativeQuery( "loan.getBranchList" );
		nativeQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, paramBean.getDiscard(), Boolean.class );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, paramBean.getApplyStatusCodes(), String.class );
		// 若 loanRecipientId 為 null，視為 eloan (有些舊 eloan 件沒有 loanRecipientId)。[loan.hbm.xml]
		nativeQuery.setParameterList( LOAN_RECIPIENT_ID_CONSTANT, paramBean.getLoanRecipientId(), Long.class );

		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( SECOND_DIFF_CONSTANT, paramBean.getSecondDiff(), Integer.class );
		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );
		
		nativeQuery.setParameter( INTRODUCE_BR_NO_CONSTANT, paramBean.getIntroduceBrNo(), String.class );
		nativeQuery.setParameter( INTRODUCE_EMP_ID_CONSTANT, paramBean.getIntroduceEmpId(), String.class );

		nativeQuery.addScalar( LOAN_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( ID_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( BIRTH_DATE_CONSTANT, Date.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( TRANSMISSION_STATUS_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( DISCARD_CONSTANT, Boolean.class );
		nativeQuery.addScalar( INTRODUCE_BR_NO_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_EMP_ID_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCE_BR_NAME_CONSTANT, String.class );

		NativeQuery countQuery = getNamedSQLQueryByCount( "loan.getBranchList.count" );
		countQuery.setParameter( ID_NO_CONSTANT, paramBean.getIdNo(), String.class );
		countQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		countQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		countQuery.setParameter( TRANSMISSION_STATUS_CODE_CONSTANT, paramBean.getTransmissionStatusCode(), String.class );
		countQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		countQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		countQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		countQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		countQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		countQuery.setParameter( LOAN_TYPE_CONSTANT, paramBean.getLoanType(), String.class );
		countQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, paramBean.getApplyStatusCodes(), String.class );
		countQuery.setParameter( SECOND_DIFF_CONSTANT, paramBean.getSecondDiff(), Integer.class );
		countQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		countQuery.setParameter( DISCARD_CONSTANT, paramBean.getDiscard(), Boolean.class );
		// 若 loanRecipientId 為 null，視為 eloan (有些舊 eloan 件沒有 loanRecipientId)。[loan.hbm.xml]
		countQuery.setParameterList( LOAN_RECIPIENT_ID_CONSTANT, paramBean.getLoanRecipientId(), Long.class );	
		
		countQuery.setParameter( INTRODUCE_BR_NO_CONSTANT, paramBean.getIntroduceBrNo(), String.class );
		countQuery.setParameter( INTRODUCE_EMP_ID_CONSTANT, paramBean.getIntroduceEmpId(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( LoanListDTO.class ) );

		return processPagination( nativeQuery, countQuery );
	}
	
	@SuppressWarnings( "unchecked" )
	public List<ApplyLoan> getPojosByBorrowerInfo( String caseNo, String name, String idNo, String mobileNumber, String loanType )
	{

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getPojosByBorrowerInfo" );
		nativeQuery.setParameter( CASE_NO_CONSTANT, caseNo, String.class );
		nativeQuery.setParameter( NAME_CONSTANT, name, String.class );
		nativeQuery.setParameter( ID_NO_CONSTANT, idNo, String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, mobileNumber, String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyLoan.class );

		return nativeQuery.getResultList();
	}

}
