package com.megabank.olp.apply.controller.management.bean.housepricing;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class HousePricingDetailGetterArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long housePricingId;

	public HousePricingDetailGetterArgBean()
	{
		// default constructor
	}

	public Long getHousePricingId()
	{
		return housePricingId;
	}

	public void setHousePricingId( Long housePricingId )
	{
		this.housePricingId = housePricingId;
	}

}
