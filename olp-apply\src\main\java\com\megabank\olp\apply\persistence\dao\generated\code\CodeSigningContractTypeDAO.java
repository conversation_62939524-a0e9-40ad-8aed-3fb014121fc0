package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeSigningContractType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeSigningContractTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeSigningContractTypeDAO extends BasePojoDAO<CodeSigningContractType, String>
{
	public CodeSigningContractType read( String signingContractType )
	{
		Validate.notBlank( signingContractType );

		return getPojoByPK( signingContractType, CodeSigningContractType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeSigningContractType> getPojoClass()
	{
		return CodeSigningContractType.class;
	}
}
