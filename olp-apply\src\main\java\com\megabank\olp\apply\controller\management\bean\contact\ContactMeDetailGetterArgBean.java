package com.megabank.olp.apply.controller.management.bean.contact;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class ContactMeDetailGetterArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long contactMeId;

	public ContactMeDetailGetterArgBean()
	{
		// default constructor
	}

	public Long getContactMeId()
	{
		return contactMeId;
	}

	public void setContactMeId( Long contactMeId )
	{
		this.contactMeId = contactMeId;
	}

}
