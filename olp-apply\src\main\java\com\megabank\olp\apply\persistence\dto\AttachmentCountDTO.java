package com.megabank.olp.apply.persistence.dto;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class AttachmentCountDTO extends BaseBean
{
	private Long loanId;

	private Long loanRecipientId;

	private Long attachmentId;

	private String name;

	public AttachmentCountDTO()
	{
		// default constructor
	}

	public Long getAttachmentId()
	{
		return attachmentId;
	}

	public Long getLoanRecipientId()
	{
		return loanRecipientId;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getName()
	{
		return name;
	}

	public void setAttachmentId( Long attachmentId )
	{
		this.attachmentId = attachmentId;
	}

	public void setLoanRecipientId( Long loanRecipientId )
	{
		this.loanRecipientId = loanRecipientId;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setName( String name )
	{
		this.name = name;
	}

}
