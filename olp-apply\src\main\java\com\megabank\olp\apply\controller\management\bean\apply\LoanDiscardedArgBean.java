/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.apply;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class LoanDiscardedArgBean extends BaseBean
{
	@NotBlank
	private String employeeId;

	@NotBlank
	private String employeeName;

	@NotBlank
	private String caseNo;

	public LoanDiscardedArgBean()
	{
		// default constructor
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

}
