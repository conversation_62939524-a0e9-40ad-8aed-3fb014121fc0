/**
 *
 */
package com.megabank.olp.apply.controller.mydata.bean;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class UpdateMyDataAuthArgBean extends BaseBean
{
	@NotBlank
	private String transactionId;

	public UpdateMyDataAuthArgBean()
	{
		// default constructor
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}
	
}
