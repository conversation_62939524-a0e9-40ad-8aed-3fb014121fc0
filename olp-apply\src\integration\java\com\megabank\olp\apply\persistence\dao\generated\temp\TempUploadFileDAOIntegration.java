package com.megabank.olp.apply.persistence.dao.generated.temp;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.temp.TempUploadFileCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.temp.TempUploadFile;
import com.megabank.olp.base.bean.ImmutableByteArray;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class TempUploadFileDAOIntegration
{
	@Autowired
	private TempUploadFileDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long loanId = 1L;
		String attachmentType = "other";
		String fileName = "image.jpg";
		Long fileSize = 100L;
		String loanType = "personalloan";
		byte[] fileContent = new byte[ 0 ];
		byte[] compressFileContent = new byte[ 0 ];

		TempUploadFileCreatedParamBean paramBean = new TempUploadFileCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setAttachmentType( attachmentType );
		paramBean.setFileName( fileName );
		paramBean.setFileSize( fileSize );
		paramBean.setFileContent( new ImmutableByteArray( fileContent ) );
		paramBean.setCompressFileContent( new ImmutableByteArray( compressFileContent ) );

		Long id = dao.create( paramBean, loanType );

		logger.info( "id:{}", id );
	}

	@Test
	public void delete()
	{
		Long uploadFileId = 2L;
		Long loanId = 1L;

		dao.delete( uploadFileId, loanId );
	}

	@Test
	public void getPojoById()
	{
		Long uploadFileId = 1L;
		Long loanId = 1L;

		TempUploadFile pojo = dao.getPojoById( uploadFileId, loanId );

		logger.info( "pojo:{}", pojo );
	}

}
