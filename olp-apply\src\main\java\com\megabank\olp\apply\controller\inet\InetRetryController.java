package com.megabank.olp.apply.controller.inet;

import java.io.IOException;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.service.inet.InetRetryService;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.client.sender.micro.apply.inet.bean.InetReportInfoBean;
import com.megabank.olp.client.utility.enums.InetReportCodeEnum;

@RestController
@RequestMapping( "inet/retry" )
public class InetRetryController extends BaseController
{
	@Autowired
	private InetRetryService inetRetryService;

	@PostMapping( "getPdf" )
	public Map<String, Object> retryGetPdfFromInet( @RequestBody InetReportInfoBean inetReportInfo ) throws IOException
	{
		if( InetReportCodeEnum.HOUSE_LOAN_SIGNING_CONTRACT.getCode().equals( inetReportInfo.getCode() ) )
			inetRetryService.retryGetLoanSigningContract( inetReportInfo.getCode() );
		if( InetReportCodeEnum.PERSONAL_LOAN_SIGNING_CONTRACT.getCode().equals( inetReportInfo.getCode() ) )
			inetRetryService.retryGetLoanSigningContract( inetReportInfo.getCode() );

		return getResponseMap();
	}

	@PostMapping( "collateralContractAttachment/getPdf" )
	public Map<String, Object> retryGetCollateralContractAttachmentPdfFromInet() throws IOException
	{
		inetRetryService.retryGetCollateralContractAttachmentPdf();

		return getResponseMap();
	}
}
