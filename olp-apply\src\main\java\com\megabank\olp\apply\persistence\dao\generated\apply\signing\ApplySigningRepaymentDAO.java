package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningRepaymentParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningRepayment;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

@Repository
public class ApplySigningRepaymentDAO extends BasePojoDAO<ApplySigningRepayment, Long>
{

	public Long create( SigningRepaymentParamBean paramBean )
	{
		Validate.notNull( paramBean.getSigningContractId() );
		Validate.notNull( paramBean.getBankCode() );
		Validate.notNull( paramBean.getBankName() );
		Validate.notNull( paramBean.getRepaymentProductType() );
		Validate.notNull( paramBean.getOriginalAmt() );

		ApplySigningRepayment pojo = new ApplySigningRepayment();
		pojo.setSigningContractId( paramBean.getSigningContractId() );
		pojo.setBankCode( paramBean.getBankCode() );
		pojo.setBankName( paramBean.getBankName() );
		pojo.setRepaymentProductType( paramBean.getRepaymentProductType() );
		pojo.setOriginalAmt( paramBean.getOriginalAmt() );

		return super.createPojo( pojo );
	}

	public List<ApplySigningRepayment> getPojoByContractId( Long contractId )
	{
		Validate.notNull( contractId );

		NameValueBean condition = new NameValueBean( ApplySigningRepayment.SIGNING_CONTRACT_ID_CONSTANT, contractId );

		return getPojosByProperty( condition );
	}

	@Override
	protected Class<ApplySigningRepayment> getPojoClass()
	{
		return ApplySigningRepayment.class;
	}

}
