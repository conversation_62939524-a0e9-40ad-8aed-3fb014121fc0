package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanContactInfoUpdatedAddrTextParamBean extends BaseBean
{
	private Long loanId;

	private Long homeAddrTextId;

	private Long mailingAddrTextId;

	public ApplyLoanContactInfoUpdatedAddrTextParamBean()
	{
		// default constructor
	}

	public Long getHomeAddrTextId()
	{
		return homeAddrTextId;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public Long getMailingAddrTextId()
	{
		return mailingAddrTextId;
	}

	public void setHomeAddrTextId( Long homeAddrTextId )
	{
		this.homeAddrTextId = homeAddrTextId;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setMailingAddrTextId( Long mailingAddrTextId )
	{
		this.mailingAddrTextId = mailingAddrTextId;
	}

}
