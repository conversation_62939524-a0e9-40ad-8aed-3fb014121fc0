package com.megabank.olp.apply.service.loan;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.contact.ContactMeSubmittedParamBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ContactServiceIntegration
{
	@Autowired
	private ContactService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getThankyouMessage()
	{
		MessageResBean resBean = service.getThankyouMessage();

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void submitContactMe()
	{
		ContactMeSubmittedParamBean paramBean = getContactMeSubmittedParamBean();

		Long id = service.submitContactMe( paramBean );

		logger.info( "id:{}", id );
	}

	private ContactMeSubmittedParamBean getContactMeSubmittedParamBean()
	{
		String name = "王大明";
		String email = "<EMAIL>";
		String mobileNumber = "09********";
		String otherMsg = "none";
		String phoneCode = "02";
		String phoneNumber = "********";
		String phoneExt = "123";
		String branchBankCode = "1";
		String contactTimeCode = "01";
		String sexCode = "M";

		ContactMeSubmittedParamBean paramBean = new ContactMeSubmittedParamBean();
		paramBean.setName( name );
		paramBean.setEmail( email );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setOtherMsg( otherMsg );
		paramBean.setContactTimeCode( contactTimeCode );
		paramBean.setSexCode( sexCode );

		return paramBean;
	}

}