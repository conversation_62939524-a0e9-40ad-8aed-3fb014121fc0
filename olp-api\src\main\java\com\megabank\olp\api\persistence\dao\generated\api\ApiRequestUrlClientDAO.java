package com.megabank.olp.api.persistence.dao.generated.api;

import com.megabank.olp.api.persistence.pojo.api.ApiRequestUrlClient;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.springframework.stereotype.Repository;

/**
 * The ApiRequestUrlClientDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApiRequestUrlClientDAO extends BasePojoDAO<ApiRequestUrlClient, Long>
{
	@Override
	protected Class<ApiRequestUrlClient> getPojoClass()
	{
		return ApiRequestUrlClient.class;
	}
}
