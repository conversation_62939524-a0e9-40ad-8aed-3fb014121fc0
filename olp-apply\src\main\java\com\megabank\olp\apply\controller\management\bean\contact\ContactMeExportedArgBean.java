package com.megabank.olp.apply.controller.management.bean.contact;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.validator.CheckDateRange;

@CheckDateRange( dateStart = "dateStart", dateEnd = "dateEnd" )
public class ContactMeExportedArgBean extends BaseBean
{
	private String branchBankCode;

	private String notificationStatusCode;

	private String processStatusCode;

	private String contactTimeCode;

	private String name;

	private String mobileNumber;

	private Date dateStart;

	private Date dateEnd;

	private String sortColumn;

	private String sortDirection;

	private String introducerBranchBankId;

	private String introducerEmpId;

	private Boolean headOffice;

	private Long permissionBranchBankId;

	private String permissionEmpId;

	public ContactMeExportedArgBean()
	{
		// default constructor
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	public Date getDateStart()
	{
		return dateStart;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatusCode()
	{
		return notificationStatusCode;
	}

	public String getProcessStatusCode()
	{
		return processStatusCode;
	}

	public String getSortColumn()
	{
		return sortColumn;
	}

	public String getSortDirection()
	{
		return sortDirection;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatusCode( String notificationStatusCode )
	{
		this.notificationStatusCode = notificationStatusCode;
	}

	public void setProcessStatusCode( String processStatusCode )
	{
		this.processStatusCode = processStatusCode;
	}

	public void setSortColumn( String sortColumn )
	{
		this.sortColumn = sortColumn;
	}

	public void setSortDirection( String sortDirection )
	{
		this.sortDirection = sortDirection;
	}

	public String getIntroducerBranchBankId()
	{
		return introducerBranchBankId;
	}

	public void setIntroducerBranchBankId( String introducerBranchBankId )
	{
		this.introducerBranchBankId = introducerBranchBankId;
	}

	public String getIntroducerEmpId()
	{
		return introducerEmpId;
	}

	public void setIntroducerEmpId( String introducerEmpId )
	{
		this.introducerEmpId = introducerEmpId;
	}

	public Boolean getHeadOffice()
	{
		return headOffice;
	}

	public void setHeadOffice( Boolean headOffice )
	{
		this.headOffice = headOffice;
	}

	public Long getPermissionBranchBankId()
	{
		return permissionBranchBankId;
	}

	public void setPermissionBranchBankId( Long permissionBranchBankId )
	{
		this.permissionBranchBankId = permissionBranchBankId;
	}

	public String getPermissionEmpId()
	{
		return permissionEmpId;
	}

	public void setPermissionEmpId( String permissionEmpId )
	{
		this.permissionEmpId = permissionEmpId;
	}
}
