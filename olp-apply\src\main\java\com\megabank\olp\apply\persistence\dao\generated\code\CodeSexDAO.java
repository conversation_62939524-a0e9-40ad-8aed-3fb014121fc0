package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeSex;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeSexDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeSexDAO extends BasePojoDAO<CodeSex, String>
{
	public CodeSex read( String sexCode )
	{
		Validate.notBlank( sexCode );

		return getPojoByPK( sexCode, CodeSex.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeSex> getPojoClass()
	{
		return CodeSex.class;
	}
}
