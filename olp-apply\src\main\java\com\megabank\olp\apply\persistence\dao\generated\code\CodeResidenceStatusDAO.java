package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeResidenceStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeResidenceStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeResidenceStatusDAO extends BasePojoDAO<CodeResidenceStatus, String>
{
	public List<CodeResidenceStatus> getList()
	{
		return getAllPojos();
	}

	public CodeResidenceStatus read( String residenceStatusCode )
	{
		Validate.notBlank( residenceStatusCode );

		return getPojoByPK( residenceStatusCode, CodeResidenceStatus.TABLENAME_CONSTANT );
	}

	public CodeResidenceStatus readToNull( String residenceStatusCode )
	{
		Validate.notBlank( residenceStatusCode );

		return getPojoByPK( residenceStatusCode );
	}

	@Override
	protected Class<CodeResidenceStatus> getPojoClass()
	{
		return CodeResidenceStatus.class;
	}
}
