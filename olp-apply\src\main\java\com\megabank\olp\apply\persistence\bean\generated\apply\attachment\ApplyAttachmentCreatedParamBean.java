package com.megabank.olp.apply.persistence.bean.generated.apply.attachment;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

public class ApplyAttachmentCreatedParamBean extends BaseBean
{
	private Long loanId;

	private Long validatedIdentityId;

	private String attachmentType;

	private String fileName;

	private Long fileSize;

	private transient ImmutableByteArray fileContent;

	private transient ImmutableByteArray compressFileContent;

	private String transmissionStatusCode;

	public ApplyAttachmentCreatedParamBean()
	{}

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public ImmutableByteArray getCompressFileContent()
	{
		return compressFileContent;
	}

	public ImmutableByteArray getFileContent()
	{
		return fileContent;
	}

	public String getFileName()
	{
		return fileName;
	}

	public Long getFileSize()
	{
		return fileSize;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setCompressFileContent( ImmutableByteArray compressFileContent )
	{
		this.compressFileContent = compressFileContent;
	}

	public void setFileContent( ImmutableByteArray fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setFileSize( Long fileSize )
	{
		this.fileSize = fileSize;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

}