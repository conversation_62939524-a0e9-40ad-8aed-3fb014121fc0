package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanOccupationCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanOccupationUpdatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.address.ApplyAddressDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeAmountPerMonthDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeJobSubTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTitleTypeDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanOccupation;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanOccupationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanOccupationDAO extends BasePojoDAO<ApplyLoanOccupation, Long>
{
	@Autowired
	private ApplyAddressDAO applyAddressDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private CodeAmountPerMonthDAO codeAmountPerMonthDAO;

	@Autowired
	private CodeTitleTypeDAO codeTitleTypeDAO;

	@Autowired
	private CodeJobSubTypeDAO codeJobSubTypeDAO;

	public Long create( ApplyLoanOccupationCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanOccupation pojo = new ApplyLoanOccupation();
		pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );
		pojo.setCodeJobSubType( paramBean.getJobSubTypeId() == null ? null : codeJobSubTypeDAO.read( paramBean.getJobSubTypeId() ) );
		pojo.setCompanyName( paramBean.getCompanyName() );
		pojo.setAnnualIncome( paramBean.getAnnualIncome() );
		pojo.setSeniorityYear( paramBean.getSeniorityYear() );
		pojo.setSeniorityMonth( paramBean.getSeniorityMonth() );
		pojo.setCodeTitleType( StringUtils.isBlank( paramBean.getTitleType() ) ? null : codeTitleTypeDAO.read( paramBean.getTitleType() ) );
		pojo.setTaxNo( paramBean.getCompanyTaxNo() );
		pojo.setCompanyPhoneCode( paramBean.getCompanyPhoneCode() );
		pojo.setCompanyPhoneNumber( paramBean.getCompanyPhoneNumber() );
		pojo.setCompanyPhoneExt( paramBean.getCompanyPhoneExt() );
		pojo.setApplyAddress( paramBean.getCompanyAddressId() == null ? null : applyAddressDAO.read( paramBean.getCompanyAddressId() ) );
		pojo.setCodeAmountPerMonth( StringUtils
					.isBlank( paramBean.getAmountPerMonthCode() ) ? null : codeAmountPerMonthDAO.read( paramBean.getAmountPerMonthCode() ) );
		pojo.setEmpNo( paramBean.getEmpNo() );
		pojo.setJobPosition( paramBean.getJobPosition() );

		return super.createPojo( pojo );
	}

	public ApplyLoanOccupation read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, ApplyLoanOccupation.TABLENAME_CONSTANT );
	}

	public ApplyLoanOccupation readToNull( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId );
	}

	public Long update( ApplyLoanOccupationUpdatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanOccupation pojo = read( paramBean.getLoanId() );
		pojo.setCodeJobSubType( paramBean.getJobSubTypeId() == null ? null : codeJobSubTypeDAO.read( paramBean.getJobSubTypeId() ) );
		pojo.setCompanyName( paramBean.getCompanyName() );
		pojo.setAnnualIncome( paramBean.getAnnualIncome() );
		pojo.setSeniorityYear( paramBean.getSeniorityYear() );
		pojo.setSeniorityMonth( paramBean.getSeniorityMonth() );
		pojo.setCodeTitleType( StringUtils.isBlank( paramBean.getTitleType() ) ? null : codeTitleTypeDAO.read( paramBean.getTitleType() ) );
		pojo.setTaxNo( paramBean.getCompanyTaxNo() );
		pojo.setCompanyPhoneCode( paramBean.getCompanyPhoneCode() );
		pojo.setCompanyPhoneNumber( paramBean.getCompanyPhoneNumber() );
		pojo.setCompanyPhoneExt( paramBean.getCompanyPhoneExt() );
		pojo.setApplyAddress( paramBean.getCompanyAddressId() == null ? null : applyAddressDAO.read( paramBean.getCompanyAddressId() ) );
		pojo.setCodeAmountPerMonth( StringUtils
					.isBlank( paramBean.getAmountPerMonthCode() ) ? null : codeAmountPerMonthDAO.read( paramBean.getAmountPerMonthCode() ) );
		pojo.setEmpNo( paramBean.getEmpNo() );
		pojo.setJobPosition( paramBean.getJobPosition() );

		return pojo.getLoanId();
	}

	@Override
	protected Class<ApplyLoanOccupation> getPojoClass()
	{
		return ApplyLoanOccupation.class;
	}
}
