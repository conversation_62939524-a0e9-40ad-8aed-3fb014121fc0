package com.megabank.olp.apply.persistence.dao.generated.apply.survey;

import java.math.BigDecimal;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.apply.survey.ApplySurveyCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.survey.ApplySurvey;
import com.megabank.olp.base.config.BasePersistenceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplySurveyDAOIntegration
{
	@Autowired
	private ApplySurveyDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		ApplySurveyCreatedParamBean paramBean = getApplySurveyCreatedParamBean();

		Long id = dao.create( paramBean );

		logger.info( "id:{}", id );
	}

	@Test
	public void getLatestData()
	{
		Long tokenId = 1L;

		ApplySurvey pojo = dao.getLatestData( tokenId );

		logger.info( "pojo:{}", pojo );
	}

	private ApplySurveyCreatedParamBean getApplySurveyCreatedParamBean()
	{
		Long validatedIdentityId = 1L;
		int annualIncome = 100;
		String caseNo = "123456";
		BigDecimal cashAdvance = new BigDecimal( 0 );
		Long jobSubTypeId = 1L;
		String riskLevel = "low";
		BigDecimal creditCardTotalAmt = new BigDecimal( 0 );
		BigDecimal debitCardTotalAmt = new BigDecimal( 0 );
		boolean holdingCreditCard = true;
		boolean holdingDebitCard = true;
		BigDecimal holdingPersonalLoan = new BigDecimal( 0 );
		BigDecimal revovingCredit = new BigDecimal( 0 );

		ApplySurveyCreatedParamBean paramBean = new ApplySurveyCreatedParamBean();
		paramBean.setValidatedIdentityId( validatedIdentityId );
		paramBean.setAnnualIncome( annualIncome );
		paramBean.setCaseNo( caseNo );
		paramBean.setCashAdvance( cashAdvance );
		paramBean.setJobSubTypeId( jobSubTypeId );
		paramBean.setRiskLevel( riskLevel );
		paramBean.setCreditCardTotalAmt( creditCardTotalAmt );
		paramBean.setDebitCardTotalAmt( debitCardTotalAmt );
		paramBean.setHoldingCreditCard( holdingCreditCard );
		paramBean.setHoldingDebitCard( holdingDebitCard );
		paramBean.setHoldingPersonalLoan( holdingPersonalLoan );
		paramBean.setRevovingCredit( revovingCredit );

		return paramBean;
	}

}
