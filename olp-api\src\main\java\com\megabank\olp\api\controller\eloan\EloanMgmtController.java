/**
 *
 */
package com.megabank.olp.api.controller.eloan;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.controller.eloan.bean.mgmt.EloanMgmtGetInfoAPIArgBean;
import com.megabank.olp.api.utility.BaseELoanAPIController;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "eloan/mgmt" )
public class EloanMgmtController extends BaseELoanAPIController
{

	@PostMapping( "getInfo" )
	public Map<String, Object> getInfo( @RequestHeader( value = "AccessToken" ) String accessToken,
										@RequestBody @Validated EloanMgmtGetInfoAPIArgBean argBean )
	{
		validAuth( accessToken );

		if( StringUtils.equals( argBean.getKey1(), "getJarVersion" ) )
			if( StringUtils.equals( argBean.getKey2(), "log4j" ) )
			{
				Map<String, String> rtn = new HashMap<>();
				rtn.put( "SpecificationVersion", org.apache.logging.log4j.core.Layout.class.getPackage().getSpecificationVersion() );
				rtn.put( "ImplementationVersion", org.apache.logging.log4j.core.Layout.class.getPackage().getImplementationVersion() );
				return getResponseMap( rtn );
			}
			else
				return getResponseMap( "unknown_key2" );
		else
			return getResponseMap( "unknown_key1" );
	}

}
