package com.megabank.olp.apply.service.loan.bean.signing;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class AgreementResBean extends BaseBean
{
	@JsonProperty( "borrower" )
	private List<ContractAgreementBean> borrowerAgreedBeans;

	@JsonProperty( "guarantor" )
	private List<ContractAgreementBean> guarantorAgreedBeans;

	@JsonProperty( "common" )
	private List<ContractAgreementBean> commonAgreedBeans;

	public AgreementResBean()
	{
		// default constructor
	}

	public List<ContractAgreementBean> getBorrowerAgreedBeans()
	{
		return borrowerAgreedBeans;
	}

	public List<ContractAgreementBean> getCommonAgreedBeans()
	{
		return commonAgreedBeans;
	}

	public List<ContractAgreementBean> getGuarantorAgreedBeans()
	{
		return guarantorAgreedBeans;
	}

	public void setBorrowerAgreedBeans( List<ContractAgreementBean> borrowerAgreedBeans )
	{
		this.borrowerAgreedBeans = borrowerAgreedBeans;
	}

	public void setCommonAgreedBeans( List<ContractAgreementBean> commonAgreedBeans )
	{
		this.commonAgreedBeans = commonAgreedBeans;
	}

	public void setGuarantorAgreedBeans( List<ContractAgreementBean> guarantorAgreedBeans )
	{
		this.guarantorAgreedBeans = guarantorAgreedBeans;
	}

}
