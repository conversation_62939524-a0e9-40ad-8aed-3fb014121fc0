package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class ContactMeListDTO extends BaseBean
{
	private Long contactMeId;

	private String caseNo;

	private String name;

	private String mobileNumber;

	private Date createdDate;

	private String processStatus;

	private String branchBank;

	private String notificationStatus;

	private String contactTime;

	private String loanPlanCode;

	private String loanPlanName;
	
	private String sexName;
	
	private String email;
	
	private String otherMessage;

	private String introducerBranchBankName;

	private String introducerEmpId;

	public ContactMeListDTO()
	{
		// default constructor
	}

	public String getBranchBank()
	{
		return branchBank;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Long getContactMeId()
	{
		return contactMeId;
	}

	public String getContactTime()
	{
		return contactTime;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatus()
	{
		return notificationStatus;
	}

	public String getProcessStatus()
	{
		return processStatus;
	}

	public String getLoanPlanCode() {
		return loanPlanCode;
	}

	public String getLoanPlanName() {
		return loanPlanName;
	}

	public String getSexName()
	{
		return sexName;
	}

	public String getEmail()
	{
		return email;
	}

	public String getOtherMessage()
	{
		return otherMessage;
	}

	public String getIntroducerBranchBankName()
	{
		return introducerBranchBankName;
	}

	public String getIntroducerEmpId()
	{
		return introducerEmpId;
	}

	public void setBranchBank(String branchBank )
	{
		this.branchBank = branchBank;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setContactMeId( Long contactMeId )
	{
		this.contactMeId = contactMeId;
	}

	public void setContactTime( String contactTime )
	{
		this.contactTime = contactTime;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatus( String notificationStatus )
	{
		this.notificationStatus = notificationStatus;
	}

	public void setProcessStatus( String processStatus )
	{
		this.processStatus = processStatus;
	}

	public void setLoanPlanCode(String loanPlanCode) {
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanPlanName(String loanPlanName) {
		this.loanPlanName = loanPlanName;
	}

	public void setSexName( String sexName )
	{
		this.sexName = sexName;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setOtherMessage( String otherMessage )
	{
		this.otherMessage = otherMessage;
	}

	public void setIntroducerBranchBankName( String introducerBranchBankName )
	{
		this.introducerBranchBankName = introducerBranchBankName;
	}

	public void setIntroducerEmpId( String introducerEmpId )
	{
		this.introducerEmpId = introducerEmpId;
	}
}