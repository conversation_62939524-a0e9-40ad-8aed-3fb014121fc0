package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeNotification;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeNotificationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeNotificationDAO extends BasePojoDAO<CodeNotification, String>
{
	public CodeNotification read( String notificationCode )
	{
		Validate.notBlank( notificationCode );

		return getPojoByPK( notificationCode, CodeNotification.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeNotification> getPojoClass()
	{
		return CodeNotification.class;
	}
}
