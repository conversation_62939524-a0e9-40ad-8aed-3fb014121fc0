package com.megabank.olp.apply.controller.loan.bean.upload;

import java.util.List;

public class FilesSubmittedArgBean extends LoanUploadArgBean
{
	private List<Long> fileIds;

	private String caseNo;

	private Boolean isAgreed;

	private String finalBranchBankCode;

	public FilesSubmittedArgBean()
	{
		// default constructor
	}

	public List<Long> getFileIds()
	{
		return fileIds;
	}

	public void setFileIds( List<Long> fileIds )
	{
		this.fileIds = fileIds;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public Boolean getIsAgreed()
	{
		return isAgreed;
	}

	public void setIsAgreed( Boolean isAgreed )
	{
		this.isAgreed = isAgreed;
	}

	public String getFinalBranchBankCode()
	{
		return finalBranchBankCode;
	}

	public void setFinalBranchBankCode( String finalBranchBankCode )
	{
		this.finalBranchBankCode = finalBranchBankCode;
	}
}
