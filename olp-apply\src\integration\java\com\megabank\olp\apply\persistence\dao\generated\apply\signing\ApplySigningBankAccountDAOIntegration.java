package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningBankAccount;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplySigningBankAccountDAOIntegration
{
	@Autowired
	private ApplySigningBankAccountDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getContractBankAccounts()
	{
		Long contractId = 3L;

		List<ApplySigningBankAccount> accounts = dao.getContractBankAccounts( contractId );

		logger.info( "ApplySigningBankAccount:{}", accounts );
	}

}
