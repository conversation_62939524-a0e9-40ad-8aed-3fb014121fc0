package com.megabank.olp.apply.controller.loan;

import java.util.List;
import java.util.Map;

import com.megabank.olp.apply.controller.loan.bean.upload.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.service.loan.UploadService;
import com.megabank.olp.apply.service.loan.bean.upload.FileUploadedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/upload" )
public class UploadController extends BaseController
{
	@Autowired
	private UploadService uploadService;

	/**
	 * 檢查MyData是否已授權成功
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "checkMyDataAuthSucceeded" )
	public Map<String, Object> checkMyDataAuthSucceeded( @RequestBody @Validated MyDataCheckedArgBean argBean )
	{
		return getResponseMap( uploadService.checkMyDataAuthSucceeded( argBean.getLoanType(), argBean.getTxId() ) );
	}

	/**
	 * 刪除已上傳檔案
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "deleteFile" )
	public Map<String, Object> deleteFile( @RequestBody @Validated FileDeletedArgBean argBean )
	{
		String loanType = argBean.getLoanType();
		Long fileId = argBean.getFileId();
		String caseNo = argBean.getCaseNo();

		uploadService.deleteFile( loanType, fileId, caseNo );

		return getResponseMap();
	}

	/**
	 * 取得同意事項
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getAgreement" )
	public Map<String, Object> getAgreement( @RequestBody @Validated IdentityTypeArgBean argBean )
	{
		String identityType = argBean.getIdentityType();

		return getResponseMap( uploadService.getAgreement( identityType ) );
	}

	@PostMapping( "getLatestLoanPlan" )
	public Map<String, Object> getLatestLoanPlan( @RequestBody @Validated LoanUploadArgBean argBean )
	{
		String loanType = argBean.getLoanType();

		return getResponseMap( uploadService.getLatestLoanPlan( loanType ) );
	}

	/**
	 * 取得MyData轉導網址
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getMyDataUrl" )
	public Map<String, Object> getMyDataUrl( @RequestBody @Validated MyDataUrlGettedArgBean argBean )
	{
		return getResponseMap( uploadService.getMyDataUrl( argBean.getLoanType() ) );
	}

	/**
	 * 取得補件感謝頁內容
	 *
	 * @return
	 */
	@PostMapping( "getThankyouMessage" )
	public Map<String, Object> getThankyouMessage()
	{
		return getResponseMap( uploadService.getThankyouMessage() );
	}
	
	/**
	 * 取得Mydata補件感謝頁內容
	 *
	 * @return
	 */
	@PostMapping( "getMydataThankyouMessage" )
	public Map<String, Object> getMydataThankyouMessage()
	{
		return getResponseMap( uploadService.getMydataThankyouMessage() );
	}
	
	/**
	 * 檢查使用者今日是否已經申請過 Mydata
	 *
	 * @return
	 */
	@PostMapping( "isMydataToday" )
	public Map<String, Object> isMydataToday( @RequestBody @Validated MydataTodayCheckedArgBean argBean )
	{
		return getResponseMap( uploadService.isMydataToday( argBean.getIdNo() ) );
	}

	/**
	 * 取得目前上傳檔案縮圖
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getThumbnails" )
	public Map<String, Object> getThumbnails( @RequestBody @Validated LoanUploadArgBean argBean )
	{
		return getResponseMap( uploadService.getThumbnails( argBean.getLoanType(), argBean.getCaseNo() ) );
	}

	/**
	 * 獲得使用者認證身分
	 *
	 * @return
	 */
	@PostMapping( "getUserType" )
	public Map<String, Object> getUserType( @RequestBody @Validated LoanUploadArgBean argBean )
	{
		return getResponseMap( uploadService.getUserType( argBean.getLoanType() ) );
	}

	/**
	 * 檢查使用者今日是否已經申請過 IXML
	 *
	 * @return
	 */
	@PostMapping( "isUserHaveIxmlToday" )
	public Map<String, Object> isUserHaveIxmlToday( @RequestBody @Validated IxmlTodayCheckedArgBean argBean )
	{
		return getResponseMap( uploadService.isUserHaveIxmlToday( argBean.getIdNo() ) );
	}

	/**
	 * 變更為ixml同意書狀態為已同意
	 *
	 * @return
	 */
	@PostMapping( "setIxmlApproved" )
	public Map<String, Object> setIxmlApproved( @RequestBody @Validated LoanArgBean argBean )
	{
		uploadService.setIxmlApproved( argBean.getLoanId() );

		return getResponseMap();
	}

	/**
	 * 確認已上傳檔案
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "submitFiles" )
	public Map<String, Object> submitFiles( @RequestBody @Validated FilesSubmittedArgBean argBean )
	{
		String loanType = argBean.getLoanType();
		List<Long> fileIds = argBean.getFileIds();
		String caseNo = argBean.getCaseNo();
		Boolean isAgreed = argBean.getIsAgreed();
		String finalBranchBankCode = argBean.getFinalBranchBankCode();

		uploadService.submitFiles( loanType, fileIds, caseNo, isAgreed, finalBranchBankCode );

		return getResponseMap();
	}

	/**
	 * 上傳檔案
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "uploadFile" )
	public Map<String, Object> uploadFile( @RequestBody @Validated FileUploadedArgBean argBean )
	{
		FileUploadedParamBean paramBean = new FileUploadedParamBean();
		paramBean.setLoanType( argBean.getLoanType() );
		paramBean.setAttachmentType( argBean.getAttachmentType() );
		paramBean.setFileName( argBean.getFileName() );
		paramBean.setFileContent( argBean.getFileContent() );
		paramBean.setCaseNo( argBean.getCaseNo() );

		return getResponseMap( uploadService.uploadFile( paramBean ) );
	}
}
