/**
 *
 */
package com.megabank.olp.apply.service.management;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.management.bean.signing.ContractCreatedParamBean;
import com.megabank.olp.apply.service.management.bean.signing.LoanConditionDataBean;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.utility.date.CommonDateUtils;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
class ContractServiceIntegration
{

	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private ContractService service;

	@Test
	public void createSigningContract() throws MyRuntimeException
	{
		String clientAddress = "0.0.0.0";
		ContractCreatedParamBean paramBean = getCreatedParamBean();

		Long result = service.createSigningContract( paramBean, clientAddress );

		logger.info( "result:{}", result );
	}

	@Test
	public void setDiscard()
	{
		String contractNo = "PA000001";
		Long result = service.setDiscard( contractNo );

		logger.info( "result:{}", result );
	}

	private ContractCreatedParamBean getCreatedParamBean()
	{
		ContractCreatedParamBean paramBean = new ContractCreatedParamBean();
		paramBean.setBorrowerBirthDate( CommonDateUtils.getDate( 1991, 1, 11 ) );
		paramBean.setBorrowerId( "A111111111" );
		paramBean.setBorrowerMobileNumber( "0954321678" );
		paramBean.setBorrowerName( "王大明" );
		paramBean.setBorrowerEmail( "<EMAIL>" );
		paramBean.setContractNo( "SCTest001" );
		paramBean.setExpiredDate( CommonDateUtils.getDate( 2020, 11, 11 ) );
		paramBean.setLoanConditionDataBean( getLoanConditionDataBean() );
		paramBean.setProductCode( "personalloan" );
		paramBean.setLoanType( "personalloan" );

		List<String> acctList = new ArrayList<>();
		acctList.add( "2736000213204572" );
		paramBean.setLoanAccts( acctList );

		return paramBean;
	}

	private LoanConditionDataBean getLoanConditionDataBean()
	{
		LoanConditionDataBean bean = new LoanConditionDataBean();
		bean.setAdvancedRedemptionTitle( "提前清償限制" );
		bean.setAdvancedRedemptionDesc( "甲方自撥款日期起36期內若提前清償全部或部份本金，甲方應支付以方提前清償違約金，計算方式如下：<br>自第0期至第24期內，提前清償需支付5%清償本金作為違約金<br>自第25期至第36期內，提前清償需支付1%清償本金作為違約金" );
		bean.setCreditCheckFee( 6000 );
		bean.setDrawDownType( "一次撥付" );
		bean.setLendingPlan( "限制清償期" );
		bean.setLoanAmt( 100 );
		bean.setLoanPeriod( 60 );
		bean.setOneTimeFee( 1000 );
		bean.setOtherInfoDesc( "" );
		bean.setOtherInfoTitle( "" );
		bean.setPreliminaryFee( 500 );
		bean.setAdvancedRateTitle( "浮動利率" );
		bean.setAdvancedRateDesc( "自第0期至第24期，依實際撥款日之乙方消費金融放款指標利率1.5%加計2%利息，目前合計年利率3.5%<br>自第25期至第36期，依實際撥款日之乙方消費金融放款指標利率1.5%加計1%利息，目前合計年利率2.5%" );
		bean.setRepaymentMethod( "本金按月平均攤還" );
		bean.setAdvancedApr( new BigDecimal( 1.2 ) );

		return bean;
	}
}
