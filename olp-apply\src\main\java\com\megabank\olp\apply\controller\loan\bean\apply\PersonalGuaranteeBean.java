package com.megabank.olp.apply.controller.loan.bean.apply;

import com.megabank.olp.base.bean.BaseBean;

public class PersonalGuaranteeBean extends BaseBean
{
	private String userSubType;

	private String guarantyReasonCode;

	private String otherGuarantyReason;

	private Boolean isCohabiting;

	private String relationBorrowerType;

	public PersonalGuaranteeBean()
	{
		// default constructor
	}

	public String getGuarantyReasonCode()
	{
		return guarantyReasonCode;
	}

	public Boolean getIsCohabiting()
	{
		return isCohabiting;
	}

	public String getOtherGuarantyReason()
	{
		return otherGuarantyReason;
	}

	public String getRelationBorrowerType()
	{
		return relationBorrowerType;
	}

	public String getUserSubType()
	{
		return userSubType;
	}

	public void setGuarantyReasonCode( String guarantyReasonCode )
	{
		this.guarantyReasonCode = guarantyReasonCode;
	}

	public void setIsCohabiting( Boolean isCohabiting )
	{
		this.isCohabiting = isCohabiting;
	}

	public void setOtherGuarantyReason( String otherGuarantyReason )
	{
		this.otherGuarantyReason = otherGuarantyReason;
	}

	public void setRelationBorrowerType( String relationBorrowerType )
	{
		this.relationBorrowerType = relationBorrowerType;
	}

	public void setUserSubType( String userSubType )
	{
		this.userSubType = userSubType;
	}

}
