package com.megabank.olp.apply.controller.loan.bean.apply;

import com.megabank.olp.base.bean.BaseBean;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

public class LoanApplyGetAgreedDateArgBean extends BaseBean
{
	@NotBlank
	private String idNo;

	@NotNull
	private Date birthDate;

	@NotNull
	private  String loanType;

	public LoanApplyGetAgreedDateArgBean()
	{
		// default constructor
	}

	public String getIdNo()
	{
		return idNo;
	}

	public void setIdNo(String idNo)
	{
		this.idNo = idNo;
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public void setBirthDate(Date birthDate)
	{
		this.birthDate = birthDate;
	}

	public String getLoanType() {
		return loanType;
	}

	public void setLoanType(String loanType) {
		this.loanType = loanType;
	}
}
