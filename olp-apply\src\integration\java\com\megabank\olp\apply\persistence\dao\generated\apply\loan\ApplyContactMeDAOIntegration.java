package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.contact.ApplyContactMeDAO;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyContactMeDAOIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );
	
	@Autowired
	private ApplyContactMeDAO dao;

	@Test
	public void getNeedToNotifiedContactMeIds()
	{
		List<Long> result = dao.getNeedToNotifiedContactMeIds( null );

		logger.info( "id:{}", result );
	}
}
