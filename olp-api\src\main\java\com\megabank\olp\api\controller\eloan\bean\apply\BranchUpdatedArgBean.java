/**
 *
 */
package com.megabank.olp.api.controller.eloan.bean.apply;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class BranchUpdatedArgBean extends BaseBean
{
	@NotBlank
	private String caseNo;

	@NotBlank
	private String branchCode;

	public BranchUpdatedArgBean()
	{
		// default constructor
	}

	public String getBranchCode()
	{
		return branchCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public void setBranchCode( String branchCode )
	{
		this.branchCode = branchCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

}
