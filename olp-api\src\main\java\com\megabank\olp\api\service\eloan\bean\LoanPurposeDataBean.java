package com.megabank.olp.api.service.eloan.bean;

import com.megabank.olp.base.bean.BaseBean;

public class LoanPurposeDataBean extends BaseBean
{
	private String loanPurposeName;

	private String isChecked;

	public LoanPurposeDataBean()
	{}

	public String getIsChecked()
	{
		return isChecked;
	}

	public String getLoanPurposeName()
	{
		return loanPurposeName;
	}

	public void setIsChecked( String isChecked )
	{
		this.isChecked = isChecked;
	}

	public void setLoanPurposeName( String loanPurposeName )
	{
		this.loanPurposeName = loanPurposeName;
	}
}
