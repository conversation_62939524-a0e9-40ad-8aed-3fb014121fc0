/**
 *
 */
package com.megabank.olp.apply.controller.loan.bean.signing;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractFillInfoArgBean extends BaseBean
{
	/**
	 * 案件編號
	 */
	@NotBlank
	private String contractNo;

	/**
	 * 撥款/繳款帳號銀行代碼
	 */
	private String bankAcctCode;

	/**
	 * 撥款/繳款銀行帳號
	 */
	private String bankAcctNo;

	/**
	 * 借款人契約寄送地址區域編號
	 */
	private String borrowerAddressTownCode;

	/**
	 * 借款人契約寄送地址
	 */
	private String borrowerAddressStreet;

	/**
	 * 保證人契約寄送地址區域編號
	 */
	private String guarantorAddressTownCode;

	/**
	 * 保證人契約寄送地址
	 */
	private String guarantorAddressStreet;

	/**
	 * 撥款日期
	 */
	private Date appropriationDate;

	/**
	 * 每月還款日
	 */
	private String repayment;

	/**
	 * 貸款利率調整通知方式編碼
	 */
	private String rateAdjustInformMethodCode;

	/**
	 * 借款人契約寄送方式編碼
	 */
	private String borrowerContractSendingMethodCode;

	/**
	 * 保證人契約寄送方式編碼
	 */
	private String guarantorContractSendingMethodCode;

	/**
	 * 首次還款日
	 */
	private Date firstPaymentDate;

	/**
	 * 契約審閱日
	 */
	private Date contractCheckDate;

	/**
	 * 是否同意共同行銷
	 */
	private Boolean hasAgreedCrossSelling;

	/**
	 * 是否需要辦理他行自動扣款ACH
	 */
	private Boolean isNeedACH;

	private String adMark;

	private String mortgageSettingDesc;

	/**
	 * 借款人逾期還款通知方式
	 */
	private BigDecimal borrowerOverdueInformMethod;

	public SigningContractFillInfoArgBean()
	{}

	public Date getAppropriationDate()
	{
		return appropriationDate;
	}

	public String getBankAcctCode()
	{
		return bankAcctCode;
	}

	public String getBankAcctNo()
	{
		return bankAcctNo;
	}

	public String getBorrowerAddressStreet()
	{
		return borrowerAddressStreet;
	}

	public String getBorrowerAddressTownCode()
	{
		return borrowerAddressTownCode;
	}

	public String getBorrowerContractSendingMethodCode()
	{
		return borrowerContractSendingMethodCode;
	}

	public Date getContractCheckDate()
	{
		return contractCheckDate;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public Date getFirstPaymentDate()
	{
		return firstPaymentDate;
	}

	public String getGuarantorAddressStreet()
	{
		return guarantorAddressStreet;
	}

	public String getGuarantorAddressTownCode()
	{
		return guarantorAddressTownCode;
	}

	public String getGuarantorContractSendingMethodCode()
	{
		return guarantorContractSendingMethodCode;
	}

	public Boolean getHasAgreedCrossSelling()
	{
		return hasAgreedCrossSelling;
	}

	public Boolean getIsNeedACH()
	{
		return isNeedACH;
	}

	public String getAdMark()
	{
		return adMark;
	}

	public String getRateAdjustInformMethodCode()
	{
		return rateAdjustInformMethodCode;
	}

	public String getRepayment()
	{
		return repayment;
	}

	public BigDecimal getBorrowerOverdueInformMethod()
	{
		return borrowerOverdueInformMethod;
	}

	public void setAppropriationDate( Date appropriationDate )
	{
		this.appropriationDate = appropriationDate;
	}

	public void setBankAcctCode( String bankAcctCode )
	{
		this.bankAcctCode = bankAcctCode;
	}

	public void setBankAcctNo( String bankAcctNo )
	{
		this.bankAcctNo = bankAcctNo;
	}

	public void setBorrowerAddressStreet( String borrowerAddressStreet )
	{
		this.borrowerAddressStreet = borrowerAddressStreet;
	}

	public void setBorrowerAddressTownCode( String borrowerAddressTownCode )
	{
		this.borrowerAddressTownCode = borrowerAddressTownCode;
	}

	public void setBorrowerContractSendingMethodCode( String borrowerContractSendingMethodCode )
	{
		this.borrowerContractSendingMethodCode = borrowerContractSendingMethodCode;
	}

	public void setContractCheckDate( Date contractCheckDate )
	{
		this.contractCheckDate = contractCheckDate;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setFirstPaymentDate( Date firstPaymentDate )
	{
		this.firstPaymentDate = firstPaymentDate;
	}

	public void setGuarantorAddressStreet( String guarantorAddressStreet )
	{
		this.guarantorAddressStreet = guarantorAddressStreet;
	}

	public void setGuarantorAddressTownCode( String guarantorAddressTownCode )
	{
		this.guarantorAddressTownCode = guarantorAddressTownCode;
	}

	public void setGuarantorContractSendingMethodCode( String guarantorContractSendingMethodCode )
	{
		this.guarantorContractSendingMethodCode = guarantorContractSendingMethodCode;
	}

	public void setHasAgreedCrossSelling( Boolean hasAgreedCrossSelling )
	{
		this.hasAgreedCrossSelling = hasAgreedCrossSelling;
	}

	public void setIsNeedACH( Boolean isNeedACH )
	{
		this.isNeedACH = isNeedACH;
	}

	public void setRateAdjustInformMethodCode( String rateAdjustInformMethodCode )
	{
		this.rateAdjustInformMethodCode = rateAdjustInformMethodCode;
	}

	public void setRepayment( String repayment )
	{
		this.repayment = repayment;
	}

	public void setAdMark( String adMark )
	{
		this.adMark = adMark;
	}

	public void setBorrowerOverdueInformMethod( BigDecimal borrowerOverdueInformMethod )
	{
		this.borrowerOverdueInformMethod = borrowerOverdueInformMethod;
	}

	public String getMortgageSettingDesc()
	{
		return mortgageSettingDesc;
	}

	public void setMortgageSettingDesc( String mortgageSettingDesc )
	{
		this.mortgageSettingDesc = mortgageSettingDesc;
	}
}
