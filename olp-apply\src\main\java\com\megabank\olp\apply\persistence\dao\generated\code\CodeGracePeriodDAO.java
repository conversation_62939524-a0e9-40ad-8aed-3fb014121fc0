package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeGracePeriod;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeGracePeriodDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeGracePeriodDAO extends BasePojoDAO<CodeGracePeriod, String>
{
	public CodeGracePeriod read( String gracePeriodCode )
	{
		Validate.notBlank( gracePeriodCode );

		return getPojoByPK( gracePeriodCode, CodeGracePeriod.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeGracePeriod> getPojoClass()
	{
		return CodeGracePeriod.class;
	}
}
