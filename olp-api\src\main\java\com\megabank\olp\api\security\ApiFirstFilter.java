package com.megabank.olp.api.security;

import com.megabank.olp.api.service.jwt.ApiJwtDecryptService;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.service.jwt.AuthJwtService;
import com.megabank.olp.base.service.jwt.decrypt.JwtService;
import com.megabank.olp.base.threadlocal.RequestInfoThreadLocal;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.system.filter.MyFirstFilter;
import com.megabank.olp.system.service.TranLogService;

public class ApiFirstFilter extends MyFirstFilter
{	
	private ApiJwtDecryptService apiJwtDecryptService;

	public ApiFirstFilter( ApiJwtDecryptService apiJwtDecryptService, AuthJwtService authJwtService, JwtService jwtService,
						   TranLogService tranLogService, String[] permitUrls, RequestInfoThreadLocal requestInfoThreadLocal, SessionInfoThreadLocal sessionInfoThreadLocal )
	{
		super( authJwtService, jwtService, tranLogService, permitUrls, requestInfoThreadLocal, sessionInfoThreadLocal );

		this.apiJwtDecryptService = apiJwtDecryptService;
	}

	@Override
	protected JwtService getCurrentJwtService()
	{
		return apiJwtDecryptService;
	}

	@Override
	protected void processJWT( String jwt, String requestUrl, JwtService currentJwtService )
	{
		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();

		sessionInfoThreadLocal.set( localBean );
	}
}
