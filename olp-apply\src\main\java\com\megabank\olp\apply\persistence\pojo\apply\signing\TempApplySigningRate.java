package com.megabank.olp.apply.persistence.pojo.apply.signing;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

@Entity
@Table( name = "temp_apply_signing_rate" )
public class TempApplySigningRate extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "temp_apply_signing_rate";

	public static final String SIGNING_CONTRACT_ID_CONSTANT = "signingContractId";

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "signing_contract_rate_id" )
	private Long signingContractRateId;

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "signing_contract_id", nullable = false )
	private ApplySigningContract applySigningContract;

	@Column( name = "period", nullable = false )
	private String period;

	@Column( name = "loan_amt", nullable = false )
	private String LoanAmt;

	@Column( name = "compound_amt", nullable = false )
	private String compoundAmt;

	@Column( name = "principal_amt", nullable = false )
	private String principalAmt;

	@Column( name = "interest_amt", nullable = false )
	private String interestAmt;

	@Column( name = "now_loan_amt", nullable = false )
	private String nowLoanAmt;

	public TempApplySigningRate()
	{
		super();
	}

	public TempApplySigningRate( long signingContractRateId, ApplySigningContract applySigningContract, String period, String loanAmt,
									 String compoundAmt, String principalAmt, String interestAmt, String nowLoanAmt )
	{
		super();
		this.signingContractRateId = signingContractRateId;
		this.applySigningContract = applySigningContract;
		this.period = period;
		LoanAmt = loanAmt;
		this.compoundAmt = compoundAmt;
		this.principalAmt = principalAmt;
		this.interestAmt = interestAmt;
		this.nowLoanAmt = nowLoanAmt;
	}

	public ApplySigningContract getApplySigningContract()
	{
		return applySigningContract;
	}

	public String getCompoundAmt()
	{
		return compoundAmt;
	}

	public String getInterestAmt()
	{
		return interestAmt;
	}

	public String getLoanAmt()
	{
		return LoanAmt;
	}

	public String getNowLoanAmt()
	{
		return nowLoanAmt;
	}

	public String getPeriod()
	{
		return period;
	}

	public String getPrincipalAmt()
	{
		return principalAmt;
	}

	public long getSigningContractRateId()
	{
		return signingContractRateId;
	}

	public void setApplySigningContract( ApplySigningContract applySigningContract )
	{
		this.applySigningContract = applySigningContract;
	}

	public void setCompoundAmt( String compoundAmt )
	{
		this.compoundAmt = compoundAmt;
	}

	public void setInterestAmt( String interestAmt )
	{
		this.interestAmt = interestAmt;
	}

	public void setLoanAmt( String loanAmt )
	{
		LoanAmt = loanAmt;
	}

	public void setNowLoanAmt( String nowLoanAmt )
	{
		this.nowLoanAmt = nowLoanAmt;
	}

	public void setPeriod( String period )
	{
		this.period = period;
	}

	public void setPrincipalAmt( String principalAmt )
	{
		this.principalAmt = principalAmt;
	}

	public void setSigningContractRateId( long signingContractRateId )
	{
		this.signingContractRateId = signingContractRateId;
	}

	@Override
	public String toString()
	{
		return "ApplySigningContractRate [signingContractRateId=" + signingContractRateId + ", period=" + period + ", LoanAmt=" + LoanAmt
			+ ", compoundAmt=" + compoundAmt + ", principalAmt=" + principalAmt + ", interestAmt=" + interestAmt + ", nowLoanAmt=" + nowLoanAmt + "]";
	}

}
