package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

public class CbAfft1ContentBean extends BaseBean
{
	private String cbAfft1_1;

	private String cbAfft1_2;

	private String cbAfft1_3_year;

	private String cbAfft1_3_mth;

	private String cbAfft1_3_day;

	private Integer cbAfft1_4;

	private List<String> cbAfft1_5;

	private String cbAfft1_6;

	private BigDecimal cbAfft1_7;

	private Integer cbAfft1_8;

	private String cbAfft1_9_year;

	private String cbAfft1_9_mth;

	private String cbAfft1_9_day;

	private String cbAfft1_10_year;

	private String cbAfft1_10_mth;

	private String cbAfft1_10_day;

	private String cbAfft1_10_no;

	private Integer cbAfft1_11;

	private Integer cbAfft1_12;

	private Integer cbAfft1_13;

	private Integer cbAfft1_14;

	private Integer cbAfft1_15;

	private String cbAfft1_16;

	public CbAfft1ContentBean()
	{}

	public String getCbAfft1_1()
	{
		return cbAfft1_1;
	}

	public void setCbAfft1_1( String cbAfft1_1 )
	{
		this.cbAfft1_1 = cbAfft1_1;
	}

	public String getCbAfft1_2()
	{
		return cbAfft1_2;
	}

	public void setCbAfft1_2( String cbAfft1_2 )
	{
		this.cbAfft1_2 = cbAfft1_2;
	}

	public String getCbAfft1_3_year()
	{
		return cbAfft1_3_year;
	}

	public void setCbAfft1_3_year( String cbAfft1_3_year )
	{
		this.cbAfft1_3_year = cbAfft1_3_year;
	}

	public String getCbAfft1_3_mth()
	{
		return cbAfft1_3_mth;
	}

	public void setCbAfft1_3_mth( String cbAfft1_3_mth )
	{
		this.cbAfft1_3_mth = cbAfft1_3_mth;
	}

	public String getCbAfft1_3_day()
	{
		return cbAfft1_3_day;
	}

	public void setCbAfft1_3_day( String cbAfft1_3_day )
	{
		this.cbAfft1_3_day = cbAfft1_3_day;
	}

	public Integer getCbAfft1_4()
	{
		return cbAfft1_4;
	}

	public void setCbAfft1_4( Integer cbAfft1_4 )
	{
		this.cbAfft1_4 = cbAfft1_4;
	}

	public List<String> getCbAfft1_5()
	{
		return cbAfft1_5;
	}

	public void setCbAfft1_5( List<String> cbAfft1_5 )
	{
		this.cbAfft1_5 = cbAfft1_5;
	}

	public String getCbAfft1_6()
	{
		return cbAfft1_6;
	}

	public void setCbAfft1_6( String cbAfft1_6 )
	{
		this.cbAfft1_6 = cbAfft1_6;
	}

	public BigDecimal getCbAfft1_7()
	{
		return cbAfft1_7;
	}

	public void setCbAfft1_7( BigDecimal cbAfft1_7 )
	{
		this.cbAfft1_7 = cbAfft1_7;
	}

	public Integer getCbAfft1_8()
	{
		return cbAfft1_8;
	}

	public void setCbAfft1_8( Integer cbAfft1_8 )
	{
		this.cbAfft1_8 = cbAfft1_8;
	}

	public String getCbAfft1_9_year()
	{
		return cbAfft1_9_year;
	}

	public void setCbAfft1_9_year( String cbAfft1_9_year )
	{
		this.cbAfft1_9_year = cbAfft1_9_year;
	}

	public String getCbAfft1_9_mth()
	{
		return cbAfft1_9_mth;
	}

	public void setCbAfft1_9_mth( String cbAfft1_9_mth )
	{
		this.cbAfft1_9_mth = cbAfft1_9_mth;
	}

	public String getCbAfft1_9_day()
	{
		return cbAfft1_9_day;
	}

	public void setCbAfft1_9_day( String cbAfft1_9_day )
	{
		this.cbAfft1_9_day = cbAfft1_9_day;
	}

	public String getCbAfft1_10_year()
	{
		return cbAfft1_10_year;
	}

	public void setCbAfft1_10_year( String cbAfft1_10_year )
	{
		this.cbAfft1_10_year = cbAfft1_10_year;
	}

	public String getCbAfft1_10_mth()
	{
		return cbAfft1_10_mth;
	}

	public void setCbAfft1_10_mth( String cbAfft1_10_mth )
	{
		this.cbAfft1_10_mth = cbAfft1_10_mth;
	}

	public String getCbAfft1_10_day()
	{
		return cbAfft1_10_day;
	}

	public void setCbAfft1_10_day( String cbAfft1_10_day )
	{
		this.cbAfft1_10_day = cbAfft1_10_day;
	}

	public Integer getCbAfft1_11()
	{
		return cbAfft1_11;
	}

	public void setCbAfft1_11( Integer cbAfft1_11 )
	{
		this.cbAfft1_11 = cbAfft1_11;
	}

	public Integer getCbAfft1_12()
	{
		return cbAfft1_12;
	}

	public void setCbAfft1_12( Integer cbAfft1_12 )
	{
		this.cbAfft1_12 = cbAfft1_12;
	}

	public Integer getCbAfft1_13()
	{
		return cbAfft1_13;
	}

	public void setCbAfft1_13( Integer cbAfft1_13 )
	{
		this.cbAfft1_13 = cbAfft1_13;
	}

	public Integer getCbAfft1_14()
	{
		return cbAfft1_14;
	}

	public void setCbAfft1_14( Integer cbAfft1_14 )
	{
		this.cbAfft1_14 = cbAfft1_14;
	}

	public Integer getCbAfft1_15()
	{
		return cbAfft1_15;
	}

	public void setCbAfft1_15( Integer cbAfft1_15 )
	{
		this.cbAfft1_15 = cbAfft1_15;
	}

	public String getCbAfft1_16()
	{
		return cbAfft1_16;
	}

	public void setCbAfft1_16( String cbAfft1_16 )
	{
		this.cbAfft1_16 = cbAfft1_16;
	}

	public String getCbAfft1_10_no()
	{
		return cbAfft1_10_no;
	}

	public void setCbAfft1_10_no( String cbAfft1_10_no )
	{
		this.cbAfft1_10_no = cbAfft1_10_no;
	}
}
