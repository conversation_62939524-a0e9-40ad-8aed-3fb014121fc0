package com.megabank.olp.apply.persistence.bean.mixed;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class HousePricingListGetterParamBean extends BaseBean
{
	private Long branchBankId;

	private Integer notified;

	private String processCode;

	private String email;

	private String mobileNumber;

	private Date createdDateStart;

	private Date createdDateEnd;

	public HousePricingListGetterParamBean()
	{
		// default constructor
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public Date getCreatedDateEnd()
	{
		return createdDateEnd;
	}

	public Date getCreatedDateStart()
	{
		return createdDateStart;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public Integer getNotified()
	{
		return notified;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setCreatedDateEnd( Date createdDateEnd )
	{
		this.createdDateEnd = createdDateEnd;
	}

	public void setCreatedDateStart( Date createdDateStart )
	{
		this.createdDateStart = createdDateStart;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setNotified( Integer notified )
	{
		this.notified = notified;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

}
