package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContentCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContentUpdatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.address.ApplyAddressDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeCaseSourceDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeGracePeriodDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanPeriodDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanPurposeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeMortgageTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeNonPrivateUsageTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeNotificationDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodePrivateUsageTypeDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContent;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanContentDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanContentDAO extends BasePojoDAO<ApplyLoanContent, Long>
{
	@Autowired
	private ApplyAddressDAO applyAddressDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private CodeGracePeriodDAO codeGracePeriodDAO;

	@Autowired
	private CodeLoanPurposeDAO codeLoanPurposeDAO;

	@Autowired
	private CodeLoanPeriodDAO codeLoanPeriodDAO;

	@Autowired
	private CodeMortgageTypeDAO codeMortgageTypeDAO;

	@Autowired
	private CodeNonPrivateUsageTypeDAO codeNonPrivateUsageTypeDAO;

	@Autowired
	private CodePrivateUsageTypeDAO codePrivateUsageTypeDAO;

	@Autowired
	private CodeNotificationDAO codeNotificationDAO;

	@Autowired
	private CodeCaseSourceDAO codeCaseSourceDAO;

	public Long create( ApplyLoanContentCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notNull( paramBean.getLoanRequestAmt() );
		Validate.notNull( paramBean.getLoanPurposeId() );
		Validate.notBlank( paramBean.getLoanPeriod() );
		Validate.notBlank( paramBean.getNotificationCode() );

		ApplyLoanContent pojo = new ApplyLoanContent();
		pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );
		pojo.setCodeLoanPeriod( codeLoanPeriodDAO.read( paramBean.getLoanPeriod() ) );
		pojo.setCodeLoanPurpose( codeLoanPurposeDAO.read( paramBean.getLoanPurposeId() ) );
		pojo.setLoanRequestAmt( paramBean.getLoanRequestAmt() );
		pojo.setOtherPurpose( paramBean.getOtherPurpose() );
		pojo.setCodeNotification( codeNotificationDAO.read( paramBean.getNotificationCode() ) );
		pojo.setCodeGracePeriod( StringUtils.isBlank( paramBean.getGracePeriodCode() ) ? null
																					   : codeGracePeriodDAO.read( paramBean.getGracePeriodCode() ) );
		pojo.setApplyAddress( paramBean.getCollateralAddressId() == null ? null : applyAddressDAO.read( paramBean.getCollateralAddressId() ) );
		pojo.setCodeMortgageType( StringUtils.isBlank( paramBean.getMortgageType() ) ? null
																					 : codeMortgageTypeDAO.read( paramBean.getMortgageType() ) );
		pojo.setCodeNonPrivateUsageType( StringUtils
					.isBlank( paramBean.getNonPrivateUsageType() ) ? null : codeNonPrivateUsageTypeDAO.read( paramBean.getNonPrivateUsageType() ) );
		pojo.setNonPrivateUsageSubType( paramBean.getNonPrivateUsageSubType() );
		pojo.setCodePrivateUsageType( StringUtils
					.isBlank( paramBean.getPrivateUsageType() ) ? null : codePrivateUsageTypeDAO.read( paramBean.getPrivateUsageType() ) );
		pojo.setIncreasingLoan( paramBean.getIsIncreasingLoan() );
		pojo.setAppnBankCode( paramBean.getAppnBankCode() );
		pojo.setAppnDpAcct( paramBean.getAppnDpAcct() );
		pojo.setCodeCaseSource( StringUtils.isBlank( paramBean.getCaseSourceCode() ) ? null
																					 : codeCaseSourceDAO.read( paramBean.getCaseSourceCode() ) );
		pojo.setUrlToIdentifyFraud( paramBean.getUrlToIdentifyFraud() );

		return super.createPojo( pojo );
	}

	public ApplyLoanContent read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, ApplyLoanContent.TABLENAME_CONSTANT );
	}

	public ApplyLoanContent readToNull( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId );
	}

	public Long update( ApplyLoanContentUpdatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notNull( paramBean.getLoanRequestAmt() );
		Validate.notNull( paramBean.getLoanPurposeId() );
		Validate.notBlank( paramBean.getLoanPeriod() );
		Validate.notBlank( paramBean.getNotificationCode() );

		ApplyLoanContent pojo = read( paramBean.getLoanId() );
		pojo.setLoanRequestAmt( paramBean.getLoanRequestAmt() );
		pojo.setCodeLoanPurpose( codeLoanPurposeDAO.read( paramBean.getLoanPurposeId() ) );
		pojo.setOtherPurpose( paramBean.getOtherPurpose() );
		pojo.setCodeLoanPeriod( codeLoanPeriodDAO.read( paramBean.getLoanPeriod() ) );
		pojo.setCodeNotification( codeNotificationDAO.read( paramBean.getNotificationCode() ) );
		pojo.setCodeGracePeriod( StringUtils.isBlank( paramBean.getGracePeriodCode() ) ? null
																					   : codeGracePeriodDAO.read( paramBean.getGracePeriodCode() ) );
		pojo.setApplyAddress( paramBean.getCollateralAddressId() == null ? null : applyAddressDAO.read( paramBean.getCollateralAddressId() ) );
		pojo.setCodeMortgageType( StringUtils.isBlank( paramBean.getMortgageType() ) ? null
																					 : codeMortgageTypeDAO.read( paramBean.getMortgageType() ) );
		pojo.setCodeNonPrivateUsageType( StringUtils
					.isBlank( paramBean.getNonPrivateUsageType() ) ? null : codeNonPrivateUsageTypeDAO.read( paramBean.getNonPrivateUsageType() ) );
		pojo.setNonPrivateUsageSubType( paramBean.getNonPrivateUsageSubType() );
		pojo.setCodePrivateUsageType( StringUtils
					.isBlank( paramBean.getPrivateUsageType() ) ? null : codePrivateUsageTypeDAO.read( paramBean.getPrivateUsageType() ) );
		pojo.setIncreasingLoan( paramBean.getIsIncreasingLoan() );
		pojo.setAppnBankCode( paramBean.getAppnBankCode() );
		pojo.setAppnDpAcct( paramBean.getAppnDpAcct() );
		pojo.setCodeCaseSource( StringUtils.isBlank( paramBean.getCaseSourceCode() ) ? null
																					 : codeCaseSourceDAO.read( paramBean.getCaseSourceCode() ) );
		pojo.setUrlToIdentifyFraud( paramBean.getUrlToIdentifyFraud() );
		return pojo.getLoanId();
	}

	@Override
	protected Class<ApplyLoanContent> getPojoClass()
	{
		return ApplyLoanContent.class;
	}
}
