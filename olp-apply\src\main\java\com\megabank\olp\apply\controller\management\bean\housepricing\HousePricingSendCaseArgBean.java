/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.housepricing;

import java.util.Date;

import javax.validation.Valid;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HousePricingSendCaseArgBean extends BaseBean
{
	private String caseNo;

	private String mobileNumber;

	private String email;

	private String branchBankCode;

	private Date createdDate;

	@Valid
	private HouseInfoBean houseInfo;

	@Valid
	private HouseEstimateInfoBean estimateInfo;

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getEmail()
	{
		return email;
	}

	public HouseEstimateInfoBean getEstimateInfo()
	{
		return estimateInfo;
	}

	public HouseInfoBean getHouseInfo()
	{
		return houseInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setEstimateInfo( HouseEstimateInfoBean estimateInfo )
	{
		this.estimateInfo = estimateInfo;
	}

	public void setHouseInfo( HouseInfoBean houseInfo )
	{
		this.houseInfo = houseInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}
}
