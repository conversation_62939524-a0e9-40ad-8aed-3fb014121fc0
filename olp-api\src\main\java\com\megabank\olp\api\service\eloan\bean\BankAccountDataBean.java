package com.megabank.olp.api.service.eloan.bean;

import com.megabank.olp.base.bean.BaseBean;

public class BankAccountDataBean extends BaseBean
{
	private String bankCode;

	private String account;

	public BankAccountDataBean()
	{}

	public String getAccount()
	{
		return account;
	}

	public String getBankCode()
	{
		return bankCode;
	}

	public void setAccount( String account )
	{
		this.account = account;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}
}
