package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.List;

import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;


import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class BranchBankDAO extends BaseDAO
{

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String HEAD_OFFICE_CONSTANT = "headOffice";

	public List<CodeBranchBank> getBranchList( String loanType )
	{
		return getList( loanType, false );
	}

	public List<CodeBranchBank> getList( String loanType, Boolean headOffice )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "branchbank.getList" );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( HEAD_OFFICE_CONSTANT, headOffice, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeBranchBank.class );

		return nativeQuery.getResultList();
	}

}
