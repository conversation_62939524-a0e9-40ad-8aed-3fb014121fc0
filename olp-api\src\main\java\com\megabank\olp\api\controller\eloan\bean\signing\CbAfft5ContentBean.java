package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

public class CbAfft5ContentBean extends BaseBean
{
	private String cbAfft5_1;

	private String cbAfft5_2;

	private String cbAfft5_3_year;

	private String cbAfft5_3_mth;

	private String cbAfft5_3_day;

	private Integer cbAfft5_4;

	private List<String> cbAfft5_5;

	private BigDecimal cbAfft5_6;

	private Integer cbAfft5_7;

	private Integer cbAfft5_8;

	private Integer cbAfft5_9;

	private Integer cbAfft5_10;

	private Integer cbAfft5_11;

	private String cbAfft5_12;

	private BigDecimal cbAfft5_13;

	private BigDecimal cbAfft5_14;

	private BigDecimal cbAfft5_15;

	public CbAfft5ContentBean()
	{}

	public String getCbAfft5_1()
	{
		return cbAfft5_1;
	}

	public void setCbAfft5_1( String cbAfft5_1 )
	{
		this.cbAfft5_1 = cbAfft5_1;
	}

	public String getCbAfft5_2()
	{
		return cbAfft5_2;
	}

	public void setCbAfft5_2( String cbAfft5_2 )
	{
		this.cbAfft5_2 = cbAfft5_2;
	}

	public String getCbAfft5_3_year()
	{
		return cbAfft5_3_year;
	}

	public void setCbAfft5_3_year( String cbAfft5_3_year )
	{
		this.cbAfft5_3_year = cbAfft5_3_year;
	}

	public String getCbAfft5_3_mth()
	{
		return cbAfft5_3_mth;
	}

	public void setCbAfft5_3_mth( String cbAfft5_3_mth )
	{
		this.cbAfft5_3_mth = cbAfft5_3_mth;
	}

	public String getCbAfft5_3_day()
	{
		return cbAfft5_3_day;
	}

	public void setCbAfft5_3_day( String cbAfft5_3_day )
	{
		this.cbAfft5_3_day = cbAfft5_3_day;
	}

	public Integer getCbAfft5_4()
	{
		return cbAfft5_4;
	}

	public void setCbAfft5_4( Integer cbAfft5_4 )
	{
		this.cbAfft5_4 = cbAfft5_4;
	}

	public List<String> getCbAfft5_5()
	{
		return cbAfft5_5;
	}

	public void setCbAfft5_5( List<String> cbAfft5_5 )
	{
		this.cbAfft5_5 = cbAfft5_5;
	}

	public BigDecimal getCbAfft5_6()
	{
		return cbAfft5_6;
	}

	public void setCbAfft5_6( BigDecimal cbAfft5_6 )
	{
		this.cbAfft5_6 = cbAfft5_6;
	}

	public Integer getCbAfft5_7()
	{
		return cbAfft5_7;
	}

	public void setCbAfft5_7( Integer cbAfft5_7 )
	{
		this.cbAfft5_7 = cbAfft5_7;
	}

	public Integer getCbAfft5_8()
	{
		return cbAfft5_8;
	}

	public void setCbAfft5_8( Integer cbAfft5_8 )
	{
		this.cbAfft5_8 = cbAfft5_8;
	}

	public Integer getCbAfft5_9()
	{
		return cbAfft5_9;
	}

	public void setCbAfft5_9( Integer cbAfft5_9 )
	{
		this.cbAfft5_9 = cbAfft5_9;
	}

	public Integer getCbAfft5_10()
	{
		return cbAfft5_10;
	}

	public void setCbAfft5_10( Integer cbAfft5_10 )
	{
		this.cbAfft5_10 = cbAfft5_10;
	}

	public Integer getCbAfft5_11()
	{
		return cbAfft5_11;
	}

	public void setCbAfft5_11( Integer cbAfft5_11 )
	{
		this.cbAfft5_11 = cbAfft5_11;
	}

	public String getCbAfft5_12()
	{
		return cbAfft5_12;
	}

	public void setCbAfft5_12( String cbAfft5_12 )
	{
		this.cbAfft5_12 = cbAfft5_12;
	}

	public BigDecimal getCbAfft5_13()
	{
		return cbAfft5_13;
	}

	public void setCbAfft5_13( BigDecimal cbAfft5_13 )
	{
		this.cbAfft5_13 = cbAfft5_13;
	}

	public BigDecimal getCbAfft5_14()
	{
		return cbAfft5_14;
	}

	public void setCbAfft5_14( BigDecimal cbAfft5_14 )
	{
		this.cbAfft5_14 = cbAfft5_14;
	}

	public BigDecimal getCbAfft5_15()
	{
		return cbAfft5_15;
	}

	public void setCbAfft5_15( BigDecimal cbAfft5_15 )
	{
		this.cbAfft5_15 = cbAfft5_15;
	}
}
