/**
 *
 */
package com.megabank.olp.api.service;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.api.service.estimation.HouseLoanService;
import com.megabank.olp.api.service.estimation.bean.CalculateBasicDataBean;
import com.megabank.olp.api.service.estimation.bean.CalculateLoanDataBean;
import com.megabank.olp.api.service.estimation.bean.ContactMeBasicDataBean;
import com.megabank.olp.api.service.estimation.bean.ContactMeLoanDataBean;
import com.megabank.olp.api.service.estimation.bean.EvaluateEstimateDataBean;
import com.megabank.olp.api.service.estimation.bean.EvaluateHouseDataBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanCalculateParamBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanContactMeParamBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanEvaluateParamBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@SpringBootTest
@ContextConfiguration( classes = ApiConfig.class )
public class HouseLoanServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private HouseLoanService service;

	@Test
	public void createHouseContact()
	{
		HouseLoanContactMeParamBean paramBean = new HouseLoanContactMeParamBean();
		paramBean.setCaseID( "apiTestHoCont001" );
		paramBean.setBranchBankCode( "007" );
		paramBean.setCreatedDate( new Date() );
		paramBean.setMobileNumber( "**********" );

		ContactMeBasicDataBean basicInfo = new ContactMeBasicDataBean();
		basicInfo.setcName( "伊靶炤" );
		basicInfo.setCallBackTime( "09:00~12:00" );
		paramBean.setBasicInfo( basicInfo );

		ContactMeLoanDataBean loanInfo = new ContactMeLoanDataBean();
		loanInfo.setLoanBalance( new BigDecimal( 1778.3 ) );
		loanInfo.setCounty( "台北市" );
		loanInfo.setDistrict( "大安區" );
		loanInfo.setAddress( "信義區三段999號" );
		loanInfo.setLoanPurpose( "我要以房養老" );
		paramBean.setLoanInfo( loanInfo );

		// logger.info( "paramBean:{}", paramBean );

		Long result = service.contactMe( paramBean );

		logger.info( "result:{}", result );
	}

	@Test
	public void createHouseLoanTrial()
	{
		HouseLoanCalculateParamBean paramBean = new HouseLoanCalculateParamBean();
		paramBean.setCaseID( "apiTestHoTr001" );
		paramBean.setBranchBankCode( "007" );
		paramBean.setCreatedDate( new Date() );
		paramBean.setMobileNumber( "**********" );

		CalculateBasicDataBean basicInfo = new CalculateBasicDataBean();
		basicInfo.setUserAge( 46 );
		basicInfo.setUserChildren( 0 );
		basicInfo.setUserJob( "自由業" );
		basicInfo.setUserTitle( "一般主管" );
		basicInfo.setUserIncome( 180 );
		basicInfo.setLoanPurpose( "0" );
		basicInfo.setBuyHouseFrom( "0" );
		basicInfo.setTotalPrice( new BigDecimal( 2431 ) );
		basicInfo.setCr3ditCard( "0" );
		basicInfo.setPay( "0" );
		basicInfo.setBorrow( "0" );
		basicInfo.setCashCard( "1" );
		basicInfo.setCashCardBalance( "0" );
		basicInfo.setBalance( "0" );
		paramBean.setBasicInfo( basicInfo );

		CalculateLoanDataBean loanInfo = new CalculateLoanDataBean();
		loanInfo.setRate( new BigDecimal( 1.46 ) );
		loanInfo.setTopLoanCredit( new BigDecimal( 1980 ) );
		paramBean.setLoanInfo( loanInfo );

		// logger.info( "paramBean:{}", paramBean );

		Long result = service.calculate( paramBean );

		// logger.info( "result:{}", result );
	}

	@Test
	public void createHousePricing()
	{
		HouseLoanEvaluateParamBean paramBean = new HouseLoanEvaluateParamBean();
		paramBean.setCaseID( "apiTestHoPri001" );
		paramBean.setBranchBankCode( "007" );
		paramBean.setCreatedDate( new Date() );
		paramBean.setEmail( "<EMAIL>" );

		EvaluateHouseDataBean houseInfo = new EvaluateHouseDataBean();
		houseInfo.setCounty( "新北市" );
		houseInfo.setDistrict( "鶯歌區" );
		houseInfo.setAddr( "中山路一段222號" );
		houseInfo.setbTypeInt( "2" );
		houseInfo.setbAge( 5 );
		houseInfo.setbAreaP( new BigDecimal( 55.6 ) );
		houseInfo.setFloors( 5 );
		houseInfo.setParking( "3" );
		houseInfo.setParkingGTY( 3 );
		houseInfo.setParkingP( new BigDecimal( 10.1 ) );
		paramBean.setHouseInfo( houseInfo );

		EvaluateEstimateDataBean estimateInfo = new EvaluateEstimateDataBean();
		estimateInfo.setAv750( new BigDecimal( 54.1 ) );
		paramBean.setEstimateInfo( estimateInfo );

		// logger.info( "paramBean:{}", paramBean );

		Long result = service.evaluate( paramBean );

		logger.info( "result:{}", result );
	}

}
