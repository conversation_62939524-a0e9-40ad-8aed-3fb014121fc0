package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.survey.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.survey.LoanSurveyDetailGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.survey.LoanSurveyExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.survey.LoanSurveyListedArgBean;
import com.megabank.olp.apply.controller.management.bean.survey.ProcessStatusUpdatedArgBean;
import com.megabank.olp.apply.service.management.LoanSurveyService;
import com.megabank.olp.apply.service.management.bean.survey.LoanSurveyExportedParamBean;
import com.megabank.olp.apply.service.management.bean.survey.LoanSurveyListedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/survey" )
public class LoanSurveyController extends BaseController
{
	@Autowired
	private LoanSurveyService loanSurveyService;

	/**
	 * 信貸試算案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportList" )
	public Map<String, Object> exportLoanSurvey( @RequestBody @Validated LoanSurveyExportedArgBean argBean )
	{
		LoanSurveyExportedParamBean paramBean = new LoanSurveyExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setContactTimeCode( argBean.getContactTimeCode() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( loanSurveyService.exportList( paramBean ) );
	}

	/**
	 * 取得信貸試算案件詳細內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getDetail" )
	public Map<String, Object> getLoanSurveyDetail( @RequestBody @Validated LoanSurveyDetailGetterArgBean argBean )
	{
		Long surveyId = argBean.getSurveyId();

		return getResponseMap( loanSurveyService.getLoanSurveyDetail( surveyId ) );
	}

	/**
	 * 取得改派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getReassignBranchBank" )
	public Map<String, Object> getReassignBranchBank( @RequestBody @Validated LoanSurveyDetailGetterArgBean argBean )
	{
		Long surveyId = argBean.getSurveyId();

		return getResponseMap( loanSurveyService.getReassignBranchBank( surveyId ) );
	}

	/**
	 * 取得信貸試算案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "list" )
	public Map<String, Object> listLoanSurvey( @RequestBody @Validated LoanSurveyListedArgBean argBean )
	{
		LoanSurveyListedParamBean paramBean = new LoanSurveyListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setContactTimeCode( argBean.getContactTimeCode() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( loanSurveyService.listLoanSurvey( paramBean ) );

	}

	/**
	 * 更改案件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		Long surveyId = argBean.getSurveyId();
		Long branchBankId = argBean.getBranchBankId();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanSurveyService.updateBranchBank( surveyId, branchBankId, employeeId, employeeName ) );
	}

	/**
	 * 更新案件處理狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateProcessStatus" )
	public Map<String, Object> updateProcessStatus( @RequestBody @Validated ProcessStatusUpdatedArgBean argBean )
	{
		Long surveyId = argBean.getSurveyId();
		String processStatus = argBean.getProcessStatus();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanSurveyService.updateProcessStatus( surveyId, processStatus, employeeId, employeeName ) );
	}

}
