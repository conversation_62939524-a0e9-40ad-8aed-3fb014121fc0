package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.dto.UploadFileDTO;
import com.megabank.olp.base.enums.AttachTypeEnum;
import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class TempDAO extends BaseDAO
{
	private static final String LOAN_ID_CONSTANT = "loanId";

	private static final String PROCESSED_CONSTANT = "processed";

	private static final String SINGLE_ATTACHMENT_TYPE_CONSTANT = "singleAttchmentType";

	private static final String MULTIPLE_ATTACHMENT_TYPE_CONSTANT = "multipleAttchmentType";

	public List<UploadFileDTO> getUploadFiles( Long loanId )
	{
		Validate.notNull( loanId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "temp.getUploadFiles" );
		nativeQuery.setParameter( LOAN_ID_CONSTANT, loanId, Long.class );
		nativeQuery.setParameter( PROCESSED_CONSTANT, true, Boolean.class );
		nativeQuery.setParameterList( SINGLE_ATTACHMENT_TYPE_CONSTANT,
									  Arrays.asList( AttachTypeEnum.FRONT.getContext(), AttachTypeEnum.BACK.getContext() ), String.class );
		nativeQuery.setParameterList( MULTIPLE_ATTACHMENT_TYPE_CONSTANT,
									  Arrays.asList( AttachTypeEnum.PROOF.getContext(), AttachTypeEnum.OTHER.getContext(),
													 AttachTypeEnum.SECONDID.getContext(), AttachTypeEnum.EMPID.getContext(),
													 AttachTypeEnum.WITHHOLDING.getContext(), AttachTypeEnum.PAYROLL.getContext() ),
										String.class);

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( UploadFileDTO.class ) );

		return nativeQuery.getResultList();
	}

	public List<UploadFileDTO> getYouthStartUpUploadFiles( Long loanId )
	{
		Validate.notNull( loanId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "temp.getYouthStartUpUploadFiles" );
		nativeQuery.setParameter( LOAN_ID_CONSTANT, loanId, Long.class );
		nativeQuery.setParameter( PROCESSED_CONSTANT, true, Boolean.class );
		nativeQuery.setParameterList( SINGLE_ATTACHMENT_TYPE_CONSTANT,
				Arrays.asList( AttachTypeEnum.FRONT.getContext(), AttachTypeEnum.BACK.getContext() ), String.class );
		nativeQuery.setParameterList( MULTIPLE_ATTACHMENT_TYPE_CONSTANT,
				Arrays.asList( AttachTypeEnum.PROOF.getContext(), AttachTypeEnum.OTHER.getContext(),
						AttachTypeEnum.SECONDID.getContext(), AttachTypeEnum.EMPID.getContext(),
						AttachTypeEnum.WITHHOLDING.getContext(), AttachTypeEnum.PAYROLL.getContext() ),
				String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( UploadFileDTO.class ) );

		return nativeQuery.getResultList();
	}

}
