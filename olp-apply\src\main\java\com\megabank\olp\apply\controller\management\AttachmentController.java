package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.attachment.AttachmentDownloadedArgBean;
import com.megabank.olp.apply.controller.management.bean.attachment.AttachmentListedArgBean;
import com.megabank.olp.apply.controller.management.bean.attachment.TransmissionStatusUpdatedArgBean;
import com.megabank.olp.apply.service.loan.DownloadService;
import com.megabank.olp.apply.service.management.AttachmentService;
import com.megabank.olp.apply.service.management.bean.attachment.AttachmentListedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/attachment" )
public class AttachmentController extends BaseController
{
	@Autowired
	private AttachmentService attachmentService;

	@Autowired
	private DownloadService downloadService;

	/**
	 * 下載檔案
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "downloadAttachment" )
	public Map<String, Object> downloadAttachment( @RequestBody @Validated AttachmentDownloadedArgBean argBean )
	{
		Long attachmentId = argBean.getAttachmentId();

		return getResponseMap( downloadService.downloadAttachment( attachmentId ) );

	}

	/**
	 * 取得房貸上傳補件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "listHouseLoanAttachment" )
	public Map<String, Object> listHouseLoanAttachment( @RequestBody @Validated AttachmentListedArgBean argBean )
	{
		AttachmentListedParamBean paramBean = new AttachmentListedParamBean();
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setBirthDate( argBean.getBirthDate() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setCaseNo( argBean.getCaseNo() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );

		return getResponseMap( attachmentService.listHouseLoanAttachment( paramBean ) );

	}

	/**
	 * 取得信貸上傳補件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "listPersonalLoanAttachment" )
	public Map<String, Object> listPersonalLoanAttachment( @RequestBody @Validated AttachmentListedArgBean argBean )
	{
		AttachmentListedParamBean paramBean = new AttachmentListedParamBean();
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setBirthDate( argBean.getBirthDate() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setCaseNo( argBean.getCaseNo() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );

		return getResponseMap( attachmentService.listPersonalLoanAttachment( paramBean ) );

	}

	/**
	 * 更新檔案進件狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateTransmissionStatus" )
	public Map<String, Object> updateTransmissionStatus( @RequestBody @Validated TransmissionStatusUpdatedArgBean argBean )
	{
		Long attachmentId = argBean.getAttachmentId();
		String transmissionStatus = argBean.getTransmissionStatusUpdatedEnum().getContext();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( attachmentService.updateTransmissionStatus( attachmentId, transmissionStatus, employeeId, employeeName ) );
	}
}
