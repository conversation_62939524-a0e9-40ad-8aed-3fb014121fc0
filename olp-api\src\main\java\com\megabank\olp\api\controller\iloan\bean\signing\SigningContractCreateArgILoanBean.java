package com.megabank.olp.api.controller.iloan.bean.signing;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.api.controller.eloan.bean.signing.BankAccountInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.GuaranteeInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.RateDataBean;
import com.megabank.olp.api.controller.eloan.bean.signing.RepaymentBean;
import com.megabank.olp.base.bean.BaseBean;

public class SigningContractCreateArgILoanBean extends BaseBean
{
	@NotBlank
	private String contractNo;

	@NotBlank
	private String contractVersion;

	@NotBlank
	private String courtName;

	@NotBlank
	private String branchCode;

	@NotBlank
	private String borrowerName;

	@NotBlank
	private String borrowerId;

	@NotNull
	private Date borrowerBirthDate;

	@NotBlank
	@JsonProperty( "borrowerCellNumber" )
	private String borrowerMobileNumber;

	@NotBlank
	private String borrowerEmail;

	private String isBorrowerYouth;

	private String relatedPersonType;

	private String relatedPersonName;

	private String relatedPersonId;

	private Date relatedPersonBirthDate;

	private String relatedPersonMobileNumber;

	private String relatedPersonEmail;

	@NotBlank
	private String productCode;

	@NotNull
	private Date expiredDate;

	@JsonProperty( "loanConditionInfo" )
	@NotNull
	@Valid
	private LoanConditionInfoIloanBean loanConditionInfoIloanBean;

	@JsonProperty( "guaranteeInfo" )
	@NotNull
	@Valid
	private GuaranteeInfoBean guaranteeInfoBean;

	@JsonProperty( "loanAcct" )
	private List<BankAccountInfoBean> bankAccountInfoBeans;

	private String loanPlan;

	private String grpCntrNo;

	private Date givenApprBegDate;

	private Date givenApprEndDate;

	private String payeeBankCode;

	private String payeeBankAccountNo;

	private String payeeBankAccountName;

	private Integer payeeTotalAmt;

	private Integer payeeRemittance;

	private Integer payeeSelfProvide;

	private BigDecimal baseRate;

	private List<RateDataBean> rateList;

	@NotBlank
	private String isRepayment;

	@NotNull
	private List<RepaymentBean> repaymentList;

	public SigningContractCreateArgILoanBean()
	{}

	public List<BankAccountInfoBean> getBankAccountInfoBeans()
	{
		return bankAccountInfoBeans;
	}

	public BigDecimal getBaseRate()
	{
		return baseRate;
	}

	public Date getBorrowerBirthDate()
	{
		return borrowerBirthDate;
	}

	public String getBorrowerEmail()
	{
		return borrowerEmail;
	}

	public String getBorrowerId()
	{
		return borrowerId;
	}

	public String getBorrowerMobileNumber()
	{
		return borrowerMobileNumber;
	}

	public String getBorrowerName()
	{
		return borrowerName;
	}

	public String getBranchCode()
	{
		return branchCode;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getContractVersion()
	{
		return contractVersion;
	}

	public String getCourtName()
	{
		return courtName;
	}

	public Date getExpiredDate()
	{
		return expiredDate;
	}

	public Date getGivenApprBegDate()
	{
		return givenApprBegDate;
	}

	public Date getGivenApprEndDate()
	{
		return givenApprEndDate;
	}

	public String getGrpCntrNo()
	{
		return grpCntrNo;
	}

	public GuaranteeInfoBean getGuaranteeInfoBean()
	{
		return guaranteeInfoBean;
	}

	public String getIsBorrowerYouth()
	{
		return isBorrowerYouth;
	}

	public String getIsRepayment()
	{
		return isRepayment;
	}

	public LoanConditionInfoIloanBean getLoanConditionInfoIloanBean()
	{
		return loanConditionInfoIloanBean;
	}

	public String getLoanPlan()
	{
		return loanPlan;
	}

	public String getPayeeBankAccountName()
	{
		return payeeBankAccountName;
	}

	public String getPayeeBankAccountNo()
	{
		return payeeBankAccountNo;
	}

	public String getPayeeBankCode()
	{
		return payeeBankCode;
	}

	public Integer getPayeeRemittance()
	{
		return payeeRemittance;
	}

	public Integer getPayeeSelfProvide()
	{
		return payeeSelfProvide;
	}

	public Integer getPayeeTotalAmt()
	{
		return payeeTotalAmt;
	}

	public String getProductCode()
	{
		return productCode;
	}

	public List<RateDataBean> getRateList()
	{
		return rateList;
	}

	public Date getRelatedPersonBirthDate()
	{
		return relatedPersonBirthDate;
	}

	public String getRelatedPersonEmail()
	{
		return relatedPersonEmail;
	}

	public String getRelatedPersonId()
	{
		return relatedPersonId;
	}

	public String getRelatedPersonMobileNumber()
	{
		return relatedPersonMobileNumber;
	}

	public String getRelatedPersonName()
	{
		return relatedPersonName;
	}

	public String getRelatedPersonType()
	{
		return relatedPersonType;
	}

	public List<RepaymentBean> getRepaymentList()
	{
		return repaymentList;
	}

	public void setBankAccountInfoBeans( List<BankAccountInfoBean> bankAccountInfoBeans )
	{
		this.bankAccountInfoBeans = bankAccountInfoBeans;
	}

	public void setBaseRate( BigDecimal baseRate )
	{
		this.baseRate = baseRate;
	}

	public void setBorrowerBirthDate( Date borrowerBirthDate )
	{
		this.borrowerBirthDate = borrowerBirthDate;
	}

	public void setBorrowerEmail( String borrowerEmail )
	{
		this.borrowerEmail = borrowerEmail;
	}

	public void setBorrowerId( String borrowerId )
	{
		this.borrowerId = borrowerId;
	}

	public void setBorrowerMobileNumber( String borrowerMobileNumber )
	{
		this.borrowerMobileNumber = borrowerMobileNumber;
	}

	public void setBorrowerName( String borrowerName )
	{
		this.borrowerName = borrowerName;
	}

	public void setBranchCode( String branchCode )
	{
		this.branchCode = branchCode;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setContractVersion( String contractVersion )
	{
		this.contractVersion = contractVersion;
	}

	public void setCourtName( String courtName )
	{
		this.courtName = courtName;
	}

	public void setExpiredDate( Date expiredDate )
	{
		this.expiredDate = expiredDate;
	}

	public void setGivenApprBegDate( Date givenApprBegDate )
	{
		this.givenApprBegDate = givenApprBegDate;
	}

	public void setGivenApprEndDate( Date givenApprEndDate )
	{
		this.givenApprEndDate = givenApprEndDate;
	}

	public void setGrpCntrNo( String grpCntrNo )
	{
		this.grpCntrNo = grpCntrNo;
	}

	public void setGuaranteeInfoBean( GuaranteeInfoBean guaranteeInfoBean )
	{
		this.guaranteeInfoBean = guaranteeInfoBean;
	}

	public void setIsBorrowerYouth( String isBorrowerYouth )
	{
		this.isBorrowerYouth = isBorrowerYouth;
	}

	public void setIsRepayment( String isRepayment )
	{
		this.isRepayment = isRepayment;
	}

	public void setLoanConditionInfoIloanBean( LoanConditionInfoIloanBean loanConditionInfoIloanBean )
	{
		this.loanConditionInfoIloanBean = loanConditionInfoIloanBean;
	}

	public void setLoanPlan( String loanPlan )
	{
		this.loanPlan = loanPlan;
	}

	public void setPayeeBankAccountName( String payeeBankAccountName )
	{
		this.payeeBankAccountName = payeeBankAccountName;
	}

	public void setPayeeBankAccountNo( String payeeBankAccountNo )
	{
		this.payeeBankAccountNo = payeeBankAccountNo;
	}

	public void setPayeeBankCode( String payeeBankCode )
	{
		this.payeeBankCode = payeeBankCode;
	}

	public void setPayeeRemittance( Integer payeeRemittance )
	{
		this.payeeRemittance = payeeRemittance;
	}

	public void setPayeeSelfProvide( Integer payeeSelfProvide )
	{
		this.payeeSelfProvide = payeeSelfProvide;
	}

	public void setPayeeTotalAmt( Integer payeeTotalAmt )
	{
		this.payeeTotalAmt = payeeTotalAmt;
	}

	public void setProductCode( String productCode )
	{
		this.productCode = productCode;
	}

	public void setRateList( List<RateDataBean> rateList )
	{
		this.rateList = rateList;
	}

	public void setRelatedPersonBirthDate( Date relatedPersonBirthDate )
	{
		this.relatedPersonBirthDate = relatedPersonBirthDate;
	}

	public void setRelatedPersonEmail( String relatedPersonEmail )
	{
		this.relatedPersonEmail = relatedPersonEmail;
	}

	public void setRelatedPersonId( String relatedPersonId )
	{
		this.relatedPersonId = relatedPersonId;
	}

	public void setRelatedPersonMobileNumber( String relatedPersonMobileNumber )
	{
		this.relatedPersonMobileNumber = relatedPersonMobileNumber;
	}

	public void setRelatedPersonName( String relatedPersonName )
	{
		this.relatedPersonName = relatedPersonName;
	}

	public void setRelatedPersonType( String relatedPersonType )
	{
		this.relatedPersonType = relatedPersonType;
	}

	public void setRepamentList( List<RepaymentBean> repaymentList )
	{
		this.repaymentList = repaymentList;
	}
}
