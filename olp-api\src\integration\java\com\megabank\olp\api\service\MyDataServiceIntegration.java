package com.megabank.olp.api.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.api.service.mydata.MyDataService;
import com.megabank.olp.system.config.SystemConfig;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@SpringBootTest
@ContextConfiguration( classes = { ApiConfig.class, SystemConfig.class } )
public class MyDataServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private MyDataService service;

	@Test
	public void notifyServlet()
	{
		String txId = "bed8a2d5-30fe-42a2-9257-62dbf2f9a7a1";
		String idNo = "A123456789";
		int waitSec = 60;

		service.notifyServlet( txId, idNo, waitSec );
	}

}
