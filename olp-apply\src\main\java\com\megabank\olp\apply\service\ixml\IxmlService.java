package com.megabank.olp.apply.service.ixml;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.megabank.olp.apply.controller.ixml.bean.DoResBean;
import com.megabank.olp.apply.persistence.bean.generated.ixml.IxmlApplyRecordCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.ixml.applyRecord.IxmlApplyRecordDAO;
import com.megabank.olp.apply.persistence.dao.generated.ixml.signatures.IxmlSignaturesDAO;
import com.megabank.olp.apply.persistence.dao.mixed.LoanDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanBasic;
import com.megabank.olp.apply.persistence.pojo.ixml.applyRecord.IxmlApplyRecord;
import com.megabank.olp.apply.service.ixml.bean.IxmlCertResBean;
import com.megabank.olp.apply.service.ixml.bean.IxmlParamsBean;
import com.megabank.olp.apply.service.ixml.bean.IxmlResBean;
import com.megabank.olp.apply.service.loan.DeliverService;
import com.megabank.olp.apply.service.loan.UploadService;
import com.megabank.olp.apply.utility.BaseApplyService;
import com.megabank.olp.apply.utility.EncryptorUtils;
import com.megabank.olp.apply.utility.enums.ApplyErrorEnum;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.bean.threadlocal.RequestInfoThreadLocalBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.enums.UserTypeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.service.jwt.decrypt.JwtService;
import com.megabank.olp.base.service.jwt.encrypt.BaseIdentityJwtService;
import com.megabank.olp.base.service.jwt.encrypt.StrongIdentityJwtService;
import com.megabank.olp.base.threadlocal.RequestInfoThreadLocal;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.client.sender.ixml.doCallback.bean.IxmlDoCallbackArgBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginArgBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginInputParamsBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginJWSInputParamsBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginResBean;
import com.megabank.olp.client.sender.ixml.querycert.bean.IxmlQueryCertArgBean;
import com.megabank.olp.client.sender.ixml.querycert.bean.IxmlQueryCertCertInfoBean;
import com.megabank.olp.client.sender.ixml.querycert.bean.IxmlQueryCertResBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultArgBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultCertParamsBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultResBean;
import com.megabank.olp.client.sender.ixml.revokecert.bean.IxmlRevokeCertArgBean;
import com.megabank.olp.client.sender.ixml.revokecert.bean.IxmlRevokeCertResBean;
import com.megabank.olp.client.service.common.UserClientService;
import com.megabank.olp.client.service.ixml.IxmlSenderService;
import com.megabank.olp.system.persistence.dao.generated.SystemTranLogDAO;
import com.megabank.olp.system.persistence.pojo.SystemTranLog;
import com.megabank.olp.system.utility.enums.ProcessStatusEnum;

import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;

@Service
@Transactional
public class IxmlService extends BaseApplyService
{
	private static final int DAY_DIFF_VALUE = 180;

	private final String REGEX = "\\{(\\w+)\\}";

	// 借款人電子簽章
	private final String BORROWER_JCIC_CONTEXT =
											   "{\\\"header\\\": {\\\"mainCode\\\": \\\"017\\\",\\\"bankCode\\\": \\\"017CL01\\\",\\\"type\\\": \\\"C01\\\",\\\"form\\\": \\\"E政府介接資料授權書\\\",\\\"serial\\\": \\\"{serial}\\\",\\\"signEnc\\\": \\\"Sign\\\",\\\"issueTime\\\": \\\"{issueTime} \\\"},\\\"context\\\": {\\\"idnBan\\\": \\\"{idnBan}\\\",\\\"name\\\": \\\"{name}\\\",\\\"businessType\\\": \\\"L\\\",\\\"purpose\\\": \\\"A,B,C\\\",\\\"startDate\\\": \\\"{startDate}\\\",\\\"endDate\\\": \\\"{endDate}\\\",\\\"ver\\\": \\\"1.1\\\",\\\"agrStatement\\\": \\\"立書人{name}({idnBan})為辦理授信業務所需，茲瞭解並同意兆豐國際商業銀行(局、社、農漁會信用部、公司、信用保證基金等)(機構代號017CL01)、金融監督管理委員會(以下簡稱金管會)、財團法人金融聯合徵信中心(以下簡稱管理機構)、管理機構之會員金融機構及其他經提供介接資料之公務機關(以下簡稱介接機關)同意使用介接資料之介接使用單位，及前揭機構授權之人員，得依本授權書及個人資料保護法或相關法規，於授信業務或信用卡業務特定目的範圍內，查調、蒐集、處理及利用財政部、勞動部等介接機關提供之立書人一切業務往來之介接資料(信用卡業務限查調財政部資料)，惟該特定目的消失時，在合理作業期間內應停止處理及利用該資料。金管會、管理機構、介接使用單位及其授權人員對介接資料之使用，如違反個人資料保護法及稅捐稽徵法等規定，致當事人權益受損害者，應負損害賠償責任(個人資料保護法第28條、第29條及稅捐稽徵法第33條等規定)。另涉及刑事責任者，依稅捐稽徵法及個人資料保護法之相關規定，移送司法機關辦理。授權起始日為{startDate}至結束日{endDate}為止，授權項目為A,B,C。\\\",\\\"sourceIp\\\": \\\"{sourceIp}\\\"}}";

	// 保證人、共借人電子簽章
	private final String GUARANTOR_JCIC_CONTEXT =
												"{\\\"header\\\": {\\\"mainCode\\\": \\\"017\\\",\\\"bankCode\\\": \\\"017CL01\\\",\\\"type\\\": \\\"C01\\\",\\\"form\\\": \\\"E政府介接資料授權書\\\",\\\"serial\\\": \\\"{serial}\\\",\\\"signEnc\\\": \\\"Sign\\\",\\\"issueTime\\\": \\\"{issueTime} \\\"},\\\"context\\\": {\\\"idnBan\\\": \\\"{idnBan}\\\",\\\"name\\\": \\\"{name}\\\",\\\"businessType\\\": \\\"L\\\",\\\"purpose\\\": \\\"A,B\\\",\\\"startDate\\\": \\\"{startDate}\\\",\\\"endDate\\\": \\\"{endDate}\\\",\\\"ver\\\": \\\"1.1\\\",\\\"agrStatement\\\": \\\"立書人{name}({idnBan})為辦理授信業務所需，茲瞭解並同意兆豐國際商業銀行(局、社、農漁會信用部、公司、信用保證基金等)(機構代號017CL01)、金融監督管理委員會(以下簡稱金管會)、財團法人金融聯合徵信中心(以下簡稱管理機構)、管理機構之會員金融機構及其他經提供介接資料之公務機關(以下簡稱介接機關)同意使用介接資料之介接使用單位，及前揭機構授權之人員，得依本授權書及個人資料保護法或相關法規，於授信業務或信用卡業務特定目的範圍內，查調、蒐集、處理及利用財政部、勞動部等介接機關提供之立書人一切業務往來之介接資料(信用卡業務限查調財政部資料)，惟該特定目的消失時，在合理作業期間內應停止處理及利用該資料。金管會、管理機構、介接使用單位及其授權人員對介接資料之使用，如違反個人資料保護法及稅捐稽徵法等規定，致當事人權益受損害者，應負損害賠償責任(個人資料保護法第28條、第29條及稅捐稽徵法第33條等規定)。另涉及刑事責任者，依稅捐稽徵法及個人資料保護法之相關規定，移送司法機關辦理。授權起始日為{startDate}至結束日{endDate}為止，授權項目為A,B。\\\",\\\"sourceIp\\\": \\\"{sourceIp}\\\"}}";

	private final String CERT_NOT_BEFORE_PATTERN = "yyyyMMddHHmmssSSS";

	@Autowired
	private IxmlSenderService ixmlSenderService;

	@Autowired
	private UserClientService userClientService;

	@Autowired
	private JwtService jwtService;

	@Autowired
	private IxmlSignaturesDAO ixmlSignaturesDAO;

	@Autowired
	private IxmlApplyRecordDAO ixmlApplyRecordDAO;

	@Autowired
	private LoanDAO loanDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private UploadService uploadService;

	@Autowired
	@Qualifier( "clientObjectMapper" )
	protected ObjectMapper mapper;

	@Autowired
	private SystemTranLogDAO systemTranLogDAO;

	@Autowired
	private DeliverService deliverService;

	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;

	@Autowired
	protected RequestInfoThreadLocal requestInfoThreadLocal;

	public String doCallback( IxmlDoCallbackArgBean argBean ) throws IOException, URISyntaxException
	{
		String url = argBean.getUrl();
		String params = argBean.getParams();

		String[] returnURIList = propertyBean.getUploadUrl().split( "#" );
		String returnParams = "";

		if( url != null && params != null )
		{
			List<NameValuePair> paramsList = URLEncodedUtils.parse( new URI( url + "/?" + params ), StandardCharsets.UTF_8 );
			Map<String, String> map = paramsList.stream().collect( Collectors.toMap( NameValuePair::getName, NameValuePair::getValue ) );
			DoResBean bean = new ObjectMapper().convertValue( map, DoResBean.class );

			if( vaildateResult( bean ) )
			{
				returnParams = "?ReturnCode=0&ReturnParams=" + bean.getReturnParams() + "&VerifyNo=" + bean.getVerifyNo() + "&Token="
					+ bean.getToken() + "#";
				return returnURIList[ 0 ] + returnParams + returnURIList[ 1 ];
			}

			returnParams = "?ReturnCode=0&ReturnParams="
				+ bean.getReturnParams().replace( "LoginCERT", "LoginCERTFail" ).replace( "LoginJWS", "LoginJWSFail" ) + "#";
			return returnURIList[ 0 ] + returnParams + returnURIList[ 1 ];
		}

		return propertyBean.getUploadUrl();
	}

	public Long getLoanId( String loanType )
	{
		Long loanId = getLatestLoanIdIn6Months( getIdNo(), getBirthDate(), loanType );

		if( loanId == null )
			throw new MyRuntimeException( ApplyErrorEnum.NO_RESULT_TO_UPLOAD );

		return loanId;
	}

	public IxmlParamsBean getParams()
	{
		IxmlParamsBean bean = new IxmlParamsBean();
		bean.setUrl( propertyBean.getIxmlUrl() );
		bean.setReturnUrl( propertyBean.getUploadUrl() );
		bean.setBusinessNo( propertyBean.getBusinessNo() );
		bean.setApiVersion( propertyBean.getApiVersion() );
		bean.setHashKeyNo( propertyBean.getHashKeyNo() );

		return bean;
	}

	public IxmlResBean login( String id, String action, String loanType, String userType, String clientAddress )
	{
		IxmlLoginArgBean argBean = new IxmlLoginArgBean();
		IxmlLoginInputParamsBean inputParamsBean = new IxmlLoginInputParamsBean();
		IxmlResBean ixmlResBean = new IxmlResBean();
		UUID uuid = UUID.randomUUID();
		String formatedUUID = uuid.toString().replace( "-", "" );

		argBean.setBusinessNo( propertyBean.getBusinessNo() );
		// argBean.setBusinessNo( "70759028" );
		argBean.setApiVersion( propertyBean.getApiVersion() );
		argBean.setHashKeyNo( propertyBean.getHashKeyNo() );
		argBean.setVerifyNo( formatedUUID );
		argBean.setReturnURL( propertyBean.getIxmlCallback() );
		argBean.setReturnParams( "LoginCERT" + loanType );

		argBean.setInputParams( inputParamsBean );
		inputParamsBean.setMemberNo( id );
		inputParamsBean.setMemberNoType( "0" );
		inputParamsBean.setAction( action );
		inputParamsBean.setPlaintext( id );
		inputParamsBean.setAssignCertPassword( EncryptorUtils.ixmlAssignCertPasswordEncrypt( id, "" ) );
		inputParamsBean.setErrCodeType( propertyBean.getErrCodeType() );

		if( "JWS".equals( action ) )
		{
			argBean.setReturnParams( "LoginJWS" + loanType );

			IxmlLoginJWSInputParamsBean jwsInputParams = new IxmlLoginJWSInputParamsBean();
			inputParamsBean.setJWSInputParams( jwsInputParams );
			jwsInputParams.setJWSType( "JCIC" );
			jwsInputParams.setIsCAVerify( "N" );
			jwsInputParams.setContents( getJCICContents( loanType, clientAddress, userType ) );
		}

		argBean.setIdentifyNo( EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), argBean.getBusinessNo(), argBean.getApiVersion(),
																 argBean.getHashKeyNo(), argBean.getVerifyNo(), argBean.getReturnParams(),
																 argBean.getInputParams().toString() ) );

		inputParamsBean.setAssignCertPassword( inputParamsBean.getAssignCertPassword().replace( "+", "%2b" ) );

		IxmlLoginResBean resBean = ixmlSenderService.login( argBean );
		recordSystemLog( "/IDPortal/Login", clientAddress, argBean.toString(), resBean.toString() );

		if( vaildateResult( resBean ) )
		{
			ixmlResBean.setStatus( true );
			ixmlResBean.setToken( resBean.getIxmlLoginOutputParamsBean().getToken() );
			ixmlResBean.setVerifyNo( formatedUUID );
			ixmlResBean.setIdentifyNo( EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), argBean.getBusinessNo(), argBean.getApiVersion(),
																		 argBean.getHashKeyNo(), argBean.getVerifyNo(), ixmlResBean.getToken() ) );

			createLoginRecord( id, clientAddress, formatedUUID, resBean.getIxmlLoginOutputParamsBean().getToken() );
		}

		return ixmlResBean;
	}

	public String queryCert( String id, String clientAddress )
	{
		IxmlQueryCertArgBean argBean = new IxmlQueryCertArgBean();
		argBean.setBusinessNo( propertyBean.getBusinessNo() );
		argBean.setHashKeyNo( propertyBean.getHashKeyNo() );
		argBean.setMemberNo( id );
		argBean.setIdentifyNo( EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), argBean.getBusinessNo(), argBean.getHashKeyNo(),
																 argBean.getMemberNo() ) );
		IxmlQueryCertResBean bean = ixmlSenderService.queryCert( argBean );
		recordSystemLog( "/IDPortal/QueryCert", clientAddress, argBean.toString(), bean.toString() );
		return vaildateResult( bean );
	}

	public IxmlCertResBean queryVerifyResult( String verifyNo, String id, String token, String loanType, String clientAddress )
		throws UnsupportedEncodingException
	{
		IxmlQueryVerifyResultArgBean argBean = mapQueryVerifyResultArgBean( verifyNo, id, token );
		IxmlQueryVerifyResultResBean bean = ixmlSenderService.queryVerifyResult( argBean );
		recordSystemLog( "/IDPortal/QueryVerifyResult", clientAddress, argBean.toString(), bean.toString() );

		IxmlCertResBean resBean = new IxmlCertResBean();

		if( vaildateResult( bean ) )
		{
			IxmlQueryVerifyResultCertParamsBean certParamsBean = bean.getIxmlQueryVerifyResultOutputParamsBean()
						.getIxmlQueryVerifyResultCertParamsBean();

			resBean.setStatus( true );
			resBean.setCertSN( certParamsBean.getCertSN() );
			resBean.setCertNotBefore( certParamsBean.getCertNotBefore() );
			resBean.setCertNotAfter( certParamsBean.getCertNotAfter() );

			// 撈出於ixml/login階段紀錄的record更新憑證資訊，並上傳統計平台
			long loginRecordId = ixmlApplyRecordDAO.getPojoByLoginInfo( verifyNo, id, token ).getApplyRecordId();

			if( isCertRecorded( certParamsBean.getCertSN() ) )
				ixmlApplyRecordDAO.updateTransmissionStatus( loginRecordId, TransmissionStatusEnum.NOT_REQUIRED.getContext() );
			else
			{
				boolean submitSuccess = deliverService.submitIxmlCert( id, certParamsBean, clientAddress );
				TransmissionStatusEnum status = submitSuccess ? TransmissionStatusEnum.COMPLETED : TransmissionStatusEnum.EXCEPTION;
				ixmlApplyRecordDAO.updateTransmissionStatus( loginRecordId, status.getContext() );
			}

			ixmlApplyRecordDAO.updateCertInfo( loginRecordId, certParamsBean );

			// 電子簽章
			if( bean.getIxmlQueryVerifyResultOutputParamsBean().getIxmlQueryVerifyResultJWSParamsBean().getJwsSignatures() != null )
			{
				resBean.setSignatureStatus( true );

				Long loanId = getLoanId( loanType );

				Long signatureId = ixmlSignaturesDAO
							.create( loanId, id,
									 bean.getIxmlQueryVerifyResultOutputParamsBean().getIxmlQueryVerifyResultJWSParamsBean().getJwsSignatures() );

				uploadService.submitIxmlFiles( loanId, signatureId );
			}
		}

		return resBean;
	}

	public void retryQueryVerifyResult()
	{
		List<IxmlApplyRecord> applyRecords = ixmlApplyRecordDAO.getUncheckedCertRecordsYesterday( TransmissionStatusEnum.NO.getContext() );

		for( IxmlApplyRecord applyRecord : applyRecords )
		{
			IxmlQueryVerifyResultArgBean argBean = mapQueryVerifyResultArgBean( applyRecord );
			IxmlQueryVerifyResultResBean resBean = ixmlSenderService.queryVerifyResult( argBean );
			recordSystemLog( "/IDPortal/QueryVerifyResult", requestInfoThreadLocal.get().getServerAddress(), argBean.toString(), resBean.toString() );

			if( !vaildateResult( resBean ) )
			{
				ixmlApplyRecordDAO.updateTransmissionStatus( applyRecord.getApplyRecordId(), TransmissionStatusEnum.NOT_REQUIRED.getContext() );
				continue;
			}

			IxmlQueryVerifyResultCertParamsBean certParamsBean = resBean.getIxmlQueryVerifyResultOutputParamsBean()
						.getIxmlQueryVerifyResultCertParamsBean();

			if( !isCertRecorded( certParamsBean.getCertSN() ) && isIssuedOnCreatedDate( resBean, applyRecord ) )
				ixmlApplyRecordDAO.updateTransmissionStatus( applyRecord.getApplyRecordId(), TransmissionStatusEnum.EXCEPTION.getContext() );
			else
				ixmlApplyRecordDAO.updateTransmissionStatus( applyRecord.getApplyRecordId(), TransmissionStatusEnum.NOT_REQUIRED.getContext() );

			ixmlApplyRecordDAO.updateCertInfo( applyRecord.getApplyRecordId(), certParamsBean );

		}
	}

	public boolean revokeCert( String id, String clientAddress )
	{
		String certSN = queryCert( id, clientAddress );

		if( certSN != null )
		{
			IxmlRevokeCertArgBean argBean = new IxmlRevokeCertArgBean();
			argBean.setBusinessNo( propertyBean.getBusinessNo() );
			argBean.setHashKeyNo( propertyBean.getHashKeyNo() );
			argBean.setHexSerial( certSN );
			argBean.setIdentifyNo( EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), argBean.getBusinessNo(), argBean.getHashKeyNo(),
																	 argBean.getHexSerial() ) );

			IxmlRevokeCertResBean bean = ixmlSenderService.revokeCert( argBean );
			recordSystemLog( "/IDPortal/RevokeCert", clientAddress, argBean.toString(), bean.toString() );
			return vaildateResult( bean );
		}

		return false;
	}

	public void setSession( HttpServletRequest request )
	{
		String jwt = jwtService.getJwtFromHeader( request.getHeader( HttpHeaders.AUTHORIZATION ) );
		Claims claims = jwtService.getCliams( jwt );
		Date birthDate = getBirthDate( claims );
		String idNo = getIdNo( claims );
		List<String> identityTypes = ( List<String> )claims.get( BaseIdentityJwtService.IDENTITITY_TYPES );

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setBirthDate( birthDate );
		localBean.setIdNo( idNo );
		localBean.setIdentityTypes( identityTypes );
		sessionInfoThreadLocal.set( localBean );
	}

	private void createLoginRecord( String idNo, String clientAddress, String verifyNo, String token )
	{
		IxmlApplyRecordCreatedParamBean createdBean = new IxmlApplyRecordCreatedParamBean();
		createdBean.setIdNo( idNo );
		createdBean.setClientAddress( clientAddress );
		createdBean.setVerifyNo( verifyNo );
		createdBean.setToken( token );
		ixmlApplyRecordDAO.create( createdBean );
	}

	private Date getBirthDate( Claims claims )
	{
		Object obj = claims.get( StrongIdentityJwtService.BIRTHDATE );

		if( obj == null )
			return null;

		Long value;

		if( obj instanceof Integer )
			value = new Long( ( Integer )obj );
		else
			value = ( Long )obj;

		return new Date( value );

	}

	private String getIdNo( Claims claims )
	{
		Object obj = claims.get( StrongIdentityJwtService.ID_NO );

		if( obj == null )
			return null;

		return ( String )obj;
	}

	private List<String> getJCICContents( String loanType, String clientAddress, String userType )
	{
		String contents;
		if( UserTypeEnum.BORROWER.getContext().equals( userType ) )
			contents = BORROWER_JCIC_CONTEXT;
		else if( UserTypeEnum.GUARANTOR.getContext().equals( userType ) )
			contents = GUARANTOR_JCIC_CONTEXT;
		else
			throw new IllegalArgumentException( "Unexpected user type: " + userType );

		Pattern pattern = Pattern.compile( REGEX );
		Matcher matcher = pattern.matcher( contents );

		Date now = new Date();
		SimpleDateFormat serialFormat = new SimpleDateFormat( "yyyyMMddkkmmss" );
		SimpleDateFormat fullFormat = new SimpleDateFormat( "yyyy-MM-dd-kk:mm:ss" );
		SimpleDateFormat shortFormat = new SimpleDateFormat( "yyyy-MM-dd" );
		Calendar calendar = Calendar.getInstance();
		calendar.setTime( now );

		Long loanId = getLoanId( loanType );
		ApplyLoan applyLoan = applyLoanDAO.read( loanId );
		ApplyLoanBasic applyLoanBasic = applyLoan.getApplyLoanBasic();

		Map<String, String> repalceMap = new HashMap<>();
		repalceMap.put( "serial", applyLoan.getCaseNo() + serialFormat.format( now ) );
		repalceMap.put( "issueTime", fullFormat.format( now ) );
		repalceMap.put( "idnBan", applyLoanBasic.getIdNo() );
		repalceMap.put( "name", applyLoanBasic.getName() );
		repalceMap.put( "startDate", shortFormat.format( now ) );

		calendar.add( Calendar.MONTH, 3 );
		now = calendar.getTime();

		repalceMap.put( "endDate", shortFormat.format( now ) );
		repalceMap.put( "sourceIp", clientAddress );

		while( matcher.find() )
			if( repalceMap.containsKey( matcher.group( 1 ) ) )
				contents = contents.replace( matcher.group(), repalceMap.get( matcher.group( 1 ) ) );

		List<String> contentsList = new ArrayList<>();
		contentsList.add( contents );

		return contentsList;
	}

	private Long getLatestLoanIdIn6Months( String idNo, Date birthDate, String loanType )
	{
		String identityType = getCurrentIdentityType();

		if( identityType.equals( IdentityTypeEnum.OTP.getContext() ) )
			return loanDAO.getLatestLoanIdInDaysByOtp( idNo, birthDate, getMobileNumber(), loanType, DAY_DIFF_VALUE );

		return loanDAO.getLatestLoanIdInDays( idNo, birthDate, null, loanType, DAY_DIFF_VALUE );
	}

	private String getMobileNumber()
	{
		return userClientService.getCurrentIdentityInfoResult().getMobileNumber();
	}

	private boolean isCertRecorded( String certSN )
	{
		return !ixmlApplyRecordDAO.getPojosByCertSn( certSN ).isEmpty();
	}

	private boolean isIssuedOnCreatedDate( IxmlQueryVerifyResultResBean resBean, IxmlApplyRecord applyRecord )
	{
		String certNotBeforeStr = resBean.getIxmlQueryVerifyResultOutputParamsBean().getIxmlQueryVerifyResultCertParamsBean().getCertNotBefore();
		Date certNotBeforeDate = CommonDateStringUtils.transString2Date( certNotBeforeStr, CERT_NOT_BEFORE_PATTERN );
		return DateUtils.isSameDay( certNotBeforeDate, applyRecord.getCreatedDate() );
	}

	private IxmlQueryVerifyResultArgBean mapQueryVerifyResultArgBean( IxmlApplyRecord record )
	{
		return mapQueryVerifyResultArgBean( record.getVerifyNo(), record.getIdNo(), record.getToken() );
	}

	private IxmlQueryVerifyResultArgBean mapQueryVerifyResultArgBean( String verifyNo, String id, String token )
	{
		IxmlQueryVerifyResultArgBean argBean = new IxmlQueryVerifyResultArgBean();
		argBean.setBusinessNo( propertyBean.getBusinessNo() );
		argBean.setApiVersion( propertyBean.getApiVersion() );
		argBean.setHashKeyNo( propertyBean.getHashKeyNo() );
		argBean.setVerifyNo( verifyNo );
		argBean.setMemberNo( id );
		argBean.setToken( token );
		argBean.setIdentifyNo( EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), argBean.getBusinessNo(), argBean.getApiVersion(),
																 argBean.getHashKeyNo(), argBean.getVerifyNo(), argBean.getMemberNo(),
																 argBean.getToken() ) );
		return argBean;
	}

	private void recordSystemLog( String requestUrl, String clientAddress, String requestData, String responseData )
	{
		RequestInfoThreadLocalBean localBean = new RequestInfoThreadLocalBean();

		SystemTranLog pojo = new SystemTranLog();
		pojo.setClientAddress( localBean.getClientAddress() );
		pojo.setCreatedDate( new Date() );
		pojo.setProcessStatus( ProcessStatusEnum.PROCESS.getContext() );
		pojo.setIsAjax( false );
		pojo.setRequestMethod( "POST" );
		pojo.setRequestUrl( requestUrl );
		pojo.setClientAddress( clientAddress );
		pojo.setRequestContentType( "application/json" );
		pojo.setRequestData( requestData );
		pojo.setResponseContentType( "application/json" );
		pojo.setResponseData( responseData );
		systemTranLogDAO.create( pojo );
	}

	private boolean vaildateResult( DoResBean bean )
	{
		// 1. 檢查 respose 是否為空
		// 2. 檢查 驗證碼 是否正確
		// 3. 檢查 身分證校驗碼 是否為1
		// 4. 檢查 執行結果 是否為S
		// 5. 檢查 回應碼 是否為0
		if( bean == null )
			return false;

		String identifyNo =
						  EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), bean.getBusinessNo(), bean.getApiVersion(),
															bean.getHashKeyNo(), bean.getVerifyNo(), bean.getReturnParams(), bean.getToken(),
															bean.getCaType(), bean.getMemberNoMapping(), bean.getResultCode(), bean.getReturnCode() );
		if( !bean.getIdentifyNo().equals( identifyNo ) )
			return false;

		if( !bean.getMemberNoMapping().equals( "1" ) )
			return false;

		if( !bean.getResultCode().equals( "S" ) )
			return false;

		if( !bean.getReturnCode().equals( "0" ) && !bean.getReturnCode().equals( "0000" ) )
			return false;

		return true;
	}

	private boolean vaildateResult( IxmlLoginResBean bean )
	{
		// 1. 檢查 respose 是否為空
		// 2. 檢查 驗證碼 是否正確
		// 3. 檢查 執行結果 是否為S
		// 4. 檢查 回應碼 是否為0
		if( bean == null )
			return false;

		String identifyNo = EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), bean.getBusinessNo(), bean.getApiVersion(),
															  bean.getHashKeyNo(), bean.getVerifyNo(), bean.getResultCode(), bean.getReturnCode(),
															  bean.getIxmlLoginOutputParamsBean().toString() )
					.toLowerCase();
		if( !bean.getIdentifyNo().equals( identifyNo ) )
			return false;

		if( !bean.getResultCode().equals( "S" ) )
			return false;

		if( !bean.getReturnCode().equals( "0" ) && !bean.getReturnCode().equals( "0000" ) )
			return false;

		return true;
	}

	private String vaildateResult( IxmlQueryCertResBean bean )
	{
		// 1. 檢查 respose 是否為空
		// 2. 檢查 驗證碼 是否正確
		// 3. 檢查 回應碼 是否為0
		// 4. 檢查 憑證清單 是否不為空
		// 5. 檢查 憑證清單 是否存在未過期憑證 (憑證狀態為0)
		if( bean == null )
			return null;

		String identifyNo =
						  EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), bean.getBusinessNo(), bean.getHashKeyNo(), bean.getMemberNo(),
															bean.getReturnCode(), bean.getReturnCodeDesc(), bean.getCertInfoList() )
									  .toLowerCase();
		if( !bean.getIdentifyNo().equals( identifyNo ) )
			return null;

		if( !bean.getReturnCode().equals( "0" ) && !bean.getReturnCode().equals( "31" ) )
			return null;

		if( bean.getCertInfoList() == null )
			return null;

		List<IxmlQueryCertCertInfoBean> certInfos = bean.getIxmlQueryCertCertInfoBeans();
		for( int i = 0; i < certInfos.size(); i++ )
			if( certInfos.get( i ).getState().equals( "0" ) )
				return certInfos.get( i ).getSerial();

		return null;
	}

	private boolean vaildateResult( IxmlQueryVerifyResultResBean bean )
	{
		// 1. 檢查 respose 是否為空
		// 2. 檢查 驗證碼 是否正確
		// 3. 檢查 執行結果 是否為S
		// 4. 檢查 回應碼 是否為0
		if( bean == null )
			return false;

		String identifyNo = EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), bean.getBusinessNo(), bean.getApiVersion(),
															  bean.getHashKeyNo(), bean.getVerifyNo(), bean.getResultCode(), bean.getReturnCode(),
															  bean.getOutputParams() );
		if( !bean.getIdentifyNo().equals( identifyNo ) )
			return false;

		if( !bean.getResultCode().equals( "S" ) )
			return false;

		if( !bean.getReturnCode().equals( "0" ) )
			return false;

		return true;
	}

	private boolean vaildateResult( IxmlRevokeCertResBean bean )
	{
		// 1. 檢查 respose 是否為空
		// 2. 檢查 驗證碼 是否正確
		// 4. 檢查 回應碼 是否為0
		if( bean == null )
			return false;

		String identifyNo = EncryptorUtils.ixmlSHA256Encrypt( propertyBean.getHashKey(), bean.getBusinessNo(), bean.getHashKeyNo(),
															  bean.getHexSerial(), bean.getReturnCode(), bean.getReturnCodeDesc() );
		if( !bean.getIdentifyNo().equals( identifyNo ) )
			return false;

		if( !bean.getReturnCode().equals( "0" ) )
			return false;

		return true;
	}
}
