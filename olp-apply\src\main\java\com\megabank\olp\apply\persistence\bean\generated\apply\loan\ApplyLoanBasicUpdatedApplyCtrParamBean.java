package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanBasicUpdatedApplyCtrParamBean extends BaseBean
{
	private Long loanId;

	private Boolean notUsTaxpayer;

	private Boolean notOuttwTaxpayer;

	private String rateAdjNotify;

	private Boolean crossMarketing;

	public ApplyLoanBasicUpdatedApplyCtrParamBean()
	{
		// default constructor
	}

	public Boolean getCrossMarketing()
	{
		return crossMarketing;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public Boolean getNotOuttwTaxpayer()
	{
		return notOuttwTaxpayer;
	}

	public Boolean getNotUsTaxpayer()
	{
		return notUsTaxpayer;
	}

	public String getRateAdjNotify()
	{
		return rateAdjNotify;
	}

	public void setCrossMarketing( Boolean crossMarketing )
	{
		this.crossMarketing = crossMarketing;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setNotOuttwTaxpayer( Boolean notOuttwTaxpayer )
	{
		this.notOuttwTaxpayer = notOuttwTaxpayer;
	}

	public void setNotUsTaxpayer( Boolean notUsTaxpayer )
	{
		this.notUsTaxpayer = notUsTaxpayer;
	}

	public void setRateAdjNotify( String rateAdjNotify )
	{
		this.rateAdjNotify = rateAdjNotify;
	}

}
