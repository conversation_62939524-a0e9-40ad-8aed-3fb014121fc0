package com.megabank.olp.apply.persistence.dao.generated.house;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.house.LoanTrialCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProcessDAO;
import com.megabank.olp.apply.persistence.pojo.house.HouseLoanTrialInfo;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseLoanTrialInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseLoanTrialInfoDAO extends BasePojoDAO<HouseLoanTrialInfo, Long>
{
	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	private static final String LOAN_TRIAL_INFO_ID_CONSTANT = "loanTrialInfoId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	@Autowired
	private CodeProcessDAO codeProcessDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	public Long create( LoanTrialCreatedParamBean paramBean )
	{
		Validate.notBlank( paramBean.getCaseNo() );
		Validate.notNull( paramBean.getCreatedDate() );
		Validate.notBlank( paramBean.getProcessCode() );
		Validate.notBlank( paramBean.getBranchBankCode() );

		HouseLoanTrialInfo pojo = new HouseLoanTrialInfo();
		pojo.setCaseNo( paramBean.getCaseNo() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setCodeBranchBank( codeBranchBankDAO.getPojoByBankCode( paramBean.getBranchBankCode() ) );
		pojo.setCodeProcess( codeProcessDAO.read( paramBean.getProcessCode() ) );
		pojo.setCreatedDate( paramBean.getCreatedDate() );
		pojo.setUpdatedDate( new Date() );

		return super.createPojo( pojo );
	}

	@SuppressWarnings( "rawtypes" )
	public Long getLatestId()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "houseloantrial.getLatestId" );

		return ( Long )nativeQuery.uniqueResult();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedBankIds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "houseloantrial.getBranchBankIds" );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedLoanTrialInfoIds( Long branchBankId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "houseloantrial.getLoanTrialInfoIds" );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	public HouseLoanTrialInfo getPojoByCaseNoToNull( String caseNo )
	{
		Validate.notBlank( caseNo );

		NameValueBean condition = new NameValueBean( HouseLoanTrialInfo.CASE_NO_CONSTANT, caseNo );

		return getUniquePojoByProperty( condition );
	}

	public HouseLoanTrialInfo read( Long houseLoanTrialInfoId )
	{
		Validate.notNull( houseLoanTrialInfoId );

		return getPojoByPK( houseLoanTrialInfoId, HouseLoanTrialInfo.TABLENAME_CONSTANT );
	}

	public Long updateBranchBank( Long houseLoanTrialInfoId, Long branchBankId )
	{
		Validate.notNull( houseLoanTrialInfoId );
		Validate.notNull( branchBankId );

		HouseLoanTrialInfo pojo = read( houseLoanTrialInfoId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( branchBankId ) );
		pojo.setNotified( false );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanTrialInfoId();

	}

	public int updateNotified( List<Long> loanTrialInfoIds )
	{
		Validate.notEmpty( loanTrialInfoIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "houseloantrial.updateNotified" );
		nativeQuery.setParameterList( LOAN_TRIAL_INFO_ID_CONSTANT, loanTrialInfoIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long houseLoanTrialInfoId )
	{
		Validate.notNull( houseLoanTrialInfoId );

		HouseLoanTrialInfo pojo = read( houseLoanTrialInfoId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanTrialInfoId();
	}

	public Long updateProcess( Long houseLoanTrialInfoId, String processCode )
	{
		Validate.notNull( houseLoanTrialInfoId );
		Validate.notBlank( processCode );

		HouseLoanTrialInfo pojo = read( houseLoanTrialInfoId );
		pojo.setCodeProcess( codeProcessDAO.read( processCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanTrialInfoId();
	}

	@Override
	protected Class<HouseLoanTrialInfo> getPojoClass()
	{
		return HouseLoanTrialInfo.class;
	}
}
