package com.megabank.olp.apply.service.management;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.management.bean.attachment.AttachmentListedParamBean;
import com.megabank.olp.apply.service.management.bean.attachment.AttachmentListedResBean;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class AttachmentServiceIntegration
{
	@Autowired
	private AttachmentService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void listHouseLoanAttachment()
	{
		int page = 1;

		AttachmentListedParamBean paramBean = new AttachmentListedParamBean();
		paramBean.setPage( page );

		AttachmentListedResBean resBean = service.listHouseLoanAttachment( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listPersonalLoanAttachment()
	{
		int page = 1;

		AttachmentListedParamBean paramBean = new AttachmentListedParamBean();
		paramBean.setPage( page );

		AttachmentListedResBean resBean = service.listPersonalLoanAttachment( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void updateTransmissionStatus()
	{
		Long attachmentId = 1L;
		String transmissionStatus = "completed";
		String employeeId = "developer-01";
		String employeeName = "developer";

		Long id = service.updateTransmissionStatus( attachmentId, transmissionStatus, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	private AttachmentListedParamBean getAttachmentListParamBean()
	{
		String transmissionStatus = "no";
		String idNo = "A123456789";
		String mobileNumber = "0912345678";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		Date dateStart = CommonDateUtils.getDate( 2020, 1, 1 );
		Date dateEnd = CommonDateUtils.getDate( 2020, 12, 31 );
		String sortColumn = null;
		String sortDirection = null;
		int page = 1;
		int length = 10;

		AttachmentListedParamBean paramBean = new AttachmentListedParamBean();
		paramBean.setTransmissionStatusCode( transmissionStatus );
		paramBean.setDateStart( dateStart );
		paramBean.setDateEnd( dateEnd );
		paramBean.setIdNo( idNo );
		paramBean.setBirthDate( birthDate );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setPage( page );
		paramBean.setLength( length );
		paramBean.setSortColumn( sortColumn );
		paramBean.setSortDirection( sortDirection );

		return paramBean;
	}
}
