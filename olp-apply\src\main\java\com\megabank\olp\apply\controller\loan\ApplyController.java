package com.megabank.olp.apply.controller.loan;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.loan.bean.apply.AgreementConfirmedArgBean;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanApplyBaseArgBean;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanApplyCreatedArgBean;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanApplySubmittedArgBean;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanContentBean;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanRelationBean;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanServedBean;
import com.megabank.olp.apply.controller.loan.bean.apply.PersonalBasicBean;
import com.megabank.olp.apply.controller.loan.bean.apply.PersonalContactBean;
import com.megabank.olp.apply.controller.loan.bean.apply.PersonalGuaranteeBean;
import com.megabank.olp.apply.controller.loan.bean.apply.PersonalInfoSubmittedArgBean;
import com.megabank.olp.apply.controller.loan.bean.apply.PersonalJobBean;
import com.megabank.olp.apply.service.loan.ApplyService;
import com.megabank.olp.apply.service.loan.bean.apply.LoanApplySubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanContentParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanRelationParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanServedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalBasicParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalContactParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalGuaranteeParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalInfoSubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalJobParamBean;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.utility.CommonStringUtils;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/apply" )
public class ApplyController extends BaseController
{
	@Autowired
	private ApplyService applyService;

	/**
	 * 回到基本申請頁狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "backToPersonalInfo" )
	public Map<String, Object> backToPersonalInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.backToPersonalInfo( argBean.getLoanType() ) );

	}

	/**
	 * 檢查申請次數
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "checkApplyCount" )
	public Map<String, Object> checkApplyCount( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.checkApplyCount( argBean.getLoanType() ) );

	}

	/**
	 * 確認同意事項
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "confirmAgreement" )
	public Map<String, Object> confirmAgreement( @RequestBody @Validated AgreementConfirmedArgBean argBean )
	{
		String loanType = argBean.getLoanType();
		List<Long> itemIds = argBean.getItemIds();

		return getResponseMap( applyService.confirmAgreement( loanType, itemIds, argBean.getNotUsTaxpayer(), argBean.getNotOuttwTaxpayer(),
															  argBean.getRateAdjNotify(), argBean.getCrossMarketing() ) );

	}

	/**
	 * 確認完成申請
	 *
	 * @param argBean
	 * @return
	 * @throws IOException
	 */
	@PostMapping( "confirmFilledInfo" )
	public Map<String, Object> confirmFilledInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean ) throws IOException
	{
		applyService.confirmFilledInfo( argBean.getLoanType() );
		return getResponseMap( applyService.submitConfirmFilledInfo( argBean.getLoanType() ) );
	}

	@PostMapping( "create" )
	public Map<String, Object> create( @RequestBody @Validated LoanApplyCreatedArgBean argBean )
	{
		String loanType = argBean.getLoanType();
		String refCaseNo = argBean.getRefCaseNo();
		String plan = StringUtils.trimToEmpty( argBean.getPlan() );
		String introduceBrNo = StringUtils.trimToEmpty( argBean.getIntroduceBrNo() );

		if( StringUtils.isNotBlank( refCaseNo ) )
		{
			// 表示為{從債務人}
			plan = "";
			introduceBrNo = "";
		}
		return getResponseMap( applyService.createLoanApply( loanType, refCaseNo, plan, introduceBrNo ) );

	}

	/**
	 * 取得同意事項
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getAgreement" )
	public Map<String, Object> getAgreement( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getAgreement( argBean.getLoanType() ) );
	}

	/**
	 * 取得借款人資訊內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getBorrowerInfo" )
	public Map<String, Object> getBorrowerInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getBorrowerInfo( argBean.getLoanType() ) );
	}

	/**
	 * 取得貸款戶派案分行
	 *
	 * @return
	 */
	@PostMapping( "getBranchBankList" )
	public Map<String, Object> getBranchBankList()
	{
		return getResponseMap( applyService.getBranchBankList() );

	}

	/**
	 * 取得最近完成申請日期
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getLatestApplyDate" )
	public Map<String, Object> getLatestApplyDate( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getLatestApplyDate( argBean.getLoanType() ) );
	}

	/**
	 * 取得申貸頁內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getLoanApplyInfo" )
	public Map<String, Object> getLoanApplyInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getLoanApplyInfo( argBean.getLoanType() ) );
	}

	/**
	 * 取得個人基本資訊內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getPersonalBasicInfo" )
	public Map<String, Object> getPersonalBasicInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getPersonalBasicInfo( argBean.getLoanType() ) );
	}

	/**
	 * 取得個人聯絡資訊內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getPersonalContactInfo" )
	public Map<String, Object> getPersonalContactInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getPersonalContactInfo( argBean.getLoanType() ) );
	}

	/**
	 * 取得保證人資訊內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getPersonalGuaranteeInfo" )
	public Map<String, Object> getPersonalGuaranteeInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getPersonalGuaranteeInfo( argBean.getLoanType() ) );
	}

	/**
	 * 取得個人職業資訊內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getPersonalJobInfo" )
	public Map<String, Object> getPersonalJobInfo( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getPersonalJobInfo( argBean.getLoanType() ) );
	}

	/**
	 * 取得感謝頁內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getThankyouMessage" )
	public Map<String, Object> getThankyouMessage( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.getThankyouMessage( argBean.getLoanType() ) );
	}

	/**
	 * 是否隱藏ixml、mydata選項
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "isIxmlMydataDisplayed" )
	public Map<String, Object> isIxmlMydataDisplayed( @RequestBody @Validated LoanApplyBaseArgBean argBean )
	{
		return getResponseMap( applyService.isIxmlMydataDisplayed( argBean.getLoanType() ) );
	}

	/**
	 * 送出申貸頁內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "submitLoanApplyInfo" )
	public Map<String, Object> submitLoanApplyInfo( @RequestBody @Validated LoanApplySubmittedArgBean argBean )
	{
		checkLoanApplyInfoArgBean( argBean.getLoanType(), argBean );

		LoanApplySubmittedParamBean paramBean = new LoanApplySubmittedParamBean();
		paramBean.setLoanType( argBean.getLoanType() );
		paramBean.setLoanContentParamBean( getLoanContentParamBean( argBean.getLoanContentBean() ) );
		paramBean.setLoanRelationParamBeans( getLoanRelationParamBeans( argBean.getLoanRelationBeans() ) );
		paramBean.setLoanServedParamBeans( getLoanServedParamBeans( argBean.getLoanServedBeans() ) );

		return getResponseMap( applyService.submitLoanApplyInfo( paramBean ) );

	}

	/**
	 * 送出個人資訊頁內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "submitPersonalInfo" )
	public Map<String, Object> submitPersonalInfo( @RequestBody @Validated PersonalInfoSubmittedArgBean argBean )
	{
		checkPersonalInfoArgBean( argBean );

		PersonalInfoSubmittedParamBean paramBean = new PersonalInfoSubmittedParamBean();
		paramBean.setLoanType( argBean.getLoanType() );
		paramBean.setPersonalBasicParamBean( getPersonalBasicParamBean( argBean.getPersonalBasicBean() ) );
		paramBean.setPersonalContactParamBean( getPersonalContactParamBean( argBean.getPersonalContactBean() ) );
		paramBean.setPersonalJobParamBean( getPersonalJobParamBean( argBean.getPersonalJobBean() ) );
		paramBean.setPersonalGuaranteeParamBean( getPersonalGuaranteeParamBean( argBean.getPersonalGuaranteeBean() ) );

		return getResponseMap( applyService.submitPersonalInfo( paramBean ) );

	}

	private void checkLoanApplyInfoArgBean( String loanType, LoanApplySubmittedArgBean argBean )
	{
		if( loanType.equals( LoanTypeEnum.HOUSE_LOAN.getContext() ) && StringUtils
					.isAnyBlank( argBean.getLoanContentBean().getCollateralAddressTownCode(),
								 argBean.getLoanContentBean().getCollateralAddressStreet(), argBean.getLoanContentBean().getGracePeriodCode() ) )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
										  new String[]{ "collateralAddressTownCode, collateralAddressStreet, gracePeriodCode", "must not be null" } );
		else if( loanType.equals( LoanTypeEnum.PERSONAL_LOAN.getContext() )
			&& StringUtils.isAnyBlank( argBean.getLoanContentBean().getCaseSourceCode() ) )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY, new String[]{ "caseSourceCode", "must not be null" } );
		else if( StringUtils.isNotBlank( argBean.getLoanContentBean().getUrlToIdentifyFraud() )
			&& !CommonStringUtils.isDomainNameValid( argBean.getLoanContentBean().getUrlToIdentifyFraud() ) )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY, new String[]{ "urlToIdentifyFraud", "invalid domain name" } );
	}

	private void checkPersonalInfoArgBean( PersonalInfoSubmittedArgBean argBean )
	{
		String serviceAssociate = argBean.getPersonalContactBean().getServiceAssociate();
		// 因 iloan 需求，當行銷單位為 兆豐銀行 時，行銷專員編號若不足 6 碼，補 0 到 6 碼。若大於 6 碼，從後端擷取 6 碼
		if( "90000".equals( argBean.getPersonalContactBean().getServiceAssociateDeptCode() )
			&& !Objects.isNull( argBean.getPersonalContactBean().getServiceAssociate() )
			&& !argBean.getPersonalContactBean().getServiceAssociate().matches( "^[0-9]{6}$" ) )
			if( argBean.getPersonalContactBean().getServiceAssociate().length() < 6 )
			serviceAssociate = StringUtils.leftPad( argBean.getPersonalContactBean().getServiceAssociate(), 6, "0" );
			else if( argBean.getPersonalContactBean().getServiceAssociate().length() > 6 )
				serviceAssociate = argBean.getPersonalContactBean().getServiceAssociate()
							.substring( argBean.getPersonalContactBean().getServiceAssociate().length() - 6 );
		argBean.getPersonalContactBean().setServiceAssociate( serviceAssociate );
	}

	private LoanContentParamBean getLoanContentParamBean( LoanContentBean contentBean )
	{
		LoanContentParamBean paramBean = new LoanContentParamBean();
		paramBean.setLoanRequestAmt( contentBean.getLoanRequestAmt() );
		paramBean.setLoanPurpose( contentBean.getLoanPurposeCode() );
		paramBean.setOtherPurpose( contentBean.getOtherPurpose() );
		paramBean.setLoanPeriod( contentBean.getLoanPeriodCode() );
		paramBean.setCollateralAddressTownCode( contentBean.getCollateralAddressTownCode() );
		paramBean.setCollateralAddressVillage( contentBean.getCollateralAddressVillage() );
		paramBean.setCollateralAddressNeighborhood( contentBean.getCollateralAddressNeighborhood() );
		paramBean.setCollateralAddressStreet( contentBean.getCollateralAddressStreet() );
		paramBean.setCollateralAddressSection( contentBean.getCollateralAddressSection() );
		paramBean.setCollateralAddressLane( contentBean.getCollateralAddressLane() );
		paramBean.setCollateralAddressAlley( contentBean.getCollateralAddressAlley() );
		paramBean.setCollateralAddressNo( contentBean.getCollateralAddressNo() );
		paramBean.setCollateralAddressFloor( contentBean.getCollateralAddressFloor() );
		paramBean.setCollateralAddressRoom( contentBean.getCollateralAddressRoom() );
		paramBean.setGracePeriodCode( contentBean.getGracePeriodCode() );
		paramBean.setNotificationCode( contentBean.getNotificationCode() );
		paramBean.setMortgageType( contentBean.getMortgageType() );
		paramBean.setNonPrivateUsageType( contentBean.getNonPrivateUsageType() );
		paramBean.setNonPrivateUsageSubType( contentBean.getNonPrivateUsageSubType() );
		paramBean.setPrivateUsageType( contentBean.getPrivateUsageType() );
		paramBean.setIsIncreasingLoan( contentBean.getIsIncreasingLoan() );
		paramBean.setAppnBankCode( contentBean.getAppnBankCode() );
		paramBean.setAppnDpAcct( contentBean.getAppnDpAcct() );
		paramBean.setCaseSourceCode( contentBean.getCaseSourceCode() );
		paramBean.setUrlToIdentifyFraud( contentBean.getUrlToIdentifyFraud() );
		return paramBean;
	}

	private List<LoanRelationParamBean> getLoanRelationParamBeans( List<LoanRelationBean> relationBeans )
	{
		List<LoanRelationParamBean> paramBeans = new ArrayList<>();
		for( LoanRelationBean relationBean : relationBeans )
		{
			LoanRelationParamBean paramBean = new LoanRelationParamBean();
			paramBean.setRelationName( relationBean.getRelationName() );
			paramBean.setRelationIdNo( relationBean.getRelationIdNo() );
			paramBean.setRelationType( relationBean.getRelationType() );

			paramBeans.add( paramBean );
		}

		return paramBeans;
	}

	private List<LoanServedParamBean> getLoanServedParamBeans( List<LoanServedBean> servedBeans )
	{
		List<LoanServedParamBean> paramBeans = new ArrayList<>();
		for( LoanServedBean servedBean : servedBeans )
		{
			LoanServedParamBean paramBean = new LoanServedParamBean();
			paramBean.setCompanyName( servedBean.getCompanyName() );
			paramBean.setServedTitle( servedBean.getServedTitle() );
			paramBean.setTaxNo( servedBean.getTaxNo() );
			paramBean.setComment( servedBean.getComment() );
			paramBean.setRepresentativeType( servedBean.getRepresentativeType() );

			paramBeans.add( paramBean );
		}

		return paramBeans;
	}

	private PersonalBasicParamBean getPersonalBasicParamBean( PersonalBasicBean basicBean )
	{
		PersonalBasicParamBean paramBean = new PersonalBasicParamBean();
		paramBean.setIdNo( basicBean.getIdNo() );
		paramBean.setName( basicBean.getName() );
		paramBean.setEngFirstName( basicBean.getEngFirstName() );
		paramBean.setEngLastName( basicBean.getEngLastName() );
		paramBean.setBirthDate( basicBean.getBirthDate() );
		paramBean.setMarriageStatus( basicBean.getMarriageStatusCode() );
		paramBean.setEducationLevel( basicBean.getEducationLevelCode() );
		paramBean.setChildrenCount( basicBean.getChildrenCount() );
		paramBean.setNationality( basicBean.getNationalityCode() );

		return paramBean;
	}

	private PersonalContactParamBean getPersonalContactParamBean( PersonalContactBean contactBean )
	{
		PersonalContactParamBean paramBean = new PersonalContactParamBean();
		paramBean.setHomeAddressTownCode( contactBean.getHomeAddressTownCode() );
		paramBean.setHomeAddressVillage( contactBean.getHomeAddressVillage() );
		paramBean.setHomeAddressNeighborhood( contactBean.getHomeAddressNeighborhood() );
		paramBean.setHomeAddressStreet( contactBean.getHomeAddressStreet() );
		paramBean.setHomeAddressSection( contactBean.getHomeAddressSection() );
		paramBean.setHomeAddressLane( contactBean.getHomeAddressLane() );
		paramBean.setHomeAddressAlley( contactBean.getHomeAddressAlley() );
		paramBean.setHomeAddressNo( contactBean.getHomeAddressNo() );
		paramBean.setHomeAddressFloor( contactBean.getHomeAddressFloor() );
		paramBean.setHomeAddressRoom( contactBean.getHomeAddressRoom() );
		paramBean.setMailingAddressTownCode( contactBean.getMailingAddressTownCode() );
		paramBean.setMailingAddressVillage( contactBean.getMailingAddressVillage() );
		paramBean.setMailingAddressNeighborhood( contactBean.getMailingAddressNeighborhood() );
		paramBean.setMailingAddressStreet( contactBean.getMailingAddressStreet() );
		paramBean.setMailingAddressSection( contactBean.getMailingAddressSection() );
		paramBean.setMailingAddressLane( contactBean.getMailingAddressLane() );
		paramBean.setMailingAddressAlley( contactBean.getMailingAddressAlley() );
		paramBean.setMailingAddressNo( contactBean.getMailingAddressNo() );
		paramBean.setMailingAddressFloor( contactBean.getMailingAddressFloor() );
		paramBean.setMailingAddressRoom( contactBean.getMailingAddressRoom() );
		paramBean.setResidenceStatus( contactBean.getResidenceStatusCode() );
		paramBean.setHomePhoneCode( contactBean.getHomePhoneCode() );
		paramBean.setHomePhoneNumber( contactBean.getHomePhoneNumber() );
		paramBean.setEmail( contactBean.getEmail() );
		paramBean.setMobileNumber( contactBean.getMobileNumber() );
		paramBean.setBranchBankCode( contactBean.getBranchBankCode() );
		paramBean.setServiceAssociateDeptCode( contactBean.getServiceAssociateDeptCode() );
		paramBean.setServiceAssociate( contactBean.getServiceAssociate() );
		paramBean.setServiceAssociateBranchCode( contactBean.getServiceAssociateBranchCode() == null ? "943"
																									 : contactBean.getServiceAssociateBranchCode() );
		paramBean.setRent( contactBean.getRent() );

		return paramBean;
	}

	private PersonalGuaranteeParamBean getPersonalGuaranteeParamBean( PersonalGuaranteeBean guaranteeBean )
	{
		PersonalGuaranteeParamBean paramBean = new PersonalGuaranteeParamBean();
		paramBean.setUserSubType( guaranteeBean.getUserSubType() );
		paramBean.setGuarantyReasonCode( guaranteeBean.getGuarantyReasonCode() );
		paramBean.setOtherGuarantyReason( guaranteeBean.getOtherGuarantyReason() );
		paramBean.setRelationBorrowerType( guaranteeBean.getRelationBorrowerType() );
		paramBean.setIsCohabiting( guaranteeBean.getIsCohabiting() );

		return paramBean;
	}

	private PersonalJobParamBean getPersonalJobParamBean( PersonalJobBean jobBean )
	{
		PersonalJobParamBean paramBean = new PersonalJobParamBean();
		paramBean.setJobSubType( jobBean.getJobSubType() );
		paramBean.setCompanyName( jobBean.getCompanyName() );
		paramBean.setAnnualIncome( jobBean.getAnnualIncome() );
		paramBean.setSeniorityYear( jobBean.getSeniorityYear() );
		paramBean.setSeniorityMonth( jobBean.getSeniorityMonth() );
		paramBean.setTitleType( jobBean.getTitleType() );
		paramBean.setCompanyTaxNo( jobBean.getCompanyTaxNo() );
		paramBean.setCompanyPhoneCode( jobBean.getCompanyPhoneCode() );
		paramBean.setCompanyPhoneNumber( jobBean.getCompanyPhoneNumber() );
		paramBean.setCompanyPhoneExt( jobBean.getCompanyPhoneExt() );
		paramBean.setCompanyAddressTownCode( jobBean.getCompanyAddressTownCode() );
		paramBean.setCompanyAddressVillage( jobBean.getCompanyAddressVillage() );
		paramBean.setCompanyAddressNeighborhood( jobBean.getCompanyAddressNeighborhood() );
		paramBean.setCompanyAddressStreet( jobBean.getCompanyAddressStreet() );
		paramBean.setCompanyAddressSection( jobBean.getCompanyAddressSection() );
		paramBean.setCompanyAddressLane( jobBean.getCompanyAddressLane() );
		paramBean.setCompanyAddressAlley( jobBean.getCompanyAddressAlley() );
		paramBean.setCompanyAddressNo( jobBean.getCompanyAddressNo() );
		paramBean.setCompanyAddressFloor( jobBean.getCompanyAddressFloor() );
		paramBean.setCompanyAddressRoom( jobBean.getCompanyAddressRoom() );
		paramBean.setAmountPerMonthCode( jobBean.getAmountPerMonthCode() );
		paramBean.setEmpNo( jobBean.getEmpNo() );
		return paramBean;
	}
}
