package com.megabank.olp.apply.controller.ixml;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.ixml.bean.LoginArgBean;
import com.megabank.olp.apply.controller.ixml.bean.QueryCertArgBean;
import com.megabank.olp.apply.controller.ixml.bean.QueryVerifyResultArgBean;
import com.megabank.olp.apply.service.ixml.IxmlService;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.utility.web.CommonAppUtils;
import com.megabank.olp.client.sender.ixml.doCallback.bean.IxmlDoCallbackArgBean;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping( "ixml" )
public class IxmlController extends BaseController
{

	@Autowired
	private IxmlService service;

	/**
	 * 申請憑證/簽章, 執行後的回調
	 *
	 * @return
	 * @throws IOException
	 * @throws URISyntaxException
	 */
	@PostMapping( "doCallback" )
	public Map<String, Object> doCallback( @RequestBody @Validated IxmlDoCallbackArgBean argBean ) throws IOException, URISyntaxException
	{
		return getResponseMap( service.doCallback( argBean ) );
	}

	/**
	 * 取得配置中的 ixml 參數
	 */
	@PostMapping( "getParams" )
	public Map<String, Object> getParams()
	{
		return getResponseMap( service.getParams() );
	}

	/**
	 * 查詢所有憑證
	 *
	 * @return
	 */
	@PostMapping( "login" )
	public Map<String, Object> login( @RequestBody @Validated LoginArgBean argBean, HttpServletRequest request )
	{
		service.setSession( request );

		String clientAddress = CommonAppUtils.getClientAddress( request );

		return getResponseMap( service.login( argBean.getId(), argBean.getAction(), argBean.getLoanType(), argBean.getUserType(), clientAddress ) );
	}

	/**
	 * 查詢所有憑證
	 *
	 * @return
	 */
	@PostMapping( "queryCert" )
	public Map<String, Object> queryCert( @RequestBody @Validated QueryCertArgBean argBean, HttpServletRequest request )
	{
		String clientAddress = CommonAppUtils.getClientAddress( request );

		boolean res = service.queryCert( argBean.getId(), clientAddress ) != null ? true : false;

		return getResponseMap( res );
	}

	/**
	 * 查詢所有憑證
	 *
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@PostMapping( "queryVerifyResult" )
	public Map<String, Object> queryVerifyResult( @RequestBody @Validated QueryVerifyResultArgBean argBean, HttpServletRequest request )
		throws UnsupportedEncodingException
	{
		service.setSession( request );

		String clientAddress = CommonAppUtils.getClientAddress( request );

		return getResponseMap( service.queryVerifyResult( argBean.getVerifyNo(), argBean.getId(), argBean.getToken(), argBean.getLoanType(),
														  clientAddress ) );
	}

	/**
	 * 重查 ixml 憑證申請紀錄
	 *
	 * @return
	 */
	@PostMapping( "retryIxmlQueryVerifyResult" )
	public Map<String, Object> retryIxmlQueryVerifyResult()
	{
		service.retryQueryVerifyResult();

		return getResponseMap();
	}

	/**
	 * 刪除憑證
	 *
	 * @return
	 */
	@PostMapping( "revokeCert" )
	public Map<String, Object> revokeCert( @RequestBody @Validated QueryCertArgBean argBean, HttpServletRequest request )
	{
		String clientAddress = CommonAppUtils.getClientAddress( request );

		return getResponseMap( service.revokeCert( argBean.getId(), clientAddress ) );
	}
}
