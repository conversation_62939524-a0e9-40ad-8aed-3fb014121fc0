package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanCounter;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanCounterDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanCounterDAO extends BasePojoDAO<ApplyLoanCounter, Long>
{
	public Long create()
	{
		ApplyLoanCounter pojo = new ApplyLoanCounter();

		return super.createPojo( pojo );
	}

	@Override
	protected Class<ApplyLoanCounter> getPojoClass()
	{
		return ApplyLoanCounter.class;
	}
}
