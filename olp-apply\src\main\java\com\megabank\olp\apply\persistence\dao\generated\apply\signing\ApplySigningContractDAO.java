package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningContractCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProdKindDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeRecipientSystemDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeSigningContractTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeSigningSendStatusDAO;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.utility.enums.SigningContractTypeEnum;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplySigningContractDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySigningContractDAO extends BasePojoDAO<ApplySigningContract, Long>
{
	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	private static final String SIGNING_CONTRACT_ID_CONSTANT = "signingContractId";

	private static final String SIGNING_CONTRACT_TYPE_CONSTANT = "signingContractType";

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeLoanTypeDAO codeLoanTypeDAO;

	@Autowired
	private CodeSigningSendStatusDAO codeSigningSendStatusDAO;

	@Autowired
	private CodeSigningContractTypeDAO codeSigningContractTypeDAO;

	@Autowired
	private CodeRecipientSystemDAO codeRecipientSystemDAO;

	@Autowired
	private CodeProdKindDAO codeProdKindDAO;

	public Long create( SigningContractCreatedParamBean paramBean )
	{
		Validate.notBlank( paramBean.getBranchBankCode() );
		Validate.notBlank( paramBean.getContractNo() );
		Validate.notBlank( paramBean.getSigningContractType() );
		Validate.notNull( paramBean.getExpiredDate() );
		Validate.notNull( paramBean.getLoanAmt() );
		Validate.notNull( paramBean.getLoanPeriod() );
		Validate.notNull( paramBean.getOneTimeFee() );
		Validate.notNull( paramBean.getCreditCheckFee() );
		Validate.notNull( paramBean.getPreliminaryFee() );
		Validate.notNull( paramBean.getPreliminaryFee() );
		Validate.notNull( paramBean.getDrawDownType() );
		Validate.notNull( paramBean.getLendingPlan() );

		ApplySigningContract pojo = new ApplySigningContract();
		pojo.setContractNo( paramBean.getContractNo() );
		pojo.setContractVersion( paramBean.getContractVersion() );
		pojo.setCourtName( paramBean.getCourtName() );
		pojo.setProductCode( paramBean.getProductCode() );
		pojo.setCodeLoanType( codeLoanTypeDAO.read( paramBean.getLoanType() ) );
		pojo.setCodeSigningContractType( codeSigningContractTypeDAO.read( paramBean.getSigningContractType() ) );
		pojo.setCodeBranchBank( codeBranchBankDAO.getPojoByBankCode( paramBean.getBranchBankCode() ) );
		pojo.setExpiredDate( paramBean.getExpiredDate() );
		pojo.setLoanAmt( paramBean.getLoanAmt() );
		pojo.setLoanPeriod( paramBean.getLoanPeriod() );
		pojo.setLoanPurpose( paramBean.getLoanPurpose() );
		pojo.setDrawDownType( paramBean.getDrawDownType() );
		pojo.setOneTimeFee( paramBean.getOneTimeFee() );
		pojo.setPreliminaryFee( paramBean.getPreliminaryFee() );
		pojo.setCreditCheckFee( paramBean.getCreditCheckFee() );
		pojo.setRenewFee( paramBean.getRenewFee() );
		pojo.setChangeFee( paramBean.getChangeFee() );
		pojo.setCertFee( paramBean.getCertFee() );
		pojo.setReissueFee( paramBean.getReissueFee() );
		pojo.setRepaymentMethod( paramBean.getRepaymentMethod() );
		pojo.setLendingPlan( paramBean.getLendingPlan() );
		pojo.setAdvancedRedemptionTitle( paramBean.getAdvancedRedemptionTitle() );
		pojo.setAdvancedRedemptionDesc( paramBean.getAdvancedRedemptionDesc() );
		pojo.setAdvancedRateTitle( paramBean.getAdvancedRateTitle() );
		pojo.setAdvancedRateDesc( paramBean.getAdvancedRateDesc() );
		pojo.setAdvancedApr( paramBean.getAdvancedApr() );
		pojo.setLimitedRedemptionTitle( paramBean.getLimitedRedemptionTitle() );
		pojo.setLimitedRedemptionDesc( paramBean.getLimitedRedemptionDesc() );
		pojo.setLimitedRateTitle( paramBean.getLimitedRateTitle() );
		pojo.setLimitedRateDesc( paramBean.getLimitedRateDesc() );
		pojo.setLimitedApr( paramBean.getLimitedApr() );
		pojo.setShowOption( paramBean.getShowOption() );
		pojo.setOtherInfoTitle( paramBean.getOtherInfoTitle() );
		pojo.setOtherInfoDesc( paramBean.getOtherInfoDesc() );
		pojo.setGeneralGuaranteePlan( paramBean.getGeneralGuaranteePlan() );
		pojo.setGeneralGuaranteePlanInfo( paramBean.getGeneralGuaranteePlanInfo() );
		pojo.setGuaranteeAmt( paramBean.getGuaranteeAmt() );
		pojo.setJointGuaranteePlan( paramBean.getJointGuaranteePlan() );
		pojo.setJointGuaranteePlanInfo( paramBean.getJointGuaranteePlanInfo() );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );
		pojo.setLoanPlanCode( paramBean.getLoanPlan() );
		pojo.setGrpCntrNo( paramBean.getGrpCntrNo() );
		pojo.setGivenApprBegDate( paramBean.getGivenApprBegDate() );
		pojo.setGivenApprEndDate( paramBean.getGivenApprEndDate() );
		pojo.setBaseRate( paramBean.getBaseRate() );
		pojo.setIsRepayment( paramBean.getIsRepayment() );
		pojo.setIsAppropiration( paramBean.getIsRepayment() == null ? null : false );
		pojo.setStaffRule( paramBean.getStaffRule() );
		pojo.setCodeRecipientSystem( codeRecipientSystemDAO.read( paramBean.getContractRecipientId() ) );
		if( paramBean.getProdKind() != null )
			pojo.setCodeProdKind( codeProdKindDAO.read( paramBean.getProdKind() ) );
		pojo.setLnDate( paramBean.getLnDate() );

		return super.createPojo( pojo );
	}

	public List<Long> getNeedToNotifiedBankIds( String loanType )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.getBranchBankIds" );
		nativeQuery.setParameter( SIGNING_CONTRACT_TYPE_CONSTANT, SigningContractTypeEnum.COMPLETED.getContext(), String.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );

		return nativeQuery.getResultList();
	}

	public ApplySigningContract getPojoByContractNo( String contractNo )
	{
		Validate.notNull( contractNo );

		NameValueBean condition = new NameValueBean( ApplySigningContract.CONTRACT_NO_CONSTANT, contractNo );

		return getUniquePojoByProperty( condition, ApplySigningContract.TABLENAME_CONSTANT );
	}

	public ApplySigningContract getPojoByContractNoToNull( String contractNo )
	{
		Validate.notNull( contractNo );

		NameValueBean condition = new NameValueBean( ApplySigningContract.CONTRACT_NO_CONSTANT, contractNo );

		return getUniquePojoByProperty( condition );
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplySigningContract> getPojoByProductCodeAndInetResponseStat( String productCode, Integer inetResponseStatus )
	{
		Validate.notBlank( productCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.getPojoByProductCodeAndInetResponseStat" );
		nativeQuery.setParameter( "productCode", productCode, String.class );
		nativeQuery.setParameter( "inetResponseStatus", BigDecimal.valueOf( inetResponseStatus ), BigDecimal.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplySigningContract.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplySigningContract> getPojosBySendStatus( String sendStatusCode )
	{
		Validate.notBlank( sendStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.getPojosBySendStatus" );
		nativeQuery.setParameter( "sendStatusCode", sendStatusCode, String.class );
		nativeQuery.setParameter( SIGNING_CONTRACT_TYPE_CONSTANT, SigningContractTypeEnum.COMPLETED.getContext(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplySigningContract.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplySigningContract> getPojosBySendStatusAndIsAppropiration( String sendStatusCode )
	{
		Validate.notBlank( sendStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.getPojosBySendStatusAndIsAppropiration" );
		nativeQuery.setParameter( "sendStatusCode", sendStatusCode, String.class );
		nativeQuery.setParameter( SIGNING_CONTRACT_TYPE_CONSTANT, SigningContractTypeEnum.COMPLETED.getContext(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplySigningContract.class );

		return nativeQuery.getResultList();
	}

	public ApplySigningContract read( Long signingContractId )
	{
		Validate.notNull( signingContractId );

		return getPojoByPK( signingContractId, ApplySigningContract.TABLENAME_CONSTANT );
	}

	public Long updateACH( String contractNo, Boolean isNeedACH )
	{
		Validate.notNull( contractNo );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setUpdatedDate( new Date() );
		pojo.setIsNeedAch( isNeedACH );

		return pojo.getSigningContractId();
	}

	public Long updateAppropirationStatusAndFee( String contractNo, Integer preliminaryFee, Integer creditCheckFee )
	{
		Validate.notNull( contractNo );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setResendCount( 0 );
		pojo.setIsAppropiration( true );
		pojo.setPreliminaryFee( preliminaryFee );
		pojo.setCreditCheckFee( creditCheckFee );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public int updateBranchBank( List<Long> signingContractIds, Long branchBankId )
	{
		Validate.notEmpty( signingContractIds );
		Validate.notNull( branchBankId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.updateBranchBank" );
		nativeQuery.setParameterList( SIGNING_CONTRACT_ID_CONSTANT, signingContractIds, Long.class );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateCompleted( String contractNo, String signingContractType, String sendStatusCode )
	{
		Validate.notNull( contractNo );
		Validate.notBlank( signingContractType );
		Validate.notBlank( sendStatusCode );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setCodeSigningContractType( codeSigningContractTypeDAO.read( signingContractType ) );
		pojo.setCodeSigningSendStatus( codeSigningSendStatusDAO.read( sendStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public Long updateCompleted( String contractNo, String signingContractType, String sendStatusCode, byte[] pdfContent )
	{
		Validate.notNull( contractNo );
		Validate.notBlank( signingContractType );
		Validate.notBlank( sendStatusCode );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setCodeSigningContractType( codeSigningContractTypeDAO.read( signingContractType ) );
		pojo.setCodeSigningSendStatus( codeSigningSendStatusDAO.read( sendStatusCode ) );
		pojo.setPdfContent( new ImmutableByteArray( pdfContent ) );
		pojo.setUpdatedDate( new Date() );
		pojo.setIsAppropiration( true );

		return pojo.getSigningContractId();
	}

	public Long updateDiscard( String contractNo )
	{
		Validate.notNull( contractNo );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setDiscard( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public Long updateInetResponseStat( String contractNo, Integer stat )
	{
		Validate.notNull( contractNo );
		Validate.notNull( stat );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setInetResponseStatus( stat );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public int updateNotified( List<Long> signingContractIds )
	{
		Validate.notEmpty( signingContractIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.updateNotified" );
		nativeQuery.setParameterList( SIGNING_CONTRACT_ID_CONSTANT, signingContractIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long signingContractId )
	{
		Validate.notNull( signingContractId );

		ApplySigningContract pojo = read( signingContractId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public Long updatePayeeInfo( Long signingContractId, Long payeeInfoId )
	{
		Validate.notNull( signingContractId );
		Validate.notNull( payeeInfoId );

		ApplySigningContract pojo = read( signingContractId );
		pojo.setPayeeInfoId( payeeInfoId );

		return pojo.getSigningContractId();
	}

	public Long updatePdfContent( String contractNo, byte[] pdfContent )
	{
		Validate.notNull( contractNo );
		Validate.notNull( pdfContent );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setPdfContent( new ImmutableByteArray( pdfContent ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public Long updateResendCount( Long signingContractId )
	{
		Validate.notNull( signingContractId );

		ApplySigningContract pojo = read( signingContractId );
		pojo.setResendCount( pojo.getResendCount() + 1 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public Long updateSendStatus( Long signingContractId, String sendStatusCode )
	{
		Validate.notNull( signingContractId );
		Validate.notBlank( sendStatusCode );

		ApplySigningContract pojo = read( signingContractId );
		pojo.setCodeSigningSendStatus( codeSigningSendStatusDAO.read( sendStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	public Long updateSigningContractType( String contractNo, String signingContractType )
	{
		Validate.notNull( contractNo );
		Validate.notBlank( signingContractType );

		ApplySigningContract pojo = getPojoByContractNo( contractNo );
		pojo.setCodeSigningContractType( codeSigningContractTypeDAO.read( signingContractType ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getSigningContractId();
	}

	@Override
	protected Class<ApplySigningContract> getPojoClass()
	{
		return ApplySigningContract.class;
	}

}
