/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.houseloantrial;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanTrialBasicInfoBean extends BaseBean
{
	@Min( 0 )
	@Max( 99 )
	private Integer userAge;

	@Min( 0 )
	@Max( 999 )
	private Integer userChildren;

	private String jobType;

	private String titleType;

	@Min( 0 )
	@Max( ********* )
	private Integer userIncome;

	private Integer loanPurpose;

	private Integer buyHouseFrom;

	@Digits( integer = 9, fraction = 0 )
	private BigDecimal totalPrice;

	private Boolean cr3ditCard;

	private Boolean pay;

	private Boolean borrow;

	private Boolean cashCard;

	private Boolean cashCardBalance;

	private Boolean balance;

	public Boolean getBalance()
	{
		return balance;
	}

	public Boolean getBorrow()
	{
		return borrow;
	}

	public Integer getBuyHouseFrom()
	{
		return buyHouseFrom;
	}

	public Boolean getCashCard()
	{
		return cashCard;
	}

	public Boolean getCashCardBalance()
	{
		return cashCardBalance;
	}

	public Boolean getCr3ditCard()
	{
		return cr3ditCard;
	}

	public String getJobType()
	{
		return jobType;
	}

	public Integer getLoanPurpose()
	{
		return loanPurpose;
	}

	public Boolean getPay()
	{
		return pay;
	}

	public String getTitleType()
	{
		return titleType;
	}

	public BigDecimal getTotalPrice()
	{
		return totalPrice;
	}

	public Integer getUserAge()
	{
		return userAge;
	}

	public Integer getUserChildren()
	{
		return userChildren;
	}

	public Integer getUserIncome()
	{
		return userIncome;
	}

	public void setBalance( Boolean balance )
	{
		this.balance = balance;
	}

	public void setBorrow( Boolean borrow )
	{
		this.borrow = borrow;
	}

	public void setBuyHouseFrom( Integer buyHouseFrom )
	{
		this.buyHouseFrom = buyHouseFrom;
	}

	public void setCashCard( Boolean cashCard )
	{
		this.cashCard = cashCard;
	}

	public void setCashCardBalance( Boolean cashCardBalance )
	{
		this.cashCardBalance = cashCardBalance;
	}

	public void setCr3ditCard( Boolean cr3ditCard )
	{
		this.cr3ditCard = cr3ditCard;
	}

	public void setJobType( String jobType )
	{
		this.jobType = jobType;
	}

	public void setLoanPurpose( Integer loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setPay( Boolean pay )
	{
		this.pay = pay;
	}

	public void setTitleType( String titleType )
	{
		this.titleType = titleType;
	}

	public void setTotalPrice( BigDecimal totalPrice )
	{
		this.totalPrice = totalPrice;
	}

	public void setUserAge( Integer userAge )
	{
		this.userAge = userAge;
	}

	public void setUserChildren( Integer userChildren )
	{
		this.userChildren = userChildren;
	}

	public void setUserIncome( Integer userIncome )
	{
		this.userIncome = userIncome;
	}
}
