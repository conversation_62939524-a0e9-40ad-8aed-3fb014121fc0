package com.megabank.olp.apply.controller.management.iloan;

import java.util.Map;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.megabank.olp.apply.controller.management.bean.apply.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.apply.LoanDiscardedArgBean;
import com.megabank.olp.apply.controller.management.bean.apply.TransferCaseArgBean;
import com.megabank.olp.apply.service.management.LoanApplyService;
import com.megabank.olp.base.layer.BaseController;

@RestController
@RequestMapping( "management/apply/iloan" )
public class LoanApplyILoanController extends BaseController
{
	@Autowired
	private LoanApplyService loanApplyService;

	@PostMapping( "discardLoan" )
	public Map<String, Object> discardLoan( @RequestBody @Validated LoanDiscardedArgBean argBean )
	{
		loanApplyService.discardLoan( argBean.getCaseNo(), argBean.getEmployeeId(), argBean.getEmployeeName() );

		return getResponseMap();
	}

	/**
	 * 更新申請完成件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		loanApplyService.updateLoanCompletedBranchBank( argBean.getCaseNo(), argBean.getBranchBankCode(), argBean.getEmployeeId(),
		                                                argBean.getEmployeeName() );

		return getResponseMap();
	}

	@PostMapping( "transferCase" )
	public Map<String, Object> transferCase( @RequestBody @Validated TransferCaseArgBean argBean )
	{
		loanApplyService.transferCase( argBean.getCaseNo() );

		return getResponseMap();
	}
}
