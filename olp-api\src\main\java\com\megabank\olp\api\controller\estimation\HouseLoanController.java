/**
 *
 */
package com.megabank.olp.api.controller.estimation;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.controller.estimation.bean.CalculateBasicInfoBean;
import com.megabank.olp.api.controller.estimation.bean.CalculateLoanInfoBean;
import com.megabank.olp.api.controller.estimation.bean.ContactMeBasicInfoBean;
import com.megabank.olp.api.controller.estimation.bean.ContactMeLoanInfoBean;
import com.megabank.olp.api.controller.estimation.bean.EvaluateEstimateInfoBean;
import com.megabank.olp.api.controller.estimation.bean.EvaluateHouseInfoBean;
import com.megabank.olp.api.controller.estimation.bean.HouseLoanCalculateArgBean;
import com.megabank.olp.api.controller.estimation.bean.HouseLoanContactMeArgBean;
import com.megabank.olp.api.controller.estimation.bean.HouseLoanEvaluateArgBean;
import com.megabank.olp.api.service.estimation.HouseLoanService;
import com.megabank.olp.api.service.estimation.bean.CalculateBasicDataBean;
import com.megabank.olp.api.service.estimation.bean.CalculateLoanDataBean;
import com.megabank.olp.api.service.estimation.bean.ContactMeBasicDataBean;
import com.megabank.olp.api.service.estimation.bean.ContactMeLoanDataBean;
import com.megabank.olp.api.service.estimation.bean.EvaluateEstimateDataBean;
import com.megabank.olp.api.service.estimation.bean.EvaluateHouseDataBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanCalculateParamBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanContactMeParamBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanEvaluateParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "estimation/houseloan" )
public class HouseLoanController extends BaseController
{
	@Autowired
	private HouseLoanService service;

	@PostMapping( "calculate" )
	public Map<String, Object> calculate( @RequestBody @Validated HouseLoanCalculateArgBean argBean )
	{
		return getResponseMap( service.calculate( mapCalculateParamBean( argBean ) ) );
	}

	@PostMapping( "contactMe" )
	public Map<String, Object> contactMe( @RequestBody @Validated HouseLoanContactMeArgBean argBean )
	{
		return getResponseMap( service.contactMe( mapContactMeParamBean( argBean ) ) );
	}

	@PostMapping( "evaluate" )
	public Map<String, Object> evaluate( @RequestBody @Validated HouseLoanEvaluateArgBean argBean )
	{
		return getResponseMap( service.evaluate( mapEvaluateParamBean( argBean ) ) );
	}

	private CalculateBasicDataBean mapCalculateBasicData( CalculateBasicInfoBean infoBean )
	{
		CalculateBasicDataBean dataBean = new CalculateBasicDataBean();

		if( infoBean == null )
			return dataBean;

		dataBean.setBalance( infoBean.getBalance() );
		dataBean.setBorrow( infoBean.getBorrow() );
		dataBean.setBuyHouseFrom( infoBean.getBuyHouseFrom() );
		dataBean.setCashCard( infoBean.getCashCard() );
		dataBean.setCashCardBalance( infoBean.getCashCardBalance() );
		dataBean.setCr3ditCard( infoBean.getCreditcard() );
		dataBean.setLoanPurpose( infoBean.getLoanPurpose() );
		dataBean.setPay( infoBean.getPay() );
		dataBean.setTotalPrice( infoBean.getTotalPrice() );
		dataBean.setUserAge( infoBean.getUserAge() );
		dataBean.setUserChildren( infoBean.getUserChildren() );
		dataBean.setUserIncome( infoBean.getUserIncome() );
		dataBean.setUserJob( infoBean.getUserJob() );
		dataBean.setUserTitle( infoBean.getUserTitle() );

		return dataBean;
	}

	private CalculateLoanDataBean mapCalculateLoanData( CalculateLoanInfoBean infoBean )
	{
		CalculateLoanDataBean dataBean = new CalculateLoanDataBean();

		if( infoBean == null )
			return dataBean;

		dataBean.setRate( infoBean.getRate() );
		dataBean.setTopLoanCredit( infoBean.getTopLoanCredit() );

		return dataBean;
	}

	private HouseLoanCalculateParamBean mapCalculateParamBean( HouseLoanCalculateArgBean argBean )
	{
		HouseLoanCalculateParamBean paramBean = new HouseLoanCalculateParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setCaseID( argBean.getCaseID() );
		paramBean.setCreatedDate( argBean.getCreatedDate() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setBasicInfo( mapCalculateBasicData( argBean.getBasicInfo() ) );
		paramBean.setLoanInfo( mapCalculateLoanData( argBean.getLoanInfo() ) );

		return paramBean;
	}

	private ContactMeBasicDataBean mapContactMeBasicData( ContactMeBasicInfoBean argBean )
	{
		ContactMeBasicDataBean paramBean = new ContactMeBasicDataBean();
		paramBean.setCallBackTime( argBean.getCallBackTime() );
		paramBean.setcName( argBean.getcName() );
		return paramBean;
	}

	private ContactMeLoanDataBean mapContactMeLoanData( ContactMeLoanInfoBean infoBean )
	{
		ContactMeLoanDataBean dataBean = new ContactMeLoanDataBean();

		if( infoBean == null )
			return dataBean;

		dataBean.setAddress( infoBean.getAddress() );
		dataBean.setCounty( infoBean.getCounty() );
		dataBean.setDistrict( infoBean.getDistrict() );
		dataBean.setLoanBalance( infoBean.getLoanBalance() );
		dataBean.setLoanPurpose( infoBean.getLoanPurpose() );

		return dataBean;
	}

	private HouseLoanContactMeParamBean mapContactMeParamBean( HouseLoanContactMeArgBean argBean )
	{
		HouseLoanContactMeParamBean paramBean = new HouseLoanContactMeParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setCaseID( argBean.getCaseID() );
		paramBean.setCreatedDate( argBean.getCreatedDate() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setOtherMsg( argBean.getOtherMsg() );
		paramBean.setBasicInfo( mapContactMeBasicData( argBean.getBasicInfo() ) );
		paramBean.setLoanInfo( mapContactMeLoanData( argBean.getLoanInfo() ) );

		return paramBean;
	}

	private EvaluateEstimateDataBean mapEvaluateEstimateData( EvaluateEstimateInfoBean infoBean )
	{
		EvaluateEstimateDataBean dataBean = new EvaluateEstimateDataBean();

		if( infoBean == null )
			return dataBean;

		dataBean.setAv750( infoBean.getAv750() );

		return dataBean;
	}

	private EvaluateHouseDataBean mapEvaluateHouseData( EvaluateHouseInfoBean infoBean )
	{
		EvaluateHouseDataBean dataBean = new EvaluateHouseDataBean();

		if( infoBean == null )
			return dataBean;

		dataBean.setAddr( infoBean.getAddr() );
		dataBean.setbAge( infoBean.getbAge() );
		dataBean.setbAreaP( infoBean.getbAreaP() );
		dataBean.setbTypeInt( infoBean.getbTypeInt() );
		dataBean.setCounty( infoBean.getCounty() );
		dataBean.setDistrict( infoBean.getDistrict() );
		dataBean.setFloors( infoBean.getFloors() );
		dataBean.setLevel( infoBean.getLevel() );
		dataBean.setLevelSelect( infoBean.getLevelSelect() );
		dataBean.setParking( infoBean.getParking() );
		dataBean.setParkingGTY( infoBean.getParkingGTY() );
		dataBean.setParkingP( infoBean.getParkingP() );

		return dataBean;
	}

	private HouseLoanEvaluateParamBean mapEvaluateParamBean( HouseLoanEvaluateArgBean argBean )
	{
		HouseLoanEvaluateParamBean paramBean = new HouseLoanEvaluateParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setCaseID( argBean.getCaseID() );
		paramBean.setCreatedDate( argBean.getCreatedDate() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setHouseInfo( mapEvaluateHouseData( argBean.getHouseInfo() ) );
		paramBean.setEstimateInfo( mapEvaluateEstimateData( argBean.getEstimateInfo() ) );

		return paramBean;
	}

}
