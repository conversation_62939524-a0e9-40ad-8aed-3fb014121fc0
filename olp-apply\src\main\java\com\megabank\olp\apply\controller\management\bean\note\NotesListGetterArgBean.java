/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.note;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

public class NotesListGetterArgBean extends BaseBean
{
	@NotNull
	private Long id;

	@NotBlank
	private String type;

	public NotesListGetterArgBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return id
	 */
	public Long getId()
	{
		return id;
	}

	/**
	 *
	 * @return type
	 */
	public String getType()
	{
		return type;
	}

	/**
	 *
	 * @param id
	 */
	public void setId( Long id )
	{
		this.id = id;
	}

	/**
	 *
	 * @param type
	 */
	public void setType( String type )
	{
		this.type = type;
	}
}
