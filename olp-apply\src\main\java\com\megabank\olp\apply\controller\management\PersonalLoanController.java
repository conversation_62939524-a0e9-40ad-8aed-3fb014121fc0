package com.megabank.olp.apply.controller.management;

import java.io.IOException;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.personalloan.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.personalloan.LoanApplyCompletedExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.personalloan.LoanApplyCompletedListedArgBean;
import com.megabank.olp.apply.controller.management.bean.personalloan.LoanApplyInterruptedExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.personalloan.LoanApplyInterruptedListedArgBean;
import com.megabank.olp.apply.controller.management.bean.personalloan.PersonalLoanDetailGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.personalloan.ProcessStatusUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.personalloan.TransmissionStatusUpdatedArgBean;
import com.megabank.olp.apply.service.loan.DownloadService;
import com.megabank.olp.apply.service.management.LoanApplyService;
import com.megabank.olp.apply.service.management.bean.loan.LoanApplyExportedParamBean;
import com.megabank.olp.apply.service.management.bean.loan.LoanApplyListedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */
@RestController
@RequestMapping( "management/personalloan" )
public class PersonalLoanController extends BaseController
{
	@Autowired
	private LoanApplyService loanApplyService;

	@Autowired
	private DownloadService downloadService;

	/**
	 * 下載信貸申請案件pdf
	 *
	 * @param argBean
	 * @return
	 * @throws IOException
	 */
	@PostMapping( "downloadPdf" )
	public Map<String, Object> downloadPdf( @RequestBody @Validated PersonalLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( downloadService.downloadUnEncryptApplyPdf( loanId ) );
	}

	/**
	 * 信貸申請完成案件列表 輸出檔(eloan)
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportLoanApplyCompletedEloan" )
	public Map<String, Object> exportLoanApplyCompletedEloan( @RequestBody @Validated LoanApplyCompletedExportedArgBean argBean )
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setDiscard( argBean.getDiscard() );
		paramBean.setEmpBranchBankCode( argBean.getEmpBranchBankCode() );

		return getResponseMap( loanApplyService.exportPersonalLoanCompletedEloan( paramBean ) );
	}

	/**
	 * 信貸申請完成案件列表 輸出檔(iloan)
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportLoanApplyCompletedIloan" )
	public Map<String, Object> exportLoanApplyCompletedIloan( @RequestBody @Validated LoanApplyCompletedExportedArgBean argBean )
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setDiscard( argBean.getDiscard() );
		paramBean.setEmpBranchBankCode( argBean.getEmpBranchBankCode() );
		
		paramBean.setEmpSubBranchBankCode( argBean.getEmpSubBranchBankCode() );
		paramBean.setIntroduceBranchBankId( argBean.getIntroduceBranchBankId() );
		paramBean.setIntroduceEmpId( argBean.getIntroduceEmpId() );
		paramBean.setEmpId( argBean.getEmpId() );
		paramBean.setOriginalBranchBankCode( argBean.getOriginalBranchBankCode() );

		return getResponseMap( loanApplyService.exportPersonalLoanCompletedIloan( paramBean ) );
	}

	/**
	 * 信貸申請中斷案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportLoanApplyInterrupted" )
	public Map<String, Object> exportLoanApplyInterrupted( @RequestBody @Validated LoanApplyInterruptedExportedArgBean argBean )
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( loanApplyService.exportPersonalLoanInterrupted( paramBean ) );
	}

	/**
	 * 取得信貸申請案件詳細內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getDetail" )
	public Map<String, Object> getDetail( @RequestBody @Validated PersonalLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( loanApplyService.getPersonalLoanDetail( loanId ) );
	}

	/**
	 * 取得信貸申請案件補件內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getLoanAttachment" )
	public Map<String, Object> getLoanAttachment( @RequestBody @Validated PersonalLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( loanApplyService.getLoanAttachment( loanId ) );
	}

	/**
	 * 取得改派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getReassignBranchBank" )
	public Map<String, Object> getReassignBranchBank( @RequestBody @Validated PersonalLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( loanApplyService.getPersonalLoanReassignBranchBank( loanId ) );
	}

	/**
	 * 取得信貸申請完成案件列表(eloan)
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "listLoanApplyCompletedEloan" )
	public Map<String, Object> listLoanApplyCompletedEloan( @RequestBody @Validated LoanApplyCompletedListedArgBean argBean )
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setDiscard( argBean.getDiscard() );
		paramBean.setEmpBranchBankCode( argBean.getEmpBranchBankCode() );

		return getResponseMap( loanApplyService.listPersonalLoanCompletedEloan( paramBean ) );
	}

	/**
	 * 取得信貸申請完成案件列表(iloan)
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "listLoanApplyCompletedIloan" )
	public Map<String, Object> listLoanApplyCompletedIloan( @RequestBody @Validated LoanApplyCompletedListedArgBean argBean )
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setDiscard( argBean.getDiscard() );
		paramBean.setEmpBranchBankCode( argBean.getEmpBranchBankCode() );		
		
		paramBean.setEmpSubBranchBankCode( argBean.getEmpSubBranchBankCode() );
		
		paramBean.setIntroduceBranchBankId( argBean.getIntroduceBranchBankId() );
		paramBean.setIntroduceEmpId( argBean.getIntroduceEmpId() );
		paramBean.setEmpId( argBean.getEmpId() );
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setOriginalBranchBankCode( argBean.getOriginalBranchBankCode() );

		return getResponseMap( loanApplyService.listPersonalLoanCompletedILoan( paramBean ) );
	}

	/**
	 * 取得信貸申請中斷案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "listLoanApplyInterrupted" )
	public Map<String, Object> listLoanApplyInterrupted( @RequestBody @Validated LoanApplyInterruptedListedArgBean argBean )
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( loanApplyService.listPersonalLoanInterrupted( paramBean ) );

	}

	/**
	 * 更改中斷案件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();
		Long branchBankId = argBean.getBranchBankId();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanApplyService.updateLoanInterruptedBranchBank( loanId, branchBankId, employeeId, employeeName ) );
	}

	/**
	 * 更新案件處理狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateProcessStatus" )
	public Map<String, Object> updateProcessStatus( @RequestBody @Validated ProcessStatusUpdatedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();
		String processStatus = argBean.getProcessStatus();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanApplyService.updateProcessStatus( loanId, processStatus, employeeId, employeeName ) );
	}

	/**
	 * 更新案件進件狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateTransmissionStatus" )
	public Map<String, Object> updateTransmissionStatus( @RequestBody @Validated TransmissionStatusUpdatedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();
		String transmissionStatus = argBean.getTransmissionStatusUpdatedEnum().getContext();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanApplyService.updateTransmissionStatus( loanId, transmissionStatus, employeeId, employeeName ) );
	}
}
