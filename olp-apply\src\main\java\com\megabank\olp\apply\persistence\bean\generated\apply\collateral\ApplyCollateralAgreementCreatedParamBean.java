package com.megabank.olp.apply.persistence.bean.generated.apply.collateral;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyCollateralAgreementCreatedParamBean extends BaseBean
{
	private Long collateralId;

	private String email;

	private Boolean isCollateralFullPayment;

	private String collateralAddressTownCode;

	private String collateralAddressStreet;

	private Long collateralAmt;

	private Long loanAmt;

	private String warrantee1;

	private String warrantee2;

	private String warrantee3;

	private Long guranteeAmt;

	private String signatory;

	private String termNo;

	private Date borrowerSignDate;

	private String loanProduct1;

	private String loanProduct2;

	private String loanProduct3;

	private String loanProduct4;

	private String loanProduct5;

	public ApplyCollateralAgreementCreatedParamBean()
	{
		// default constructor
	}

	public Date getBorrowerSignDate()
	{
		return borrowerSignDate;
	}

	public String getCollateralAddressStreet()
	{
		return collateralAddressStreet;
	}

	public String getCollateralAddressTownCode()
	{
		return collateralAddressTownCode;
	}

	public Long getCollateralAmt()
	{
		return collateralAmt;
	}

	public Long getCollateralId()
	{
		return collateralId;
	}

	public String getEmail()
	{
		return email;
	}

	public Long getGuranteeAmt()
	{
		return guranteeAmt;
	}

	public Boolean getIsCollateralFullPayment()
	{
		return isCollateralFullPayment;
	}

	public Long getLoanAmt()
	{
		return loanAmt;
	}

	public String getLoanProduct1()
	{
		return loanProduct1;
	}

	public String getLoanProduct2()
	{
		return loanProduct2;
	}

	public String getLoanProduct3()
	{
		return loanProduct3;
	}

	public String getLoanProduct4()
	{
		return loanProduct4;
	}

	public String getLoanProduct5()
	{
		return loanProduct5;
	}

	public String getSignatory()
	{
		return signatory;
	}

	public String getTermNo()
	{
		return termNo;
	}

	public String getWarrantee1()
	{
		return warrantee1;
	}

	public String getWarrantee2()
	{
		return warrantee2;
	}

	public String getWarrantee3()
	{
		return warrantee3;
	}

	public void setBorrowerSignDate( Date borrowerSignDate )
	{
		this.borrowerSignDate = borrowerSignDate;
	}

	public void setCollateralAddressStreet( String collateralAddressStreet )
	{
		this.collateralAddressStreet = collateralAddressStreet;
	}

	public void setCollateralAddressTownCode( String collateralAddressTownCode )
	{
		this.collateralAddressTownCode = collateralAddressTownCode;
	}

	public void setCollateralAmt( Long collateralAmt )
	{
		this.collateralAmt = collateralAmt;
	}

	public void setCollateralId( Long collateralId )
	{
		this.collateralId = collateralId;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setGuranteeAmt( Long guranteeAmt )
	{
		this.guranteeAmt = guranteeAmt;
	}

	public void setIsCollateralFullPayment( Boolean isCollateralFullPayment )
	{
		this.isCollateralFullPayment = isCollateralFullPayment;
	}

	public void setLoanAmt( Long loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanProduct1( String loanProduct1 )
	{
		this.loanProduct1 = loanProduct1;
	}

	public void setLoanProduct2( String loanProduct2 )
	{
		this.loanProduct2 = loanProduct2;
	}

	public void setLoanProduct3( String loanProduct3 )
	{
		this.loanProduct3 = loanProduct3;
	}

	public void setLoanProduct4( String loanProduct4 )
	{
		this.loanProduct4 = loanProduct4;
	}

	public void setLoanProduct5( String loanProduct5 )
	{
		this.loanProduct5 = loanProduct5;
	}

	public void setSignatory( String signatory )
	{
		this.signatory = signatory;
	}

	public void setTermNo( String termNo )
	{
		this.termNo = termNo;
	}

	public void setWarrantee1( String warrantee1 )
	{
		this.warrantee1 = warrantee1;
	}

	public void setWarrantee2( String warrantee2 )
	{
		this.warrantee2 = warrantee2;
	}

	public void setWarrantee3( String warrantee3 )
	{
		this.warrantee3 = warrantee3;
	}

}
