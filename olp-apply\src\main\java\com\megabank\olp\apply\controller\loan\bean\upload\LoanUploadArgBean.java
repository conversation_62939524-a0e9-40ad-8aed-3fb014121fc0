package com.megabank.olp.apply.controller.loan.bean.upload;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class LoanUploadArgBean extends BaseBean
{
	@NotBlank
	private String loanType;

	private String caseNo;

	public LoanUploadArgBean()
	{
		// default constructor
	}

	public String getLoanType()
	{
		return loanType;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
}
