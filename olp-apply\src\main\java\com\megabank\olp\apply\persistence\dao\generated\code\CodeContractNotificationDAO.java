package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeContractNotification;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeContractNotificationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeContractNotificationDAO extends BasePojoDAO<CodeContractNotification, String>
{
	public CodeContractNotification read( String notificationCode )
	{
		Validate.notBlank( notificationCode );

		return getPojoByPK( notificationCode, CodeContractNotification.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeContractNotification> getPojoClass()
	{
		return CodeContractNotification.class;
	}
}
