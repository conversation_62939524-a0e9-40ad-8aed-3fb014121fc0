package com.megabank.olp.apply.persistence.dao.generated.apply.contact;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.contact.ApplyContactMeCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeContactTimeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProcessDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeSexDAO;
import com.megabank.olp.apply.persistence.pojo.apply.contact.ApplyContactMe;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyContactMeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyContactMeDAO extends BasePojoDAO<ApplyContactMe, Long>
{
	private static final String CONTACT_ME_ID_CONSTANT = "contactMeId";

	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String PROCESS_CODE_CONSTANT = "processCode";

	private static final String CURRENT_TIME_CONSTANT = "currentTime";

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeContactTimeDAO codeContactTimeDAO;

	@Autowired
	private CodeProcessDAO codeProcessDAO;

	@Autowired
	private CodeSexDAO codeSexDAO;

	public Long create( ApplyContactMeCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getBranchBankId() );
		Validate.notNull( paramBean.getFinalBranchBankId() );
		Validate.notBlank( paramBean.getCaseNo() );
		Validate.notBlank( paramBean.getName() );
		Validate.notBlank( paramBean.getMobileNumber() );
		Validate.notBlank( paramBean.getContactTimeCode() );
		Validate.notBlank( paramBean.getProcessCode() );
		Validate.notBlank( paramBean.getSexCode() );

		ApplyContactMe pojo = new ApplyContactMe();
		pojo.setCaseNo( paramBean.getCaseNo() );
		pojo.setName( paramBean.getName() );
		pojo.setPhoneCode( paramBean.getPhoneCode() );
		pojo.setPhoneNumber( paramBean.getPhoneNumber() );
		pojo.setPhoneExt( paramBean.getPhoneExt() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setOtherMsg( paramBean.getOtherMsg() );
		pojo.setCodeBranchBankByBranchBankId( codeBranchBankDAO.read( paramBean.getBranchBankId() ) );
		pojo.setCodeBranchBankByFinalBranchBankId( codeBranchBankDAO.read( paramBean.getFinalBranchBankId() ) );
		pojo.setCodeContactTime( codeContactTimeDAO.read( paramBean.getContactTimeCode() ) );
		pojo.setCodeProcess( codeProcessDAO.read( paramBean.getProcessCode() ) );
		pojo.setNotified( false );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );
		pojo.setCodeSex( codeSexDAO.read( paramBean.getSexCode() ) );
		pojo.setLoanPlanCode( paramBean.getLoanPlanCode() );
		pojo.setIntroducerBranchBankId( paramBean.getIntroducerBranchBankId() );
		pojo.setIntroducerEmpId( paramBean.getIntroducerEmpId() );

		return super.createPojo( pojo );
	}

	public Long getLatestId()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "contactme.getLatestId" );

		return ( Long )nativeQuery.uniqueResult();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedBankIds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "contactme.getBranchBankIds" );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedContactMeIds( Long finalBranchBankId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "contactme.getContactMeIds" );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, finalBranchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	public ApplyContactMe read( Long contactMeId )
	{
		Validate.notNull( contactMeId );

		return getPojoByPK( contactMeId, ApplyContactMe.TABLENAME_CONSTANT );
	}

	public Long updateFinalBranchBank( Long contactMeId, Long branchBankId )
	{
		Validate.notNull( contactMeId );
		Validate.notNull( branchBankId );

		ApplyContactMe pojo = read( contactMeId );
		pojo.setCodeBranchBankByFinalBranchBankId( codeBranchBankDAO.read( branchBankId ) );
		pojo.setNotified( false );
		pojo.setUpdatedDate( new Date() );

		return pojo.getContactMeId();

	}

	public int updateNotified( List<Long> contactMeIds )
	{
		Validate.notEmpty( contactMeIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "contactme.updateNotified" );
		nativeQuery.setParameterList( CONTACT_ME_ID_CONSTANT, contactMeIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long contactMeId )
	{
		Validate.notNull( contactMeId );

		ApplyContactMe pojo = read( contactMeId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getContactMeId();
	}

	public Long updateProcess( Long contactMeId, String processCode )
	{
		Validate.notNull( contactMeId );
		Validate.notBlank( processCode );

		ApplyContactMe pojo = read( contactMeId );
		pojo.setCodeProcess( codeProcessDAO.read( processCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getContactMeId();
	}

	@Override
	protected Class<ApplyContactMe> getPojoClass()
	{
		return ApplyContactMe.class;
	}

	public List<Long> getUncompletedCaseIn30Days( String mobileNumber, List<String> processStatusCodes )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "contactme.getUncompletedCaseIn30Days" );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, mobileNumber, String.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, processStatusCodes, String.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );

		return nativeQuery.getResultList();
	}
}
