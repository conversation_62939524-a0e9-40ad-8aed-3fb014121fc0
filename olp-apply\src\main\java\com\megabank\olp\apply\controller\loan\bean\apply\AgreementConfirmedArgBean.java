package com.megabank.olp.apply.controller.loan.bean.apply;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AgreementConfirmedArgBean extends LoanApplyBaseArgBean
{
	@JsonProperty( "items" )
	private List<Long> itemIds;

	private Boolean notUsTaxpayer;

	private Boolean notOuttwTaxpayer;

	private String rateAdjNotify;

	private Boolean crossMarketing;

	public AgreementConfirmedArgBean()
	{
		// default constructor
	}

	public Boolean getCrossMarketing()
	{
		return crossMarketing;
	}

	public List<Long> getItemIds()
	{
		return itemIds;
	}

	public Boolean getNotOuttwTaxpayer()
	{
		return notOuttwTaxpayer;
	}

	public Boolean getNotUsTaxpayer()
	{
		return notUsTaxpayer;
	}

	public String getRateAdjNotify()
	{
		return rateAdjNotify;
	}

	public void setCrossMarketing( Boolean crossMarketing )
	{
		this.crossMarketing = crossMarketing;
	}

	public void setItemIds( List<Long> itemIds )
	{
		this.itemIds = itemIds;
	}

	public void setNotOuttwTaxpayer( Boolean notOuttwTaxpayer )
	{
		this.notOuttwTaxpayer = notOuttwTaxpayer;
	}

	public void setNotUsTaxpayer( Boolean notUsTaxpayer )
	{
		this.notUsTaxpayer = notUsTaxpayer;
	}

	public void setRateAdjNotify( String rateAdjNotify )
	{
		this.rateAdjNotify = rateAdjNotify;
	}

}
