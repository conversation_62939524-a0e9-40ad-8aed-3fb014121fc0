package com.megabank.olp.apply.persistence.pojo.house;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseLoanInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_loan_info" )
public class HouseLoanInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_loan_info";

	public static final String LOAN_TRIAL_INFO_ID_CONSTANT = "loanTrialInfoId";

	public static final String HOUSE_LOAN_TRIAL_INFO_CONSTANT = "houseLoanTrialInfo";

	public static final String TOP_LOAN_CREDIT_CONSTANT = "topLoanCredit";

	public static final String RATE_CONSTANT = "rate";

	private long loanTrialInfoId;

	private transient HouseLoanTrialInfo houseLoanTrialInfo;

	private BigDecimal topLoanCredit;

	private BigDecimal rate;

	public HouseLoanInfo()
	{}

	public HouseLoanInfo( HouseLoanTrialInfo houseLoanTrialInfo )
	{
		this.houseLoanTrialInfo = houseLoanTrialInfo;
	}

	public HouseLoanInfo( Long loanTrialInfoId )
	{
		this.loanTrialInfoId = loanTrialInfoId;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public HouseLoanTrialInfo getHouseLoanTrialInfo()
	{
		return houseLoanTrialInfo;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "houseLoanTrialInfo" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "loan_trial_info_id", unique = true, nullable = false )
	public long getLoanTrialInfoId()
	{
		return loanTrialInfoId;
	}

	@Column( name = "rate", precision = 10 )
	public BigDecimal getRate()
	{
		return rate;
	}

	@Column( name = "top_loan_credit", precision = 11 )
	public BigDecimal getTopLoanCredit()
	{
		return topLoanCredit;
	}

	public void setHouseLoanTrialInfo( HouseLoanTrialInfo houseLoanTrialInfo )
	{
		this.houseLoanTrialInfo = houseLoanTrialInfo;
	}

	public void setLoanTrialInfoId( long loanTrialInfoId )
	{
		this.loanTrialInfoId = loanTrialInfoId;
	}

	public void setRate( BigDecimal rate )
	{
		this.rate = rate;
	}

	public void setTopLoanCredit( BigDecimal topLoanCredit )
	{
		this.topLoanCredit = topLoanCredit;
	}
}