package com.megabank.olp.api.controller.eloan.bean.signing;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class BankAccountInfoBean extends BaseBean
{
	@NotBlank
	private String bankCode;

	@NotBlank
	private String account;

	public BankAccountInfoBean()
	{}

	public String getAccount()
	{
		return account;
	}

	public String getBankCode()
	{
		return bankCode;
	}

	public void setAccount( String account )
	{
		this.account = account;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}
}
