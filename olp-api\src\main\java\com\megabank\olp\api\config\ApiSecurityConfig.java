package com.megabank.olp.api.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

import com.megabank.olp.api.security.ApiAuthJwtTokenFilter;
import com.megabank.olp.api.security.ApiFirstFilter;
import com.megabank.olp.api.service.jwt.ApiJwtDecryptService;
import com.megabank.olp.base.filter.MyAuthJWTTokenFilter;
import com.megabank.olp.system.config.BaseSecurityConfig;
import com.megabank.olp.system.filter.MyFirstFilter;

@Configuration
@EnableWebSecurity
public class ApiSecurityConfig extends BaseSecurityConfig
{
	@Autowired
	private ApiJwtDecryptService apiJwtDecryptService;
	
	@Override
	public MyAuthJWTTokenFilter authJWTTokenFilter()
	{
		return new ApiAuthJwtTokenFilter( authJwtService, sessionInfoThreadLocal );
	}

	@Override
	public MyFirstFilter firstFilter()
	{
		return new ApiFirstFilter( apiJwtDecryptService, authJwtService, jwtService, tranLogService, getPermitUrls(), requestInfoThreadLocal, sessionInfoThreadLocal );
	}

	@Override
	protected String[] getPermitUrls()
	{
		return new String[]{ "/open/**", "/error/**", "/btt/**", "/eloan/**", "/estimation/houseloan/**", "/iloan/**", "/mydata/**" };
	}
}
