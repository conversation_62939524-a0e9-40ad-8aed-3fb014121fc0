package com.megabank.olp.apply.controller.loan;

import java.io.IOException;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.loan.bean.apply.LoanApplyBaseArgBean;
import com.megabank.olp.apply.service.loan.UploadService;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/generate" )
public class GenerateController extends BaseController
{
	@Autowired
	private UploadService uploadService;

	/**
	 * 更新ixml pdf內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "generateIxmlAuthorizePdf" )
	public Map<String, Object> generateIxmlAuthorizePdf( @RequestBody @Validated LoanApplyBaseArgBean argBean ) throws IOException
	{
		return getResponseMap( uploadService.updateIxmlPdfContent( argBean.getLoanType() ) );
	}
}
