package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanRelationCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeRelationTypeDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanRelation;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanRelationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanRelationDAO extends BasePojoDAO<ApplyLoanRelation, Long>
{
	@Autowired
	private ApplyLoanContentDAO applyLoanContentDAO;

	@Autowired
	private CodeRelationTypeDAO codeRelationTypeDAO;

	public Long create( ApplyLoanRelationCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notBlank( paramBean.getRelationIdNo() );
		Validate.notBlank( paramBean.getRelationName() );
		Validate.notBlank( paramBean.getRelationType() );

		ApplyLoanRelation pojo = new ApplyLoanRelation();
		pojo.setApplyLoanContent( applyLoanContentDAO.read( paramBean.getLoanId() ) );
		pojo.setRelationName( paramBean.getRelationName() );
		pojo.setRelationIdNo( paramBean.getRelationIdNo() );
		pojo.setCodeRelationType( codeRelationTypeDAO.read( paramBean.getRelationType() ) );

		return super.createPojo( pojo );
	}

	public void deletePojosByLoanId( Long loanId )
	{
		List<ApplyLoanRelation> pojos = getPojosByLoanId( loanId );

		super.deletePojos( pojos );
	}

	public List<ApplyLoanRelation> getPojosByLoanId( Long loanId )
	{
		Validate.notNull( loanId );

		NameValueBean condition = new NameValueBean( ApplyLoanRelation.APPLY_LOAN_CONTENT_CONSTANT, applyLoanContentDAO.read( loanId ) );

		return getPojosByProperty( condition );
	}

	@Override
	protected Class<ApplyLoanRelation> getPojoClass()
	{
		return ApplyLoanRelation.class;
	}
}
