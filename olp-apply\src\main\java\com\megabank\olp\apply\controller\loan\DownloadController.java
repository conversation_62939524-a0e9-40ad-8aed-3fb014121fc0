package com.megabank.olp.apply.controller.loan;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.loan.bean.download.PdfDownloadedArgBean;
import com.megabank.olp.apply.service.loan.DownloadService;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/download" )
public class DownloadController extends BaseController
{
	@Autowired
	private DownloadService downloadService;

	/**
	 * 下載聯徵中心同意書pdf (IXML)
	 *
	 * @return
	 */
	@PostMapping( "downloadIxmlAuthorizePdf" )
	public Map<String, Object> downloadIxmlAuthorizePdfById( @RequestBody @Validated PdfDownloadedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( downloadService.downloadIxmlAuthorizePdf( loanId ) );
	}

	/**
	 * 下載申請書
	 *
	 * @return
	 */
	@PostMapping( "downloadPdf" )
	public Map<String, Object> downloadPdf( @RequestBody @Validated PdfDownloadedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( downloadService.downloadEncryptApplyPdf( loanId ) );
	}

	/**
	 * 取得申請案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getLoanApplyList" )
	public Map<String, Object> getLoanApplyList()
	{
		return getResponseMap( downloadService.getApplyList() );
	}

}
