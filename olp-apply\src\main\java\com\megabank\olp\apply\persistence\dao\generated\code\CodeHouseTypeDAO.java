package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeHouseType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeHouseTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeHouseTypeDAO extends BasePojoDAO<CodeHouseType, String>
{
	public CodeHouseType read( String code )
	{
		Validate.notNull( code );

		return getPojoByPK( code, CodeHouseType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeHouseType> getPojoClass()
	{
		return CodeHouseType.class;
	}
}
