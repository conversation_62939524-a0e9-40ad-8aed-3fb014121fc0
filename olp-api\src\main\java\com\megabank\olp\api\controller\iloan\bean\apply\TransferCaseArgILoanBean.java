package com.megabank.olp.api.controller.iloan.bean.apply;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoBean;
import com.megabank.olp.base.bean.BaseBean;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

public class TransferCaseArgILoanBean extends BaseBean
{

	@NotBlank
	private String caseNo;

	public TransferCaseArgILoanBean()
	{
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}


}
