package com.megabank.olp.apply.persistence.pojo.apply.note;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import com.megabank.olp.apply.persistence.pojo.apply.attachment.ApplyAttachment;
import com.megabank.olp.apply.persistence.pojo.apply.contact.ApplyContactMe;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.survey.ApplySurvey;
import com.megabank.olp.apply.persistence.pojo.code.CodeNotesAction;
import com.megabank.olp.apply.persistence.pojo.house.HouseContactInfo;
import com.megabank.olp.apply.persistence.pojo.house.HouseLoanTrialInfo;
import com.megabank.olp.apply.persistence.pojo.house.HousePricingInfo;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyNote is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_note" )
public class ApplyNote extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_note";

	public static final String NOTE_ID_CONSTANT = "noteId";

	public static final String APPLY_ATTACHMENT_CONSTANT = "applyAttachment";

	public static final String APPLY_CONTACT_ME_CONSTANT = "applyContactMe";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String APPLY_SURVEY_CONSTANT = "applySurvey";

	public static final String CODE_NOTES_ACTION_CONSTANT = "codeNotesAction";

	public static final String HOUSE_CONTACT_INFO_CONSTANT = "houseContactInfo";

	public static final String HOUSE_LOAN_TRIAL_INFO_CONSTANT = "houseLoanTrialInfo";

	public static final String HOUSE_PRICING_INFO_CONSTANT = "housePricingInfo";

	public static final String EMPLOYEE_ID_CONSTANT = "employeeId";

	public static final String EMPLOYEE_NAME_CONSTANT = "employeeName";

	public static final String NOTE_CONSTANT = "note";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	private Long noteId;

	private transient ApplyAttachment applyAttachment;

	private transient ApplyContactMe applyContactMe;

	private transient ApplyLoan applyLoan;

	private transient ApplySurvey applySurvey;

	private transient CodeNotesAction codeNotesAction;

	private transient HouseContactInfo houseContactInfo;

	private transient HouseLoanTrialInfo houseLoanTrialInfo;

	private transient HousePricingInfo housePricingInfo;

	private String employeeId;

	private String employeeName;

	private String note;

	private Date createdDate;

	public ApplyNote()
	{}

	public ApplyNote( CodeNotesAction codeNotesAction, String employeeId, String employeeName, Date createdDate )
	{
		this.codeNotesAction = codeNotesAction;
		this.employeeId = employeeId;
		this.employeeName = employeeName;
		this.createdDate = createdDate;
	}

	public ApplyNote( Long noteId )
	{
		this.noteId = noteId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "attachment_id" )
	public ApplyAttachment getApplyAttachment()
	{
		return applyAttachment;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "contact_me_id" )
	public ApplyContactMe getApplyContactMe()
	{
		return applyContactMe;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_id" )
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "survey_id" )
	public ApplySurvey getApplySurvey()
	{
		return applySurvey;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "notes_action_code", nullable = false )
	public CodeNotesAction getCodeNotesAction()
	{
		return codeNotesAction;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "employee_id", nullable = false, length = 30 )
	public String getEmployeeId()
	{
		return employeeId;
	}

	@Column( name = "employee_name", nullable = false )
	public String getEmployeeName()
	{
		return employeeName;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "house_contact_info_id" )
	public HouseContactInfo getHouseContactInfo()
	{
		return houseContactInfo;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "house_loan_trial_info_id" )
	public HouseLoanTrialInfo getHouseLoanTrialInfo()
	{
		return houseLoanTrialInfo;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "house_pricing_info_id" )
	public HousePricingInfo getHousePricingInfo()
	{
		return housePricingInfo;
	}

	@Column( name = "note" )
	public String getNote()
	{
		return note;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "note_id", unique = true, nullable = false )
	public Long getNoteId()
	{
		return noteId;
	}

	public void setApplyAttachment( ApplyAttachment applyAttachment )
	{
		this.applyAttachment = applyAttachment;
	}

	public void setApplyContactMe( ApplyContactMe applyContactMe )
	{
		this.applyContactMe = applyContactMe;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setApplySurvey( ApplySurvey applySurvey )
	{
		this.applySurvey = applySurvey;
	}

	public void setCodeNotesAction( CodeNotesAction codeNotesAction )
	{
		this.codeNotesAction = codeNotesAction;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setHouseContactInfo( HouseContactInfo houseContactInfo )
	{
		this.houseContactInfo = houseContactInfo;
	}

	public void setHouseLoanTrialInfo( HouseLoanTrialInfo houseLoanTrialInfo )
	{
		this.houseLoanTrialInfo = houseLoanTrialInfo;
	}

	public void setHousePricingInfo( HousePricingInfo housePricingInfo )
	{
		this.housePricingInfo = housePricingInfo;
	}

	public void setNote( String note )
	{
		this.note = note;
	}

	public void setNoteId( Long noteId )
	{
		this.noteId = noteId;
	}
}