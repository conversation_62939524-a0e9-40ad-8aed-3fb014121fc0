package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningUser;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplySigningUserDAOIntegration
{
	@Autowired
	private ApplySigningUserDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void read()
	{
		Long contractId = 1L;
		Long validatedIdentityId = 1L;

		ApplySigningUser signingUser = dao.read( contractId, validatedIdentityId );

		logger.info( "ApplySigningUser:{}", signingUser );
	}

}
