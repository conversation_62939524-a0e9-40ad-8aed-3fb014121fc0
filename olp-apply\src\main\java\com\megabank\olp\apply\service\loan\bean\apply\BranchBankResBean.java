package com.megabank.olp.apply.service.loan.bean.apply;

import com.megabank.olp.base.bean.BaseBean;

public class BranchBankResBean extends BaseBean
{
	private String code;

	private String name;

	private String address;

	public BranchBankResBean()
	{
		// default constructor
	}

	public String getAddress()
	{
		return address;
	}

	/**
	 *
	 * @return code
	 */
	public String getCode()
	{
		return code;
	}

	/**
	 *
	 * @return name
	 */
	public String getName()
	{
		return name;
	}

	public void setAddress( String address )
	{
		this.address = address;
	}

	/**
	 *
	 * @param code
	 */
	public void setCode( String code )
	{
		this.code = code;
	}

	/**
	 *
	 * @param name
	 */
	public void setName( String name )
	{
		this.name = name;
	}
}
