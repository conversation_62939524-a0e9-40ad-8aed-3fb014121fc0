package com.megabank.olp.apply.controller.loan.bean.apply;

import java.util.List;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;

public class LoanApplySubmittedArgBean extends LoanApplyBaseArgBean
{
	@JsonProperty( "content" )
	@Valid
	private LoanContentBean loanContentBean;

	@JsonProperty( "relation" )
	private List<LoanRelationBean> loanRelationBeans;

	@JsonProperty( "served" )
	private List<LoanServedBean> loanServedBeans;

	public LoanApplySubmittedArgBean()
	{
		// default constructor
	}

	public LoanContentBean getLoanContentBean()
	{
		return loanContentBean;
	}

	public List<LoanRelationBean> getLoanRelationBeans()
	{
		return loanRelationBeans;
	}

	public List<LoanServedBean> getLoanServedBeans()
	{
		return loanServedBeans;
	}

	public void setLoanContentBean( LoanContentBean loanContentBean )
	{
		this.loanContentBean = loanContentBean;
	}

	public void setLoanRelationBeans( List<LoanRelationBean> loanRelationBeans )
	{
		this.loanRelationBeans = loanRelationBeans;
	}

	public void setLoanServedBeans( List<LoanServedBean> loanServedBeans )
	{
		this.loanServedBeans = loanServedBeans;
	}

}