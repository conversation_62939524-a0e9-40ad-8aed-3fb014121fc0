package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningUserCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningUserUpdateParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBorrowerOverdueInformMethodDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeContractNotificationDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTownDAO;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningUser;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.system.persistence.dao.generated.SystemExceptionLogDAO;

/**
 * The ApplySigningUserDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySigningUserDAO extends BasePojoDAO<ApplySigningUser, Long>
{
	@Autowired
	private CodeContractNotificationDAO codeContractNotificationDAO;

	@Autowired
	private CodeTownDAO codeTownDAO;

	@Autowired
	private CodeBorrowerOverdueInformMethodDAO codeBorrowerOverdueInformMethodDAO;

	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	private SystemExceptionLogDAO systemExceptionLogDAO;

	public Long create( SigningUserCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getSigningContractId() );
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notBlank( paramBean.getName() );
		Validate.notBlank( paramBean.getEmail() );
		Validate.notBlank( paramBean.getMobileNumber() );

		ApplySigningUser pojo = new ApplySigningUser();
		pojo.setApplySigningContract( applySigningContractDAO.read( paramBean.getSigningContractId() ) );
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );
		pojo.setName( paramBean.getName() );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setIsYouth( paramBean.getIsYouth() );

		return super.createPojo( pojo );
	}

	public ApplySigningUser read( Long contractId, Long validatedIdentityId )
	{
		Validate.notNull( contractId );
		Validate.notNull( validatedIdentityId );

		NameValueBean[] conditions = new NameValueBean[]{
														  new NameValueBean( ApplySigningUser.APPLY_SIGNING_CONTRACT_CONSTANT,
																			 applySigningContractDAO.read( contractId ) ),
														  new NameValueBean( ApplySigningUser.VALIDATED_IDENTITY_ID_CONSTANT, validatedIdentityId ) };

		return getUniquePojoByProperties( conditions, ApplySigningUser.TABLENAME_CONSTANT );
	}

	public Long update( SigningUserUpdateParamBean paramBean )
	{
		return update( paramBean, new Date() );
	}

	public Long update( SigningUserUpdateParamBean paramBean, Date ts )
	{
		Validate.notNull( paramBean.getContractId() );
		Validate.notNull( paramBean.getContractIdentityId() );
		Validate.notNull( paramBean.getUpdatedIdentityId() );

		ApplySigningUser pojo = read( paramBean.getContractId(), paramBean.getContractIdentityId() );
		pojo.setValidatedIdentityId( paramBean.getUpdatedIdentityId() );
		pojo.setCodeContractNotification( StringUtils
					.isBlank( paramBean.getContractNotificationCode() ) ? null
																		: codeContractNotificationDAO
																					.read( paramBean.getContractNotificationCode() ) );
		pojo.setCodeTown( StringUtils.isBlank( paramBean.getTownCode() ) ? null : codeTownDAO.read( paramBean.getTownCode() ) );
		pojo.setNotificationAddress( paramBean.getStreet() );
		pojo.setAgreeCrossSelling( paramBean.getHasAgreedCrossSelling() );
		pojo.setSingingDate( ts );
		pojo.setSingingTime( ts );
		pojo.setCodeBorrowerOverdueInformMethod( Objects
					.isNull( paramBean.getBorrowerOverdueInformMethod() ) ? null
																		  : codeBorrowerOverdueInformMethodDAO
																					  .read( paramBean.getBorrowerOverdueInformMethod() ) );
		return pojo.getSigningUserId();
	}

	public Long updateVerifiedEmail( Long contractId, Long validatedIdentityId, String verifiedEmail )
	{
		Validate.notNull( contractId );
		Validate.notNull( validatedIdentityId );
		Validate.notNull( verifiedEmail );

		ApplySigningUser pojo = read( contractId, validatedIdentityId );
		pojo.setVerifiedEmail( verifiedEmail );

		return pojo.getSigningUserId();
	}

	@Override
	protected Class<ApplySigningUser> getPojoClass()
	{
		return ApplySigningUser.class;
	}

}
