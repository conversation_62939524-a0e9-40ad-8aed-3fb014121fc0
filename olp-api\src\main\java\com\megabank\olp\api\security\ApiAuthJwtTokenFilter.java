package com.megabank.olp.api.security;

import com.megabank.olp.base.filter.MyAuthJWTTokenFilter;
import com.megabank.olp.base.service.jwt.AuthJwtService;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;

public class ApiAuthJwtTokenFilter extends MyAuthJWTTokenFilter
{
	public ApiAuthJwtTokenFilter( AuthJwtService authJwtService, SessionInfoThreadLocal sessionInfoThreadLocal )
	{
		super( authJwtService, sessionInfoThreadLocal );
	}

	@Override
	protected boolean checkEnoughIdentity()
	{
		return false;
	}

}
