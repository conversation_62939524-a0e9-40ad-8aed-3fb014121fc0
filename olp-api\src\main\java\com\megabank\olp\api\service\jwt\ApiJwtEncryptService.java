package com.megabank.olp.api.service.jwt;

import java.util.Map;

import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.service.jwt.bean.ApiJwtEncryptArgBean;
import com.megabank.olp.base.service.jwt.encrypt.BaseJwtService;

@Service
public class ApiJwtEncryptService extends BaseJwtService<ApiJwtEncryptArgBean> {
	@Value("${eloan.secret.token}")
	private String jwtSecretApi;

	@Override
	protected Map<String, Object> getClaimsMap(ApiJwtEncryptArgBean argBean) {
		return new HashedMap();
	}

	@Override
	protected String getCurrentJwtSecret() {
		return jwtSecretApi;
	}
}
