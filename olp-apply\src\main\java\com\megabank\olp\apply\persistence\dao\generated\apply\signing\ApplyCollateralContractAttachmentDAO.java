package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.signing.ApplyCollateralContractAttachmentCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeAttachmentTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyCollateralContractAttachment;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

@Repository
public class ApplyCollateralContractAttachmentDAO extends BasePojoDAO<ApplyCollateralContractAttachment, Long>
{
	private static final String SIGNING_CONTRACT_ID_CONSTANT = "signingContractId";

	private static final String INET_RESPONSE_STATUS = "inetResponseStatus";

	private static final String TRANSMISSION_STATUS_CODE = "transmissionStatusCode";

	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@Autowired
	private CodeAttachmentTypeDAO codeAttachmentTypeDAO;

	public Long create( ApplyCollateralContractAttachmentCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getSigningContractId() );
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notBlank( paramBean.getAttachmentType() );
		Validate.notBlank( paramBean.getFileName() );
		Validate.notBlank( paramBean.getTransmissionStatusCode() );

		ApplyCollateralContractAttachment pojo = new ApplyCollateralContractAttachment();
		pojo.setApplySigningContract( applySigningContractDAO.read( paramBean.getSigningContractId() ) );
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setCodeAttachmentType( codeAttachmentTypeDAO.read( paramBean.getAttachmentType() ) );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( paramBean.getTransmissionStatusCode() ) );
		pojo.setCompressFileContent( paramBean.getCompressFileContent() );
		pojo.setFileName( paramBean.getFileName() );
		pojo.setFileSize( paramBean.getFileSize() );
		pojo.setFileContent( paramBean.getFileContent() );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );
		pojo.setResend( 0 );
		pojo.setInetResponseStatus( paramBean.getInetResponseStatus() );
		pojo.setInetRptName( paramBean.getInetRptName() );

		return super.createPojo( pojo );
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplyCollateralContractAttachment> getPojosByInetResponseStatus( Integer inetResponseStatus )
	{
		Validate.notNull( inetResponseStatus );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "applyCollateralContractAttachment.getPojosByInetResponseStatus" );
		nativeQuery.setParameter( INET_RESPONSE_STATUS, BigDecimal.valueOf( inetResponseStatus ), BigDecimal.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyCollateralContractAttachment.class );

		return nativeQuery.getResultList();
	}

	public List<ApplyCollateralContractAttachment> getPojosBySigningContractId( Long signingContractId )
	{
		NameValueBean condition = new NameValueBean( ApplyCollateralContractAttachment.SIGNING_CONTRACT_ID_CONSTANT,
													 applySigningContractDAO.read( signingContractId ) );
		return getPojosByProperty( condition );
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplyCollateralContractAttachment> getPojosBySigningContractIdAndInetResponseStatus( Long signingContractId,
																									 Integer inetResponseStatus )
	{
		Validate.notNull( signingContractId );

		NativeQuery nativeQuery =
								( NativeQuery )getNamedQuery( "applyCollateralContractAttachment.getPojosBySigningContractIdAndInetResponseStatus" );
		nativeQuery.setParameter( SIGNING_CONTRACT_ID_CONSTANT, signingContractId );
		nativeQuery.setParameter( INET_RESPONSE_STATUS, BigDecimal.valueOf( inetResponseStatus ), BigDecimal.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyCollateralContractAttachment.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplyCollateralContractAttachment> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "applyCollateralContractAttachment.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CODE, transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyCollateralContractAttachment.class );

		return nativeQuery.getResultList();
	}

	public ApplyCollateralContractAttachment read( Long attachmentId )
	{
		Validate.notNull( attachmentId );
		return getPojoByPK( attachmentId, ApplyCollateralContractAttachment.TABLENAME_CONSTANT );
	}

	public Long updatePdfContentViaInet( Long attachmentId, byte[] pdfContent, Integer inetResponseStatus )
	{
		Validate.notNull( attachmentId );
		Validate.notNull( inetResponseStatus );
		Validate.notNull( pdfContent );

		ApplyCollateralContractAttachment pojo = read( attachmentId );
		pojo.setFileContent( new ImmutableByteArray( pdfContent ) );
		pojo.setFileSize( ( long )pdfContent.length );
		pojo.setInetResponseStatus( inetResponseStatus );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long updateResend( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		ApplyCollateralContractAttachment pojo = read( attachmentId );
		pojo.setResend( pojo.getResend() + 1 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long updateTransmissionStatus( Long attachmentId, String transmissionStatusCode )
	{
		Validate.notNull( attachmentId );
		Validate.notBlank( transmissionStatusCode );

		ApplyCollateralContractAttachment pojo = read( attachmentId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	@Override
	protected Class<ApplyCollateralContractAttachment> getPojoClass()
	{
		return ApplyCollateralContractAttachment.class;
	}
}
