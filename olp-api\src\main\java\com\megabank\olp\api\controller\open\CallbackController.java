package com.megabank.olp.api.controller.open;

import java.io.IOException;

import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.megabank.olp.api.service.sso.SsoService;
import com.megabank.olp.api.service.sso.bean.SsoRedirectInfoResBean;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.message.MyObjectMapper;
import com.megabank.olp.system.persistence.dao.generated.SystemTranLogDAO;

@Controller
@RequestMapping( "open/callback" )
public class CallbackController extends BaseController
{
	@Value( "${callback.client.url.pib}" )
	private String clientUrlPib;

	@Value( "${callback.client.url.otherbank}" )
	private String clientUrlOtherbank;

	@Value( "${callback.client.url.sso}" )
	private String clientUrlSso;

	@Value( "${callback.client.key.finGetResultsKey1}" )
	private String finGetResultsKey1;

	@Value( "${callback.client.key.finGetResultsKey2}" )
	private String finGetResultsKey2;

	@Autowired
	private SsoService ssoService;

	@Autowired
	SystemTranLogDAO systemTranLogDAO;

	@Autowired
	@Qualifier( "serviceObjectMapper" )
	private MyObjectMapper objectMapper;

	@RequestMapping( "otherbank" )
	public void callbackByOtherBank( HttpServletResponse response, String userType, String serviceType, String loanType ) throws IOException
	{
		userType = filterString( userType );
		serviceType = filterString( serviceType );
		loanType = filterString( loanType );

		StringBuilder builder = new StringBuilder();
		builder.append( clientUrlOtherbank );
		builder.append( "?" );
		builder.append( "userType=" + userType );
		builder.append( "&" );
		builder.append( "serviceType=" + serviceType );
		builder.append( "&" );
		builder.append( "loanType=" + loanType );

		String location = builder.toString();

		response.sendRedirect( location );
	}

	@RequestMapping( "pib" )
	public void callbackByPib( HttpServletResponse response, String authCode ) throws IOException
	{
		authCode = filterString( authCode );

		StringBuilder builder = new StringBuilder();
		builder.append( clientUrlPib );
		builder.append( "?authCode=" + authCode );

		String location = builder.toString();

		response.sendRedirect( location );
	}

	@RequestMapping( "sso" )
	public void callbackBySso( HttpServletResponse response, @RequestParam( value = "x-auth-token" ) String authToken, String loginType, String sysId,
							   String serviceType, String userType )
		throws IOException
	{

		authToken = filterString( authToken );
		loginType = filterString( loginType );
		sysId = filterString( sysId );
		serviceType = filterString( serviceType );
		userType = filterString( userType );

		SsoRedirectInfoResBean resBean = ssoService.getRedirectInfo( serviceType, userType );

		StringBuilder builder = new StringBuilder();
		builder.append( clientUrlSso );
		builder.append( "?authToken=" + authToken );
		builder.append( "&loginType=" + loginType );
		builder.append( "&sysId=" + sysId );
		builder.append( "&serviceType=" + resBean.getServiceType() );
		builder.append( "&loanType=" + resBean.getLoanType() );
		builder.append( "&userType=" + resBean.getUserType() );

		String location = builder.toString();

		response.sendRedirect( location );
	}

	private String filterString( String str )
	{
		return StringUtils.removeAll( StringUtils.trimToEmpty( str ), "[^a-zA-z0-9+-=]" );
	}
}
