/**
 *
 */
package com.megabank.olp.apply.service.loan.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class AppropriationBean extends BaseBean
{
	/**
	 * 撥款/繳款帳號銀行代碼
	 */
	private String bankAcctCode;

	/**
	 * 撥款/繳款銀行帳號
	 */
	private String bankAcctNo;

	/**
	 * 契約寄送地址縣市名稱
	 */
	private String borrowerAddressCityCode;

	/**
	 * 契約寄送地址區域名稱
	 */
	private String borrowerAddressTownCode;

	/**
	 * 契約寄送地址
	 */
	private String borrowerAddressStreet;

	/**
	 * 撥款日期
	 */
	private String appropriationDate;

	/**
	 * 借款人契約寄送方式編碼
	 */
	private String borrowerContractSendingMethodCode;

	private String borrowerContractSendingMethod;

	/**
	 * 利率調整通知方式編碼
	 */
	private String rateAdjustInformMethodCode;

	private String rateAdjustInformMethod;

	/**
	 * 首次還款日
	 */
	private String firstPaymentDate;

	/**
	 * 每月還款日
	 */
	private String repayment;

	public AppropriationBean()
	{}

	public String getAppropriationDate()
	{
		return appropriationDate;
	}

	public String getBankAcctCode()
	{
		return bankAcctCode;
	}

	public String getBankAcctNo()
	{
		return bankAcctNo;
	}

	public String getBorrowerAddressCityCode()
	{
		return borrowerAddressCityCode;
	}

	public String getBorrowerAddressStreet()
	{
		return borrowerAddressStreet;
	}

	public String getBorrowerAddressTownCode()
	{
		return borrowerAddressTownCode;
	}

	public String getBorrowerContractSendingMethod()
	{
		return borrowerContractSendingMethod;
	}

	public String getBorrowerContractSendingMethodCode()
	{
		return borrowerContractSendingMethodCode;
	}

	public String getFirstPaymentDate()
	{
		return firstPaymentDate;
	}

	public String getRateAdjustInformMethod()
	{
		return rateAdjustInformMethod;
	}

	public String getRateAdjustInformMethodCode()
	{
		return rateAdjustInformMethodCode;
	}

	public String getRepayment()
	{
		return repayment;
	}

	public void setAppropriationDate( String appropriationDate )
	{
		this.appropriationDate = appropriationDate;
	}

	public void setBankAcctCode( String bankAcctCode )
	{
		this.bankAcctCode = bankAcctCode;
	}

	public void setBankAcctNo( String bankAcctNo )
	{
		this.bankAcctNo = bankAcctNo;
	}

	public void setBorrowerAddressCityCode( String borrowerAddressCityCode )
	{
		this.borrowerAddressCityCode = borrowerAddressCityCode;
	}

	public void setBorrowerAddressStreet( String borrowerAddressStreet )
	{
		this.borrowerAddressStreet = borrowerAddressStreet;
	}

	public void setBorrowerAddressTownCode( String borrowerAddressTownCode )
	{
		this.borrowerAddressTownCode = borrowerAddressTownCode;
	}

	public void setBorrowerContractSendingMethod( String borrowerContractSendingMethod )
	{
		this.borrowerContractSendingMethod = borrowerContractSendingMethod;
	}

	public void setBorrowerContractSendingMethodCode( String borrowerContractSendingMethodCode )
	{
		this.borrowerContractSendingMethodCode = borrowerContractSendingMethodCode;
	}

	public void setFirstPaymentDate( String firstPaymentDate )
	{
		this.firstPaymentDate = firstPaymentDate;
	}

	public void setRateAdjustInformMethod( String rateAdjustInformMethod )
	{
		this.rateAdjustInformMethod = rateAdjustInformMethod;
	}

	public void setRateAdjustInformMethodCode( String rateAdjustInformMethodCode )
	{
		this.rateAdjustInformMethodCode = rateAdjustInformMethodCode;
	}

	public void setRepayment( String repayment )
	{
		this.repayment = repayment;
	}

}
