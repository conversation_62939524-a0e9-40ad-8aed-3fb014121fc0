/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import com.megabank.olp.base.bean.BaseBean;

import java.math.BigDecimal;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningUserUpdateParamBean extends BaseBean
{
	private Long contractId;

	private Long contractIdentityId;

	private Long updatedIdentityId;

	private String contractNotificationCode;

	private String townCode;

	private String street;

	private Boolean hasAgreedCrossSelling;

	private BigDecimal borrowerOverdueInformMethod;

	public SigningUserUpdateParamBean()
	{}

	public Long getContractId()
	{
		return contractId;
	}

	public Long getContractIdentityId()
	{
		return contractIdentityId;
	}

	public String getContractNotificationCode()
	{
		return contractNotificationCode;
	}

	public Boolean getHasAgreedCrossSelling()
	{
		return hasAgreedCrossSelling;
	}

	public String getStreet()
	{
		return street;
	}

	public String getTownCode()
	{
		return townCode;
	}

	public Long getUpdatedIdentityId()
	{
		return updatedIdentityId;
	}

	public BigDecimal getBorrowerOverdueInformMethod()
	{
		return borrowerOverdueInformMethod;
	}

	public void setContractId( Long contractId )
	{
		this.contractId = contractId;
	}

	public void setContractIdentityId( Long contractIdentityId )
	{
		this.contractIdentityId = contractIdentityId;
	}

	public void setContractNotificationCode( String contractNotificationCode )
	{
		this.contractNotificationCode = contractNotificationCode;
	}

	public void setHasAgreedCrossSelling( Boolean hasAgreedCrossSelling )
	{
		this.hasAgreedCrossSelling = hasAgreedCrossSelling;
	}

	public void setStreet( String street )
	{
		this.street = street;
	}

	public void setTownCode( String townCode )
	{
		this.townCode = townCode;
	}

	public void setUpdatedIdentityId( Long updatedIdentityId )
	{
		this.updatedIdentityId = updatedIdentityId;
	}

	public void setBorrowerOverdueInformMethod( BigDecimal borrowerOverdueInformMethod )
	{
		this.borrowerOverdueInformMethod = borrowerOverdueInformMethod;
	}

}
