{"info": {"name": "C001 中鋼總公司消費性貸款測試", "description": "C001中鋼總公司消費性貸款完整申請流程測試", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "loanId", "value": "", "type": "string"}], "item": [{"name": "1. 創建C001申請案件", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loanType\": \"personalloan\",\n  \"plan\": \"C001\",\n  \"refCaseNo\": \"\",\n  \"introduceBrNo\": \"002\"\n}"}, "url": {"raw": "{{baseUrl}}/apply/create", "host": ["{{baseUrl}}"], "path": ["apply", "create"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has loanId\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.loanId).to.exist;", "    pm.collectionVariables.set(\"loanId\", jsonData.data.loanId);", "});"]}}]}, {"name": "2. 確認同意事項", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loanType\": \"personalloan\",\n  \"itemIds\": [1, 2, 3, 5],\n  \"notUsTaxpayer\": true,\n  \"notOuttwTaxpayer\": true,\n  \"rateAdjNotify\": \"Y\",\n  \"crossMarketing\": false\n}"}, "url": {"raw": "{{baseUrl}}/apply/confirmAgreement", "host": ["{{baseUrl}}"], "path": ["apply", "confirmAgreement"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Agreement confirmed\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}, {"name": "3. 提交申請資料", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loanType\": \"personalloan\",\n  \"basicBean\": {\n    \"name\": \"王大明\",\n    \"idNo\": \"A123456789\",\n    \"birthDate\": \"1985-05-15\",\n    \"childrenCount\": 0,\n    \"notUsTaxpayer\": true,\n    \"notOuttwTaxpayer\": true,\n    \"crossMarketing\": false\n  },\n  \"contactBean\": {\n    \"mobileNumber\": \"0912345678\",\n    \"email\": \"<EMAIL>\",\n    \"residenceStatusCode\": \"\",\n    \"homePhoneCode\": \"\",\n    \"homePhoneNumber\": \"\"\n  },\n  \"loanContentBean\": {\n    \"loanRequestAmt\": 500000,\n    \"loanPeriodCode\": \"7\",\n    \"loanPurposeCode\": \"01\",\n    \"caseSourceCode\": \"01\",\n    \"increasingLoan\": false\n  }\n}"}, "url": {"raw": "{{baseUrl}}/apply/submitLoanApplyInfo", "host": ["{{baseUrl}}"], "path": ["apply", "submitLoanApplyInfo"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Application submitted\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}, {"name": "4. 送出申請", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/apply/deliverLoanApply", "host": ["{{baseUrl}}"], "path": ["apply", "deliverLoanApply"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Application delivered\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}, {"name": "5. 查詢申請狀態", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loanType\": \"personalloan\"\n}"}, "url": {"raw": "{{baseUrl}}/apply/getLoanApplyInfo", "host": ["{{baseUrl}}"], "path": ["apply", "getLoanApplyInfo"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Application info retrieved\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.loanPlanCode).to.eql(\"C001\");", "});"]}}]}]}