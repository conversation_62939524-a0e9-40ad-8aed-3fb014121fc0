/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.house;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HousePricingCreatedParamBean extends BaseBean
{
	private String caseNo;

	private String mobileNumber;

	private String email;

	private String branchBankCode;

	private Date createdDate;

	private String processCode;

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}
}
