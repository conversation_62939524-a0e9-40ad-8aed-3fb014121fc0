package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningAppropriationPayment;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean;

@Repository
public class ApplySigningAppropriationPaymentDAO extends BasePojoDAO<ApplySigningAppropriationPayment, Long>
{
	private static final String SIGNING_CONTRACT_ID_CONSTANT = "signingContractId";

	public Long create( Long contractId, PaymentInfoBean bean )
	{
		Validate.notNull( contractId );

		ApplySigningAppropriationPayment pojo = new ApplySigningAppropriationPayment();
		pojo.setSigningContractId( contractId );
		pojo.setBankCode( bean.getBankCode() );
		pojo.setBankName( bean.getBankName() );
		pojo.setRepaymentProductType( bean.getRepaymentProductType() );
		pojo.setRepaymentProduct( bean.getRepaymentProduct() );
		pojo.setBankAcctNo( bean.getBankAcctNo() );
		pojo.setRepaymentAmt( bean.getRepaymentAmt() );
		pojo.setAccountName( bean.getAccountName() );

		return super.createPojo( pojo );
	}

	public List<ApplySigningAppropriationPayment> getPojoByContractId( Long contractId )
	{
		Validate.notNull( contractId );

		NameValueBean condition = new NameValueBean( ApplySigningAppropriationPayment.SIGNING_CONTRACT_ID_CONSTANT, contractId );

		return getPojosByProperty( condition );
	}

	public List<ApplySigningAppropriationPayment> getPojoByContractId( Long contractId, Boolean isDuplicated )
	{
		Validate.notNull( contractId );
		Validate.notNull( isDuplicated );

		NameValueBean[] conditions =
								   new NameValueBean[]{ new NameValueBean( ApplySigningAppropriationPayment.SIGNING_CONTRACT_ID_CONSTANT,
																		   contractId ),
														new NameValueBean( ApplySigningAppropriationPayment.IS_DUPLICATED_CONSTANT, isDuplicated ) };

		return this.getPojosByProperties( conditions );
	}

	public ApplySigningAppropriationPayment read( Long signingContractId )
	{
		Validate.notNull( signingContractId );

		return getPojoByPK( signingContractId, ApplySigningAppropriationPayment.TABLENAME_CONSTANT );
	}

	public int updateIsDuplicated( Long signingContractId )
	{
		Validate.notNull( signingContractId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.updateIsDuplicated" );
		nativeQuery.setParameter( SIGNING_CONTRACT_ID_CONSTANT, signingContractId, Long.class );

		return nativeQuery.executeUpdate();
	}

	@Override
	protected Class<ApplySigningAppropriationPayment> getPojoClass()
	{
		return ApplySigningAppropriationPayment.class;
	}

}
