package com.megabank.olp.apply.service.loan;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.survey.ContactInfoResBean;
import com.megabank.olp.apply.service.loan.bean.survey.ContactInfoSubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.survey.SurveyResultResBean;
import com.megabank.olp.apply.service.loan.bean.survey.SurveySubmittedParamBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class SurveyServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;

	@Autowired
	private SurveyService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getContactInfo()
	{
		ContactInfoResBean resBean = service.getContactInfo();

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getSurveyResult()
	{
		SurveyResultResBean resBean = service.getSurveyResult();

		// logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getThankyouMessage()
	{
		MessageResBean resBean = service.getThankyouMessage();

		logger.info( "resBean:{}", resBean );
	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	@Test
	public void submitContactInfo()
	{
		ContactInfoSubmittedParamBean paramBean = getContactInfoSubmittedParamBean();

		Long id = service.submitContactInfo( paramBean );

		logger.info( "id:{}", id );
	}

	@Test
	public void submitSurvey()
	{
		SurveySubmittedParamBean paramBean = getSurveySubmittedParamBean();

		Long resBean = service.submitSurvey( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	private ContactInfoSubmittedParamBean getContactInfoSubmittedParamBean()
	{
		String name = "王大明";
		String branchBankCode = "3";
		String phoneCode = "02";
		String phoneNumber = "********";
		String phoneExt = "1";
		String mobileNumber = "09********";
		String email = "<EMAIL>";
		String contactTimeCode = "01";
		String otherMsg = "無";

		ContactInfoSubmittedParamBean paramBean = new ContactInfoSubmittedParamBean();
		paramBean.setName( name );
		paramBean.setBranchBankCode( branchBankCode );
		paramBean.setPhoneCode( phoneCode );
		paramBean.setPhoneNumber( phoneNumber );
		paramBean.setPhoneExt( phoneExt );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setEmail( email );
		paramBean.setContactTimeCode( contactTimeCode );
		paramBean.setOtherMsg( otherMsg );

		return paramBean;
	}

	private SurveySubmittedParamBean getSurveySubmittedParamBean()
	{

		int annualIncome = 100; // 年收入
		BigDecimal cashAdvance = new BigDecimal( 0 ); // 信用卡目前預借現金餘額
		BigDecimal creditCardTotalAmt = new BigDecimal( 0 );// 信用卡目前應繳總金額 (含分期、循環及預借現金餘額)
		BigDecimal debitCardTotalAmt = new BigDecimal( 0 ); // 目前現金卡餘額
		boolean holdingCreditCard = true; // 是否持有信用卡
		boolean holdingDebitCard = false; // 是否持有現金卡
		BigDecimal holdingPersonalLoan = new BigDecimal( 0 ); // 目前信用貸款餘額
		String jobSubType = "1";
		BigDecimal revovingCredit = new BigDecimal( 0 ); // 信用卡目前循環信用餘額

		SurveySubmittedParamBean paramBean = new SurveySubmittedParamBean();
		paramBean.setAnnualIncome( annualIncome );
		paramBean.setCashAdvance( cashAdvance );
		paramBean.setCreditCardTotalAmt( creditCardTotalAmt );
		paramBean.setDebitCardTotalAmt( debitCardTotalAmt );
		paramBean.setHoldingCr3ditCard( holdingCreditCard );
		paramBean.setHoldingDebitCard( holdingDebitCard );
		paramBean.setHoldingPersonalLoan( holdingPersonalLoan );
		paramBean.setJobSubType( jobSubType );
		paramBean.setRevolvingCr3dit( revovingCredit );

		return paramBean;
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A********9";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.OTHER_BANK.getContext(), IdentityTypeEnum.OTP.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}

}
