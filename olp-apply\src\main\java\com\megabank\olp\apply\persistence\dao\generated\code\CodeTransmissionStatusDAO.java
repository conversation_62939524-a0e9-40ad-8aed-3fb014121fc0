package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeTransmissionStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeTransmissionStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeTransmissionStatusDAO extends BasePojoDAO<CodeTransmissionStatus, String>
{

	public List<CodeTransmissionStatus> getList()
	{
		return getAllPojos();
	}

	public CodeTransmissionStatus read( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		return getPojoByPK( transmissionStatusCode, CodeTransmissionStatus.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeTransmissionStatus> getPojoClass()
	{
		return CodeTransmissionStatus.class;
	}
}
