package com.megabank.olp.apply.persistence.dao.generated.apply.note;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.dao.generated.apply.attachment.ApplyAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.contact.ApplyContactMeDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.survey.ApplySurveyDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeNotesActionDAO;
import com.megabank.olp.apply.persistence.dao.generated.house.HouseContactInfoDAO;
import com.megabank.olp.apply.persistence.dao.generated.house.HouseLoanTrialInfoDAO;
import com.megabank.olp.apply.persistence.dao.generated.house.HousePricingInfoDAO;
import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.enums.OrderEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyNoteDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyNoteDAO extends BasePojoDAO<ApplyNote, Long>
{
	@Autowired
	private ApplyContactMeDAO applyContactMeDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private ApplySurveyDAO applySurveyDAO;

	@Autowired
	private ApplyAttachmentDAO applyAttachmentDAO;

	@Autowired
	private CodeNotesActionDAO codeNotesActionDAO;

	@Autowired
	private HouseContactInfoDAO houseContactInfoDAO;

	@Autowired
	private HouseLoanTrialInfoDAO houseLoanTrialInfoDAO;

	@Autowired
	private HousePricingInfoDAO housePricingInfoDAO;

	public Long createAttachmentNote( Long attachmentId, String employeeId, String employeeName, String notesActionCode, String note )
	{
		Validate.notNull( attachmentId );
		Validate.notBlank( employeeId );
		Validate.notBlank( employeeName );
		Validate.notBlank( notesActionCode );

		ApplyNote pojo = new ApplyNote();
		pojo.setCodeNotesAction( codeNotesActionDAO.read( notesActionCode ) );
		pojo.setApplyAttachment( applyAttachmentDAO.read( attachmentId ) );
		pojo.setEmployeeId( employeeId );
		pojo.setEmployeeName( employeeName );
		pojo.setNote( note );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public Long createContactMeNote( Long contactMeId, String employeeId, String employeeName, String notesActionCode, String note )
	{
		Validate.notNull( contactMeId );
		Validate.notBlank( employeeId );
		Validate.notBlank( employeeName );
		Validate.notBlank( notesActionCode );

		ApplyNote pojo = new ApplyNote();
		pojo.setCodeNotesAction( codeNotesActionDAO.read( notesActionCode ) );
		pojo.setApplyContactMe( applyContactMeDAO.read( contactMeId ) );
		pojo.setEmployeeId( employeeId );
		pojo.setEmployeeName( employeeName );
		pojo.setNote( note );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public Long createHouseContactNote( Long houseContactId, String employeeId, String employeeName, String notesActionCode, String note )
	{
		Validate.notNull( houseContactId );
		Validate.notBlank( employeeId );
		Validate.notBlank( employeeName );
		Validate.notBlank( notesActionCode );

		ApplyNote pojo = new ApplyNote();
		pojo.setCodeNotesAction( codeNotesActionDAO.read( notesActionCode ) );
		pojo.setHouseContactInfo( houseContactInfoDAO.read( houseContactId ) );
		pojo.setEmployeeId( employeeId );
		pojo.setEmployeeName( employeeName );
		pojo.setNote( note );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public Long createHouseLoanTrialNote( Long loanTrialId, String employeeId, String employeeName, String notesActionCode, String note )
	{
		Validate.notNull( loanTrialId );
		Validate.notBlank( employeeId );
		Validate.notBlank( employeeName );
		Validate.notBlank( notesActionCode );

		ApplyNote pojo = new ApplyNote();
		pojo.setCodeNotesAction( codeNotesActionDAO.read( notesActionCode ) );
		pojo.setHouseLoanTrialInfo( houseLoanTrialInfoDAO.read( loanTrialId ) );
		pojo.setEmployeeId( employeeId );
		pojo.setEmployeeName( employeeName );
		pojo.setNote( note );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public Long createHousePricingNote( Long housePricingId, String employeeId, String employeeName, String notesActionCode, String note )
	{
		Validate.notNull( housePricingId );
		Validate.notBlank( employeeId );
		Validate.notBlank( employeeName );
		Validate.notBlank( notesActionCode );

		ApplyNote pojo = new ApplyNote();
		pojo.setCodeNotesAction( codeNotesActionDAO.read( notesActionCode ) );
		pojo.setHousePricingInfo( housePricingInfoDAO.read( housePricingId ) );
		pojo.setEmployeeId( employeeId );
		pojo.setEmployeeName( employeeName );
		pojo.setNote( note );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public Long createLoanNote( Long loanId, String employeeId, String employeeName, String notesActionCode, String note )
	{
		Validate.notNull( loanId );
		Validate.notBlank( employeeId );
		Validate.notBlank( employeeName );
		Validate.notBlank( notesActionCode );

		ApplyNote pojo = new ApplyNote();
		pojo.setCodeNotesAction( codeNotesActionDAO.read( notesActionCode ) );
		pojo.setApplyLoan( applyLoanDAO.read( loanId ) );
		pojo.setEmployeeId( employeeId );
		pojo.setEmployeeName( employeeName );
		pojo.setNote( note );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public Long createSurveyNote( Long surveyId, String employeeId, String employeeName, String notesActionCode, String note )
	{
		Validate.notNull( surveyId );
		Validate.notBlank( employeeId );
		Validate.notBlank( employeeName );
		Validate.notBlank( notesActionCode );

		ApplyNote pojo = new ApplyNote();
		pojo.setCodeNotesAction( codeNotesActionDAO.read( notesActionCode ) );
		pojo.setApplySurvey( applySurveyDAO.read( surveyId ) );
		pojo.setEmployeeId( employeeId );
		pojo.setEmployeeName( employeeName );
		pojo.setNote( note );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	@SuppressWarnings( { "unchecked", "rawtypes" } )
	public List<ApplyNote> getApplyAttachmentNotes( Long attachmentId )
	{
		NativeQuery<ApplyNote> nativeQuery = ( NativeQuery )getNamedQuery( "applynote.getApplyAttachmentNotes" );
		nativeQuery.setParameter( "attachmentId", attachmentId, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyNote.class );

		return nativeQuery.getResultList();
	}

	public List<ApplyNote> getApplyAttachmentNotes( Long attachmentId, int maxResults )
	{
		Validate.notNull( attachmentId );

		NameValueBean condition = new NameValueBean( ApplyNote.APPLY_ATTACHMENT_CONSTANT, applyAttachmentDAO.read( attachmentId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyNote.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, maxResults, orderBeans );
	}

	public List<ApplyNote> getApplyContactMeNotes( Long contactMeId, int maxResults )
	{
		Validate.notNull( contactMeId );

		NameValueBean condition = new NameValueBean( ApplyNote.APPLY_CONTACT_ME_CONSTANT, applyContactMeDAO.read( contactMeId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyNote.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, maxResults, orderBeans );
	}

	@SuppressWarnings( { "unchecked", "rawtypes" } )
	public List<ApplyNote> getApplyLoanNotes( Long loanId )
	{
		NativeQuery<ApplyNote> nativeQuery = ( NativeQuery )getNamedQuery( "applynote.getApplyLoanNotes" );
		nativeQuery.setParameter( "loanId", loanId, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyNote.class );

		return nativeQuery.getResultList();
	}

	public List<ApplyNote> getApplyLoanNotes( Long loanId, int maxResults )
	{
		Validate.notNull( loanId );

		NameValueBean condition = new NameValueBean( ApplyNote.APPLY_LOAN_CONSTANT, applyLoanDAO.read( loanId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyNote.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, maxResults, orderBeans );
	}

	public List<ApplyNote> getApplySurveyNotes( Long surveyId, int maxResults )
	{
		Validate.notNull( surveyId );

		NameValueBean condition = new NameValueBean( ApplyNote.APPLY_SURVEY_CONSTANT, applySurveyDAO.read( surveyId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyNote.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, maxResults, orderBeans );
	}

	public List<ApplyNote> getHouseContactNotes( Long houseContactId, int maxResults )
	{
		Validate.notNull( houseContactId );

		NameValueBean condition = new NameValueBean( ApplyNote.HOUSE_CONTACT_INFO_CONSTANT, houseContactInfoDAO.read( houseContactId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyNote.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, maxResults, orderBeans );
	}

	public List<ApplyNote> getHouseLoanTrialNotes( Long houseLoanTrialInfoId, int maxResults )
	{
		Validate.notNull( houseLoanTrialInfoId );

		NameValueBean condition = new NameValueBean( ApplyNote.HOUSE_LOAN_TRIAL_INFO_CONSTANT, houseLoanTrialInfoDAO.read( houseLoanTrialInfoId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyNote.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, maxResults, orderBeans );
	}

	public List<ApplyNote> getHousePricingNotes( Long housePricingInfoId, int maxResults )
	{
		Validate.notNull( housePricingInfoId );

		NameValueBean condition = new NameValueBean( ApplyNote.HOUSE_PRICING_INFO_CONSTANT, housePricingInfoDAO.read( housePricingInfoId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyNote.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, maxResults, orderBeans );
	}

	public ApplyNote read( Long noteId )
	{
		Validate.notNull( noteId );
		return getPojoByPK( noteId, ApplyNote.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<ApplyNote> getPojoClass()
	{
		return ApplyNote.class;
	}
}
