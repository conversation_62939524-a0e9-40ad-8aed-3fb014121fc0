package com.megabank.olp.api.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.api.service.eloan.SigningContractService;
import com.megabank.olp.api.service.eloan.bean.BankAccountDataBean;
import com.megabank.olp.api.service.eloan.bean.LendingPlanDataBean;
import com.megabank.olp.api.service.eloan.bean.LoanConditionDataBean;
import com.megabank.olp.api.service.eloan.bean.SigningContractCreatedParamBean;
import com.megabank.olp.base.utility.date.CommonDateUtils;

/**
 *
 */

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@SpringBootTest
@ContextConfiguration( classes = ApiConfig.class )
public class SigningContractServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private SigningContractService service;

	@Test
	public void createSigningContract()
	{
		SigningContractCreatedParamBean paramBean = getCreatedParamBean();
		String result = service.createContract( paramBean );

		logger.info( "result:{}", result );
	}

	@Test
	public void setDiscard()
	{
		String contractNo = "PA000002";
		String result = service.discardContract( contractNo );

		logger.info( "result:{}", result );
	}

	private SigningContractCreatedParamBean getCreatedParamBean()
	{
		SigningContractCreatedParamBean paramBean = new SigningContractCreatedParamBean();
		paramBean.setBranchCode( "017" );
		paramBean.setBorrowerBirthDate( CommonDateUtils.getDate( 1990, 1, 1 ) );
		paramBean.setBorrowerId( "A123456789" );
		paramBean.setBorrowerMobileNumber( "**********" );
		paramBean.setBorrowerName( "王大明" );
		paramBean.setBorrowerEmail( "<EMAIL>" );
		paramBean.setContractNo( "PA1200000" );
		paramBean.setExpiredDate( CommonDateUtils.getDate( 2020, 11, 11 ) );
		paramBean.setLoanConditionDataBean( getLoanConditionInfoBean() );
		paramBean.setProductCode( "personalloan" );

		BankAccountDataBean acctInfo = new BankAccountDataBean();
		acctInfo.setBankCode( "017" );
		acctInfo.setBankCode( "****************" );
		List<BankAccountDataBean> acctList = new ArrayList<>();
		acctList.add( acctInfo );
		paramBean.setBankAccountDataBeans( acctList );

		return paramBean;
	}

	private LendingPlanDataBean getLendingPlanDateBean()
	{
		LendingPlanDataBean dataBean = new LendingPlanDataBean();

		dataBean.setAdvancedRedemptionTitle( "提前清償限制" );
		List<String> advancedRedemptionList = new ArrayList<>();
		advancedRedemptionList.add( "甲方自撥款日期起36期內若提前清償全部或部份本金，甲方應支付以方提前清償違約金，計算方式如下：" );
		advancedRedemptionList.add( "自第0期至第24期內，提前清償需支付5%清償本金作為違約金" );
		advancedRedemptionList.add( "自第25期至第36期內，提前清償需支付1%清償本金作為違約金" );
		dataBean.setAdvancedRedemptionDesc( advancedRedemptionList );

		dataBean.setAdvancedRateTitle( "浮動利率" );
		List<String> rateList = new ArrayList<>();
		rateList.add( "自第0期至第24期，依實際撥款日之乙方消費金融放款指標利率1.5%加計2%利息，目前合計年利率3.5%" );
		rateList.add( "自第25期至第36期，依實際撥款日之乙方消費金融放款指標利率1.5%加計1%利息，目前合計年利率2.5%" );
		dataBean.setAdvancedRateDesc( rateList );

		dataBean.setOtherInfoTitle( "" );
		dataBean.setAdvancedApr( new BigDecimal( 1.2 ) );

		return dataBean;
	}

	private LoanConditionDataBean getLoanConditionInfoBean()
	{
		LoanConditionDataBean bean = new LoanConditionDataBean();
		bean.setCreditCheckFee( 6000 );
		bean.setDrawDownType( "一次撥付" );
		bean.setLendingPlan( "限制清償期" );
		bean.setLoanAmt( 100 );
		bean.setLoanPeriod( 60 );
		bean.setOneTimeFee( 1000 );
		bean.setPreliminaryFee( 500 );
		bean.setRepaymentMethod( "本金按月平均攤還" );
		bean.setLendingPlanDataBean( getLendingPlanDateBean() );

		return bean;
	}
}
