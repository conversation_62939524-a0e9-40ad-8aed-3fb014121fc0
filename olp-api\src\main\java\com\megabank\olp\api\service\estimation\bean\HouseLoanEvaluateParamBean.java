/**
 *
 */
package com.megabank.olp.api.service.estimation.bean;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanEvaluateParamBean extends BaseBean
{
	private String caseID;

	private String mobileNumber;

	private String email;

	private Date createdDate;

	private EvaluateHouseDataBean houseInfo;

	private EvaluateEstimateDataBean estimateInfo;

	private String branchBankCode;

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseID()
	{
		return caseID;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getEmail()
	{
		return email;
	}

	public EvaluateEstimateDataBean getEstimateInfo()
	{
		return estimateInfo;
	}

	public EvaluateHouseDataBean getHouseInfo()
	{
		return houseInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseID( String caseID )
	{
		this.caseID = caseID;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setEstimateInfo( EvaluateEstimateDataBean estimateInfo )
	{
		this.estimateInfo = estimateInfo;
	}

	public void setHouseInfo( EvaluateHouseDataBean houseInfo )
	{
		this.houseInfo = houseInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}
}
