package com.megabank.olp.apply.persistence.pojo.house;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.apply.persistence.pojo.code.CodeHouseParking;
import com.megabank.olp.apply.persistence.pojo.code.CodeHouseType;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_info" )
public class HouseInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_info";

	public static final String PRICING_INFO_ID_CONSTANT = "pricingInfoId";

	public static final String CODE_HOUSE_PARKING_CONSTANT = "codeHouseParking";

	public static final String CODE_HOUSE_TYPE_CONSTANT = "codeHouseType";

	public static final String HOUSE_PRICING_INFO_CONSTANT = "housePricingInfo";

	public static final String COUNTY_CONSTANT = "county";

	public static final String DISTRICT_CONSTANT = "district";

	public static final String ADDR_CONSTANT = "addr";

	public static final String _B_AGE_CONSTANT = "BAge";

	public static final String _B_AREA_P_CONSTANT = "BAreaP";

	public static final String FLOORS_CONSTANT = "floors";

	public static final String LEVEL_SELECT_CONSTANT = "levelSelect";

	public static final String LEVEL_CONSTANT = "level";

	public static final String PARKING_GTY_CONSTANT = "parkingGty";

	public static final String PARKING_P_CONSTANT = "parkingP";

	private long pricingInfoId;

	private transient CodeHouseParking codeHouseParking;

	private transient CodeHouseType codeHouseType;

	private transient HousePricingInfo housePricingInfo;

	private String county;

	private String district;

	private String addr;

	private Integer BAge;

	private BigDecimal BAreaP;

	private Integer floors;

	private Integer levelSelect;

	private Integer level;

	private Integer parkingGty;

	private BigDecimal parkingP;

	public HouseInfo()
	{}

	public HouseInfo( HousePricingInfo housePricingInfo )
	{
		this.housePricingInfo = housePricingInfo;
	}

	public HouseInfo( Long pricingInfoId )
	{
		this.pricingInfoId = pricingInfoId;
	}

	@Column( name = "addr" )
	public String getAddr()
	{
		return addr;
	}

	@Column( name = "b_age", precision = 5, scale = 0 )
	public Integer getBAge()
	{
		return BAge;
	}

	@Column( name = "b_area_p", precision = 10 )
	public BigDecimal getBAreaP()
	{
		return BAreaP;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "parking" )
	public CodeHouseParking getCodeHouseParking()
	{
		return codeHouseParking;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "b_type_int" )
	public CodeHouseType getCodeHouseType()
	{
		return codeHouseType;
	}

	@Column( name = "county" )
	public String getCounty()
	{
		return county;
	}

	@Column( name = "district" )
	public String getDistrict()
	{
		return district;
	}

	@Column( name = "floors", precision = 5, scale = 0 )
	public Integer getFloors()
	{
		return floors;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public HousePricingInfo getHousePricingInfo()
	{
		return housePricingInfo;
	}

	@Column( name = "level", precision = 5, scale = 0 )
	public Integer getLevel()
	{
		return level;
	}

	@Column( name = "level_select", precision = 5, scale = 0 )
	public Integer getLevelSelect()
	{
		return levelSelect;
	}

	@Column( name = "parking_gty", precision = 5, scale = 0 )
	public Integer getParkingGty()
	{
		return parkingGty;
	}

	@Column( name = "parking_p", precision = 10 )
	public BigDecimal getParkingP()
	{
		return parkingP;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "housePricingInfo" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "pricing_info_id", unique = true, nullable = false )
	public long getPricingInfoId()
	{
		return pricingInfoId;
	}

	public void setAddr( String addr )
	{
		this.addr = addr;
	}

	public void setBAge( Integer BAge )
	{
		this.BAge = BAge;
	}

	public void setBAreaP( BigDecimal BAreaP )
	{
		this.BAreaP = BAreaP;
	}

	public void setCodeHouseParking( CodeHouseParking codeHouseParking )
	{
		this.codeHouseParking = codeHouseParking;
	}

	public void setCodeHouseType( CodeHouseType codeHouseType )
	{
		this.codeHouseType = codeHouseType;
	}

	public void setCounty( String county )
	{
		this.county = county;
	}

	public void setDistrict( String district )
	{
		this.district = district;
	}

	public void setFloors( Integer floors )
	{
		this.floors = floors;
	}

	public void setHousePricingInfo( HousePricingInfo housePricingInfo )
	{
		this.housePricingInfo = housePricingInfo;
	}

	public void setLevel( Integer level )
	{
		this.level = level;
	}

	public void setLevelSelect( Integer levelSelect )
	{
		this.levelSelect = levelSelect;
	}

	public void setParkingGty( Integer parkingGty )
	{
		this.parkingGty = parkingGty;
	}

	public void setParkingP( BigDecimal parkingP )
	{
		this.parkingP = parkingP;
	}

	public void setPricingInfoId( long pricingInfoId )
	{
		this.pricingInfoId = pricingInfoId;
	}
}