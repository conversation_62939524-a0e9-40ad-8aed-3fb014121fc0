package com.megabank.olp.apply.persistence.pojo.apply.signing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeLoanType;
import com.megabank.olp.apply.persistence.pojo.code.CodeProdKind;
import com.megabank.olp.apply.persistence.pojo.code.CodeRecipientSystem;
import com.megabank.olp.apply.persistence.pojo.code.CodeSigningContractType;
import com.megabank.olp.apply.persistence.pojo.code.CodeSigningResponseStatus;
import com.megabank.olp.apply.persistence.pojo.code.CodeSigningSendStatus;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;

/**
 * The ApplySigningContract is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_signing_contract", uniqueConstraints = @UniqueConstraint( columnNames = "contract_no" ) )
public class ApplySigningContract extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_signing_contract";

	public static final String SIGNING_CONTRACT_ID_CONSTANT = "signingContractId";

	public static final String CODE_BRANCH_BANK_CONSTANT = "codeBranchBank";

	public static final String CODE_LOAN_TYPE_CONSTANT = "codeLoanType";

	public static final String CODE_SIGNING_CONTRACT_TYPE_CONSTANT = "codeSigningContractType";

	public static final String CODE_SIGNING_RESPONSE_STATUS_CONSTANT = "codeSigningResponseStatus";

	public static final String CODE_SIGNING_SEND_STATUS_CONSTANT = "codeSigningSendStatus";

	public static final String CODE_PROD_KIND = "codeProdKind";

	public static final String CONTRACT_NO_CONSTANT = "contractNo";

	public static final String CONTRACT_VERSION_CONSTANT = "contractVersion";

	public static final String COURT_NAME_CONSTANT = "courtName";

	public static final String PRODUCT_CODE_CONSTANT = "productCode";

	public static final String LOAN_PURPOSE_CONSTANT = "loanPurpose";

	public static final String EXPIRED_DATE_CONSTANT = "expiredDate";

	public static final String DISCARD_CONSTANT = "discard";

	public static final String LOAN_AMT_CONSTANT = "loanAmt";

	public static final String LOAN_PERIOD_CONSTANT = "loanPeriod";

	public static final String DRAW_DOWN_TYPE_CONSTANT = "drawDownType";

	public static final String ONE_TIME_FEE_CONSTANT = "oneTimeFee";

	public static final String PRELIMINARY_FEE_CONSTANT = "preliminaryFee";

	public static final String CREDIT_CHECK_FEE_CONSTANT = "creditCheckFee";

	public static final String REPAYMENT_METHOD_CONSTANT = "repaymentMethod";

	public static final String LENDING_PLAN_CONSTANT = "lendingPlan";

	public static final String ADVANCED_REDEMPTION_TITLE_CONSTANT = "advancedRedemptionTitle";

	public static final String ADVANCED_REDEMPTION_DESC_CONSTANT = "advancedRedemptionDesc";

	public static final String ADVANCED_RATE_TITLE_CONSTANT = "advancedRateTitle";

	public static final String ADVANCED_RATE_DESC_CONSTANT = "advancedRateDesc";

	public static final String ADVANCED_APR_CONSTANT = "advancedApr";

	public static final String LIMITED_REDEMPTION_TITLE_CONSTANT = "limitedRedemptionTitle";

	public static final String LIMITED_REDEMPTION_DESC_CONSTANT = "limitedRedemptionDesc";

	public static final String LIMITED_RATE_TITLE_CONSTANT = "limitedRateTitle";

	public static final String LIMITED_RATE_DESC_CONSTANT = "limitedRateDesc";

	public static final String LIMITED_APR_CONSTANT = "limitedApr";

	public static final String SHOW_OPTION_CONSTANT = "showOption";

	public static final String OTHER_INFO_TITLE_CONSTANT = "otherInfoTitle";

	public static final String OTHER_INFO_DESC_CONSTANT = "otherInfoDesc";

	public static final String GENERAL_GUARANTEE_PLAN_CONSTANT = "generalGuaranteePlan";

	public static final String GENERAL_GUARANTEE_PLAN_INFO_CONSTANT = "generalGuaranteePlanInfo";

	public static final String GUARANTEE_AMT_CONSTANT = "guaranteeAmt";

	public static final String JOINT_GUARANTEE_PLAN_CONSTANT = "jointGuaranteePlan";

	public static final String JOINT_GUARANTEE_PLAN_INFO_CONSTANT = "jointGuaranteePlanInfo";

	public static final String PDF_CONTENT_CONSTANT = "pdfContent";

	public static final String RESEND_COUNT_CONSTANT = "resendCount";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String LOAN_PLAN_CODE_CONSTANT = "loanPlanCode";

	public static final String GRP_CNTR_NO_CONSTANT = "grpCntrNo";

	public static final String GIVEN_APPR_BEG_DATE_CONSTANT = "givenApprBegDate";

	public static final String GIVEN_APPR_END_DATE_CONSTANT = "givenApprEndDate";

	public static final String PAYEE_INFO_ID_CONSTANT = "payeeInfoId";

	public static final String IS_NEED_ACH_CONSTANT = "isNeedACH";

	public static final String BASERATE_CONSTANT = "baseRate";

	public static final String STAFF_RULE = "staffRule";

	public static final String IN_DATE = "inDate";

	public static final String APPLY_SIGNING_BANK_ACCOUNTS_CONSTANT = "applySigningBankAccounts";

	public static final String APPLY_SIGNING_USERS_CONSTANT = "applySigningUsers";

	public static final String APPLY_SIGNING_APPROPRIATION_CONSTANT = "applySigningAppropriation";

	public static final String APPLY_SIGNING_EDDA = "applySigningEdda";

	public static final String APPLY_SIGNING_RATES_CONSTANT = "applySigningRates";

	private Long signingContractId;

	private transient CodeBranchBank codeBranchBank;

	private transient CodeLoanType codeLoanType;

	private transient CodeSigningContractType codeSigningContractType;

	private transient CodeSigningResponseStatus codeSigningResponseStatus;

	private transient CodeSigningSendStatus codeSigningSendStatus;

	private transient CodeRecipientSystem codeRecipientSystem;

	private transient CodeProdKind codeProdKind;

	private String contractNo;

	private String contractVersion;

	private String courtName;

	private String productCode;

	private String loanPurpose;

	private Date expiredDate;

	private boolean discard;

	private Integer loanAmt;

	private Integer loanPeriod;

	private String drawDownType;

	private Integer oneTimeFee;

	private Integer preliminaryFee;

	private Integer creditCheckFee;

	private BigDecimal renewFee;

	private BigDecimal changeFee;

	private BigDecimal certFee;

	private BigDecimal reissueFee;

	private String repaymentMethod;

	private String lendingPlan;

	private String advancedRedemptionTitle;

	private String advancedRedemptionDesc;

	private String advancedRateTitle;

	private String advancedRateDesc;

	private BigDecimal advancedApr;

	private String limitedRedemptionTitle;

	private String limitedRedemptionDesc;

	private String limitedRateTitle;

	private String limitedRateDesc;

	private BigDecimal limitedApr;

	private int showOption;

	private String otherInfoTitle;

	private String otherInfoDesc;

	private String generalGuaranteePlan;

	private String generalGuaranteePlanInfo;

	private BigDecimal guaranteeAmt;

	private String jointGuaranteePlan;

	private String jointGuaranteePlanInfo;

	private transient ImmutableByteArray pdfContent;

	private int resendCount;

	private boolean notified;

	private Date updatedDate;

	private Date createdDate;

	private String loanPlanCode;

	private String grpCntrNo;

	private Date givenApprBegDate;

	private Date givenApprEndDate;

	private Long payeeInfoId;

	private Boolean isNeedAch;

	private Boolean isRepayment;

	private Boolean isAppropiration;

	private BigDecimal baseRate;

	private Boolean staffRule;

	private Integer inetResponseStatus;

	private String lnDate;

	private transient Set<ApplySigningBankAccount> applySigningBankAccounts = new HashSet<>( 0 );

	private transient Set<ApplySigningUser> applySigningUsers = new HashSet<>( 0 );

	private transient Set<ApplySigningEdda> applySigningEdda = new HashSet<>( 0 );

	private transient Set<ApplySigningRate> applySigningRates = new HashSet<>( 0 );

	private ApplySigningAppropriation applySigningAppropriation;
	// private transient ApplySigningAppropriation applySigningAppropriation;

	private transient ApplyHouseSigningContract applyHouseSigningContract;

	private transient ApplyCollateralContract applyCollateralContract;

	public ApplySigningContract()
	{}

	public ApplySigningContract( CodeBranchBank codeBranchBank, CodeLoanType codeLoanType, CodeSigningContractType codeSigningContractType,
								 String contractNo, String productCode, Date expiredDate, boolean discard, int showOption, int resendCount,
								 boolean notified, Date updatedDate, Date createdDate )
	{
		this.codeBranchBank = codeBranchBank;
		this.codeLoanType = codeLoanType;
		this.codeSigningContractType = codeSigningContractType;
		this.contractNo = contractNo;
		this.productCode = productCode;
		this.expiredDate = expiredDate;
		this.discard = discard;
		this.showOption = showOption;
		this.resendCount = resendCount;
		this.notified = notified;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
	}

	public ApplySigningContract( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	@Column( name = "advanced_apr", precision = 10 )
	public BigDecimal getAdvancedApr()
	{
		return advancedApr;
	}

	@Column( name = "advanced_rate_desc" )
	public String getAdvancedRateDesc()
	{
		return advancedRateDesc;
	}

	@Column( name = "advanced_rate_title" )
	public String getAdvancedRateTitle()
	{
		return advancedRateTitle;
	}

	@Column( name = "advanced_redemption_desc" )
	public String getAdvancedRedemptionDesc()
	{
		return advancedRedemptionDesc;
	}

	@Column( name = "advanced_redemption_title" )
	public String getAdvancedRedemptionTitle()
	{
		return advancedRedemptionTitle;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applySigningContract" )
	public ApplyCollateralContract getApplyCollateralContract()
	{
		return applyCollateralContract;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applySigningContract" )
	public ApplyHouseSigningContract getApplyHouseSigningContract()
	{
		return applyHouseSigningContract;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applySigningContract" )
	public ApplySigningAppropriation getApplySigningAppropriation()
	{
		return applySigningAppropriation;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applySigningContract" )
	public Set<ApplySigningBankAccount> getApplySigningBankAccounts()
	{
		return applySigningBankAccounts;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applySigningContract" )
	public Set<ApplySigningEdda> getApplySigningEdda()
	{
		return applySigningEdda;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applySigningContract" )
	public Set<ApplySigningRate> getApplySigningRates()
	{
		return applySigningRates;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applySigningContract" )
	public Set<ApplySigningUser> getApplySigningUsers()
	{
		return applySigningUsers;
	}

	public BigDecimal getBaseRate()
	{
		return baseRate;
	}

	@Column( name = "cert_fee", precision = 10, scale = 0 )
	public BigDecimal getCertFee()
	{
		return certFee;
	}

	@Column( name = "change_fee", precision = 10, scale = 0 )
	public BigDecimal getChangeFee()
	{
		return changeFee;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "branch_bank_id", nullable = false )
	public CodeBranchBank getCodeBranchBank()
	{
		return codeBranchBank;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_type", nullable = false )
	public CodeLoanType getCodeLoanType()
	{
		return codeLoanType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "prod_kind" )
	public CodeProdKind getCodeProdKind()
	{
		return codeProdKind;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "contract_recipient_id" )
	public CodeRecipientSystem getCodeRecipientSystem()
	{
		return codeRecipientSystem;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "signing_contract_type", nullable = false )
	public CodeSigningContractType getCodeSigningContractType()
	{
		return codeSigningContractType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "response_status_code" )
	public CodeSigningResponseStatus getCodeSigningResponseStatus()
	{
		return codeSigningResponseStatus;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "send_status_code" )
	public CodeSigningSendStatus getCodeSigningSendStatus()
	{
		return codeSigningSendStatus;
	}

	@Column( name = "contract_no", unique = true, nullable = false, length = 30 )
	public String getContractNo()
	{
		return contractNo;
	}

	@Column( name = "contract_version" )
	public String getContractVersion()
	{
		return contractVersion;
	}

	@Column( name = "court_name" )
	public String getCourtName()
	{
		return courtName;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "credit_check_fee", precision = 9, scale = 0 )
	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	@Column( name = "draw_downType" )
	public String getDrawDownType()
	{
		return drawDownType;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "expired_date", nullable = false, length = 23 )
	public Date getExpiredDate()
	{
		return expiredDate;
	}

	@Column( name = "general_guarantee_plan" )
	public String getGeneralGuaranteePlan()
	{
		return generalGuaranteePlan;
	}

	@Column( name = "general_guarantee_plan_info" )
	public String getGeneralGuaranteePlanInfo()
	{
		return generalGuaranteePlanInfo;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "given_appr_beg_date", length = 23 )
	public Date getGivenApprBegDate()
	{
		return givenApprBegDate;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "given_appr_end_date", length = 23 )
	public Date getGivenApprEndDate()
	{
		return givenApprEndDate;
	}

	@Column( name = "grp_cntr_no", length = 12 )
	public String getGrpCntrNo()
	{
		return grpCntrNo;
	}

	@Column( name = "guarantee_amt", precision = 10 )
	public BigDecimal getGuaranteeAmt()
	{
		return guaranteeAmt;
	}

	@Column( name = "inet_response_status", nullable = true, precision = 1 )
	public Integer getInetResponseStatus()
	{
		return inetResponseStatus;
	}

	@Column( name = "is_appropiration", nullable = true, precision = 1, scale = 0 )
	public Boolean getIsAppropiration()
	{
		return isAppropiration;
	}

	@Column( name = "is_need_ach", nullable = true, precision = 1, scale = 0 )
	public Boolean getIsNeedAch()
	{
		return isNeedAch;
	}

	@Column( name = "is_repayment", nullable = true, precision = 1, scale = 0 )
	public Boolean getIsRepayment()
	{
		return isRepayment;
	}

	@Column( name = "joint_guarantee_plan" )
	public String getJointGuaranteePlan()
	{
		return jointGuaranteePlan;
	}

	@Column( name = "joint_guarantee_plan_info" )
	public String getJointGuaranteePlanInfo()
	{
		return jointGuaranteePlanInfo;
	}

	@Column( name = "lending_plan" )
	public String getLendingPlan()
	{
		return lendingPlan;
	}

	@Column( name = "limited_apr", precision = 10 )
	public BigDecimal getLimitedApr()
	{
		return limitedApr;
	}

	@Column( name = "limited_rate_desc" )
	public String getLimitedRateDesc()
	{
		return limitedRateDesc;
	}

	@Column( name = "limited_rate_title" )
	public String getLimitedRateTitle()
	{
		return limitedRateTitle;
	}

	@Column( name = "limited_redemption_desc" )
	public String getLimitedRedemptionDesc()
	{
		return limitedRedemptionDesc;
	}

	@Column( name = "limited_redemption_title" )
	public String getLimitedRedemptionTitle()
	{
		return limitedRedemptionTitle;
	}

	@Column( name = "ln_date", length = 10 )
	public String getLnDate()
	{
		return lnDate;
	}

	@Column( name = "loan_amt", precision = 9, scale = 0 )
	public Integer getLoanAmt()
	{
		return loanAmt;
	}

	@Column( name = "loan_period", precision = 5, scale = 0 )
	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	@Column( name = "loan_plan_code", length = 20 )
	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	@Column( name = "loan_purpose" )
	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	@Column( name = "one_time_fee", precision = 9, scale = 0 )
	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	@Column( name = "other_info_desc" )
	public String getOtherInfoDesc()
	{
		return otherInfoDesc;
	}

	@Column( name = "other_info_title" )
	public String getOtherInfoTitle()
	{
		return otherInfoTitle;
	}

	@Column( name = "payee_info_id" )
	public Long getPayeeInfoId()
	{
		return payeeInfoId;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "pdf_content" ) ) } )
	public ImmutableByteArray getPdfContent()
	{
		return pdfContent;
	}

	@Column( name = "preliminary_fee", precision = 9, scale = 0 )
	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	@Column( name = "product_code", nullable = false, length = 30 )
	public String getProductCode()
	{
		return productCode;
	}

	@Column( name = "reissue_fee", precision = 10, scale = 0 )
	public BigDecimal getReissueFee()
	{
		return reissueFee;
	}

	@Column( name = "renew_fee", precision = 10, scale = 0 )
	public BigDecimal getRenewFee()
	{
		return renewFee;
	}

	@Column( name = "repayment_method", length = 50 )
	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	@Column( name = "resend_count", nullable = false, precision = 5, scale = 0 )
	public int getResendCount()
	{
		return resendCount;
	}

	@Column( name = "show_option", nullable = false, precision = 5, scale = 0 )
	public int getShowOption()
	{
		return showOption;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "signing_contract_id", unique = true, nullable = false )
	public Long getSigningContractId()
	{
		return signingContractId;
	}

	@Column( name = "staff_rule", precision = 1, scale = 0 )
	public Boolean getStaffRule()
	{
		return staffRule;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "discard", nullable = false, precision = 1, scale = 0 )
	public boolean isDiscard()
	{
		return discard;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	public void setAdvancedApr( BigDecimal advancedApr )
	{
		this.advancedApr = advancedApr;
	}

	public void setAdvancedRateDesc( String advancedRateDesc )
	{
		this.advancedRateDesc = advancedRateDesc;
	}

	public void setAdvancedRateTitle( String advancedRateTitle )
	{
		this.advancedRateTitle = advancedRateTitle;
	}

	public void setAdvancedRedemptionDesc( String advancedRedemptionDesc )
	{
		this.advancedRedemptionDesc = advancedRedemptionDesc;
	}

	public void setAdvancedRedemptionTitle( String advancedRedemptionTitle )
	{
		this.advancedRedemptionTitle = advancedRedemptionTitle;
	}

	public void setApplyCollateralContract( ApplyCollateralContract applyCollateralContract )
	{
		this.applyCollateralContract = applyCollateralContract;
	}

	public void setApplyHouseSigningContract( ApplyHouseSigningContract applyHouseSigningContract )
	{
		this.applyHouseSigningContract = applyHouseSigningContract;
	}

	public void setApplySigningAppropriation( ApplySigningAppropriation applySigningAppropriation )
	{
		this.applySigningAppropriation = applySigningAppropriation;
	}

	public void setApplySigningBankAccounts( Set<ApplySigningBankAccount> applySigningBankAccounts )
	{
		this.applySigningBankAccounts = applySigningBankAccounts;
	}

	public void setApplySigningEdda( Set<ApplySigningEdda> applySigningEdda )
	{
		this.applySigningEdda = applySigningEdda;
	}

	public void setApplySigningRates( Set<ApplySigningRate> applySigningRate )
	{
		applySigningRates = applySigningRate;
	}

	public void setApplySigningUsers( Set<ApplySigningUser> applySigningUsers )
	{
		this.applySigningUsers = applySigningUsers;
	}

	public void setBaseRate( BigDecimal baseRate )
	{
		this.baseRate = baseRate;
	}

	public void setCertFee( BigDecimal certFee )
	{
		this.certFee = certFee;
	}

	public void setChangeFee( BigDecimal changeFee )
	{
		this.changeFee = changeFee;
	}

	public void setCodeBranchBank( CodeBranchBank codeBranchBank )
	{
		this.codeBranchBank = codeBranchBank;
	}

	public void setCodeLoanType( CodeLoanType codeLoanType )
	{
		this.codeLoanType = codeLoanType;
	}

	public void setCodeProdKind( CodeProdKind codeProdKind )
	{
		this.codeProdKind = codeProdKind;
	}

	public void setCodeRecipientSystem( CodeRecipientSystem codeRecipientSystem )
	{
		this.codeRecipientSystem = codeRecipientSystem;
	}

	public void setCodeSigningContractType( CodeSigningContractType codeSigningContractType )
	{
		this.codeSigningContractType = codeSigningContractType;
	}

	public void setCodeSigningResponseStatus( CodeSigningResponseStatus codeSigningResponseStatus )
	{
		this.codeSigningResponseStatus = codeSigningResponseStatus;
	}

	public void setCodeSigningSendStatus( CodeSigningSendStatus codeSigningSendStatus )
	{
		this.codeSigningSendStatus = codeSigningSendStatus;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setContractVersion( String contractVersion )
	{
		this.contractVersion = contractVersion;
	}

	public void setCourtName( String courtName )
	{
		this.courtName = courtName;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public void setDiscard( boolean discard )
	{
		this.discard = discard;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public void setExpiredDate( Date expiredDate )
	{
		this.expiredDate = expiredDate;
	}

	public void setGeneralGuaranteePlan( String generalGuaranteePlan )
	{
		this.generalGuaranteePlan = generalGuaranteePlan;
	}

	public void setGeneralGuaranteePlanInfo( String generalGuaranteePlanInfo )
	{
		this.generalGuaranteePlanInfo = generalGuaranteePlanInfo;
	}

	public void setGivenApprBegDate( Date givenApprBegDate )
	{
		this.givenApprBegDate = givenApprBegDate;
	}

	public void setGivenApprEndDate( Date givenApprEndDate )
	{
		this.givenApprEndDate = givenApprEndDate;
	}

	public void setGrpCntrNo( String grpCntrNo )
	{
		this.grpCntrNo = grpCntrNo;
	}

	public void setGuaranteeAmt( BigDecimal guaranteeAmt )
	{
		this.guaranteeAmt = guaranteeAmt;
	}

	public void setInetResponseStatus( Integer inetResponseStatus )
	{
		this.inetResponseStatus = inetResponseStatus;
	}

	public void setIsAppropiration( Boolean isAppropiration )
	{
		this.isAppropiration = isAppropiration;
	}

	public void setIsNeedAch( Boolean isNeedAch )
	{
		this.isNeedAch = isNeedAch;
	}

	public void setIsRepayment( Boolean isRepayment )
	{
		this.isRepayment = isRepayment;
	}

	public void setJointGuaranteePlan( String jointGuaranteePlan )
	{
		this.jointGuaranteePlan = jointGuaranteePlan;
	}

	public void setJointGuaranteePlanInfo( String jointGuaranteePlanInfo )
	{
		this.jointGuaranteePlanInfo = jointGuaranteePlanInfo;
	}

	public void setLendingPlan( String lendingPlan )
	{
		this.lendingPlan = lendingPlan;
	}

	public void setLimitedApr( BigDecimal limitedApr )
	{
		this.limitedApr = limitedApr;
	}

	public void setLimitedRateDesc( String limitedRateDesc )
	{
		this.limitedRateDesc = limitedRateDesc;
	}

	public void setLimitedRateTitle( String limitedRateTitle )
	{
		this.limitedRateTitle = limitedRateTitle;
	}

	public void setLimitedRedemptionDesc( String limitedRedemptionDesc )
	{
		this.limitedRedemptionDesc = limitedRedemptionDesc;
	}

	public void setLimitedRedemptionTitle( String limitedRedemptionTitle )
	{
		this.limitedRedemptionTitle = limitedRedemptionTitle;
	}

	public void setLnDate( String lnDate )
	{
		this.lnDate = lnDate;
	}

	public void setLoanAmt( Integer loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public void setOtherInfoDesc( String otherInfoDesc )
	{
		this.otherInfoDesc = otherInfoDesc;
	}

	public void setOtherInfoTitle( String otherInfoTitle )
	{
		this.otherInfoTitle = otherInfoTitle;
	}

	public void setPayeeInfoId( Long payeeInfoId )
	{
		this.payeeInfoId = payeeInfoId;
	}

	public void setPdfContent( ImmutableByteArray pdfContent )
	{
		this.pdfContent = pdfContent;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public void setProductCode( String productCode )
	{
		this.productCode = productCode;
	}

	public void setReissueFee( BigDecimal reissueFee )
	{
		this.reissueFee = reissueFee;
	}

	public void setRenewFee( BigDecimal renewFee )
	{
		this.renewFee = renewFee;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}

	public void setResendCount( int resendCount )
	{
		this.resendCount = resendCount;
	}

	public void setShowOption( int showOption )
	{
		this.showOption = showOption;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setStaffRule( Boolean staffRule )
	{
		this.staffRule = staffRule;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}
}