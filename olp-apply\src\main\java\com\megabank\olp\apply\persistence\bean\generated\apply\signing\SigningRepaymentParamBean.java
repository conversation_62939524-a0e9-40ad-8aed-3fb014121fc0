package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class SigningRepaymentParamBean extends BaseBean
{
	private Long signingRepaymentId;

	private Long signingContractId;

	private String bankCode;

	private String bankName;

	private String repaymentProductType;

	private BigDecimal originalAmt;

	public SigningRepaymentParamBean()
	{}

	public String getBankCode()
	{
		return bankCode;
	}

	public String getBankName()
	{
		return bankName;
	}

	public BigDecimal getOriginalAmt()
	{
		return originalAmt;
	}

	public String getRepaymentProductType()
	{
		return repaymentProductType;
	}

	public Long getSigningContractId()
	{
		return signingContractId;
	}

	public Long getSigningRepaymentId()
	{
		return signingRepaymentId;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setBankName( String bankName )
	{
		this.bankName = bankName;
	}

	public void setOriginalAmt( BigDecimal originalAmt )
	{
		this.originalAmt = originalAmt;
	}

	public void setRepaymentProductType( String repaymentProductType )
	{
		this.repaymentProductType = repaymentProductType;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setSigningRepaymentId( Long signingRepaymentId )
	{
		this.signingRepaymentId = signingRepaymentId;
	}
}
