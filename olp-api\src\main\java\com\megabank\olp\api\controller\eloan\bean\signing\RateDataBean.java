package com.megabank.olp.api.controller.eloan.bean.signing;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class RateDataBean extends BaseBean
{
	@NotBlank
	private String rate_Type;
	@NotBlank
	private String rate_Bgn;
	@NotBlank
	private String rate_End;
	@NotBlank
	private String rate;

	public RateDataBean()
	{}

	public String getRate_Type() {
		return rate_Type;
	}

	public void setRate_Type(String rate_Type) {
		this.rate_Type = rate_Type;
	}

	public String getRate_Bgn() {
		return rate_Bgn;
	}

	public void setRate_Bgn(String rate_Bgn) {
		this.rate_Bgn = rate_Bgn;
	}

	public String getRate_End() {
		return rate_End;
	}

	public void setRate_End(String rate_End) {
		this.rate_End = rate_End;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}
}
