package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanRelationCreatedParamBean extends BaseBean
{
	private Long loanId;

	private String relationName;

	private String relationIdNo;

	private String relationType;

	public ApplyLoanRelationCreatedParamBean()
	{}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getRelationIdNo()
	{
		return relationIdNo;
	}

	public String getRelationName()
	{
		return relationName;
	}

	public String getRelationType()
	{
		return relationType;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setRelationIdNo( String relationIdNo )
	{
		this.relationIdNo = relationIdNo;
	}

	public void setRelationName( String relationName )
	{
		this.relationName = relationName;
	}

	public void setRelationType( String relationType )
	{
		this.relationType = relationType;
	}

}
