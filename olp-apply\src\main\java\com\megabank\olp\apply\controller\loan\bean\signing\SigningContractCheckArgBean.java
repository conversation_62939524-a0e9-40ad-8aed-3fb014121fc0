/**
 *
 */
package com.megabank.olp.apply.controller.loan.bean.signing;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractCheckArgBean extends BaseBean
{
	@NotBlank
	private String idNo;

	@NotNull
	private Date birthDate;

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}
}
