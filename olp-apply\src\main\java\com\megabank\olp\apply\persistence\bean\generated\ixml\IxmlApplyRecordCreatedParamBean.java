package com.megabank.olp.apply.persistence.bean.generated.ixml;

import com.megabank.olp.base.bean.BaseBean;

public class IxmlApplyRecordCreatedParamBean extends BaseBean
{
	private String idNo;

	private String certNo;

	private String certSn;

	private String certNoBefore;

	private String certNoAfter;

	private String clientAddress;

	private String verifyNo;

	private String token;

	public String getCertNo()
	{
		return certNo;
	}

	public String getCertNoAfter()
	{
		return certNoAfter;
	}

	public String getCertNoBefore()
	{
		return certNoBefore;
	}

	public String getCertSn()
	{
		return certSn;
	}

	public String getClientAddress()
	{
		return clientAddress;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getToken()
	{
		return token;
	}

	public String getVerifyNo()
	{
		return verifyNo;
	}

	public void setCertNo( String certNo )
	{
		this.certNo = certNo;
	}

	public void setCertNoAfter( String certNoAfter )
	{
		this.certNoAfter = certNoAfter;
	}

	public void setCertNoBefore( String certNoBefore )
	{
		this.certNoBefore = certNoBefore;
	}

	public void setCertSn( String certSn )
	{
		this.certSn = certSn;
	}

	public void setClientAddress( String clientAddress )
	{
		this.clientAddress = clientAddress;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setToken( String token )
	{
		this.token = token;
	}

	public void setVerifyNo( String verifyNo )
	{
		this.verifyNo = verifyNo;
	}
}
