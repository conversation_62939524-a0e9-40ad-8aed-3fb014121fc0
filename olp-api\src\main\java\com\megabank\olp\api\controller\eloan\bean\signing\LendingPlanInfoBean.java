/**
 *
 */
package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class LendingPlanInfoBean extends BaseBean
{
	@NotBlank
	private String advancedRedemptionTitle;

	@NotNull
	private List<String> advancedRedemptionDesc = new ArrayList<>();

	@NotBlank
	private String advancedRateTitle;

	@NotNull
	@NotEmpty
	private List<String> advancedRateDesc = new ArrayList<>();

	@JsonProperty( "advancedAPR" )
	private BigDecimal advancedApr;

	@NotBlank
	private String limitedRedemptionTitle;

	@NotNull
	private List<String> limitedRedemptionDesc = new ArrayList<>();

	@NotBlank
	private String limitedRateTitle;

	@NotNull
	@NotEmpty
	private List<String> limitedRateDesc = new ArrayList<>();

	@JsonProperty( "limitedAPR" )
	private BigDecimal limitedApr;

	@NotBlank
	private String showOption;

	private String otherInfoTitle;

	@NotNull
	private List<String> otherInfoDesc = new ArrayList<>();

	public LendingPlanInfoBean()
	{}

	public BigDecimal getAdvancedApr()
	{
		return advancedApr;
	}

	public List<String> getAdvancedRateDesc()
	{
		return advancedRateDesc;
	}

	public String getAdvancedRateTitle()
	{
		return advancedRateTitle;
	}

	public List<String> getAdvancedRedemptionDesc()
	{
		return advancedRedemptionDesc;
	}

	public String getAdvancedRedemptionTitle()
	{
		return advancedRedemptionTitle;
	}

	public BigDecimal getLimitedApr()
	{
		return limitedApr;
	}

	public List<String> getLimitedRateDesc()
	{
		return limitedRateDesc;
	}

	public String getLimitedRateTitle()
	{
		return limitedRateTitle;
	}

	public List<String> getLimitedRedemptionDesc()
	{
		return limitedRedemptionDesc;
	}

	public String getLimitedRedemptionTitle()
	{
		return limitedRedemptionTitle;
	}

	public List<String> getOtherInfoDesc()
	{
		return otherInfoDesc;
	}

	public String getOtherInfoTitle()
	{
		return otherInfoTitle;
	}

	public String getShowOption()
	{
		return showOption;
	}

	public void setAdvancedApr( BigDecimal advancedApr )
	{
		this.advancedApr = advancedApr;
	}

	public void setAdvancedRateDesc( List<String> advancedRateDesc )
	{
		this.advancedRateDesc = advancedRateDesc;
	}

	public void setAdvancedRateTitle( String advancedRateTitle )
	{
		this.advancedRateTitle = advancedRateTitle;
	}

	public void setAdvancedRedemptionDesc( List<String> advancedRedemptionDesc )
	{
		this.advancedRedemptionDesc = advancedRedemptionDesc;
	}

	public void setAdvancedRedemptionTitle( String advancedRedemptionTitle )
	{
		this.advancedRedemptionTitle = advancedRedemptionTitle;
	}

	public void setLimitedApr( BigDecimal limitedApr )
	{
		this.limitedApr = limitedApr;
	}

	public void setLimitedRateDesc( List<String> limitedRateDesc )
	{
		this.limitedRateDesc = limitedRateDesc;
	}

	public void setLimitedRateTitle( String limitedRateTitle )
	{
		this.limitedRateTitle = limitedRateTitle;
	}

	public void setLimitedRedemptionDesc( List<String> limitedRedemptionDesc )
	{
		this.limitedRedemptionDesc = limitedRedemptionDesc;
	}

	public void setLimitedRedemptionTitle( String limitedRedemptionTitle )
	{
		this.limitedRedemptionTitle = limitedRedemptionTitle;
	}

	public void setOtherInfoDesc( List<String> otherInfoDesc )
	{
		this.otherInfoDesc = otherInfoDesc;
	}

	public void setOtherInfoTitle( String otherInfoTitle )
	{
		this.otherInfoTitle = otherInfoTitle;
	}

	public void setShowOption( String showOption )
	{
		this.showOption = showOption;
	}

}
