package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.apply.ApplyCustInfoGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.apply.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.apply.LoanDiscardedArgBean;
import com.megabank.olp.apply.service.management.LoanApplyService;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/apply" )
public class LoanApplyController extends BaseController
{
	@Autowired
	private LoanApplyService loanApplyService;

	@PostMapping( "discardLoan" )
	public Map<String, Object> discardLoan( @RequestBody @Validated LoanDiscardedArgBean argBean )
	{
		loanApplyService.discardLoan( argBean.getCaseNo(), argBean.getEmployeeId(), argBean.getEmployeeName() );

		return getResponseMap();
	}

	/**
	 * 取得申請件客戶資料
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getApplyCustInfo" )
	public Map<String, Object> getApplyCustInfo( @RequestBody @Validated ApplyCustInfoGetterArgBean argBean )
	{
		return getResponseMap( loanApplyService.getCustInfo( argBean.getCaseNo() ) );
	}

	/**
	 * 更新申請完成件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		loanApplyService.updateLoanCompletedBranchBank( argBean.getCaseNo(), argBean.getBranchBankCode(), argBean.getEmployeeId(),
														argBean.getEmployeeName() );

		return getResponseMap();
	}
}
