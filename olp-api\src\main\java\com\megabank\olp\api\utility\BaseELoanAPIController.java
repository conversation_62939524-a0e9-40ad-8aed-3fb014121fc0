/**
 *
 */
package com.megabank.olp.api.utility;

import org.springframework.beans.factory.annotation.Value;

import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.security.CommonSecurityUtils;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class BaseELoanAPIController extends BaseController
{
	@Value( "${eloan.secret.token}" )
	private String secretToken;

	@Value( "${eloan.key}" )
	private String eloanKey;

	public String getSecretToken()
	{
		return secretToken;
	}

	public void setSecretToken( String secretToken )
	{
		this.secretToken = secretToken;
	}

	protected void validAuth( String accessToken )
	{
		String text = CommonSecurityUtils.decrypt( accessToken, eloanKey );
		if( !secretToken.equals( text ) )
			throw new MyRuntimeException( SystemErrorEnum.ACCESS_DENIED );
	}

}
