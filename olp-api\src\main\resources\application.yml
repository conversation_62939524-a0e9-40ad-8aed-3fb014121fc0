callback:
  client:
    url:
      otherbank: ${server.url}/#/otherBankAuthentication
      pib: ${server.url}/#/pibAuthentication
      sso: ${server.url}/#/ssoAuthentication
eloan:
  secret:
    token: megabank@loan2020
iloan:
  secret:
    token: megabank@loan2020
print:
  properties:
    api: callback.client.url.otherbank,callback.client.url.pib,eloan.secret.token,iloan.secret.token
serviceName: api
---
logging:
  config: classpath:log4j2/log4j2-dev.xml
server:
  port: 9005
spring:
  config:
    activate:
      on-profile: dev
callback:
  client:
    url:
      otherbank: http://localhost:3000/#/otherBankAuthentication
      pib: http://localhost:3000/#/pibAuthentication
      sso: http://localhost:3000/#/ssoAuthentication
    key:
      finGetResultsKey1: be0b93d87068923800883c09de5f5c20
      finGetResultsKey2: d5ab5018ce52ea0cc0de266adea649b3 
---
logging:
  config: classpath:log4j2/log4j2-sit.xml
spring:
  config:
    activate:
      on-profile: sit
callback:
  client:
    key:
      finGetResultsKey1: be0b93d87068923800883c09de5f5c20
      finGetResultsKey2: d5ab5018ce52ea0cc0de266adea649b3 
---
logging:
  config: classpath:log4j2/log4j2-uat.xml
spring:
  config:
    activate:
      on-profile: uat
callback:
  client:
     key:
      finGetResultsKey1: be0b93d87068923800883c09de5f5c20
      finGetResultsKey2: d5ab5018ce52ea0cc0de266adea649b3
---
logging:
  config: classpath:log4j2/log4j2-prod.xml
spring:
  config:
    activate:
      on-profile: prod
callback:
  client:
     key:
      finGetResultsKey1: be0b93d87068923800883c09de5f5c20
      finGetResultsKey2: d5ab5018ce52ea0cc0de266adea649b3
---
logging:
  config: classpath:log4j2/log4j2-stress.xml
spring:
  config:
    activate:
      on-profile: stress