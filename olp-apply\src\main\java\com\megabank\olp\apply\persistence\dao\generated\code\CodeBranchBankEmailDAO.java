package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBankEmail;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeBranchBankEmailDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeBranchBankEmailDAO extends BasePojoDAO<CodeBranchBankEmail, Long>
{
	@Autowired
	private CodeBranchBankLoanDAO codeBranchBankLoanDAO;

	public List<CodeBranchBankEmail> getPojosByBranchBankLoan( Long branchBankLoanId )
	{
		Validate.notNull( branchBankLoanId );

		NameValueBean condition = new NameValueBean( CodeBranchBankEmail.CODE_BRANCH_BANK_LOAN_CONSTANT,
													 codeBranchBankLoanDAO.read( branchBankLoanId ) );

		return getPojosByProperty( condition );
	}

	@Override
	protected Class<CodeBranchBankEmail> getPojoClass()
	{
		return CodeBranchBankEmail.class;
	}
}
