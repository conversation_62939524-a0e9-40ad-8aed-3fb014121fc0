package com.megabank.olp.apply.controller.loan.bean.apply;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class LoanApplyBaseArgBean extends BaseBean
{
	@NotBlank
	private String loanType;

	public LoanApplyBaseArgBean()
	{
		// default constructor
	}

	public String getLoanType()
	{
		return loanType;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

}
