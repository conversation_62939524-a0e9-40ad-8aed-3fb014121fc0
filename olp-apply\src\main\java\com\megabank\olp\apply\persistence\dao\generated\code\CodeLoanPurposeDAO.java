package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPurpose;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeLoanPurposeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeLoanPurposeDAO extends BasePojoDAO<CodeLoanPurpose, Long>
{
	@Autowired
	private CodeLoanTypeDAO codeLoanTypeDAO;

	public List<CodeLoanPurpose> getList()
	{
		return getAllPojos();
	}

	public CodeLoanPurpose getPojoByLoanTypeAndPurposeCode( String loanType, String loanPurposeCode )
	{
		Validate.notBlank( loanType );
		Validate.notBlank( loanPurposeCode );

		// NameValueBean _loanType = new NameValueBean( CodeLoanPurpose.CODE_LOAN_TYPE_CONSTANT, loanType );
		NameValueBean _loanType = new NameValueBean( CodeLoanPurpose.CODE_LOAN_TYPE_CONSTANT, codeLoanTypeDAO.read( loanType ) );
		NameValueBean _loanPurposeCode = new NameValueBean( CodeLoanPurpose.LOAN_PURPOSE_CODE_CONSTANT, loanPurposeCode );
		NameValueBean disabled = new NameValueBean( CodeLoanPurpose.DISABLED_CONSTANT, false );

		NameValueBean[] conditions = new NameValueBean[]{ _loanType, _loanPurposeCode, disabled };

		return this.getUniquePojoByProperties( conditions );
	}

	public CodeLoanPurpose read( Long loanPurposeId )
	{
		Validate.notNull( loanPurposeId );

		return getPojoByPK( loanPurposeId, CodeLoanPurpose.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeLoanPurpose> getPojoClass()
	{
		return CodeLoanPurpose.class;
	}
}
