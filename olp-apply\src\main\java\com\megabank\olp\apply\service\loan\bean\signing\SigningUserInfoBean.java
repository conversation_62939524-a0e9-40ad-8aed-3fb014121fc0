/**
 *
 */
package com.megabank.olp.apply.service.loan.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningUserInfoBean extends BaseBean
{
	private String name;

	private String mobileNumber;

	private String idNo;

	// mailAddress
	private String mlAddr;

	public SigningUserInfoBean()
	{
		// default constructor
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMlAddr()
	{
		return mlAddr;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMlAddr( String mlAddr )
	{
		this.mlAddr = mlAddr;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

}
