package com.megabank.olp.apply.persistence.pojo.apply.loan;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.apply.persistence.pojo.code.CodeGuarantyReason;
import com.megabank.olp.apply.persistence.pojo.code.CodeRelationBorrowerType;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyLoanGuaranteeInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_guarantee_info" )
public class ApplyLoanGuaranteeInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_guarantee_info";

	public static final String LOAN_ID_CONSTANT = "loanId";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String CODE_GUARANTY_REASON_CONSTANT = "codeGuarantyReason";

	public static final String CODE_RELATION_BORROWER_TYPE_CONSTANT = "codeRelationBorrowerType";

	public static final String OTHER_GUARANTY_REASON_CONSTANT = "otherGuarantyReason";

	public static final String COHABITATION_FLAG_CONSTANT = "cohabitationFlag";

	private long loanId;

	private transient ApplyLoan applyLoan;

	private transient CodeGuarantyReason codeGuarantyReason;

	private transient CodeRelationBorrowerType codeRelationBorrowerType;

	private String otherGuarantyReason;

	private boolean cohabitationFlag;

	public ApplyLoanGuaranteeInfo()
	{}

	public ApplyLoanGuaranteeInfo( ApplyLoan applyLoan, CodeRelationBorrowerType codeRelationBorrowerType, boolean cohabitationFlag )
	{
		this.applyLoan = applyLoan;
		this.codeRelationBorrowerType = codeRelationBorrowerType;
		this.cohabitationFlag = cohabitationFlag;
	}

	public ApplyLoanGuaranteeInfo( Long loanId )
	{
		this.loanId = loanId;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "guaranty_reason_code" )
	public CodeGuarantyReason getCodeGuarantyReason()
	{
		return codeGuarantyReason;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "relation_borrower_type", nullable = false )
	public CodeRelationBorrowerType getCodeRelationBorrowerType()
	{
		return codeRelationBorrowerType;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "applyLoan" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "loan_id", unique = true, nullable = false )
	public long getLoanId()
	{
		return loanId;
	}

	@Column( name = "other_guaranty_reason" )
	public String getOtherGuarantyReason()
	{
		return otherGuarantyReason;
	}

	@Column( name = "cohabitation_flag", nullable = false, precision = 1, scale = 0 )
	public boolean isCohabitationFlag()
	{
		return cohabitationFlag;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setCodeGuarantyReason( CodeGuarantyReason codeGuarantyReason )
	{
		this.codeGuarantyReason = codeGuarantyReason;
	}

	public void setCodeRelationBorrowerType( CodeRelationBorrowerType codeRelationBorrowerType )
	{
		this.codeRelationBorrowerType = codeRelationBorrowerType;
	}

	public void setCohabitationFlag( boolean cohabitationFlag )
	{
		this.cohabitationFlag = cohabitationFlag;
	}

	public void setLoanId( long loanId )
	{
		this.loanId = loanId;
	}

	public void setOtherGuarantyReason( String otherGuarantyReason )
	{
		this.otherGuarantyReason = otherGuarantyReason;
	}
}