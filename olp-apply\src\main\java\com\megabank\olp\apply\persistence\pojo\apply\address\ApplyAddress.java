package com.megabank.olp.apply.persistence.pojo.apply.address;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContent;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanOccupation;
import com.megabank.olp.apply.persistence.pojo.code.CodeTown;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyAddress is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_address" )
public class ApplyAddress extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_address";

	public static final String ADDRESS_ID_CONSTANT = "addressId";

	public static final String CODE_TOWN_CONSTANT = "codeTown";

	public static final String VILLAGE_CONSTANT = "village";

	public static final String NEIGHBORHOOD_CONSTANT = "neighborhood";

	public static final String STREET_CONSTANT = "street";

	public static final String SECTION_CONSTANT = "section";

	public static final String LANE_CONSTANT = "lane";

	public static final String ALLEY_CONSTANT = "alley";

	public static final String NO_CONSTANT = "no";

	public static final String FLOOR_CONSTANT = "floor";

	public static final String ROOM_CONSTANT = "room";

	public static final String APPLY_LOAN_CONTENTS_CONSTANT = "applyLoanContents";

	public static final String APPLY_LOAN_OCCUPATIONS_CONSTANT = "applyLoanOccupations";

	private Long addressId;

	private transient CodeTown codeTown;

	private String village;

	private String neighborhood;

	private String street;

	private Integer section;

	private String lane;

	private String alley;

	private String no;

	private String floor;

	private Integer room;

	private transient Set<ApplyLoanContent> applyLoanContents = new HashSet<>( 0 );

	private transient Set<ApplyLoanOccupation> applyLoanOccupations = new HashSet<>( 0 );

	public ApplyAddress()
	{}

	public ApplyAddress( CodeTown codeTown, String street, String no )
	{
		this.codeTown = codeTown;
		this.street = street;
		this.no = no;
	}

	public ApplyAddress( Long addressId )
	{
		this.addressId = addressId;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "address_id", unique = true, nullable = false )
	public Long getAddressId()
	{
		return addressId;
	}

	@Column( name = "alley" )
	public String getAlley()
	{
		return alley;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyAddress" )
	public Set<ApplyLoanContent> getApplyLoanContents()
	{
		return applyLoanContents;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyAddress" )
	public Set<ApplyLoanOccupation> getApplyLoanOccupations()
	{
		return applyLoanOccupations;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "town_code", nullable = false )
	public CodeTown getCodeTown()
	{
		return codeTown;
	}

	@Column( name = "floor", length = 10 )
	public String getFloor()
	{
		return floor;
	}

	@Column( name = "lane" )
	public String getLane()
	{
		return lane;
	}

	@Column( name = "neighborhood" )
	public String getNeighborhood()
	{
		return neighborhood;
	}

	@Column( name = "no", nullable = false, length = 20 )
	public String getNo()
	{
		return no;
	}

	@Column( name = "room", precision = 5, scale = 0 )
	public Integer getRoom()
	{
		return room;
	}

	@Column( name = "section", precision = 5, scale = 0 )
	public Integer getSection()
	{
		return section;
	}

	@Column( name = "street", nullable = false )
	public String getStreet()
	{
		return street;
	}

	@Column( name = "village" )
	public String getVillage()
	{
		return village;
	}

	public void setAddressId( Long addressId )
	{
		this.addressId = addressId;
	}

	public void setAlley( String alley )
	{
		this.alley = alley;
	}

	public void setApplyLoanContents( Set<ApplyLoanContent> applyLoanContents )
	{
		this.applyLoanContents = applyLoanContents;
	}

	public void setApplyLoanOccupations( Set<ApplyLoanOccupation> applyLoanOccupations )
	{
		this.applyLoanOccupations = applyLoanOccupations;
	}

	public void setCodeTown( CodeTown codeTown )
	{
		this.codeTown = codeTown;
	}

	public void setFloor( String floor )
	{
		this.floor = floor;
	}

	public void setLane( String lane )
	{
		this.lane = lane;
	}

	public void setNeighborhood( String neighborhood )
	{
		this.neighborhood = neighborhood;
	}

	public void setNo( String no )
	{
		this.no = no;
	}

	public void setRoom( Integer room )
	{
		this.room = room;
	}

	public void setSection( Integer section )
	{
		this.section = section;
	}

	public void setStreet( String street )
	{
		this.street = street;
	}

	public void setVillage( String village )
	{
		this.village = village;
	}
}