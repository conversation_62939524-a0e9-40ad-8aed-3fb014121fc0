package com.megabank.olp.apply.persistence.dao.generated.apply.collateral;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.collateral.ApplyCollateralCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.dto.CollateralCountDTO;
import com.megabank.olp.apply.persistence.pojo.apply.collateral.ApplyCollateral;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyCollateralDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyCollateralDAO extends BasePojoDAO<ApplyCollateral, Long>
{
	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	private static final String COLLATERAL_ID_CONSTANT = "collateralId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	public ApplyCollateral create( ApplyCollateralCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notBlank( paramBean.getContractNo() );
		Validate.notBlank( paramBean.getProviderName() );
		Validate.notBlank( paramBean.getBorrowerName() );
		Validate.notBlank( paramBean.getTransmissionStatusCode() );
		Validate.notBlank( paramBean.getBranchBankCode() );

		ApplyCollateral pojo = new ApplyCollateral();
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setContractNo( paramBean.getContractNo() );
		pojo.setBorrowerName( paramBean.getBorrowerName() );
		pojo.setProviderName( paramBean.getProviderName() );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( paramBean.getTransmissionStatusCode() ) );
		pojo.setCodeBranchBank( codeBranchBankDAO.getPojoByBankCode( paramBean.getBranchBankCode() ) );
		pojo.setNotified( false );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );

		super.createPojo( pojo );

		return pojo;
	}

	public List<CollateralCountDTO> getCollateralCountByBranch( Long branchBankId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "collateral.getCollateralCount" );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( CollateralCountDTO.class ) );

		return nativeQuery.getResultList();
	}

	public List<Long> getNeedToNotifiedBankIds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "collateral.getBranchBankIds" );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	public ApplyCollateral getPojoByIdentityId( Long validatedIdentityId )
	{
		Validate.notNull( validatedIdentityId );

		NameValueBean condition = new NameValueBean( ApplyCollateral.VALIDATED_IDENTITY_ID_CONSTANT, validatedIdentityId );

		return getUniquePojoByProperty( condition );
	}

	public List<ApplyCollateral> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "collateral.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyCollateral.class );

		return nativeQuery.getResultList();
	}

	public ApplyCollateral read( Long collateralId )
	{
		Validate.notNull( collateralId );

		return getPojoByPK( collateralId, ApplyCollateral.TABLENAME_CONSTANT );
	}

	public int updateBranchBank( List<Long> collateralIds, Long branchBankId )
	{
		Validate.notEmpty( collateralIds );
		Validate.notNull( branchBankId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "collateral.updateBranchBank" );
		nativeQuery.setParameterList( COLLATERAL_ID_CONSTANT, collateralIds, Long.class );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateCompleted( Long collateralId )
	{
		Validate.notNull( collateralId );

		ApplyCollateral pojo = read( collateralId );
		pojo.setCompletedDate( new Date() );
		pojo.setUpdatedDate( new Date() );

		return pojo.getCollateralId();
	}

	public int updateNotified( List<Long> collateralIds )
	{
		Validate.notEmpty( collateralIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "collateral.updateNotified" );
		nativeQuery.setParameterList( COLLATERAL_ID_CONSTANT, collateralIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updatePdfContent( Long collateralId, byte[] pdfContent )
	{
		Validate.notNull( collateralId );
		Validate.notNull( pdfContent );

		ApplyCollateral pojo = read( collateralId );
		pojo.setPdfContent( new ImmutableByteArray( pdfContent ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getCollateralId();
	}

	public Long updateResend( Long collateralId )
	{
		Validate.notNull( collateralId );

		ApplyCollateral pojo = read( collateralId );
		pojo.setResend( pojo.getResend() + 1 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getCollateralId();
	}

	public Long updateTransmissionStatus( Long collateralId, String transmissionStatusCode )
	{
		Validate.notNull( collateralId );

		ApplyCollateral pojo = read( collateralId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getCollateralId();
	}

	@Override
	protected Class<ApplyCollateral> getPojoClass()
	{
		return ApplyCollateral.class;
	}
}
