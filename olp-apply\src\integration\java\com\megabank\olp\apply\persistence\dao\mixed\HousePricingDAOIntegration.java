package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.mixed.HousePricingListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.HousePricingListDTO;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.bean.threadlocal.PagingThreadLocalBean;
import com.megabank.olp.base.threadlocal.PagingThreadLocal;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HousePricingDAOIntegration
{
	@Autowired
	private PagingThreadLocal pagingThreadLocal;
	
	@Autowired
	private HousePricingDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getList()
	{
		HousePricingListGetterParamBean paramBean = new HousePricingListGetterParamBean();

		List<HousePricingListDTO> dtos = dao.getList( paramBean );

		logger.info( "dtos:{}", dtos );
	}

	@Test
	public void getPaging()
	{
		PagingThreadLocalBean localBean = new PagingThreadLocalBean();
		localBean.setStart( 0 );
		localBean.setLength( 10 );
		localBean.setSortColumn( "createdDate" );
		localBean.setSortDirection( "asc" );
		pagingThreadLocal.set( localBean );

		HousePricingListGetterParamBean paramBean = new HousePricingListGetterParamBean();

		PagingBean<HousePricingListDTO> result = dao.getPaging( paramBean );

		logger.info( "data:{}", result.getData() );
		logger.info( "recordsFiltered:{}", result.getRecordsFiltered() );
		logger.info( "recordsTotal:{}", result.getRecordsTotal() );

	}

}
