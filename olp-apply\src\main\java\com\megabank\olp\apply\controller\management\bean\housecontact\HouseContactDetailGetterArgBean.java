package com.megabank.olp.apply.controller.management.bean.housecontact;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class HouseContactDetailGetterArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long houseContactId;

	public HouseContactDetailGetterArgBean()
	{
		// default constructor
	}

	public Long getHouseContactId()
	{
		return houseContactId;
	}

	public void setHouseContactId( Long houseContactId )
	{
		this.houseContactId = houseContactId;
	}

}
