package com.megabank.olp.apply.controller.loan.bean.apply;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class LoanPlanCheckedArgBean extends BaseBean
{
	@NotBlank
	private String plan;

	public LoanPlanCheckedArgBean()
	{
		// default constructor
	}

	public String getPlan()
	{
		return plan;
	}

	public void setPlan( String plan )
	{
		this.plan = plan;
	}

}
