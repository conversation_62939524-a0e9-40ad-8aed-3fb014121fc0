package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanGuaranteeInfoUpdatedParamBean extends BaseBean
{
	private Long loanId;

	private String guarantyReasonCode;

	private String otherGuarantyReason;

	private Boolean cohabitationFlag;

	private String relationBorrowerType;

	public ApplyLoanGuaranteeInfoUpdatedParamBean()
	{}

	public Boolean getCohabitationFlag()
	{
		return cohabitationFlag;
	}

	public String getGuarantyReasonCode()
	{
		return guarantyReasonCode;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getOtherGuarantyReason()
	{
		return otherGuarantyReason;
	}

	public String getRelationBorrowerType()
	{
		return relationBorrowerType;
	}

	public void setCohabitationFlag( Boolean cohabitationFlag )
	{
		this.cohabitationFlag = cohabitationFlag;
	}

	public void setGuarantyReasonCode( String guarantyReasonCode )
	{
		this.guarantyReasonCode = guarantyReasonCode;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setOtherGuarantyReason( String otherGuarantyReason )
	{
		this.otherGuarantyReason = otherGuarantyReason;
	}

	public void setRelationBorrowerType( String relationBorrowerType )
	{
		this.relationBorrowerType = relationBorrowerType;
	}

}
