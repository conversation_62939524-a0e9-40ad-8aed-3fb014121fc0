package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeRepresentativeType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeRepresentativeTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeRepresentativeTypeDAO extends BasePojoDAO<CodeRepresentativeType, String>
{
	public CodeRepresentativeType read( String representativeType )
	{
		Validate.notBlank( representativeType );

		return getPojoByPK( representativeType, CodeRepresentativeType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeRepresentativeType> getPojoClass()
	{
		return CodeRepresentativeType.class;
	}
}
