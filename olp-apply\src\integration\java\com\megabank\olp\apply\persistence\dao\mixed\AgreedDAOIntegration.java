package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.base.config.BasePersistenceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class AgreedDAOIntegration
{
	@Autowired
	private AgreedDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getNeedToCheckItemIds()
	{
		String userType = "borrower"; // guarantor | borrower
		String identityType = "none"; // moica | none
		String loanType = "personalloan"; // personalloan | houseloan

		List<Long> result = dao.getNeedToCheckItemIds( userType, identityType, loanType );

		logger.info( "result:{}", result );
	}

}
