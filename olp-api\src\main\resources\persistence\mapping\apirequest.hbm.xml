<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
	
	<sql-query name="request.getRequestUrl">
		<return-scalar column="requestUrl" type="string"/>
		SELECT url.request_url requestUrl
		FROM api_request_url url
		LEFT JOIN api_request_url_client client ON url.request_url_id = client.request_url_id
		WHERE client.client_address LIKE coalesce( :clientAddress, client.client_address )
	</sql-query>
	
	
	
</hibernate-mapping>
