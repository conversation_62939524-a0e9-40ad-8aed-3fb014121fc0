package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeLoanVersion;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeLoanVersionDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeLoanVersionDAO extends BasePojoDAO<CodeLoanVersion, String>
{

	public CodeLoanVersion read( String loanVersionCode )
	{
		Validate.notBlank( loanVersionCode );

		return getPojoByPK( loanVersionCode, CodeLoanVersion.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeLoanVersion> getPojoClass()
	{
		return CodeLoanVersion.class;
	}
}
