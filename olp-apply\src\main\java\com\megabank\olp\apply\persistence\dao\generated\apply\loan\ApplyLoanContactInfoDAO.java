package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContactInfoCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContactInfoUpdatedAddrTextParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContactInfoUpdatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.address.ApplyAddrTextDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.address.ApplyAddressDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeHouseStatusDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeResidenceStatusDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeServiceAssociateDeptDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContactInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanContactInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanContactInfoDAO extends BasePojoDAO<ApplyLoanContactInfo, Long>
{
	@Autowired
	private ApplyAddressDAO applyAddressDAO;

	@Autowired
	private ApplyAddrTextDAO applyAddrTextDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeResidenceStatusDAO codeResidenceStatusDAO;

	@Autowired
	private CodeHouseStatusDAO codeHouseStatusDAO;

	@Autowired
	private CodeServiceAssociateDeptDAO codeServiceAssociateDeptDAO;

	public Long create( ApplyLoanContactInfoCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanContactInfo pojo = new ApplyLoanContactInfo();
		pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );
		pojo.setCodeHouseStatus( StringUtils.isBlank( paramBean.getHouseStatusCode() ) ? null
																					   : codeHouseStatusDAO.read( paramBean.getHouseStatusCode() ) );
		pojo.setCodeResidenceStatus( StringUtils
					.isBlank( paramBean.getResidenceStatusCode() ) ? null : codeResidenceStatusDAO.read( paramBean.getResidenceStatusCode() ) );
		pojo.setHomeAddress( paramBean.getHomeAddressId() == null ? null : applyAddressDAO.read( paramBean.getHomeAddressId() ) );
		pojo.setMailingAddress( paramBean.getMailingAddressId() == null ? null : applyAddressDAO.read( paramBean.getMailingAddressId() ) );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setHomePhoneCode( paramBean.getHomePhoneCode() );
		pojo.setHomePhoneNumber( paramBean.getHomePhoneNumber() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );
		pojo.setCodeBranchBank( paramBean.getBranchBankId() == null ? null : codeBranchBankDAO.read( paramBean.getBranchBankId() ) );
		pojo.setCodeServiceAssociateDept( StringUtils
					.isBlank( paramBean.getServiceAssociateDeptCode() ) ? null
																		: codeServiceAssociateDeptDAO
																					.read( paramBean.getServiceAssociateDeptCode() ) );
		pojo.setServiceAssociate( paramBean.getServiceAssociate() );
		pojo.setRent( paramBean.getRent() );

		return super.createPojo( pojo );
	}

	public ApplyLoanContactInfo read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, ApplyLoanContactInfo.TABLENAME_CONSTANT );
	}

	public ApplyLoanContactInfo readToNull( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId );
	}

	public Long update( ApplyLoanContactInfoUpdatedAddrTextParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanContactInfo pojo = read( paramBean.getLoanId() );
		pojo.setHomeAddrText( paramBean.getHomeAddrTextId() == null ? null : applyAddrTextDAO.read( paramBean.getHomeAddrTextId() ) );
		pojo.setMailingAddrText( paramBean.getMailingAddrTextId() == null ? null : applyAddrTextDAO.read( paramBean.getMailingAddrTextId() ) );

		return pojo.getLoanId();
	}

	public Long update( ApplyLoanContactInfoUpdatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanContactInfo pojo = read( paramBean.getLoanId() );
		pojo.setCodeHouseStatus( StringUtils.isBlank( paramBean.getHouseStatusCode() ) ? null
																					   : codeHouseStatusDAO.read( paramBean.getHouseStatusCode() ) );
		pojo.setCodeResidenceStatus( StringUtils
					.isBlank( paramBean.getResidenceStatusCode() ) ? null : codeResidenceStatusDAO.read( paramBean.getResidenceStatusCode() ) );
		pojo.setHomeAddress( paramBean.getHomeAddressId() == null ? null : applyAddressDAO.read( paramBean.getHomeAddressId() ) );
		pojo.setMailingAddress( paramBean.getMailingAddressId() == null ? null : applyAddressDAO.read( paramBean.getMailingAddressId() ) );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setHomePhoneCode( paramBean.getHomePhoneCode() );
		pojo.setHomePhoneNumber( paramBean.getHomePhoneNumber() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );
		pojo.setCodeBranchBank( paramBean.getBranchBankId() == null ? null : codeBranchBankDAO.read( paramBean.getBranchBankId() ) );
		pojo.setCodeServiceAssociateDept( StringUtils
					.isBlank( paramBean.getServiceAssociateDeptCode() ) ? null
																		: codeServiceAssociateDeptDAO
																					.read( paramBean.getServiceAssociateDeptCode() ) );
		pojo.setServiceAssociate( paramBean.getServiceAssociate() );
		pojo.setRent( paramBean.getRent() );

		return pojo.getLoanId();
	}

	@Override
	protected Class<ApplyLoanContactInfo> getPojoClass()
	{
		return ApplyLoanContactInfo.class;
	}
}
