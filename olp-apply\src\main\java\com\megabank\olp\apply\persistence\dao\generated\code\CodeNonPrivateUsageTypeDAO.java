package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeNonPrivateUsageType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeNonPrivateUsageTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeNonPrivateUsageTypeDAO extends BasePojoDAO<CodeNonPrivateUsageType, String>
{
	public CodeNonPrivateUsageType read( String nonPrivateUsageType )
	{
		Validate.notBlank( nonPrivateUsageType );

		return getPojoByPK( nonPrivateUsageType, CodeNonPrivateUsageType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeNonPrivateUsageType> getPojoClass()
	{
		return CodeNonPrivateUsageType.class;
	}
}
