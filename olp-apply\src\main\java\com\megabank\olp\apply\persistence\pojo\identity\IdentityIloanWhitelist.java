package com.megabank.olp.apply.persistence.pojo.identity;

import com.megabank.olp.base.bean.BaseBean;
import jakarta.persistence.*;
import java.util.Date;
import static jakarta.persistence.GenerationType.IDENTITY;

/**
 * The IdentityIloanWhitelist is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "identity_iloan_whitelist" )
public class IdentityIloanWhitelist extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "identity_iloan_whitelist";

	public static final String ILOAN_WHITELIST_ID_CONSTANT = "iloanWhitelistId";

	public static final String ID_NO_CONSTANT = "idNo";


	public static final String CREATED_DATE_CONSTANT = "createdDate";


	private Long iloanWhitelistId;

	private String idNo;

	private Date createdDate;

	public IdentityIloanWhitelist()
	{}

	public IdentityIloanWhitelist( String idNo, Date createdDate )
	{
		this.idNo = idNo;
		this.createdDate = createdDate;
	}

	public IdentityIloanWhitelist(Long iloanWhitelistId )
	{
		this.iloanWhitelistId = iloanWhitelistId;
	}


	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "id_no", nullable = false, length = 10 )
	public String getIdNo()
	{
		return idNo;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "iloan_whitelist_Id", unique = true, nullable = false )
	public Long getIloanWhitelistId()
	{
		return iloanWhitelistId;
	}


	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setIloanWhitelistId( Long iloanWhitelistId )
	{
		this.iloanWhitelistId = iloanWhitelistId;
	}


}