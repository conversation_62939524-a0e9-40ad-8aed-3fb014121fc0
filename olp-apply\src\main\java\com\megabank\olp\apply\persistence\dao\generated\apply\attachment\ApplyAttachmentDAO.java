package com.megabank.olp.apply.persistence.dao.generated.apply.attachment;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.attachment.ApplyAttachmentCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeAttachmentTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.pojo.apply.attachment.ApplyAttachment;
import com.megabank.olp.apply.utility.ApplyLoanUtils;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyAttachmentDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyAttachmentDAO extends BasePojoDAO<ApplyAttachment, Long>
{
	private static final String ATTACHMENT_ID_CONSTANT = "attachmentId";

	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@Autowired
	private CodeAttachmentTypeDAO codeAttachmentTypeDAO;

	public Long create( ApplyAttachmentCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getFileSize() );
		Validate.notNull( paramBean.getFileContent() );
		Validate.notBlank( paramBean.getAttachmentType() );
		Validate.notBlank( paramBean.getFileName() );
		Validate.notBlank( paramBean.getTransmissionStatusCode() );

		ApplyAttachment pojo = new ApplyAttachment();
		pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setCodeAttachmentType( codeAttachmentTypeDAO.read( paramBean.getAttachmentType() ) );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( paramBean.getTransmissionStatusCode() ) );
		pojo.setCompressFileContent( paramBean.getCompressFileContent() );
		pojo.setFileName( paramBean.getFileName() );
		pojo.setFileSize( paramBean.getFileSize() );
		pojo.setFileContent( paramBean.getFileContent() );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );

		if( pojo.getApplyLoan() != null && ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( pojo.getApplyLoan().getLoanPlanCode() ) )
			pojo.setNotified( true ); // 中鋼消貸的{補件}，不應按{目前機制}通知分行經辦

		return super.createPojo( pojo );
	}

	public List<ApplyAttachment> getPojosByLoanId( Long loanId )
	{
		NameValueBean condition = new NameValueBean( ApplyAttachment.APPLY_LOAN_CONSTANT, applyLoanDAO.read( loanId ) );

		return getPojosByProperty( condition );
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplyAttachment> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "attachment.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyAttachment.class );

		return nativeQuery.getResultList();
	}

	public ApplyAttachment read( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		return getPojoByPK( attachmentId, ApplyAttachment.TABLENAME_CONSTANT );
	}

	public int updateNotified( List<Long> attachmentIds )
	{
		Validate.notEmpty( attachmentIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "attachment.updateNotified" );
		nativeQuery.setParameterList( ATTACHMENT_ID_CONSTANT, attachmentIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		ApplyAttachment pojo = read( attachmentId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long updateResend( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		ApplyAttachment pojo = read( attachmentId );
		pojo.setResend( pojo.getResend() + 1 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long initializeResend( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		ApplyAttachment pojo = read( attachmentId );
		pojo.setResend( 0 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long updateTransmissionStatus( Long attachmentId, String transmissionStatusCode )
	{
		Validate.notNull( attachmentId );

		ApplyAttachment pojo = read( attachmentId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	@Override
	protected Class<ApplyAttachment> getPojoClass()
	{
		return ApplyAttachment.class;
	}
}
