package com.megabank.olp.apply.controller.loan;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.megabank.olp.apply.controller.loan.bean.apply.LoanApplyGetAgreedDateArgBean;
import com.megabank.olp.apply.controller.loan.bean.contact.HouseContactMeSubmittedArgBean;
import com.megabank.olp.apply.service.management.HouseContactService;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactBasicDataBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactLoanDataBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactSubmittedParamBean;
import com.megabank.olp.base.enums.ProductCodeEnum;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanApplyCheckedGuarantorArgBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.loan.bean.apply.LoanApplyCheckedArgBean;
import com.megabank.olp.apply.controller.loan.bean.apply.LoanPlanCheckedArgBean;
import com.megabank.olp.apply.controller.loan.bean.collateral.LoanCollateralCheckedArgBean;
import com.megabank.olp.apply.controller.loan.bean.contact.ContactMeSubmittedArgBean;
import com.megabank.olp.apply.controller.loan.bean.download.LoanDownloadCheckedArgBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractAccountArgBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractCheckArgBean;
import com.megabank.olp.apply.controller.loan.bean.upload.LoanUploadCheckedArgBean;
import com.megabank.olp.apply.controller.mydata.bean.UpdateMyDataAuthArgBean;
import com.megabank.olp.apply.service.loan.ApplyService;
import com.megabank.olp.apply.service.loan.CollateralService;
import com.megabank.olp.apply.service.loan.ContactService;
import com.megabank.olp.apply.service.loan.DownloadService;
import com.megabank.olp.apply.service.loan.SigningContractService;
import com.megabank.olp.apply.service.loan.UploadService;
import com.megabank.olp.apply.service.loan.YouthStartUpService;
import com.megabank.olp.apply.service.loan.bean.contact.ContactMeSubmittedParamBean;
import com.megabank.olp.apply.service.mydata.MyDataService;
import com.megabank.olp.apply.utility.enums.YouthApplyStatusEnum;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.message.MyObjectMapper;
import com.megabank.olp.client.sender.eloan.getted.accountAuth.EloanAccountAuthInfoClient;
import com.megabank.olp.client.sender.eloan.getted.accountAuth.bean.EloanAccountAuthInfoArgBean;
import com.megabank.olp.client.sender.eloan.getted.accountAuth.bean.EloanAccountAuthInfoResultBean;
import com.megabank.olp.client.sender.eloan.submitted.EloanSubmittedResultBean;
import com.megabank.olp.client.sender.eloan.submitted.accountAuth.EloanAccountAuthTxRecordClient;
import com.megabank.olp.client.sender.eloan.submitted.accountAuth.bean.EloanAccountAuthTxRecordArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.accountAuth.FetchAccountAuthTxResultsClient;
import com.megabank.olp.client.sender.micro.otherdatabase.accountAuth.bean.FetchAccountAuthTxResultsArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "open/loan" )
public class OpenLoanController extends BaseController
{
	@Autowired
	private ApplyService applyService;

	@Autowired
	private CollateralService collateralService;

	@Autowired
	private DownloadService downloadService;

	@Autowired
	private SigningContractService signingContractService;

	@Autowired
	private UploadService uploadService;

	@Autowired
	private ContactService contactService;

	@Autowired
	private EloanAccountAuthInfoClient eloanAccountAuthInfoClient;

	@Autowired
	private YouthStartUpService youthStartUpService;

	@Autowired
	private FetchAccountAuthTxResultsClient fetchAccountAuthTxResultsClient;

	@Autowired
	private EloanAccountAuthTxRecordClient eloanAccountAuthTxRecordClient;

	@Autowired
	private MyDataService myDataService;

	@Autowired
	private HouseContactService houseContactService;
	
	@Autowired
	@Qualifier( "serviceObjectMapper" )
	private MyObjectMapper objectMapper;

	@Value( "${youthStartUp.client.key.finGetResultsKey1}" )
	private String finGetResultsKey1;

	@Value( "${youthStartUp.client.key.finGetResultsKey2}" )
	private String finGetResultsKey2;

	private final Logger logger = LogManager.getLogger( getClass() );

	/**
	 * 檢查輸入案件編號的案件是否存在
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "apply/checkCaseExisted" )
	public Map<String, Object> checkLoanApplyExisted( @RequestBody @Validated LoanApplyCheckedArgBean argBean )
	{
		String caseNo = argBean.getCaseNo();
		String name = argBean.getName();
		String idNo = argBean.getIdNo();
		String mobileNumber = argBean.getMobileNumber();
		String loanType = argBean.getLoanType();

		return getResponseMap( applyService.checkLoanApplyExisted( caseNo, name, idNo, mobileNumber, loanType ) );
	}

	/**
	 * 檢查使用者是否有可擔保案件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "apply/collateral/checkCaseExisted" )
	public Map<String, Object> checkLoanCollateralExisted( @RequestBody @Validated LoanCollateralCheckedArgBean argBean )
	{
		return getResponseMap( collateralService.checkLoanCollateralExisted( argBean.getIdNo() ) );
	}

	/**
	 * 檢查使用者是否有案件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "download/checkCaseExisted" )
	public Map<String, Object> checkLoanDownloadExisted( @RequestBody @Validated LoanDownloadCheckedArgBean argBean )
	{
		String idNo = argBean.getIdNo();
		Date birthDate = argBean.getBirthDate();

		return getResponseMap( downloadService.checkLoanApplyExisted( idNo, birthDate ) );
	}

	/**
	 * 檢查行銷方案狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "apply/checkLoanPlan" )
	public Map<String, Object> checkLoanPlan( @RequestBody LoanPlanCheckedArgBean argBean )
	{
		String plan = argBean.getPlan();
		/*
		 * curl -X POST -H "Content-type: application/json" -d "{\"plan\":\"C001\"}"
		 * http://127.0.0.1:9010/open/loan/apply/checkLoanPlan
		 */
		return getResponseMap( applyService.checkLoanPlanCode_cmp_endTs( plan ) );
	}

	/**
	 * 檢查使用者是否有指定貸款類型的案件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "upload/checkCaseExisted" )
	public Map<String, Object> checkLoanUploadExisted( @RequestBody @Validated LoanUploadCheckedArgBean argBean )
	{
		String idNo = argBean.getIdNo();
		Date birthDate = argBean.getBirthDate();
		String loanType = argBean.getLoanType();

		return getResponseMap( uploadService.checkLoanApplyExisted( idNo, birthDate, loanType ) );
	}

	/**
	 * 檢查當前客戶是否有信貸對保案件
	 */
	@PostMapping( "signingcontract/checkCaseExisted" )
	public Map<String, Object> checkSigningContractExisted( @RequestBody @Validated SigningContractCheckArgBean argBean )
	{
		String idNo = argBean.getIdNo();
		Date birthDate = argBean.getBirthDate();

		return getResponseMap( signingContractService.checkSigningContractExisted( idNo, birthDate, ProductCodeEnum.PERSONAL_LOAN.getContext() ) );
	}

	/**
	 * 檢查當前客戶是否有房貸對保案件
	 */
	@PostMapping( "housesigningcontract/checkCaseExisted" )
	public Map<String, Object> checkHouseSigningContractExisted( @RequestBody @Validated SigningContractCheckArgBean argBean )
	{
		String idNo = argBean.getIdNo();
		Date birthDate = argBean.getBirthDate();

		return getResponseMap( signingContractService.checkSigningContractExisted( idNo, birthDate, ProductCodeEnum.HOUSE_LOAN.getContext() ) );
	}

	/**
	 * 檢查案件是否提供保證人申請
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "apply/checkGuarantorAvailable" )
	public Map<String, Object> checkGuarantorAvailable( @RequestBody @Validated LoanApplyCheckedGuarantorArgBean argBean )
	{
		String caseNo = argBean.getCaseNo();

		return getResponseMap( applyService.checkGuarantorAvailable( caseNo ) );
	}

	@PostMapping( "signingcontract/createBankAccount" )
	public Map<String, Object> createBankAccount( @RequestBody @Validated SigningContractAccountArgBean argBean )
	{

		String bankAccount = argBean.getBankAccount();
		String bankCode = argBean.getBankCode();
		String bankBranchCode = argBean.getBankBranchCode();
		Long signingContractId = argBean.getSingingContractId();
		return getResponseMap( signingContractService.createBankAccount( signingContractId, bankCode, bankBranchCode, bankAccount ) );
	}

	@PostMapping( "housesigningcontract/createBankAccount" )
	public Map<String, Object> createHouseBankAccount( @RequestBody @Validated SigningContractAccountArgBean argBean )
	{

		String bankAccount = argBean.getBankAccount();
		String bankCode = argBean.getBankCode();
		String bankBranchCode = argBean.getBankBranchCode();
		Long signingContractId = argBean.getSingingContractId();
		return getResponseMap( signingContractService.createBankAccount( signingContractId, bankCode, bankBranchCode, bankAccount ) );
	}

	@PostMapping( "signingcontract/getLatestSigningContract" )
	public Map<String, Object> getLatestSigningContract( @RequestBody @Validated SigningContractCheckArgBean argBean )
	{
		String idNo = argBean.getIdNo();
		Date birthDate = argBean.getBirthDate();
		return getResponseMap( signingContractService.getSigningContractLatestOne( idNo, birthDate, ProductCodeEnum.PERSONAL_LOAN.getContext() ) );
	}

	@PostMapping( "housesigningcontract/getLatestSigningContract" )
	public Map<String, Object> getLatestHouseSigningContract( @RequestBody @Validated SigningContractCheckArgBean argBean )
	{
		String idNo = argBean.getIdNo();
		Date birthDate = argBean.getBirthDate();
		return getResponseMap( signingContractService.getSigningContractLatestOne( idNo, birthDate, ProductCodeEnum.HOUSE_LOAN.getContext() ) );
	}

	/**
	 * 取得行銷方案說明
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "apply/getLoanPlanDesc" )
	public Map<String, Object> getLoanPlanDesc( @RequestBody LoanPlanCheckedArgBean argBean )
	{
		String plan = argBean.getPlan();
		/*
		 * curl -X POST -H "Content-type: application/json" -d "{\"plan\":\"C001\"}"
		 * http://127.0.0.1:9010/open/loan/apply/getLoanPlanDesc
		 */
		// 此版本只回傳一行，在 Apply.js 有加工去判斷 let infoArray = info.split('申貸期間');
		return getResponseMap( applyService.getLoanPlanDesc( plan ) );
	}

	/**
	 * 取得行銷方案說明
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "apply/getLoanPlanDescList" )
	public Map<String, Object> getLoanPlanDescList( @RequestBody LoanPlanCheckedArgBean argBean )
	{
		/*
		 * curl -X POST -H "Content-type: application/json" -d "{\"plan\":\"C001\"}"
		 * http://127.0.0.1:9010/open/loan/apply/getLoanPlanDescList
		 */
		return getResponseMap( applyService.getLoanPlanDescList( argBean.getPlan() ) );
	}

	/**
	 * 取得信貸專人聯絡感謝頁內容
	 *
	 * @return
	 */
	@PostMapping( "contact/getThankyouMessage" )
	public Map<String, Object> getThankyouMessage()
	{
		return getResponseMap( contactService.getThankyouMessage() );
	}

	/**
	 * 送出信貸專人聯絡內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "contact/submitContactMe" )
	public Map<String, Object> submitContactMe( @RequestBody @Validated ContactMeSubmittedArgBean argBean )
	{
		ContactMeSubmittedParamBean paramBean = getContactMeSubmittedParamBean( argBean );

		return getResponseMap( contactService.submitContactMe( paramBean ) );
	}

	@PostMapping( "accountVerifiedResults" )
	public Map<String, Object> youthStartUpAccountVerifiedResults( @RequestBody @Validated EloanAccountAuthInfoArgBean accInfoArgBean )
		throws IOException, NoSuchAlgorithmException, ParseException
	{
		String oid = accInfoArgBean.getOid();
		String code = YouthApplyStatusEnum.FIN_VALID_VALID.getCode();
		String msg = YouthApplyStatusEnum.FIN_VALID_VALID.getContext();
		String title = YouthApplyStatusEnum.FIN_VALID_TITLE_VALID.getContext();

		oid = filterString( oid );

		logger.debug( "oid= {}", oid );

		// -----------------------------------------------------------------------------------------------------------------------------
		/*** get uuid ***/
		accInfoArgBean.setActionCode( "R" );
		EloanAccountAuthInfoResultBean uuidInfoBean = eloanAccountAuthInfoClient.send( accInfoArgBean );
		youthStartUpService.recordSystemLog( "accountVerifiedResults: get uuid", objectMapper.writeValueAsString( accInfoArgBean ),
											 objectMapper.writeValueAsString( uuidInfoBean ) );
		// -----------------------------------------------------------------------------------------------------------------------------
		/*** 交易紀錄查詢 ***/
		FetchAccountAuthTxResultsArgBean fetchArgBean = new FetchAccountAuthTxResultsArgBean();
		fetchArgBean.setUuid( uuidInfoBean.getUuid() );
		fetchArgBean.setRequestCheckSum( youthStartUpService.getTxRecordInquiryChecksum( fetchArgBean, finGetResultsKey1, finGetResultsKey2 ) );
		String results = fetchAccountAuthTxResultsClient.send( fetchArgBean );
		youthStartUpService.setResponseValue( results, fetchArgBean );
		youthStartUpService.recordSystemLog( "accountVerifiedResults: 交易紀錄查詢", objectMapper.writeValueAsString( fetchArgBean ),
											 objectMapper.writeValueAsString( fetchArgBean ) );

		// -----------------------------------------------------------------------------------------------------------------------------
		/*** save tx record ***/
		EloanAccountAuthTxRecordArgBean argBean = new EloanAccountAuthTxRecordArgBean();
		argBean.setActionCode( "C" );
		argBean.setOid( oid );
		youthStartUpService.setRequestValueForSavingTxRecord( argBean, fetchArgBean );
		EloanSubmittedResultBean submitResultBean = eloanAccountAuthTxRecordClient.send( argBean );
		youthStartUpService.recordSystemLog( "accountVerifiedResults: save tx record", objectMapper.writeValueAsString( argBean ),
											 objectMapper.writeValueAsString( submitResultBean ) );

		if( fetchArgBean.isSuccess() && youthStartUpService.isResponseCheckSumOk( fetchArgBean, finGetResultsKey1, finGetResultsKey2 ) )
		{
			code = YouthApplyStatusEnum.FIN_VALID_SUCCESS.getCode();
			msg = YouthApplyStatusEnum.FIN_VALID_SUCCESS.getContext();
			title = YouthApplyStatusEnum.FIN_VALID_TITLE_SUCCESS.getContext();
		}
		else
		{
			code = YouthApplyStatusEnum.FIN_VALID_FAIL.getCode();
			msg = YouthApplyStatusEnum.FIN_VALID_FAIL.getContext();
			title = YouthApplyStatusEnum.FIN_VALID_TITLE_FAIL.getContext();
		}

		msg = youthStartUpService.processMessage( msg, argBean.getUpdateTime() );

		Map<String, Object> rtnParamMap = new HashMap<>();
		rtnParamMap.put( "sourceCode", "EG" );
		rtnParamMap.put( "statusCode", code );
		rtnParamMap.put( "stat", "ok" );
		Map<String, String> resultMap = new HashMap<>();
		resultMap.put( "title", title );
		resultMap.put( "content", msg );
		rtnParamMap.put( "result", resultMap );

		return rtnParamMap;
	}
	
	/**
	 * 更新MyData狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "/updateMyDataAuth" )
	public Map<String, Object> updateMyDataAuth( @RequestBody @Validated UpdateMyDataAuthArgBean argBean )
	{
		return getResponseMap( myDataService.updateMyDataAuth(argBean.getTransactionId()) );
	}

	@SuppressWarnings( "deprecation" )
	private String filterString( String str )
	{
		return StringUtils.removeAll( StringUtils.trimToEmpty( str ), "[^a-zA-z0-9+-=]" );
	}

	private ContactMeSubmittedParamBean getContactMeSubmittedParamBean( ContactMeSubmittedArgBean argBean )
	{
		ContactMeSubmittedParamBean paramBean = new ContactMeSubmittedParamBean();
		paramBean.setContactTimeCode( argBean.getContactTimeCode() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setName( argBean.getName() );
		paramBean.setOtherMsg( argBean.getOtherMsg() );
		paramBean.setSexCode( argBean.getSexCode() );
		paramBean.setLoanPlanCode( argBean.getLoanPlanCode() );
		paramBean.setIntroducerBranchBankId( argBean.getIntroducerBranchBankId() );
		paramBean.setIntroducerEmpId( argBean.getIntroducerEmpId() );

		return paramBean;
	}

	/**
	 * 取得同意聯徵時間
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "upload/getAgreedDate" )
	public Map<String, Object> getAgreedDate( @RequestBody LoanApplyGetAgreedDateArgBean argBean )
	{
		return getResponseMap( uploadService.getAgreedDate( argBean.getIdNo(), argBean.getBirthDate(), argBean.getLoanType() ) );
	}

	/**
	 * 取得房貸與我聯繫同意事項
	 *
	 * @return
	 */
	@PostMapping( "housecontact/getAgreement" )
	public Map<String, Object> getAgreement()
	{
		return getResponseMap( houseContactService.getAgreement() );
	}

	/**
	 * 送出房貸專人聯絡內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "housecontact/submitHouseContact" )
	public Map<String, Object> submitHouseContact( @RequestBody @Validated HouseContactMeSubmittedArgBean argBean )
	{
		HouseContactSubmittedParamBean paramBean = getHouseContactMeSubmittedParamBean( argBean );

		return getResponseMap( houseContactService.create( paramBean ) );
	}

	private HouseContactSubmittedParamBean getHouseContactMeSubmittedParamBean( HouseContactMeSubmittedArgBean argBean )
	{
		HouseContactSubmittedParamBean paramBean = new HouseContactSubmittedParamBean();
		paramBean.setBranchBankId( argBean.getBranchBankId() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setLoanPlanCode( argBean.getLoanPlanCode() );
		paramBean.setOtherMsg( argBean.getOtherMsg() );
		paramBean.setBasicInfo( mapHouseContactMeBasicData( argBean ) );
		paramBean.setLoanInfo( mapHouseContactMeLoanData( argBean ) );

		return paramBean;
	}

	private HouseContactBasicDataBean mapHouseContactMeBasicData( HouseContactMeSubmittedArgBean argBean )
	{
		HouseContactBasicDataBean paramBean = new HouseContactBasicDataBean();
		paramBean.setCallBackTime( argBean.getCallBackTime() );
		paramBean.setcName( argBean.getName() );
		return paramBean;
	}

	private HouseContactLoanDataBean mapHouseContactMeLoanData( HouseContactMeSubmittedArgBean argBean )
	{
		HouseContactLoanDataBean dataBean = new HouseContactLoanDataBean();

		dataBean.setAddress( argBean.getAddress() );
		dataBean.setCounty( argBean.getCounty() );
		dataBean.setDistrict( argBean.getDistrict() );
		dataBean.setLoanBalance( argBean.getLoanBalance() );
		dataBean.setLoanPurpose( argBean.getLoanPurpose() );

		return dataBean;
	}


}
