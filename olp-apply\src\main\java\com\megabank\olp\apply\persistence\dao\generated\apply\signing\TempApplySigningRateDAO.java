package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.util.List;
import java.util.Objects;

import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyHouseSigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.utility.enums.SigningContractTypeEnum;
import com.megabank.olp.base.bean.NameValueBean;
import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.signing.TempApplySigningRate;
import com.megabank.olp.base.layer.BasePojoDAO;

@Repository
public class TempApplySigningRateDAO extends BasePojoDAO<TempApplySigningRate, Long>
{

	public Long create( List<TempApplySigningRate> list )
	{
		Long count = 0L;
		for( TempApplySigningRate paramBean : list )
		{
			Validate.notBlank( paramBean.getPeriod() );
			Validate.notBlank( paramBean.getLoanAmt() );
			Validate.notNull( paramBean.getApplySigningContract().getSigningContractId() );
			Validate.notBlank( paramBean.getCompoundAmt() );
			Validate.notBlank( paramBean.getPrincipalAmt() );
			Validate.notBlank( paramBean.getInterestAmt() );
			Validate.notBlank( paramBean.getNowLoanAmt() );

			try
			{
				super.createPojo( paramBean );
			}
			catch( Exception exception )
			{
				exception.printStackTrace();
			}

		}
		return count;
	}

	public void deleteAllBySigningContractId( Long signingContractId ){
		List<TempApplySigningRate> pojos = getPojosBySigningContractId( signingContractId );

		if ( !pojos.isEmpty() ){ deletePojos( pojos ); }
	}

	public List<TempApplySigningRate> getPojosBySigningContractId( Long signingContractId )
	{
		Validate.notNull( signingContractId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "temp.getTempApplySigningRate" );
		nativeQuery.setParameter( TempApplySigningRate.SIGNING_CONTRACT_ID_CONSTANT, signingContractId, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( TempApplySigningRate.class );

		return nativeQuery.getResultList();
	}

	@Override
	protected Class<TempApplySigningRate> getPojoClass()
	{
		return TempApplySigningRate.class;
	}

}
