package com.megabank.olp.apply.controller.management.bean.signing;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.megabank.olp.base.bean.BaseBean;

import java.math.BigDecimal;

public class InterestInfoCreateArgBean extends BaseBean
{
	private BigDecimal interestInfoType;

	private String formula1;

	private BigDecimal firstPeriodFrom1;

	private BigDecimal firstPeriodTo1;

	private String firstPeriodRate1;

	private BigDecimal secondPeriodFrom1;

	private BigDecimal secondPeriodTo1;

	private String secondPeriodRate1;

	private BigDecimal thirdPeriodFrom1;

	private BigDecimal thirdPeriodTo1;

	private String thirdPeriodRate1;

	private String rate1;

	private String other1;

	private String formula2;

	@JsonAlias( "rate2-1" )
	private String rate2_1;

	@JsonAlias( "rate2-2" )
	private String rate2_2;

	@JsonAlias( "rate2-3-1" )
	private String rate2_3_1;

	@JsonAlias( "rate2-3-2" )
	private String rate2_3_2;

	private String other2;

	private String other3;

	public InterestInfoCreateArgBean()
	{
	}

	public BigDecimal getInterestInfoType()
	{
		return interestInfoType;
	}

	public String getFormula1()
	{
		return formula1;
	}

	public BigDecimal getFirstPeriodFrom1()
	{
		return firstPeriodFrom1;
	}

	public BigDecimal getFirstPeriodTo1()
	{
		return firstPeriodTo1;
	}

	public String getFirstPeriodRate1()
	{
		return firstPeriodRate1;
	}

	public BigDecimal getSecondPeriodFrom1()
	{
		return secondPeriodFrom1;
	}

	public BigDecimal getSecondPeriodTo1()
	{
		return secondPeriodTo1;
	}

	public String getSecondPeriodRate1()
	{
		return secondPeriodRate1;
	}

	public BigDecimal getThirdPeriodFrom1()
	{
		return thirdPeriodFrom1;
	}

	public BigDecimal getThirdPeriodTo1()
	{
		return thirdPeriodTo1;
	}

	public String getThirdPeriodRate1()
	{
		return thirdPeriodRate1;
	}

	public String getRate1()
	{
		return rate1;
	}

	public String getOther1()
	{
		return other1;
	}

	public String getFormula2()
	{
		return formula2;
	}

	public String getRate2_1()
	{
		return rate2_1;
	}

	public String getRate2_2()
	{
		return rate2_2;
	}

	public String getRate2_3_1()
	{
		return rate2_3_1;
	}

	public String getRate2_3_2()
	{
		return rate2_3_2;
	}

	public String getOther2()
	{
		return other2;
	}

	public String getOther3()
	{
		return other3;
	}

	public void setInterestInfoType( BigDecimal interestInfoType )
	{
		this.interestInfoType = interestInfoType;
	}

	public void setFormula1( String formula1 )
	{
		this.formula1 = formula1;
	}

	public void setFirstPeriodFrom1( BigDecimal firstPeriodFrom1 )
	{
		this.firstPeriodFrom1 = firstPeriodFrom1;
	}

	public void setFirstPeriodTo1( BigDecimal firstPeriodTo1 )
	{
		this.firstPeriodTo1 = firstPeriodTo1;
	}

	public void setFirstPeriodRate1( String firstPeriodRate1 )
	{
		this.firstPeriodRate1 = firstPeriodRate1;
	}

	public void setSecondPeriodFrom1( BigDecimal secondPeriodFrom1 )
	{
		this.secondPeriodFrom1 = secondPeriodFrom1;
	}

	public void setSecondPeriodTo1( BigDecimal secondPeriodTo1 )
	{
		this.secondPeriodTo1 = secondPeriodTo1;
	}

	public void setSecondPeriodRate1( String secondPeriodRate1 )
	{
		this.secondPeriodRate1 = secondPeriodRate1;
	}

	public void setThirdPeriodFrom1( BigDecimal thirdPeriodFrom1 )
	{
		this.thirdPeriodFrom1 = thirdPeriodFrom1;
	}

	public void setThirdPeriodTo1( BigDecimal thirdPeriodTo1 )
	{
		this.thirdPeriodTo1 = thirdPeriodTo1;
	}

	public void setThirdPeriodRate1( String thirdPeriodRate1 )
	{
		this.thirdPeriodRate1 = thirdPeriodRate1;
	}

	public void setRate1( String rate1 )
	{
		this.rate1 = rate1;
	}

	public void setOther1( String other1 )
	{
		this.other1 = other1;
	}

	public void setFormula2( String formula2 )
	{
		this.formula2 = formula2;
	}

	public void setRate2_1( String rate2_1 )
	{
		this.rate2_1 = rate2_1;
	}

	public void setRate2_2( String rate2_2 )
	{
		this.rate2_2 = rate2_2;
	}

	public void setRate2_3_1( String rate2_3_1 )
	{
		this.rate2_3_1 = rate2_3_1;
	}

	public void setRate2_3_2( String rate2_3_2 )
	{
		this.rate2_3_2 = rate2_3_2;
	}

	public void setOther2( String other2 )
	{
		this.other2 = other2;
	}

	public void setOther3( String other3 )
	{
		this.other3 = other3;
	}
}