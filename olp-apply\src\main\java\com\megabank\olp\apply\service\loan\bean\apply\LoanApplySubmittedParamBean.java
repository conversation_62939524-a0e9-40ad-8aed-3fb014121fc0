package com.megabank.olp.apply.service.loan.bean.apply;

import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

public class LoanApplySubmittedParamBean extends BaseBean
{
	private String loanType;

	private LoanContentParamBean loanContentParamBean;

	private List<LoanRelationParamBean> loanRelationParamBeans;

	private List<LoanServedParamBean> loanServedParamBeans;

	public LoanApplySubmittedParamBean()
	{
		// default constructor
	}

	public LoanContentParamBean getLoanContentParamBean()
	{
		return loanContentParamBean;
	}

	public List<LoanRelationParamBean> getLoanRelationParamBeans()
	{
		return loanRelationParamBeans;
	}

	public List<LoanServedParamBean> getLoanServedParamBeans()
	{
		return loanServedParamBeans;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public void setLoanContentParamBean( LoanContentParamBean loanContentParamBean )
	{
		this.loanContentParamBean = loanContentParamBean;
	}

	public void setLoanRelationParamBeans( List<LoanRelationParamBean> loanRelationParamBeans )
	{
		this.loanRelationParamBeans = loanRelationParamBeans;
	}

	public void setLoanServedParamBeans( List<LoanServedParamBean> loanServedParamBeans )
	{
		this.loanServedParamBeans = loanServedParamBeans;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

}