package com.megabank.olp.apply.persistence.bean.mixed;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class SurveyListGetterParamBean extends BaseBean
{
	private Long finalBranchBankId;

	private Integer notified;

	private String processCode;

	private String contactTimeCode;

	private String name;

	private String mobileNumber;

	private Date dateStart;

	private Date dateEnd;

	public SurveyListGetterParamBean()
	{
		// default constructor
	}

	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	public Date getDateStart()
	{
		return dateStart;
	}

	public Long getFinalBranchBankId()
	{
		return finalBranchBankId;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public Integer getNotified()
	{
		return notified;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	public void setFinalBranchBankId( Long finalBranchBankId )
	{
		this.finalBranchBankId = finalBranchBankId;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotified( Integer notified )
	{
		this.notified = notified;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

}
