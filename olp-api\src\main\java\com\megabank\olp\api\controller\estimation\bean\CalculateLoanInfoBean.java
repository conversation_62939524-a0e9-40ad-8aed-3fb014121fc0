/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class CalculateLoanInfoBean extends BaseBean
{
	@Digits( integer = 11, fraction = 2 )
	private BigDecimal topLoanCredit;

	@Digits( integer = 4, fraction = 2 )
	private BigDecimal rate;

	public BigDecimal getRate()
	{
		return rate;
	}

	public BigDecimal getTopLoanCredit()
	{
		return topLoanCredit;
	}

	public void setRate( BigDecimal rate )
	{
		this.rate = rate;
	}

	public void setTopLoanCredit( BigDecimal topLoanCredit )
	{
		this.topLoanCredit = topLoanCredit;
	}
}
