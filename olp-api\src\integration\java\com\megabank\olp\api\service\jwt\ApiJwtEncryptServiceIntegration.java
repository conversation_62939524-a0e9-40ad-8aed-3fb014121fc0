package com.megabank.olp.api.service.jwt;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.api.service.jwt.bean.ApiJwtEncryptArgBean;
import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApiConfig.class )
public class ApiJwtEncryptServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private ApiJwtEncryptService service;

	@Test
	public void generateJwtToken()
	{
		ApiJwtEncryptArgBean argBean = new ApiJwtEncryptArgBean();

		String jwt = service.generateJwtToken( argBean );

		// logger.info( "jwt:{}", jwt );
	}
}
