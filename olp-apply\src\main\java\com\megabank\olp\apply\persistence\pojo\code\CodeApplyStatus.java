package com.megabank.olp.apply.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeApplyStatus is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_apply_status" )
public class CodeApplyStatus extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_apply_status";

	public static final String APPLY_STATUS_CODE_CONSTANT = "applyStatusCode";

	public static final String NAME_CONSTANT = "name";

	public static final String APPLY_LOANS_CONSTANT = "applyLoans";

	private String applyStatusCode;

	private String name;

	private transient Set<ApplyLoan> applyLoans = new HashSet<>( 0 );

	public CodeApplyStatus()
	{}

	public CodeApplyStatus( String applyStatusCode )
	{
		this.applyStatusCode = applyStatusCode;
	}

	public CodeApplyStatus( String applyStatusCode, String name )
	{
		this.applyStatusCode = applyStatusCode;
		this.name = name;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeApplyStatus" )
	public Set<ApplyLoan> getApplyLoans()
	{
		return applyLoans;
	}

	@Id
	@Column( name = "apply_status_code", unique = true, nullable = false, length = 20 )
	public String getApplyStatusCode()
	{
		return applyStatusCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setApplyLoans( Set<ApplyLoan> applyLoans )
	{
		this.applyLoans = applyLoans;
	}

	public void setApplyStatusCode( String applyStatusCode )
	{
		this.applyStatusCode = applyStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}