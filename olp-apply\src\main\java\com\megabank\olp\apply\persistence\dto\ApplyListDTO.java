package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class ApplyListDTO extends BaseBean
{
	private Long loanId;

	private String caseNo;

	private String name;

	private String loanType;

	private Date applyDate;

	private String branchBank;

	private Boolean discard;

	private String loanPlanCode;

	private Boolean ixmlApprovedFlag;

	public ApplyListDTO()
	{
		// default constructor
	}

	public Date getApplyDate()
	{
		return applyDate;
	}

	public String getBranchBank()
	{
		return branchBank;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Boolean getDiscard()
	{
		return discard;
	}

	public Boolean getIxmlApprovedFlag()
	{
		return ixmlApprovedFlag;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getName()
	{
		return name;
	}

	public void setApplyDate( Date applyDate )
	{
		this.applyDate = applyDate;
	}

	public void setBranchBank( String branchBank )
	{
		this.branchBank = branchBank;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setDiscard( Boolean discard )
	{
		this.discard = discard;
	}

	public void setIxmlApprovedFlag( Boolean ixmlApprovedFlag )
	{
		this.ixmlApprovedFlag = ixmlApprovedFlag;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}
