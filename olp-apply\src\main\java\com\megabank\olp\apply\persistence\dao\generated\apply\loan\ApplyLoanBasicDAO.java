package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicUpdatedApplyCtrParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicUpdatedEngNameParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicUpdatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeEducationLevelDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeMarriageStatusDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeNationalityDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanBasic;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanBasicDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanBasicDAO extends BasePojoDAO<ApplyLoanBasic, Long>
{
	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private CodeMarriageStatusDAO codeMarriageStatusDAO;

	@Autowired
	private CodeEducationLevelDAO codeEducationLevelDAO;

	@Autowired
	private CodeNationalityDAO codeNationalityDAO;

	public Long create( ApplyLoanBasicCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notBlank( paramBean.getIdNo() );
		Validate.notNull( paramBean.getBirthDate() );

		ApplyLoanBasic pojo = new ApplyLoanBasic();
		pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );
		pojo.setIdNo( paramBean.getIdNo() );
		pojo.setName( paramBean.getName() );
		pojo.setEngFirstName( paramBean.getEngFirstName() );
		pojo.setEngLastName( paramBean.getEngLastName() );
		pojo.setBirthDate( paramBean.getBirthDate() );
		pojo.setCodeMarriageStatus( StringUtils
					.isBlank( paramBean.getMarriageStatusCode() ) ? null : codeMarriageStatusDAO.read( paramBean.getMarriageStatusCode() ) );
		pojo.setCodeEducationLevel( StringUtils
					.isBlank( paramBean.getEducationLevelCode() ) ? null : codeEducationLevelDAO.read( paramBean.getEducationLevelCode() ) );
		pojo.setChildrenCount( paramBean.getChildrenCount() );
		pojo.setCodeNationality( StringUtils.isBlank( paramBean.getNationalityCode() ) ? null
																					   : codeNationalityDAO.read( paramBean.getNationalityCode() ) );

		return super.createPojo( pojo );
	}

	public ApplyLoanBasic read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, ApplyLoanBasic.TABLENAME_CONSTANT );
	}

	public ApplyLoanBasic readToNull( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId );
	}

	public Long update( ApplyLoanBasicUpdatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanBasic pojo = read( paramBean.getLoanId() );
		pojo.setName( paramBean.getName() );
		pojo.setEngFirstName( paramBean.getEngFirstName() );
		pojo.setEngLastName( paramBean.getEngLastName() );
		pojo.setCodeMarriageStatus( StringUtils
					.isBlank( paramBean.getMarriageStatusCode() ) ? null : codeMarriageStatusDAO.read( paramBean.getMarriageStatusCode() ) );
		pojo.setCodeEducationLevel( StringUtils
					.isBlank( paramBean.getEducationLevelCode() ) ? null : codeEducationLevelDAO.read( paramBean.getEducationLevelCode() ) );
		pojo.setChildrenCount( paramBean.getChildrenCount() );
		pojo.setCodeNationality( StringUtils.isBlank( paramBean.getNationalityCode() ) ? null
																					   : codeNationalityDAO.read( paramBean.getNationalityCode() ) );

		return pojo.getLoanId();
	}

	public Long updateWhenHasApplyCtr( ApplyLoanBasicUpdatedApplyCtrParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanBasic pojo = read( paramBean.getLoanId() );
		pojo.setNotUsTaxpayer( paramBean.getNotUsTaxpayer() );
		pojo.setNotOuttwTaxpayer( paramBean.getNotOuttwTaxpayer() );
		pojo.setRateAdjNotify( paramBean.getRateAdjNotify() );
		pojo.setCrossMarketing( paramBean.getCrossMarketing() );
		return pojo.getLoanId();
	}

	public Long updateWhenHasEngName( ApplyLoanBasicUpdatedEngNameParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );

		ApplyLoanBasic pojo = read( paramBean.getLoanId() );
		pojo.setEngNameFg( paramBean.getEngNameFg() );
		pojo.setEngName( paramBean.getEngName() );
		return pojo.getLoanId();
	}

	@Override
	protected Class<ApplyLoanBasic> getPojoClass()
	{
		return ApplyLoanBasic.class;
	}
}
