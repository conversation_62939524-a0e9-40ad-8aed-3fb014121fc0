package com.megabank.olp.apply.persistence.pojo.house;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseLoanBasicInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_loan_basic_info" )
public class HouseLoanBasicInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_loan_basic_info";

	public static final String LOAN_TRIAL_INFO_ID_CONSTANT = "loanTrialInfoId";

	public static final String HOUSE_LOAN_TRIAL_INFO_CONSTANT = "houseLoanTrialInfo";

	public static final String USER_AGE_CONSTANT = "userAge";

	public static final String USER_CHILDREN_CONSTANT = "userChildren";

	public static final String JOB_TYPE_CONSTANT = "jobType";

	public static final String TITLE_TYPE_CONSTANT = "titleType";

	public static final String USER_INCOME_CONSTANT = "userIncome";

	public static final String LOAN_PURPOSE_CONSTANT = "loanPurpose";

	public static final String BUY_HOUSE_FROM_CONSTANT = "buyHouseFrom";

	public static final String TOTAL_PRICE_CONSTANT = "totalPrice";

	public static final String CREDITCARD_CONSTANT = "creditcard";

	public static final String PAY_CONSTANT = "pay";

	public static final String BORROW_CONSTANT = "borrow";

	public static final String CASH_CARD_CONSTANT = "cashCard";

	public static final String CASH_CARD_BALANCE_CONSTANT = "cashCardBalance";

	public static final String BALANCE_CONSTANT = "balance";

	private long loanTrialInfoId;

	private transient HouseLoanTrialInfo houseLoanTrialInfo;

	private Integer userAge;

	private Integer userChildren;

	private String jobType;

	private String titleType;

	private Integer userIncome;

	private Integer loanPurpose;

	private Integer buyHouseFrom;

	private BigDecimal totalPrice;

	private Boolean creditcard;

	private Boolean pay;

	private Boolean borrow;

	private Boolean cashCard;

	private Boolean cashCardBalance;

	private Boolean balance;

	public HouseLoanBasicInfo()
	{}

	public HouseLoanBasicInfo( HouseLoanTrialInfo houseLoanTrialInfo )
	{
		this.houseLoanTrialInfo = houseLoanTrialInfo;
	}

	public HouseLoanBasicInfo( Long loanTrialInfoId )
	{
		this.loanTrialInfoId = loanTrialInfoId;
	}

	@Column( name = "balance", precision = 1, scale = 0 )
	public Boolean getBalance()
	{
		return balance;
	}

	@Column( name = "borrow", precision = 1, scale = 0 )
	public Boolean getBorrow()
	{
		return borrow;
	}

	@Column( name = "buy_house_from", precision = 5, scale = 0 )
	public Integer getBuyHouseFrom()
	{
		return buyHouseFrom;
	}

	@Column( name = "cash_card", precision = 1, scale = 0 )
	public Boolean getCashCard()
	{
		return cashCard;
	}

	@Column( name = "cash_card_balance", precision = 1, scale = 0 )
	public Boolean getCashCardBalance()
	{
		return cashCardBalance;
	}

	@Column( name = "creditcard", precision = 1, scale = 0 )
	public Boolean getCreditcard()
	{
		return creditcard;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public HouseLoanTrialInfo getHouseLoanTrialInfo()
	{
		return houseLoanTrialInfo;
	}

	@Column( name = "job_type" )
	public String getJobType()
	{
		return jobType;
	}

	@Column( name = "loan_purpose", precision = 5, scale = 0 )
	public Integer getLoanPurpose()
	{
		return loanPurpose;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "houseLoanTrialInfo" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "loan_trial_info_id", unique = true, nullable = false )
	public long getLoanTrialInfoId()
	{
		return loanTrialInfoId;
	}

	@Column( name = "pay", precision = 1, scale = 0 )
	public Boolean getPay()
	{
		return pay;
	}

	@Column( name = "title_type" )
	public String getTitleType()
	{
		return titleType;
	}

	@Column( name = "total_price", precision = 11 )
	public BigDecimal getTotalPrice()
	{
		return totalPrice;
	}

	@Column( name = "user_age", precision = 5, scale = 0 )
	public Integer getUserAge()
	{
		return userAge;
	}

	@Column( name = "user_children", precision = 5, scale = 0 )
	public Integer getUserChildren()
	{
		return userChildren;
	}

	@Column( name = "user_income", precision = 9, scale = 0 )
	public Integer getUserIncome()
	{
		return userIncome;
	}

	public void setBalance( Boolean balance )
	{
		this.balance = balance;
	}

	public void setBorrow( Boolean borrow )
	{
		this.borrow = borrow;
	}

	public void setBuyHouseFrom( Integer buyHouseFrom )
	{
		this.buyHouseFrom = buyHouseFrom;
	}

	public void setCashCard( Boolean cashCard )
	{
		this.cashCard = cashCard;
	}

	public void setCashCardBalance( Boolean cashCardBalance )
	{
		this.cashCardBalance = cashCardBalance;
	}

	public void setCreditcard( Boolean creditcard )
	{
		this.creditcard = creditcard;
	}

	public void setHouseLoanTrialInfo( HouseLoanTrialInfo houseLoanTrialInfo )
	{
		this.houseLoanTrialInfo = houseLoanTrialInfo;
	}

	public void setJobType( String jobType )
	{
		this.jobType = jobType;
	}

	public void setLoanPurpose( Integer loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setLoanTrialInfoId( long loanTrialInfoId )
	{
		this.loanTrialInfoId = loanTrialInfoId;
	}

	public void setPay( Boolean pay )
	{
		this.pay = pay;
	}

	public void setTitleType( String titleType )
	{
		this.titleType = titleType;
	}

	public void setTotalPrice( BigDecimal totalPrice )
	{
		this.totalPrice = totalPrice;
	}

	public void setUserAge( Integer userAge )
	{
		this.userAge = userAge;
	}

	public void setUserChildren( Integer userChildren )
	{
		this.userChildren = userChildren;
	}

	public void setUserIncome( Integer userIncome )
	{
		this.userIncome = userIncome;
	}
}