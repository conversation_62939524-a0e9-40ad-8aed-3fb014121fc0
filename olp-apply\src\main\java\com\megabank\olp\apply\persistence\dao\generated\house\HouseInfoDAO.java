package com.megabank.olp.apply.persistence.dao.generated.house;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.house.HouseInfoCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeHouseParkingDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeHouseTypeDAO;
import com.megabank.olp.apply.persistence.pojo.house.HouseInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseInfoDAO extends BasePojoDAO<HouseInfo, Long>
{
	@Autowired
	private HousePricingInfoDAO housePricingInfoDAO;

	@Autowired
	private CodeHouseTypeDAO codeHouseTypeDAO;

	@Autowired
	private CodeHouseParkingDAO codeHouseParkingDAO;

	public Long create( HouseInfoCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getHousePricingInfoId() );

		HouseInfo pojo = new HouseInfo();
		pojo.setHousePricingInfo( housePricingInfoDAO.read( paramBean.getHousePricingInfoId() ) );
		pojo.setCounty( paramBean.getCounty() );
		pojo.setDistrict( paramBean.getDistrict() );
		pojo.setAddr( paramBean.getAddr() );
		pojo.setCodeHouseType( paramBean.getHouseType() != null ? codeHouseTypeDAO.read( paramBean.getHouseType() ) : null );
		pojo.setBAge( paramBean.getbAge() );
		pojo.setBAreaP( paramBean.getbArea() );
		pojo.setFloors( paramBean.getFloors() );
		pojo.setLevel( paramBean.getLevel() );
		pojo.setLevelSelect( paramBean.getLevelSelect() );
		pojo.setCodeHouseParking( paramBean.getHouseParkingCode() != null ? codeHouseParkingDAO.read( paramBean.getHouseParkingCode() ) : null );
		pojo.setParkingGty( paramBean.getParkingGty() );
		pojo.setParkingP( paramBean.getParkingP() );

		return super.createPojo( pojo );
	}

	@Override
	protected Class<HouseInfo> getPojoClass()
	{
		return HouseInfo.class;
	}
}
