package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class LoanListDTO extends BaseBean
{
	private Long loanId;

	private String caseNo;

	private String idNo;

	private String name;

	private Date birthDate;

	private String mobileNumber;

	private Date createdDate;

	private String loanPlanCode;

	private String loanPlanName;

	private String transmissionStatusCode;

	private String transmissionStatusName;

	private String processStatus;

	private String branchBank;

	private String notificationStatus;

	private Boolean discard;
	
	private String introduceBrNo;
	
	private String introduceEmpId;
	
	private String introduceBrName;
	
	private String email;

	private String companyName;
	
	private String companyPhoneCode;
	
	private String companyPhoneNumber;
	
	private String companyPhoneExt;

	private String marketingBranchBankName;

	public LoanListDTO()
	{
		// default constructor
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getBranchBank()
	{
		return branchBank;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public Boolean getDiscard()
	{
		return discard;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public String getLoanPlanName()
	{
		return loanPlanName;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatus()
	{
		return notificationStatus;
	}

	public String getProcessStatus()
	{
		return processStatus;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public String getTransmissionStatusName()
	{
		return transmissionStatusName;
	}

	public String getIntroduceBrNo()
	{
		return introduceBrNo;
	}

	public String getIntroduceEmpId()
	{
		return introduceEmpId;
	}

	public String getIntroduceBrName()
	{
		return introduceBrName;
	}

	public String getEmail()
	{
		return email;
	}

	public String getCompanyName()
	{
		return companyName;
	}

	public String getCompanyPhoneCode()
	{
		return companyPhoneCode;
	}

	public String getCompanyPhoneNumber()
	{
		return companyPhoneNumber;
	}

	public String getCompanyPhoneExt()
	{
		return companyPhoneExt;
	}

	public String getMarketingBranchBankName()
	{
		return marketingBranchBankName;
	}

	public void setBirthDate(Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setBranchBank( String branchBank )
	{
		this.branchBank = branchBank;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setDiscard( Boolean discard )
	{
		this.discard = discard;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanPlanName( String loanPlanName )
	{
		this.loanPlanName = loanPlanName;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatus( String notificationStatus )
	{
		this.notificationStatus = notificationStatus;
	}

	public void setProcessStatus( String processStatus )
	{
		this.processStatus = processStatus;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setTransmissionStatusName( String transmissionStatusName )
	{
		this.transmissionStatusName = transmissionStatusName;
	}

	public void setIntroduceBrNo( String introduceBrNo )
	{
		this.introduceBrNo = introduceBrNo;
	}

	public void setIntroduceEmpId( String introduceEmpId )
	{
		this.introduceEmpId = introduceEmpId;
	}

	public void setIntroduceBrName( String introduceBrName )
	{
		this.introduceBrName = introduceBrName;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public void setCompanyPhoneCode( String companyPhoneCode )
	{
		this.companyPhoneCode = companyPhoneCode;
	}

	public void setCompanyPhoneNumber( String companyPhoneNumber )
	{
		this.companyPhoneNumber = companyPhoneNumber;
	}

	public void setCompanyPhoneExt( String companyPhoneExt )
	{
		this.companyPhoneExt = companyPhoneExt;
	}

	public void setMarketingBranchBankName( String marketingBranchBankName )
	{
		this.marketingBranchBankName = marketingBranchBankName;
	}
}