package com.megabank.olp.apply.controller.mydata;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.mydata.bean.DataTransferArgBean;
import com.megabank.olp.apply.controller.mydata.bean.ServletNotifiedArgBean;
import com.megabank.olp.apply.controller.mydata.bean.UserDataGettedArgBean;
import com.megabank.olp.apply.service.mydata.MyDataService;
import com.megabank.olp.apply.service.mydata.bean.MyDataResBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "mydata" )
public class MyDataController extends BaseController
{
	@Autowired
	private MyDataService myDataService;

	/**
	 * 檢查MyData取件狀態
	 * 
	 * @return
	 */
	@PostMapping( "checkMyDataStatus" )
	public Map<String, Object> checkMyDataStatus()
	{
		myDataService.checkMyDataStatus();

		return getResponseMap();
	}

	/**
	 * MyData取件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getUserData" )
	public Map<String, Object> getUserData( @RequestBody @Validated UserDataGettedArgBean argBean )
	{
		myDataService.getUserData( argBean.getTxId() );

		return getResponseMap();

	}

	/**
	 * 取得取件時間通知
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "notifyServlet" )
	public Map<String, Object> notifyServlet( @RequestBody @Validated ServletNotifiedArgBean argBean )
	{
		myDataService.notifyServlet( argBean.getTxId(), argBean.getIdNo(), argBean.getWaitSec() );

		return getResponseMap();

	}

	/**
	 * MEGAPLOAN-194 偉康模式一 資料傳遞API
	 * WebComm資料傳遞API
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "dataTransfer" )
	public Map<String, Object> dataTransfer( @RequestBody @Validated DataTransferArgBean argBean )
	{
		MyDataResBean myDataResBean = new MyDataResBean( 200, "Success" , "" );
		try 
		{
			myDataResBean = myDataService.dataTransfer( argBean );
		} catch( Exception ex )
		{
			myDataResBean.setErrorCode( ex.getMessage() );
			return getResponseMap( myDataResBean );
		}
		return getResponseMap( myDataResBean );
		
	}

}
