package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class CbAfft3ContentBean extends BaseBean
{
	private String cbAfft3_1;

	private String cbAfft3_2;

	private String cbAfft3_3_year;

	private String cbAfft3_3_mth;

	private String cbAfft3_3_day;

	private Integer cbAfft3_4;

	private BigDecimal cbAfft3_5;

	public CbAfft3ContentBean()
	{}

	public String getCbAfft3_1()
	{
		return cbAfft3_1;
	}

	public void setCbAfft3_1( String cbAfft3_1 )
	{
		this.cbAfft3_1 = cbAfft3_1;
	}

	public String getCbAfft3_2()
	{
		return cbAfft3_2;
	}

	public void setCbAfft3_2( String cbAfft3_2 )
	{
		this.cbAfft3_2 = cbAfft3_2;
	}

	public String getCbAfft3_3_year()
	{
		return cbAfft3_3_year;
	}

	public void setCbAfft3_3_year( String cbAfft3_3_year )
	{
		this.cbAfft3_3_year = cbAfft3_3_year;
	}

	public String getCbAfft3_3_mth()
	{
		return cbAfft3_3_mth;
	}

	public void setCbAfft3_3_mth( String cbAfft3_3_mth )
	{
		this.cbAfft3_3_mth = cbAfft3_3_mth;
	}

	public String getCbAfft3_3_day()
	{
		return cbAfft3_3_day;
	}

	public void setCbAfft3_3_day( String cbAfft3_3_day )
	{
		this.cbAfft3_3_day = cbAfft3_3_day;
	}

	public Integer getCbAfft3_4()
	{
		return cbAfft3_4;
	}

	public void setCbAfft3_4( Integer cbAfft3_4 )
	{
		this.cbAfft3_4 = cbAfft3_4;
	}

	public BigDecimal getCbAfft3_5()
	{
		return cbAfft3_5;
	}

	public void setCbAfft3_5( BigDecimal cbAfft3_5 )
	{
		this.cbAfft3_5 = cbAfft3_5;
	}
}
