/**
 *
 */
package com.megabank.olp.api.service.estimation.bean;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class CalculateBasicDataBean extends BaseBean
{
	private Integer userAge;

	private Integer userChildren;

	private String userJob;

	private String userTitle;

	private Integer userIncome;

	private String loanPurpose;

	private String buyHouseFrom;

	private BigDecimal totalPrice;

	private String cr3ditCard;

	private String pay;

	private String borrow;

	private String cashCard;

	private String cashCardBalance;

	private String balance;

	public String getBalance()
	{
		return balance;
	}

	public String getBorrow()
	{
		return borrow;
	}

	public String getBuyHouseFrom()
	{
		return buyHouseFrom;
	}

	public String getCashCard()
	{
		return cashCard;
	}

	public String getCashCardBalance()
	{
		return cashCardBalance;
	}

	public String getCr3ditCard()
	{
		return cr3ditCard;
	}

	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public String getPay()
	{
		return pay;
	}

	public BigDecimal getTotalPrice()
	{
		return totalPrice;
	}

	public Integer getUserAge()
	{
		return userAge;
	}

	public Integer getUserChildren()
	{
		return userChildren;
	}

	public Integer getUserIncome()
	{
		return userIncome;
	}

	public String getUserJob()
	{
		return userJob;
	}

	public String getUserTitle()
	{
		return userTitle;
	}

	public void setBalance( String balance )
	{
		this.balance = balance;
	}

	public void setBorrow( String borrow )
	{
		this.borrow = borrow;
	}

	public void setBuyHouseFrom( String buyHouseFrom )
	{
		this.buyHouseFrom = buyHouseFrom;
	}

	public void setCashCard( String cashCard )
	{
		this.cashCard = cashCard;
	}

	public void setCashCardBalance( String cashCardBalance )
	{
		this.cashCardBalance = cashCardBalance;
	}

	public void setCr3ditCard( String cr3ditCard )
	{
		this.cr3ditCard = cr3ditCard;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setPay( String pay )
	{
		this.pay = pay;
	}

	public void setTotalPrice( BigDecimal totalPrice )
	{
		this.totalPrice = totalPrice;
	}

	public void setUserAge( Integer userAge )
	{
		this.userAge = userAge;
	}

	public void setUserChildren( Integer userChildren )
	{
		this.userChildren = userChildren;
	}

	public void setUserIncome( Integer userIncome )
	{
		this.userIncome = userIncome;
	}

	public void setUserJob( String userJob )
	{
		this.userJob = userJob;
	}

	public void setUserTitle( String userTitle )
	{
		this.userTitle = userTitle;
	}
}
