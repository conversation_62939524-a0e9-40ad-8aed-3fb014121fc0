package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.mixed.AttachmentListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.AttachmentListDTO;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.bean.threadlocal.PagingThreadLocalBean;
import com.megabank.olp.base.threadlocal.PagingThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class AttachmentDAOIntegration
{
	@Autowired
	private PagingThreadLocal pagingThreadLocal;
	
	@Autowired
	private AttachmentDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getList()
	{
		String loanType = "personalloan";

		AttachmentListGetterParamBean paramBean = new AttachmentListGetterParamBean();
		paramBean.setLoanType( loanType );

		List<AttachmentListDTO> result = dao.getList( paramBean );

		logger.info( "result:{}", result );
		logger.info( "size:{}", result.size() );
	}

	@Test
	public void getPaging()
	{
		setPagingThreadLocalBean();

		String loanType = "personalloan";

		AttachmentListGetterParamBean paramBean = new AttachmentListGetterParamBean();
		paramBean.setLoanType( loanType );

		PagingBean<AttachmentListDTO> result = dao.getPaging( paramBean );

		logger.info( "data:{}", result.getData() );
		logger.info( "recordsFiltered:{}", result.getRecordsFiltered() );
		logger.info( "recordsTotal:{}", result.getRecordsTotal() );

	}

	private AttachmentListGetterParamBean getAttachmentListGetterParamBean()
	{
		String caseNo = "123456";
		String loanType = "personalloan";
		String transmissionStatus = "no";
		String idNo = "A123456789";
		String mobileNumber = "0912345678";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		Date dateStart = CommonDateUtils.getDate( 2020, 1, 1 );
		Date dateEnd = CommonDateUtils.getDate( 2020, 12, 31 );

		AttachmentListGetterParamBean paramBean = new AttachmentListGetterParamBean();
		paramBean.setTransmissionStatusCode( transmissionStatus );
		paramBean.setDateStart( dateStart );
		paramBean.setDateEnd( dateEnd );
		paramBean.setIdNo( idNo );
		paramBean.setBirthDate( birthDate );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setLoanType( loanType );
		paramBean.setCaseNo( caseNo );

		return paramBean;
	}

	private void setPagingThreadLocalBean()
	{
		PagingThreadLocalBean localBean = new PagingThreadLocalBean();
		localBean.setStart( 0 );
		localBean.setLength( 10 );
		localBean.setSortColumn( "createdDate" );
		localBean.setSortDirection( "asc" );

		pagingThreadLocal.set( localBean );
	}

}
