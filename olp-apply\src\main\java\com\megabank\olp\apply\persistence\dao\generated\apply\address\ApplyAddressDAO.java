package com.megabank.olp.apply.persistence.dao.generated.apply.address;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.address.ApplyAddressParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTownDAO;
import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddress;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyAddressDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyAddressDAO extends BasePojoDAO<ApplyAddress, Long>
{
	@Autowired
	private CodeTownDAO codeTownDAO;

	public Long create( ApplyAddressParamBean paramBean )
	{
		Validate.notBlank( paramBean.getTownCode() );
		Validate.notBlank( paramBean.getStreet() );
		Validate.notBlank( paramBean.getNo() );

		ApplyAddress pojo = new ApplyAddress();
		pojo.setCodeTown( codeTownDAO.read( paramBean.getTownCode() ) );
		pojo.setVillage( paramBean.getVillage() );
		pojo.setNeighborhood( paramBean.getNeighborhood() );
		pojo.setStreet( paramBean.getStreet() );
		pojo.setSection( paramBean.getSection() );
		pojo.setLane( paramBean.getLane() );
		pojo.setAlley( paramBean.getAlley() );
		pojo.setNo( paramBean.getNo() );
		pojo.setFloor( paramBean.getFloor() );
		pojo.setRoom( paramBean.getRoom() );

		return super.createPojo( pojo );
	}

	public ApplyAddress read( Long addressId )
	{
		Validate.notNull( addressId );

		return getPojoByPK( addressId, ApplyAddress.TABLENAME_CONSTANT );
	}

	public Long update( Long addressId, ApplyAddressParamBean paramBean )
	{
		Validate.notNull( addressId );
		Validate.notBlank( paramBean.getTownCode() );
		Validate.notBlank( paramBean.getStreet() );
		Validate.notBlank( paramBean.getNo() );

		ApplyAddress pojo = read( addressId );
		pojo.setCodeTown( codeTownDAO.read( paramBean.getTownCode() ) );
		pojo.setVillage( paramBean.getVillage() );
		pojo.setNeighborhood( paramBean.getNeighborhood() );
		pojo.setStreet( paramBean.getStreet() );
		pojo.setSection( paramBean.getSection() );
		pojo.setLane( paramBean.getLane() );
		pojo.setAlley( paramBean.getAlley() );
		pojo.setNo( paramBean.getNo() );
		pojo.setFloor( paramBean.getFloor() );
		pojo.setRoom( paramBean.getRoom() );

		return pojo.getAddressId();
	}

	@Override
	protected Class<ApplyAddress> getPojoClass()
	{
		return ApplyAddress.class;
	}
}
