package com.megabank.olp.apply.controller.loan.bean.apply;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class LoanApplyCheckedArgBean extends BaseBean
{
	@NotBlank
	private String caseNo;

	@NotBlank
	@JsonProperty( "borrowerName" )
	private String name;

	@NotBlank
	@JsonProperty( "borrowerIdNo" )
	private String idNo;

	@NotBlank
	@JsonProperty( "borrowerMobileNumber" )
	private String mobileNumber;

	@NotBlank
	private String loanType;

	public LoanApplyCheckedArgBean()
	{
		// default constructor
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

}
