package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeNationality;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeNationalityDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeNationalityDAO extends BasePojoDAO<CodeNationality, String>
{
	public CodeNationality read( String nationalityCode )
	{
		Validate.notBlank( nationalityCode );

		return getPojoByPK( nationalityCode, CodeNationality.TABLENAME_CONSTANT );
	}

	public CodeNationality readToNull( String nationalityCode )
	{
		Validate.notBlank( nationalityCode );

		return getPojoByPK( nationalityCode );
	}

	@Override
	protected Class<CodeNationality> getPojoClass()
	{
		return CodeNationality.class;
	}
}
