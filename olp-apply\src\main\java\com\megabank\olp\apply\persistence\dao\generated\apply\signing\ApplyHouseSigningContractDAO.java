package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import com.megabank.olp.apply.persistence.bean.generated.apply.signing.HouseSigningContractCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyHouseSigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class ApplyHouseSigningContractDAO extends BasePojoDAO<ApplyHouseSigningContract, Long>
{
	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Override
	protected Class<ApplyHouseSigningContract> getPojoClass()
	{
		return ApplyHouseSigningContract.class;
	}

	public Long create( Long contractId, HouseSigningContractCreatedParamBean paramBean )
	{
		ApplyHouseSigningContract pojo = new ApplyHouseSigningContract();
		pojo.setApplySigningContract( applySigningContractDAO.read( contractId ) );
		pojo.setGuaranteeType( paramBean.getGuaranteeType() );
		pojo.setWitness( paramBean.getWitness() );
		pojo.setPayeeInfoType( paramBean.getPayeeInfoType() );
		pojo.setPayeeInfoAccountType( paramBean.getPayeeInfoAccountType() );
		pojo.setExpireInfoType( paramBean.getExpireInfoType() );
		pojo.setStartYear1( paramBean.getStartYear1() );
		pojo.setStartMonth1( paramBean.getStartMonth1() );
		pojo.setStartDay1( paramBean.getStartDay1() );
		pojo.setExpireYear1( paramBean.getExpireYear1() );
		pojo.setExpireMonth1( paramBean.getExpireMonth1() );
		pojo.setExpireDay1( paramBean.getExpireDay1() );
		pojo.setDuration1( paramBean.getDuration1() );
		pojo.setStartYear2( paramBean.getStartYear2() );
		pojo.setStartMonth2( paramBean.getStartMonth2() );
		pojo.setStartDay2( paramBean.getStartDay2() );
		pojo.setExpireYear2( paramBean.getExpireYear2() );
		pojo.setExpireMonth2( paramBean.getExpireMonth2() );
		pojo.setExpireDay2( paramBean.getExpireDay2() );
		pojo.setDuration2( paramBean.getDuration2() );
		pojo.setStartYear3( paramBean.getStartYear3() );
		pojo.setStartMonth3( paramBean.getStartMonth3() );
		pojo.setStartDay3( paramBean.getStartDay3() );
		pojo.setExpireYear3( paramBean.getExpireYear3() );
		pojo.setExpireMonth3( paramBean.getExpireMonth3() );
		pojo.setExpireDay3( paramBean.getExpireDay3() );
		pojo.setDuration3( paramBean.getDuration3() );
		pojo.setDuration4( paramBean.getDuration4() );
		pojo.setMaxYear4( paramBean.getMaxYear4() );
		pojo.setExpireYear4( paramBean.getExpireYear4() );
		pojo.setExpireMonth4( paramBean.getExpireMonth4() );
		pojo.setExpireDay4( paramBean.getExpireDay4() );
		pojo.setOther5( paramBean.getOther5() );
		pojo.setRepaymentInfoType( paramBean.getRepaymentInfoType() );
		pojo.setLimtedYear4( paramBean.getLimtedYear4() );
		pojo.setLimtedMonth4( paramBean.getLimtedMonth4() );
		pojo.setYear4( paramBean.getYear4() );
		pojo.setMonth4( paramBean.getMonth4() );
		pojo.setLimtedYear5( paramBean.getLimtedYear5() );
		pojo.setLimtedMonth5( paramBean.getLimtedMonth5() );
		pojo.setYear5( paramBean.getYear5() );
		pojo.setMonth5( paramBean.getMonth5() );
		pojo.setPeriod6( paramBean.getPeriod6() );
		pojo.setYear6( paramBean.getYear6() );
		pojo.setOther7( paramBean.getOther7() );
		pojo.setHouseRedemption( paramBean.getHouseRedemption() );
		pojo.setFirstRate( paramBean.getFirstRate() );
		pojo.setSecondRate( paramBean.getSecondRate() );
		pojo.setInterestInfoType( paramBean.getInterestInfoType() );
		pojo.setFormula1( paramBean.getFormula1() );
		pojo.setFormula2( paramBean.getFormula2() );
		pojo.setFirstPeriodFrom1( paramBean.getFirstPeriodFrom1() );
		pojo.setFirstPeriodTo1( paramBean.getFirstPeriodTo1() );
		pojo.setFirstPeriodRate1( paramBean.getFirstPeriodRate1() );
		pojo.setSecondPeriodFrom1( paramBean.getSecondPeriodFrom1() );
		pojo.setSecondPeriodTo1( paramBean.getSecondPeriodTo1() );
		pojo.setSecondPeriodRate1( paramBean.getSecondPeriodRate1() );
		pojo.setThirdPeriodFrom1( paramBean.getThirdPeriodFrom1() );
		pojo.setThirdPeriodTo1( paramBean.getThirdPeriodTo1() );
		pojo.setThirdPeriodRate1( paramBean.getThirdPeriodRate1() );
		pojo.setRate1( paramBean.getRate1() );
		pojo.setOther1( paramBean.getOther1() );
		pojo.setFormula2( paramBean.getFormula2() );
		pojo.setRate2_1( paramBean.getRate2_1() );
		pojo.setRate2_2( paramBean.getRate2_2() );
		pojo.setRate2_3_1( paramBean.getRate2_3_1() );
		pojo.setRate2_3_2( paramBean.getRate2_3_2() );
		pojo.setOther2( paramBean.getOther2() );
		pojo.setOther3( paramBean.getOther3() );
		pojo.setBrNoTel( paramBean.getBrNoTel() );
		pojo.setBrNoFax( paramBean.getBrNoFax() );

		return super.createPojo( pojo );
	}

	public ApplyHouseSigningContract getPojoBySigningContractId(Long signingContractId )
	{
		Validate.notNull( signingContractId );

		NameValueBean condition = new NameValueBean( ApplyHouseSigningContract.SIGNING_CONTRACT_ID_CONSTANT, signingContractId );

		return getUniquePojoByProperty( condition, ApplyHouseSigningContract.TABLENAME_CONSTANT );
	}
}
