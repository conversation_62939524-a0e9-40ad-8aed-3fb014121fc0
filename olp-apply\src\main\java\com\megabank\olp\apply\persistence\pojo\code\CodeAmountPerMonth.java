package com.megabank.olp.apply.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanOccupation;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeAmountPerMonth is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_amount_per_month" )
public class CodeAmountPerMonth extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_amount_per_month";

	public static final String AMOUNT_PER_MONTH_CODE_CONSTANT = "amountPerMonthCode";

	public static final String NAME_CONSTANT = "name";

	public static final String APPLY_LOAN_OCCUPATIONS_CONSTANT = "applyLoanOccupations";

	private String amountPerMonthCode;

	private String name;

	private transient Set<ApplyLoanOccupation> applyLoanOccupations = new HashSet<>( 0 );

	public CodeAmountPerMonth()
	{}

	public CodeAmountPerMonth( String amountPerMonthCode )
	{
		this.amountPerMonthCode = amountPerMonthCode;
	}

	public CodeAmountPerMonth( String amountPerMonthCode, String name )
	{
		this.amountPerMonthCode = amountPerMonthCode;
		this.name = name;
	}

	@Id
	@Column( name = "amount_per_month_code", unique = true, nullable = false, length = 20 )
	public String getAmountPerMonthCode()
	{
		return amountPerMonthCode;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeAmountPerMonth" )
	public Set<ApplyLoanOccupation> getApplyLoanOccupations()
	{
		return applyLoanOccupations;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setAmountPerMonthCode( String amountPerMonthCode )
	{
		this.amountPerMonthCode = amountPerMonthCode;
	}

	public void setApplyLoanOccupations( Set<ApplyLoanOccupation> applyLoanOccupations )
	{
		this.applyLoanOccupations = applyLoanOccupations;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}