package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBankLoan;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeBranchBankLoanDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeBranchBankLoanDAO extends BasePojoDAO<CodeBranchBankLoan, Long>
{
	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeLoanTypeDAO codeLoanTypeDAO;

	public CodeBranchBankLoan getPojoByLoanType( Long branchBankId, String loanType )
	{
		Validate.notNull( branchBankId );

		NameValueBean codeBranchBank = new NameValueBean( CodeBranchBankLoan.CODE_BRANCH_BANK_CONSTANT, codeBranchBankDAO.read( branchBankId ) );
		NameValueBean codeLoanType = new NameValueBean( CodeBranchBankLoan.CODE_LOAN_TYPE_CONSTANT, codeLoanTypeDAO.read( loanType ) );
		NameValueBean[] conditions = new NameValueBean[]{ codeBranchBank, codeLoanType };

		return getUniquePojoByProperties( conditions );
	}

	public CodeBranchBankLoan read( Long branchBankLoanId )
	{
		Validate.notNull( branchBankLoanId );

		return getPojoByPK( branchBankLoanId, CodeBranchBankLoan.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeBranchBankLoan> getPojoClass()
	{
		return CodeBranchBankLoan.class;
	}
}
