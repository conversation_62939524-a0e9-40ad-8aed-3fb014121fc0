/**
 *
 */
package com.megabank.olp.api.service.sso;

import org.springframework.stereotype.Service;

import com.megabank.olp.api.service.sso.bean.SsoRedirectInfoResBean;
import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.ServiceTypeEnum;
import com.megabank.olp.base.enums.UserTypeEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
public class SsoService extends BaseApiService
{

	public SsoRedirectInfoResBean getRedirectInfo( String redirectService, String redirectUser )
	{
		String loanType = LoanTypeEnum.PERSONAL_LOAN.getContext();
		String serviceType = ServiceTypeEnum.PERSONAL_LOAN.getContext();
		String userType = UserTypeEnum.BORROWER.getContext();

		SsoRedirectInfoResBean resBean = new SsoRedirectInfoResBean();

		if( "hloanapply".equals( redirectService ) )
		{
			loanType = LoanTypeEnum.HOUSE_LOAN.getContext();
			serviceType = ServiceTypeEnum.HOUSE_LOAN.getContext();
		}

		if( UserTypeEnum.GUARANTOR.getContext().equals( redirectUser ) || UserTypeEnum.PROVIDER.getContext().equals( redirectUser ) )
			userType = redirectUser;

		resBean.setLoanType( loanType );
		resBean.setServiceType( serviceType );
		resBean.setUserType( userType );

		return resBean;

	}

}
