/**
 *
 */
package com.megabank.olp.api.service.eloan.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class GuaranteeDataBean extends BaseBean
{
	private String generalGuaranteePlan;

	private List<String> generalGuaranteePlanInfo = new ArrayList<>();

	private String jointGuaranteePlan;

	private List<String> jointGuaranteePlanInfo = new ArrayList<>();

	private BigDecimal guaranteeAmt;

	public GuaranteeDataBean()
	{}

	public String getGeneralGuaranteePlan()
	{
		return generalGuaranteePlan;
	}

	public List<String> getGeneralGuaranteePlanInfo()
	{
		return generalGuaranteePlanInfo;
	}

	public BigDecimal getGuaranteeAmt()
	{
		return guaranteeAmt;
	}

	public String getJointGuaranteePlan()
	{
		return jointGuaranteePlan;
	}

	public List<String> getJointGuaranteePlanInfo()
	{
		return jointGuaranteePlanInfo;
	}

	public void setGeneralGuaranteePlan( String generalGuaranteePlan )
	{
		this.generalGuaranteePlan = generalGuaranteePlan;
	}

	public void setGeneralGuaranteePlanInfo( List<String> generalGuaranteePlanInfo )
	{
		this.generalGuaranteePlanInfo = generalGuaranteePlanInfo;
	}

	public void setGuaranteeAmt( BigDecimal guaranteeAmt )
	{
		this.guaranteeAmt = guaranteeAmt;
	}

	public void setJointGuaranteePlan( String jointGuaranteePlan )
	{
		this.jointGuaranteePlan = jointGuaranteePlan;
	}

	public void setJointGuaranteePlanInfo( List<String> jointGuaranteePlanInfo )
	{
		this.jointGuaranteePlanInfo = jointGuaranteePlanInfo;
	}

}
