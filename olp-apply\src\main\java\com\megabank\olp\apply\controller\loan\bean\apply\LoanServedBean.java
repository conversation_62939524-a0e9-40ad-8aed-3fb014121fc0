package com.megabank.olp.apply.controller.loan.bean.apply;

import com.megabank.olp.base.bean.BaseBean;

public class LoanServedBean extends BaseBean
{
	private String companyName;

	private String taxNo;

	private String servedTitle;

	private String comment;

	private String representativeType;

	public LoanServedBean()
	{}

	public String getComment()
	{
		return comment;
	}

	public String getCompanyName()
	{
		return companyName;
	}

	public String getRepresentativeType()
	{
		return representativeType;
	}

	public String getServedTitle()
	{
		return servedTitle;
	}

	public String getTaxNo()
	{
		return taxNo;
	}

	public void setComment( String comment )
	{
		this.comment = comment;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public void setRepresentativeType( String representativeType )
	{
		this.representativeType = representativeType;
	}

	public void setServedTitle( String servedTitle )
	{
		this.servedTitle = servedTitle;
	}

	public void setTaxNo( String taxNo )
	{
		this.taxNo = taxNo;
	}
}
