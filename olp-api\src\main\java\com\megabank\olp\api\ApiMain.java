package com.megabank.olp.api;

import java.util.TimeZone;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.Import;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.api.config.ApiSecurityConfig;
import com.megabank.olp.base.BaseMain;
import com.megabank.olp.base.config.BaseMicroWebConfig;
import com.megabank.olp.system.config.SystemConfig;

@EnableAutoConfiguration( exclude = { HibernateJpaAutoConfiguration.class } )
@Import( { ApiConfig.class, BaseMicroWebConfig.class, ApiSecurityConfig.class, SystemConfig.class } )
public class ApiMain extends BaseMain
{
	public static void main( String[] args )
	{
		TimeZone.setDefault( TimeZone.getTimeZone( "Asia/Taipei" ) );

		SpringApplication.run( ApiMain.class, args );
	}

	@Override
	protected String[] getOtherKyes()
	{
		return env.getProperty( "print.properties.api" ).split( "," );
	}
}
