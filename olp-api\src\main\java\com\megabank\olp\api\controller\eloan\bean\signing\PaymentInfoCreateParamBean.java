package com.megabank.olp.api.controller.eloan.bean.signing;

import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

public class PaymentInfoCreateParamBean extends BaseBean
{
	private String contractNo;

	private Integer preliminaryFee;

	private Integer crChkFee;

	private List<PaymentInfoBean> paymentInfoList;

	public PaymentInfoCreateParamBean()
	{}

	public String getContractNo()
	{
		return contractNo;
	}

	public Integer getCrChkFee()
	{
		return crChkFee;
	}

	public List<PaymentInfoBean> getPaymentInfoList()
	{
		return paymentInfoList;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setCrChkFee( Integer crChkFee )
	{
		this.crChkFee = crChkFee;
	}

	public void setPaymentInfoList( List<PaymentInfoBean> paymentInfoList )
	{
		this.paymentInfoList = paymentInfoList;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

}
