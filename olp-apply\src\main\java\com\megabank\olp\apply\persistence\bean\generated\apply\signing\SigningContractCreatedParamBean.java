/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import java.math.BigDecimal;
import java.util.Date;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractCreatedParamBean extends BaseBean
{
	private String branchBankCode;

	private String contractNo;

	private String contractVersion;

	private String courtName;

	private String productCode;

	private String loanType;

	private Date expiredDate;

	private Integer loanAmt;

	private Integer loanPeriod;

	private String loanPurpose;

	private String drawDownType;

	private Integer oneTimeFee;

	private Integer preliminaryFee;

	private Integer creditCheckFee;

	private BigDecimal renewFee;

	private BigDecimal changeFee;

	private BigDecimal certFee;

	private BigDecimal reissueFee;

	private String repaymentMethod;

	private String lendingPlan;

	private String advancedRedemptionTitle;

	private String advancedRedemptionDesc;

	private String advancedRateTitle;

	private String advancedRateDesc;

	private BigDecimal advancedApr;

	private String limitedRedemptionTitle;

	private String limitedRedemptionDesc;

	private String limitedRateTitle;

	private String limitedRateDesc;

	private BigDecimal limitedApr;

	private Integer showOption;

	private String otherInfoTitle;

	private String otherInfoDesc;

	private String generalGuaranteePlan;

	private String generalGuaranteePlanInfo;

	private String jointGuaranteePlan;

	private String jointGuaranteePlanInfo;

	private BigDecimal guaranteeAmt;

	private String signingContractType;

	private String loanPlan;

	private String grpCntrNo;

	private Date givenApprBegDate;

	private Date givenApprEndDate;

	private String payeeBankCode;

	private String payeeBankAccountNo;

	private String payeeBankAccountName;

	private Integer payeeTotalAmt;

	private Integer payeeRemittance;

	private Integer payeeSelfProvide;

	private BigDecimal baseRate;

	private Boolean isRepayment;

	private Boolean staffRule;

	private Integer contractRecipientId;

	private String prodKind;

	private String lnDate;

	public SigningContractCreatedParamBean()
	{
	}

	public BigDecimal getAdvancedApr()
	{
		return advancedApr;
	}

	public String getAdvancedRateDesc()
	{
		return advancedRateDesc;
	}

	public String getAdvancedRateTitle()
	{
		return advancedRateTitle;
	}

	public String getAdvancedRedemptionDesc()
	{
		return advancedRedemptionDesc;
	}

	public String getAdvancedRedemptionTitle()
	{
		return advancedRedemptionTitle;
	}

	public BigDecimal getBaseRate()
	{
		return baseRate;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getContractVersion()
	{
		return contractVersion;
	}

	public String getCourtName()
	{
		return courtName;
	}

	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	public BigDecimal getRenewFee()
	{
		return renewFee;
	}

	public BigDecimal getChangeFee()
	{
		return changeFee;
	}

	public BigDecimal getCertFee()
	{
		return certFee;
	}

	public BigDecimal getReissueFee()
	{
		return reissueFee;
	}

	public String getDrawDownType()
	{
		return drawDownType;
	}

	public Date getExpiredDate()
	{
		return expiredDate;
	}

	public String getGeneralGuaranteePlan()
	{
		return generalGuaranteePlan;
	}

	public String getGeneralGuaranteePlanInfo()
	{
		return generalGuaranteePlanInfo;
	}

	public Date getGivenApprBegDate()
	{
		return givenApprBegDate;
	}

	public Date getGivenApprEndDate()
	{
		return givenApprEndDate;
	}

	public String getGrpCntrNo()
	{
		return grpCntrNo;
	}

	public BigDecimal getGuaranteeAmt()
	{
		return guaranteeAmt;
	}

	public Boolean getIsRepayment()
	{
		return isRepayment;
	}

	public String getJointGuaranteePlan()
	{
		return jointGuaranteePlan;
	}

	public String getJointGuaranteePlanInfo()
	{
		return jointGuaranteePlanInfo;
	}

	public String getLendingPlan()
	{
		return lendingPlan;
	}

	public BigDecimal getLimitedApr()
	{
		return limitedApr;
	}

	public String getLimitedRateDesc()
	{
		return limitedRateDesc;
	}

	public String getLimitedRateTitle()
	{
		return limitedRateTitle;
	}

	public String getLimitedRedemptionDesc()
	{
		return limitedRedemptionDesc;
	}

	public String getLimitedRedemptionTitle()
	{
		return limitedRedemptionTitle;
	}

	public Integer getLoanAmt()
	{
		return loanAmt;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public String getLoanPlan()
	{
		return loanPlan;
	}

	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	public String getOtherInfoDesc()
	{
		return otherInfoDesc;
	}

	public String getOtherInfoTitle()
	{
		return otherInfoTitle;
	}

	public String getPayeeBankAccountName()
	{
		return payeeBankAccountName;
	}

	public String getPayeeBankAccountNo()
	{
		return payeeBankAccountNo;
	}

	public String getPayeeBankCode()
	{
		return payeeBankCode;
	}

	public Integer getPayeeRemittance()
	{
		return payeeRemittance;
	}

	public Integer getPayeeSelfProvide()
	{
		return payeeSelfProvide;
	}

	public Integer getPayeeTotalAmt()
	{
		return payeeTotalAmt;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public String getProductCode()
	{
		return productCode;
	}

	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	public Integer getShowOption()
	{
		return showOption;
	}

	public String getSigningContractType()
	{
		return signingContractType;
	}

	public Boolean getStaffRule()
	{
		return staffRule;
	}

	public Integer getContractRecipientId()
	{
		return contractRecipientId;
	}

	public String getProdKind()
	{
		return prodKind;
	}

	public String getLnDate()
	{
		return lnDate;
	}

	public void setAdvancedApr(BigDecimal advancedApr )
	{
		this.advancedApr = advancedApr;
	}

	public void setAdvancedRateDesc( String advancedRateDesc )
	{
		this.advancedRateDesc = advancedRateDesc;
	}

	public void setAdvancedRateTitle( String advancedRateTitle )
	{
		this.advancedRateTitle = advancedRateTitle;
	}

	public void setAdvancedRedemptionDesc( String advancedRedemptionDesc )
	{
		this.advancedRedemptionDesc = advancedRedemptionDesc;
	}

	public void setAdvancedRedemptionTitle( String advancedRedemptionTitle )
	{
		this.advancedRedemptionTitle = advancedRedemptionTitle;
	}

	public void setBaseRate( BigDecimal baseRate )
	{
		this.baseRate = baseRate;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setContractVersion( String contractVersion )
	{
		this.contractVersion = contractVersion;
	}

	public void setCourtName( String courtName )
	{
		this.courtName = courtName;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public void setRenewFee( BigDecimal renewFee )
	{
		this.renewFee = renewFee;
	}

	public void setChangeFee( BigDecimal changeFee )
	{
		this.changeFee = changeFee;
	}

	public void setCertFee( BigDecimal certFee )
	{
		this.certFee = certFee;
	}

	public void setReissueFee( BigDecimal reissueFee )
	{
		this.reissueFee = reissueFee;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public void setExpiredDate( Date expiredDate )
	{
		this.expiredDate = expiredDate;
	}

	public void setGeneralGuaranteePlan( String generalGuaranteePlan )
	{
		this.generalGuaranteePlan = generalGuaranteePlan;
	}

	public void setGeneralGuaranteePlanInfo( String generalGuaranteePlanInfo )
	{
		this.generalGuaranteePlanInfo = generalGuaranteePlanInfo;
	}

	public void setGivenApprBegDate( Date givenApprBegDate )
	{
		this.givenApprBegDate = givenApprBegDate;
	}

	public void setGivenApprEndDate( Date givenApprEndDate )
	{
		this.givenApprEndDate = givenApprEndDate;
	}

	public void setGrpCntrNo( String grpCntrNo )
	{
		this.grpCntrNo = grpCntrNo;
	}

	public void setGuaranteeAmt( BigDecimal guaranteeAmt )
	{
		this.guaranteeAmt = guaranteeAmt;
	}

	public void setIsRepayment( Boolean isRepayment )
	{
		this.isRepayment = isRepayment;
	}

	public void setJointGuaranteePlan( String jointGuaranteePlan )
	{
		this.jointGuaranteePlan = jointGuaranteePlan;
	}

	public void setJointGuaranteePlanInfo( String jointGuaranteePlanInfo )
	{
		this.jointGuaranteePlanInfo = jointGuaranteePlanInfo;
	}

	public void setLendingPlan( String lendingPlan )
	{
		this.lendingPlan = lendingPlan;
	}

	public void setLimitedApr( BigDecimal limitedApr )
	{
		this.limitedApr = limitedApr;
	}

	public void setLimitedRateDesc( String limitedRateDesc )
	{
		this.limitedRateDesc = limitedRateDesc;
	}

	public void setLimitedRateTitle( String limitedRateTitle )
	{
		this.limitedRateTitle = limitedRateTitle;
	}

	public void setLimitedRedemptionDesc( String limitedRedemptionDesc )
	{
		this.limitedRedemptionDesc = limitedRedemptionDesc;
	}

	public void setLimitedRedemptionTitle( String limitedRedemptionTitle )
	{
		this.limitedRedemptionTitle = limitedRedemptionTitle;
	}

	public void setLoanAmt( Integer loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanPlan( String loanPlan )
	{
		this.loanPlan = loanPlan;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public void setOtherInfoDesc( String otherInfoDesc )
	{
		this.otherInfoDesc = otherInfoDesc;
	}

	public void setOtherInfoTitle( String otherInfoTitle )
	{
		this.otherInfoTitle = otherInfoTitle;
	}

	public void setPayeeBankAccountName( String payeeBankAccountName )
	{
		this.payeeBankAccountName = payeeBankAccountName;
	}

	public void setPayeeBankAccountNo( String payeeBankAccountNo )
	{
		this.payeeBankAccountNo = payeeBankAccountNo;
	}

	public void setPayeeBankCode( String payeeBankCode )
	{
		this.payeeBankCode = payeeBankCode;
	}

	public void setPayeeRemittance( Integer payeeRemittance )
	{
		this.payeeRemittance = payeeRemittance;
	}

	public void setPayeeSelfProvide( Integer payeeSelfProvide )
	{
		this.payeeSelfProvide = payeeSelfProvide;
	}

	public void setPayeeTotalAmt( Integer payeeTotalAmt )
	{
		this.payeeTotalAmt = payeeTotalAmt;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public void setProductCode( String productCode )
	{
		this.productCode = productCode;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}

	public void setShowOption( Integer showOption )
	{
		this.showOption = showOption;
	}

	public void setSigningContractType( String signingContractType )
	{
		this.signingContractType = signingContractType;
	}

	public void setStaffRule( Boolean staffRule )
	{
		this.staffRule = staffRule;
	}

	public void setContractRecipientId( Integer contractRecipientId )
	{
		this.contractRecipientId = contractRecipientId;
	}

	public void setProdKind( String prodKind )
	{
		this.prodKind = prodKind;
	}

	public void setLnDate( String lnDate )
	{
		this.lnDate = lnDate;
	}
}
