/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.house;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class ContactCreatedParamBean extends BaseBean
{
	private String caseNo;

	private String mobileNumber;

	private String branchBankCode;

	private Date createdDate;

	private String processCode;

	private String otherMsg;

	private String loanPlanCode;

	private Long branchBankId;

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public void setBranchBankCode(String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}
}
