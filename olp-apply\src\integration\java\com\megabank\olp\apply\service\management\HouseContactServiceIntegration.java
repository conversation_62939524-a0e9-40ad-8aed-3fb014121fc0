package com.megabank.olp.apply.service.management;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.download.FileDownloadedResBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactBasicDataBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactDetailResBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactExportedParamBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactListedParamBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactListedResBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactLoanDataBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactSendCaseParamBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HouseContactServiceIntegration
{
	@Autowired
	private HouseContactService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long result = service.create( getContactSendCaseParamBean() );

		logger.info( "result:{}", result );
	}

	@Test
	public void exportList()
	{
		HouseContactExportedParamBean paramBean = new HouseContactExportedParamBean();

		FileDownloadedResBean resBean = service.exportList( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getDetail()
	{
		Long houseContactId = 1L;

		HouseContactDetailResBean resBean = service.getDetail( houseContactId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listContactMe()
	{
		HouseContactListedParamBean paramBean = new HouseContactListedParamBean();
		paramBean.setPage( 1 );

		HouseContactListedResBean resBean = service.listContactMe( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void updateProcessStatus()
	{
		Long contactMeId = 1L;
		String processCode = "processing";
		String employeeId = "developer-01";
		String employeeName = "developer";

		Long id = service.updateProcessStatus( contactMeId, processCode, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	private HouseContactSendCaseParamBean getContactSendCaseParamBean()
	{
		HouseContactSendCaseParamBean paramBean = new HouseContactSendCaseParamBean();
		paramBean.setCaseNo( "HoCoTest001" );
		paramBean.setCreatedDate( new Date() );
		paramBean.setMobileNumber( "**********" );
		paramBean.setBranchBankCode( "007" );

		HouseContactBasicDataBean basicInfo = new HouseContactBasicDataBean();
		basicInfo.setCallBackTime( "09:00~12:00" );
		basicInfo.setcName( "游勇" );
		paramBean.setBasicInfo( basicInfo );

		HouseContactLoanDataBean loanInfo = new HouseContactLoanDataBean();
		loanInfo.setAddress( "急勇路666號" );
		loanInfo.setCounty( "水族市" );
		loanInfo.setDistrict( "露天區" );
		loanInfo.setLoanBalance( new BigDecimal( 1314.2 ) );
		loanInfo.setLoanPurpose( "我要購買房屋" );
		paramBean.setLoanInfo( loanInfo );

		return paramBean;
	}
}
