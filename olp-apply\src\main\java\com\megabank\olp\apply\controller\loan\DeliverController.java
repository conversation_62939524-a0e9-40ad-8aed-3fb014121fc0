package com.megabank.olp.apply.controller.loan;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.service.loan.DeliverService;
import com.megabank.olp.apply.service.mydata.MyDataService;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.client.sender.micro.apply.loan.deliver.bean.TargetRecipientSystemArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/deliver" )
public class DeliverController extends BaseController
{
	@Autowired
	private DeliverService deliverService;

	@Autowired
	private MyDataService myDataService;

	/**
	 * 已進件申貸案件PDF重送 eloan
	 *
	 * @return
	 */
	@PostMapping( "retryApplyPdfToEloan" )
	public Map<String, Object> retryApplyPdfToEloan()
	{
		deliverService.retryApplyPdfToEloan();

		return getResponseMap();
	}

	/**
	 * 上傳補件重送 eloan / iloan
	 *
	 * @return
	 */
	@PostMapping( "retryAttachment" )
	public Map<String, Object> retryAttachment( @RequestBody TargetRecipientSystemArgBean argBean )
	{
		deliverService.retrySubmitAttachment( argBean.getSystemId() );

		return getResponseMap();
	}

	/**
	 * 青創上傳補件重送 eloan
	 *
	 * @return
	 */
	@PostMapping( "retryYouthAttachment" )
	public Map<String, Object> retryYouthAttachment()
	{
		deliverService.retrySubmitYouthAttachment();

		return getResponseMap();
	}

	/**
	 * 擔保品提供人案件重送 eloan
	 *
	 * @return
	 */
	@PostMapping( "retryCollateralAgreement" )
	public Map<String, Object> retryCollateralAgreement()
	{
		deliverService.retrySubmitCollateral();

		return getResponseMap();
	}

	@PostMapping( "retryContractPdfToEloan" )
	public Map<String, Object> retryContractPdfToEloan()
	{
		deliverService.retryContractPdfToEloan();

		return getResponseMap();
	}

	/**
	 * 上傳 ixml 補件重送 eloan / iloan
	 *
	 * @return
	 */
	@PostMapping( "retryIxmlAttachment" )
	public Map<String, Object> retryIxmlAttachment( @RequestBody TargetRecipientSystemArgBean argBean )
	{
		deliverService.retrySubmitIxmlAttachment( argBean.getSystemId() );

		return getResponseMap();
	}

	/**
	 * 批次傳輸 ixml 憑證申請紀錄
	 *
	 * @return
	 */
	@PostMapping( "retryIxmlSaveCert" )
	public Map<String, Object> retryIxmlSaveCert()
	{
		deliverService.retryIxmlCert();

		return getResponseMap();
	}

	/**
	 * 貸款案件重送 eloan / iloan
	 *
	 * @return
	 */
	@PostMapping( "retryLoan" )
	public Map<String, Object> retryLoan( @RequestBody TargetRecipientSystemArgBean argBean )
	{
		deliverService.retrySubmitLoan( argBean.getSystemId() );

		return getResponseMap();
	}

	/**
	 * MyData重送 eloan
	 *
	 * @return
	 */
	@PostMapping( "retryMyData" )
	public Map<String, Object> retryMyData( @RequestBody TargetRecipientSystemArgBean argBean )
	{
		myDataService.retryDeliverMyData( argBean.getSystemId() );

		return getResponseMap();
	}

	/**
	 * 對保案件重送 eloan / iloan
	 *
	 * @return
	 */
	@PostMapping( "retrySigningContract" )
	public Map<String, Object> retrySigningContract( @RequestBody TargetRecipientSystemArgBean argBean )
	{
		deliverService.retrySubmitSigningContract( argBean.getSystemId() );

		return getResponseMap();
	}

	/**
	 * 撥款後 PDF 重送 eloan / iloan
	 *
	 * @return
	 */
	@PostMapping( "retrySigningContractPdf" )
	public Map<String, Object> retrySigningContractPdf( @RequestBody TargetRecipientSystemArgBean argBean )
	{
		deliverService.retrySubmitSigningContractPdf( argBean.getSystemId() );

		return getResponseMap();
	}

	/**
	 * 房貸增貸對保完成後 重送 各項約據PDF&央行切結書PDF至 eloan
	 *
	 * @return
	 */
	@PostMapping( "retryCollateralContractAttachment" )
	public Map<String, Object> retryCollateralContractAttachment()
	{
		deliverService.retryCollateralContractAttachment();

		return getResponseMap();
	}

}
