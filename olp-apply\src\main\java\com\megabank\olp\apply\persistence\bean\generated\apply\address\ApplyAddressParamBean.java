package com.megabank.olp.apply.persistence.bean.generated.apply.address;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyAddressParamBean extends BaseBean
{
	private String townCode;

	private String village;

	private String neighborhood;

	private String street;

	private Integer section;

	private String lane;

	private String alley;

	private String no;

	private String floor;

	private Integer room;

	public ApplyAddressParamBean()
	{
		// default constructor
	}

	public String getAlley()
	{
		return alley;
	}

	public String getFloor()
	{
		return floor;
	}

	public String getLane()
	{
		return lane;
	}

	public String getNeighborhood()
	{
		return neighborhood;
	}

	public String getNo()
	{
		return no;
	}

	public Integer getRoom()
	{
		return room;
	}

	public Integer getSection()
	{
		return section;
	}

	public String getStreet()
	{
		return street;
	}

	public String getTownCode()
	{
		return townCode;
	}

	public String getVillage()
	{
		return village;
	}

	public void setAlley( String alley )
	{
		this.alley = alley;
	}

	public void setFloor( String floor )
	{
		this.floor = floor;
	}

	public void setLane( String lane )
	{
		this.lane = lane;
	}

	public void setNeighborhood( String neighborhood )
	{
		this.neighborhood = neighborhood;
	}

	public void setNo( String no )
	{
		this.no = no;
	}

	public void setRoom( Integer room )
	{
		this.room = room;
	}

	public void setSection( Integer section )
	{
		this.section = section;
	}

	public void setStreet( String street )
	{
		this.street = street;
	}

	public void setTownCode( String townCode )
	{
		this.townCode = townCode;
	}

	public void setVillage( String village )
	{
		this.village = village;
	}

}
