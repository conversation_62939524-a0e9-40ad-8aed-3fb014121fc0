package com.megabank.olp.apply.controller.management.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

import java.math.BigDecimal;

public class RepaymentInfoCreateArgBean extends BaseBean
{
	private BigDecimal repaymentInfoType;

	private BigDecimal limtedYear4;

	private BigDecimal limtedMonth4;

	private BigDecimal year4;

	private BigDecimal month4;

	private BigDecimal limtedYear5;

	private BigDecimal limtedMonth5;

	private BigDecimal year5;

	private BigDecimal month5;

	private BigDecimal period6;

	private BigDecimal year6;

	private String other7;

	private BigDecimal houseRedemption;

	private String firstRate;

	private String secondRate;

	public RepaymentInfoCreateArgBean()
	{
	}

	public BigDecimal getRepaymentInfoType()
	{
		return repaymentInfoType;
	}

	public BigDecimal getLimtedYear4()
	{
		return limtedYear4;
	}

	public BigDecimal getLimtedMonth4()
	{
		return limtedMonth4;
	}

	public BigDecimal getYear4()
	{
		return year4;
	}

	public BigDecimal getMonth4()
	{
		return month4;
	}

	public BigDecimal getLimtedYear5()
	{
		return limtedYear5;
	}

	public BigDecimal getLimtedMonth5()
	{
		return limtedMonth5;
	}

	public BigDecimal getYear5()
	{
		return year5;
	}

	public BigDecimal getMonth5()
	{
		return month5;
	}

	public BigDecimal getPeriod6()
	{
		return period6;
	}

	public BigDecimal getYear6()
	{
		return year6;
	}

	public String getOther7()
	{
		return other7;
	}

	public BigDecimal getHouseRedemption()
	{
		return houseRedemption;
	}

	public String getFirstRate()
	{
		return firstRate;
	}

	public String getSecondRate()
	{
		return secondRate;
	}

	public void setRepaymentInfoType( BigDecimal repaymentInfoType )
	{
		this.repaymentInfoType = repaymentInfoType;
	}

	public void setLimtedYear4( BigDecimal limtedYear4 )
	{
		this.limtedYear4 = limtedYear4;
	}

	public void setLimtedMonth4( BigDecimal limtedMonth4 )
	{
		this.limtedMonth4 = limtedMonth4;
	}

	public void setYear4( BigDecimal year4 )
	{
		this.year4 = year4;
	}

	public void setMonth4( BigDecimal month4 )
	{
		this.month4 = month4;
	}

	public void setLimtedYear5( BigDecimal limtedYear5 )
	{
		this.limtedYear5 = limtedYear5;
	}

	public void setLimtedMonth5( BigDecimal limtedMonth5 )
	{
		this.limtedMonth5 = limtedMonth5;
	}

	public void setYear5( BigDecimal year5 )
	{
		this.year5 = year5;
	}

	public void setMonth5( BigDecimal month5 )
	{
		this.month5 = month5;
	}

	public void setPeriod6( BigDecimal period6 )
	{
		this.period6 = period6;
	}

	public void setYear6( BigDecimal year6 )
	{
		this.year6 = year6;
	}

	public void setOther7( String other7 )
	{
		this.other7 = other7;
	}

	public void setHouseRedemption( BigDecimal houseRedemption )
	{
		this.houseRedemption = houseRedemption;
	}

	public void setFirstRate( String firstRate )
	{
		this.firstRate = firstRate;
	}

	public void setSecondRate( String secondRate )
	{
		this.secondRate = secondRate;
	}
}
