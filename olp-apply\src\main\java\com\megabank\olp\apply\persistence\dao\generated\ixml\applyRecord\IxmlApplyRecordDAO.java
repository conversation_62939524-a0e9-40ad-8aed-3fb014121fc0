package com.megabank.olp.apply.persistence.dao.generated.ixml.applyRecord;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.ixml.IxmlApplyRecordCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.pojo.ixml.applyRecord.IxmlApplyRecord;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultCertParamsBean;

@Repository
public class IxmlApplyRecordDAO extends BasePojoDAO<IxmlApplyRecord, Long>
{

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	public Long create( IxmlApplyRecordCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getIdNo() );
		Validate.notNull( paramBean.getClientAddress() );
		Validate.notNull( paramBean.getVerifyNo() );
		Validate.notNull( paramBean.getToken() );

		IxmlApplyRecord pojo = new IxmlApplyRecord();
		pojo.setIdNo( paramBean.getIdNo() );
		pojo.setClientAddress( paramBean.getClientAddress() );
		pojo.setVerifyNo( paramBean.getVerifyNo() );
		pojo.setToken( paramBean.getToken() );
		pojo.setCreatedDate( new Date() );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( TransmissionStatusEnum.NO.getContext() ) );

		return super.createPojo( pojo );
	}

	public Long create( String idNo, String certNo, String certSn, String certNoBefore, String certNoAfter, String clientAddress, String verifyNo )
	{
		Validate.notNull( idNo );
		Validate.notNull( certNo );
		Validate.notNull( certSn );
		Validate.notNull( certNoBefore );
		Validate.notNull( certNoAfter );
		Validate.notNull( clientAddress );
		Validate.notNull( verifyNo );

		IxmlApplyRecord pojo = new IxmlApplyRecord( idNo, certNo, certSn, certNoBefore, certNoAfter, clientAddress, verifyNo );
		pojo.setCreatedDate( new Date() );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( TransmissionStatusEnum.NO.getContext() ) );

		return super.createPojo( pojo );
	}

	public IxmlApplyRecord getPojoByLoginInfo( String verifyNo, String id, String token )
	{
		Validate.notBlank( verifyNo );
		Validate.notBlank( id );
		Validate.notBlank( token );

		NameValueBean verifyNoCondition = new NameValueBean( IxmlApplyRecord.VERIFY_NO, verifyNo );
		NameValueBean idNoCondition = new NameValueBean( IxmlApplyRecord.ID_NO, id );
		NameValueBean tokenCondition = new NameValueBean( IxmlApplyRecord.TOKEN, token );
		NameValueBean[] conditions = new NameValueBean[]{ verifyNoCondition, idNoCondition, tokenCondition };

		return this.getUniquePojoByProperties( conditions );
	}

	public List<IxmlApplyRecord> getPojosByCertSn( String certSn )
	{
		Validate.notBlank( certSn );

		NameValueBean certSnCondition = new NameValueBean( IxmlApplyRecord.CERT_SN_CONSTANT, certSn );
		NameValueBean[] conditions = new NameValueBean[]{ certSnCondition };

		return this.getPojosByProperties( conditions );
	}

	public List<IxmlApplyRecord> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "ixml.applyRecord.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( IxmlApplyRecord.class );

		return nativeQuery.getResultList();
	}

	public List<IxmlApplyRecord> getUncheckedCertRecordsYesterday( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "ixml.applyRecord.getUncheckedCertRecordsYesterday" );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( IxmlApplyRecord.class );

		return nativeQuery.getResultList();
	}

	public IxmlApplyRecord read( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		return getPojoByPK( attachmentId, IxmlApplyRecord.TABLENAME_CONSTANT );
	}

	public Long updateCertInfo( Long attachmentId, IxmlQueryVerifyResultCertParamsBean certInfoBean )
	{
		Validate.notNull( attachmentId );

		IxmlApplyRecord pojo = read( attachmentId );

		pojo.setCertNo( certInfoBean.getCertCN() );
		pojo.setCertSn( certInfoBean.getCertSN() );
		pojo.setCertNoBefore( certInfoBean.getCertNotBefore() );
		pojo.setCertNoAfter( certInfoBean.getCertNotAfter() );

		return pojo.getApplyRecordId();
	}

	public Long updateResend( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		IxmlApplyRecord pojo = read( attachmentId );
		pojo.setResend( pojo.getResend() + 1 );

		return pojo.getApplyRecordId();
	}

	public Long updateTransmissionStatus( Long attachmentId, String transmissionStatusCode )
	{
		Validate.notNull( attachmentId );

		IxmlApplyRecord pojo = read( attachmentId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );

		return pojo.getApplyRecordId();
	}

	@Override
	protected Class<IxmlApplyRecord> getPojoClass()
	{
		return IxmlApplyRecord.class;
	}

}
