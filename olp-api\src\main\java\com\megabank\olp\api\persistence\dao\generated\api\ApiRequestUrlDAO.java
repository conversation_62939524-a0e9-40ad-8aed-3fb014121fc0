package com.megabank.olp.api.persistence.dao.generated.api;

import com.megabank.olp.api.persistence.pojo.api.ApiRequestUrl;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.springframework.stereotype.Repository;

/**
 * The ApiRequestUrlDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApiRequestUrlDAO extends BasePojoDAO<ApiRequestUrl, Long>
{
	@Override
	protected Class<ApiRequestUrl> getPojoClass()
	{
		return ApiRequestUrl.class;
	}
}
