package com.megabank.olp.apply.persistence.bean.generated.temp;

import com.megabank.olp.base.bean.BaseBean;

public class TempMyDataFileCreatedParamBean extends BaseBean
{
	private Long myDataId;

	private String fileType;

	private transient String fileContent;

	public TempMyDataFileCreatedParamBean()
	{
		// default constructor
	}

	public String getFileContent()
	{
		return fileContent;
	}

	public String getFileType()
	{
		return fileType;
	}

	public Long getMyDataId()
	{
		return myDataId;
	}

	public void setFileContent( String fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileType( String fileType )
	{
		this.fileType = fileType;
	}

	public void setMyDataId( Long myDataId )
	{
		this.myDataId = myDataId;
	}

}