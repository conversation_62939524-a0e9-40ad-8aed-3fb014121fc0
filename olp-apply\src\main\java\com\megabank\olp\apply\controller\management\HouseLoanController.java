package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.houseloan.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloan.HouseLoanDetailGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloan.LoanApplyCompletedExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloan.LoanApplyCompletedListedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloan.LoanApplyInterruptedExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloan.LoanApplyInterruptedListedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloan.ProcessStatusUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.houseloan.TransmissionStatusUpdatedArgBean;
import com.megabank.olp.apply.service.loan.DownloadService;
import com.megabank.olp.apply.service.management.LoanApplyService;
import com.megabank.olp.apply.service.management.bean.loan.LoanApplyExportedParamBean;
import com.megabank.olp.apply.service.management.bean.loan.LoanApplyListedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/houseloan" )
public class HouseLoanController extends BaseController
{
	@Autowired
	private LoanApplyService loanApplyService;

	@Autowired
	private DownloadService downloadService;

	/**
	 * 下載房貸申請案件pdf
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "downloadPdf" )
	public Map<String, Object> downloadPdf( @RequestBody @Validated HouseLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( downloadService.downloadUnEncryptApplyPdf( loanId ) );
	}

	/**
	 * 房貸申請完成案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportLoanApplyCompleted" )
	public Map<String, Object> exportLoanApplyCompleted( @RequestBody @Validated LoanApplyCompletedExportedArgBean argBean )
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setDiscard( argBean.getDiscard() );

		return getResponseMap( loanApplyService.exportHouseLoanCompleted( paramBean ) );
	}

	/**
	 * 房貸申請中斷案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportLoanApplyInterrupted" )
	public Map<String, Object> exportLoanApplyInterrupted( @RequestBody @Validated LoanApplyInterruptedExportedArgBean argBean )
	{
		LoanApplyExportedParamBean paramBean = new LoanApplyExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( loanApplyService.exportHouseLoanInterrupted( paramBean ) );
	}

	/**
	 * 取得房貸申請案件詳細內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getDetail" )
	public Map<String, Object> getDetail( @RequestBody @Validated HouseLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( loanApplyService.getHouseLoanDetail( loanId ) );
	}

	/**
	 * 取得房貸申請案件補件內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getLoanAttachment" )
	public Map<String, Object> getLoanAttachment( @RequestBody @Validated HouseLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( loanApplyService.getLoanAttachment( loanId ) );
	}

	/**
	 * 取得改派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getReassignBranchBank" )
	public Map<String, Object> getReassignBranchBank( @RequestBody @Validated HouseLoanDetailGetterArgBean argBean )
	{
		Long loanId = argBean.getLoanId();

		return getResponseMap( loanApplyService.getHouseLoanReassignBranchBank( loanId ) );
	}

	/**
	 * 取得房貸申請完成案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "listLoanApplyCompleted" )
	public Map<String, Object> listLoanApplyCompleted( @RequestBody @Validated LoanApplyCompletedListedArgBean argBean )
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setTransmissionStatusCode( argBean.getTransmissionStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );
		paramBean.setDiscard( argBean.getDiscard() );

		return getResponseMap( loanApplyService.listHouseLoanCompleted( paramBean ) );
	}

	/**
	 * 取得房貸申請中斷案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "listLoanApplyInterrupted" )
	public Map<String, Object> listLoanApplyInterrupted( @RequestBody @Validated LoanApplyInterruptedListedArgBean argBean )
	{
		LoanApplyListedParamBean paramBean = new LoanApplyListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setIdNo( argBean.getIdNo() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( loanApplyService.listHouselLoanInterrupted( paramBean ) );

	}

	/**
	 * 更改中斷案件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();
		Long branchBankId = argBean.getBranchBankId();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanApplyService.updateLoanInterruptedBranchBank( loanId, branchBankId, employeeId, employeeName ) );
	}

	/**
	 * 更新案件處理狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateProcessStatus" )
	public Map<String, Object> updateProcessStatus( @RequestBody @Validated ProcessStatusUpdatedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();
		String processStatus = argBean.getProcessStatus();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanApplyService.updateProcessStatus( loanId, processStatus, employeeId, employeeName ) );
	}

	/**
	 * 更新案件進件狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateTransmissionStatus" )
	public Map<String, Object> updateTransmissionStatus( @RequestBody @Validated TransmissionStatusUpdatedArgBean argBean )
	{
		Long loanId = argBean.getLoanId();
		String transmissionStatus = argBean.getTransmissionStatusUpdatedEnum().getContext();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( loanApplyService.updateTransmissionStatus( loanId, transmissionStatus, employeeId, employeeName ) );
	}

}
