package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPeriod;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeLoanPeriodDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeLoanPeriodDAO extends BasePojoDAO<CodeLoanPeriod, String>
{
	public List<CodeLoanPeriod> getList()
	{
		return getAllPojos();
	}

	public CodeLoanPeriod getPojoByName( String name )
	{
		Validate.notBlank( name );

		NameValueBean _name = new NameValueBean( CodeLoanPeriod.NAME_CONSTANT, name );

		NameValueBean[] conditions = new NameValueBean[]{ _name };

		return this.getUniquePojoByProperties( conditions );
	}

	public CodeLoanPeriod read( String loadPeriodCode )
	{
		Validate.notBlank( loadPeriodCode );

		return getPojoByPK( loadPeriodCode, CodeLoanPeriod.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeLoanPeriod> getPojoClass()
	{
		return CodeLoanPeriod.class;
	}
}
