package com.megabank.olp.apply.persistence.dao.generated.code;

import com.megabank.olp.apply.persistence.pojo.code.CodeRecipientSystem;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

@Repository
public class CodeRecipientSystemDAO extends BasePojoDAO<CodeRecipientSystem, Integer>
{
	public CodeRecipientSystem read(Integer systemId )
	{
		Validate.notNull( systemId );

		return getPojoByPK( systemId, CodeRecipientSystem.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeRecipientSystem> getPojoClass()
	{
		return CodeRecipientSystem.class;
	}
}
