package com.megabank.olp.api.service.jwt;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApiConfig.class )
public class ApiJwtDecryptServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private ApiJwtDecryptService service;

	@Test
	public void validateJwt()
	{
		String jwt = "eyJhbGciOiJIUzUxMiJ9.***********************************************.RrjXoMSWCD4N4JC"
			+ "fg6JMv-GHc1K0MOXr-AUtekCysOd3dvUBLnx-YmpyrvitJ6wQhmPaFPFoXCB9KD5sbXyirQ";

		boolean validaed = service.validateJwt( jwt );

		logger.info( "validaed:{}", validaed );
	}
}
