package com.megabank.olp.apply.persistence.dao.generated.apply.survey;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.survey.ApplySurveyContactCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeContactTimeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeSexDAO;
import com.megabank.olp.apply.persistence.pojo.apply.survey.ApplySurveyContact;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplySurveyContactDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySurveyContactDAO extends BasePojoDAO<ApplySurveyContact, Long>
{
	@Autowired
	private ApplySurveyDAO applySurveyDAO;

	@Autowired
	private CodeContactTimeDAO codeContactTimeDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeSexDAO codeSexDAO;

	public Long create( ApplySurveyContactCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getSurveyId() );
		Validate.notNull( paramBean.getBranchBankId() );
		Validate.notBlank( paramBean.getName() );
		Validate.notBlank( paramBean.getMobileNumber() );
		Validate.notBlank( paramBean.getContactTimeCode() );
		Validate.notBlank( paramBean.getSexCode() );

		ApplySurveyContact pojo = new ApplySurveyContact();
		pojo.setApplySurvey( applySurveyDAO.read( paramBean.getSurveyId() ) );
		pojo.setCodeContactTime( codeContactTimeDAO.read( paramBean.getContactTimeCode() ) );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( paramBean.getBranchBankId() ) );
		pojo.setName( paramBean.getName() );
		pojo.setPhoneCode( paramBean.getPhoneCode() );
		pojo.setPhoneNumber( paramBean.getPhoneNumber() );
		pojo.setPhoneExt( paramBean.getPhoneExt() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setOtherMsg( paramBean.getOtherMsg() );
		pojo.setCodeSex( codeSexDAO.read( paramBean.getSexCode() ) );

		return super.createPojo( pojo );
	}

	public ApplySurveyContact read( Long surveyId )
	{
		Validate.notNull( surveyId );

		return getPojoByPK( surveyId, ApplySurveyContact.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<ApplySurveyContact> getPojoClass()
	{
		return ApplySurveyContact.class;
	}
}
