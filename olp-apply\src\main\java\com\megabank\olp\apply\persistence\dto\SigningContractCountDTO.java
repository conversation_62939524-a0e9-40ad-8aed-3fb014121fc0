package com.megabank.olp.apply.persistence.dto;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class SigningContractCountDTO extends BaseBean
{

	private Long signingContractId;

	private Long validatedIdentityId;

	private String name;

	private Long contractRecipientId;

	public SigningContractCountDTO()
	{
		// default constructor
	}

	public String getName()
	{
		return name;
	}

	public Long getSigningContractId()
	{
		return signingContractId;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public Long getContractRecipientId()
	{
		return contractRecipientId;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

	public void setContractRecipientId( Long contractRecipientId )
	{
		this.contractRecipientId = contractRecipientId;
	}
}
