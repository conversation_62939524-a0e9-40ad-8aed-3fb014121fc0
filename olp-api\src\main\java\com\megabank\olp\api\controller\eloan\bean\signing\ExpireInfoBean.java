package com.megabank.olp.api.controller.eloan.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

import java.math.BigDecimal;

public class ExpireInfoBean extends BaseBean
{
	private BigDecimal expireInfoType;

	private BigDecimal startYear1;

	private BigDecimal startMonth1;

	private BigDecimal startDay1;

	private BigDecimal expireYear1;

	private BigDecimal expireMonth1;

	private BigDecimal expireDay1;

	private BigDecimal duration1;

	private BigDecimal startYear2;

	private BigDecimal startMonth2;

	private BigDecimal startDay2;

	private BigDecimal expireYear2;

	private BigDecimal expireMonth2;

	private BigDecimal expireDay2;

	private BigDecimal duration2;

	private BigDecimal startYear3;

	private BigDecimal startMonth3;

	private BigDecimal startDay3;

	private BigDecimal expireYear3;

	private BigDecimal expireMonth3;

	private BigDecimal expireDay3;

	private BigDecimal duration3;

	private BigDecimal maxYear4;

	private BigDecimal expireYear4;

	private BigDecimal expireMonth4;

	private BigDecimal expireDay4;

	private BigDecimal duration4;

	private String other5;

	public ExpireInfoBean()
	{
	}

	public BigDecimal getExpireInfoType()
	{
		return expireInfoType;
	}

	public BigDecimal getStartYear1()
	{
		return startYear1;
	}

	public BigDecimal getStartMonth1()
	{
		return startMonth1;
	}

	public BigDecimal getStartDay1()
	{
		return startDay1;
	}

	public BigDecimal getExpireYear1()
	{
		return expireYear1;
	}

	public BigDecimal getExpireMonth1()
	{
		return expireMonth1;
	}

	public BigDecimal getExpireDay1()
	{
		return expireDay1;
	}

	public BigDecimal getDuration1()
	{
		return duration1;
	}

	public BigDecimal getStartYear2()
	{
		return startYear2;
	}

	public BigDecimal getStartMonth2()
	{
		return startMonth2;
	}

	public BigDecimal getStartDay2()
	{
		return startDay2;
	}

	public BigDecimal getExpireYear2()
	{
		return expireYear2;
	}

	public BigDecimal getExpireMonth2()
	{
		return expireMonth2;
	}

	public BigDecimal getExpireDay2()
	{
		return expireDay2;
	}

	public BigDecimal getDuration2()
	{
		return duration2;
	}

	public BigDecimal getStartYear3()
	{
		return startYear3;
	}

	public BigDecimal getStartMonth3()
	{
		return startMonth3;
	}

	public BigDecimal getStartDay3()
	{
		return startDay3;
	}

	public BigDecimal getExpireYear3()
	{
		return expireYear3;
	}

	public BigDecimal getExpireMonth3()
	{
		return expireMonth3;
	}

	public BigDecimal getExpireDay3()
	{
		return expireDay3;
	}

	public BigDecimal getDuration3()
	{
		return duration3;
	}

	public BigDecimal getMaxYear4()
	{
		return maxYear4;
	}

	public BigDecimal getExpireYear4()
	{
		return expireYear4;
	}

	public BigDecimal getExpireMonth4()
	{
		return expireMonth4;
	}

	public BigDecimal getExpireDay4()
	{
		return expireDay4;
	}

	public BigDecimal getDuration4()
	{
		return duration4;
	}

	public String getOther5()
	{
		return other5;
	}

	public void setExpireInfoType( BigDecimal expireInfoType )
	{
		this.expireInfoType = expireInfoType;
	}

	public void setStartYear1( BigDecimal startYear1 )
	{
		this.startYear1 = startYear1;
	}

	public void setStartMonth1( BigDecimal startMonth1 )
	{
		this.startMonth1 = startMonth1;
	}

	public void setStartDay1( BigDecimal startDay1 )
	{
		this.startDay1 = startDay1;
	}

	public void setExpireYear1( BigDecimal expireYear1 )
	{
		this.expireYear1 = expireYear1;
	}

	public void setExpireMonth1( BigDecimal expireMonth1 )
	{
		this.expireMonth1 = expireMonth1;
	}

	public void setExpireDay1( BigDecimal expireDay1 )
	{
		this.expireDay1 = expireDay1;
	}

	public void setDuration1( BigDecimal duration1 )
	{
		this.duration1 = duration1;
	}

	public void setStartYear2( BigDecimal startYear2 )
	{
		this.startYear2 = startYear2;
	}

	public void setStartMonth2( BigDecimal startMonth2 )
	{
		this.startMonth2 = startMonth2;
	}

	public void setStartDay2( BigDecimal startDay2 )
	{
		this.startDay2 = startDay2;
	}

	public void setExpireYear2( BigDecimal expireYear2 )
	{
		this.expireYear2 = expireYear2;
	}

	public void setExpireMonth2( BigDecimal expireMonth2 )
	{
		this.expireMonth2 = expireMonth2;
	}

	public void setExpireDay2( BigDecimal expireDay2 )
	{
		this.expireDay2 = expireDay2;
	}

	public void setDuration2( BigDecimal duration2 )
	{
		this.duration2 = duration2;
	}

	public void setStartYear3( BigDecimal startYear3 )
	{
		this.startYear3 = startYear3;
	}

	public void setStartMonth3( BigDecimal startMonth3 )
	{
		this.startMonth3 = startMonth3;
	}

	public void setStartDay3( BigDecimal startDay3 )
	{
		this.startDay3 = startDay3;
	}

	public void setExpireYear3( BigDecimal expireYear3 )
	{
		this.expireYear3 = expireYear3;
	}

	public void setExpireMonth3( BigDecimal expireMonth3 )
	{
		this.expireMonth3 = expireMonth3;
	}

	public void setExpireDay3( BigDecimal expireDay3 )
	{
		this.expireDay3 = expireDay3;
	}

	public void setDuration3( BigDecimal duration3 )
	{
		this.duration3 = duration3;
	}

	public void setMaxYear4( BigDecimal maxYear4 )
	{
		this.maxYear4 = maxYear4;
	}

	public void setExpireYear4( BigDecimal expireYear4 )
	{
		this.expireYear4 = expireYear4;
	}

	public void setExpireMonth4( BigDecimal expireMonth4 )
	{
		this.expireMonth4 = expireMonth4;
	}

	public void setExpireDay4( BigDecimal expireDay4 )
	{
		this.expireDay4 = expireDay4;
	}

	public void setDuration4( BigDecimal duration4 )
	{
		this.duration4 = duration4;
	}

	public void setOther5( String other5 )
	{
		this.other5 = other5;
	}
}
