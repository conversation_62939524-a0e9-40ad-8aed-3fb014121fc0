-- =====================================================
-- C001 中鋼總公司消費性貸款測試資料建立腳本
-- =====================================================

-- 1. 建立貸款方案資料
INSERT INTO code_loan_plan (
    loan_plan_code, 
    name, 
    display_order, 
    beg_ts, 
    end_ts, 
    taxno, 
    company_name, 
    plan_desc
) VALUES (
    'C001',
    '中鋼消貸總公司員工',
    1,
    '2022-10-01 00:00:00.000',
    '2025-12-31 23:59:59.000',
    '91811100',
    '中國鋼鐵股份有限公司',
    '110年中鋼公司福委會第46期 消費性貸款專案'
);

-- 2. 建立中鋼控管名單
INSERT INTO code_list (code_type, code_value, code_desc, code_display_order, code_disabled, code_modeify_time) VALUES
('ChinaSteel_GrpCntrNo', '918111000325', '中國鋼鐵股份有限公司總公司', 1, 0, GETDATE()),
('ChinaSteel_GrpCntrNo', '918111000326', '中國鋼鐵股份有限公司測試用', 2, 0, GETDATE());

-- 3. 建立EIP系統時間配置
INSERT INTO code_list (code_type, code_value, code_desc, code_display_order, code_disabled, code_modeify_time) VALUES
('ChinaSteel_EIP_Time', '11/10-11/14', 'EIP系統申請時間', 1, 0, GETDATE()),
('ChinaSteel_fac_Time', '11/16-11/17', '廠區紙本申貸時間', 2, 0, GETDATE()),
('ChinaSteel_head_Time', '11/18', '總部紙本申貸時間', 3, 0, GETDATE());

-- 4. 建立測試用分行資料 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_branch_bank WHERE bank_code = '002')
BEGIN
    INSERT INTO code_branch_bank (bank_code, name, disabled) VALUES 
    ('002', '中鋼專案分行', 0);
END

-- 5. 建立測試用申請狀態 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_apply_status WHERE apply_status_code = '01')
BEGIN
    INSERT INTO code_apply_status (apply_status_code, name, disabled) VALUES 
    ('01', '已建立', 0),
    ('02', '已同意', 0),
    ('03', '已完成基本資料', 0),
    ('04', '已完成貸款資料', 0),
    ('05', '已送出', 0);
END

-- 6. 建立測試用貸款類型 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_loan_type WHERE loan_type_code = 'personalloan')
BEGIN
    INSERT INTO code_loan_type (loan_type_code, name, disabled) VALUES 
    ('personalloan', '個人信用貸款', 0);
END

-- 7. 建立測試用流程狀態 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_process WHERE process_code = '01')
BEGIN
    INSERT INTO code_process (process_code, name, disabled) VALUES 
    ('01', '申請中', 0),
    ('02', '審核中', 0),
    ('03', '已核准', 0);
END

-- 8. 建立測試用傳輸狀態 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_transmission_status WHERE transmission_status_code = '01')
BEGIN
    INSERT INTO code_transmission_status (transmission_status_code, name, disabled) VALUES 
    ('01', '未傳送', 0),
    ('02', '已傳送', 0),
    ('03', '傳送失敗', 0);
END

-- 9. 建立測試用貸款期間 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_loan_period WHERE loan_period_code = '7')
BEGIN
    INSERT INTO code_loan_period (loan_period_code, name, disabled) VALUES 
    ('7', '7年', 0);
END

-- 10. 建立測試用貸款用途 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_loan_purpose WHERE loan_purpose_code = '01')
BEGIN
    INSERT INTO code_loan_purpose (loan_purpose_code, name, disabled) VALUES 
    ('01', '一般消費', 0),
    ('02', '投資理財', 0),
    ('03', '債務整合', 0);
END

-- 11. 建立測試用案件來源 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_case_source WHERE case_source_code = '01')
BEGIN
    INSERT INTO code_case_source (case_source_code, name, disabled) VALUES 
    ('01', '網路申請', 0),
    ('02', '分行申請', 0);
END

-- 12. 建立測試用貸款版本 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_loan_version WHERE loan_type_code = 'personalloan')
BEGIN
    INSERT INTO code_loan_version (loan_type_code, loan_version, disabled) VALUES 
    ('personalloan', 'V1.0', 0);
END

-- 13. 建立測試用同意事項 (如果不存在)
IF NOT EXISTS (SELECT 1 FROM code_agreed WHERE agreed_id = 1)
BEGIN
    INSERT INTO code_agreed (agreed_id, user_type, identity_type, loan_type, content, need_to_check, disabled) VALUES 
    (1, 'borrower', 'internal', 'personalloan', '個人資料蒐集、處理及利用同意書', 1, 0),
    (2, 'borrower', 'internal', 'personalloan', '信用查詢同意書', 1, 0),
    (3, 'borrower', 'internal', 'personalloan', '電子文件同意書', 1, 0),
    (4, 'borrower', 'internal', 'personalloan', '行銷同意書', 0, 0),
    (5, 'borrower', 'internal', 'personalloan', '中鋼專案特殊條款', 1, 0);
END

PRINT 'C001 中鋼總公司測試資料建立完成！';
