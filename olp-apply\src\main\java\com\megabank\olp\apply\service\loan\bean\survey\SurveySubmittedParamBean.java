package com.megabank.olp.apply.service.loan.bean.survey;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class SurveySubmittedParamBean extends BaseBean
{
	private String jobSubType;

	private Integer annualIncome;

	private Boolean holdingCr3ditCard;

	private BigDecimal revolvingCr3dit;

	private BigDecimal cashAdvance;

	private BigDecimal creditCardTotalAmt;

	private Boolean holdingDebitCard;

	private BigDecimal debitCardTotalAmt;

	private BigDecimal holdingPersonalLoan;

	private String loanPlanCode;

	public SurveySubmittedParamBean()
	{}

	public Integer getAnnualIncome()
	{
		return annualIncome;
	}

	public BigDecimal getCashAdvance()
	{
		return cashAdvance;
	}

	public BigDecimal getCreditCardTotalAmt()
	{
		return creditCardTotalAmt;
	}

	public BigDecimal getDebitCardTotalAmt()
	{
		return debitCardTotalAmt;
	}

	public Boolean getHoldingCr3ditCard()
	{
		return holdingCr3ditCard;
	}

	public Boolean getHoldingDebitCard()
	{
		return holdingDebitCard;
	}

	public BigDecimal getHoldingPersonalLoan()
	{
		return holdingPersonalLoan;
	}

	public String getJobSubType()
	{
		return jobSubType;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public BigDecimal getRevolvingCr3dit()
	{
		return revolvingCr3dit;
	}

	public void setAnnualIncome( Integer annualIncome )
	{
		this.annualIncome = annualIncome;
	}

	public void setCashAdvance( BigDecimal cashAdvance )
	{
		this.cashAdvance = cashAdvance;
	}

	public void setCreditCardTotalAmt( BigDecimal creditCardTotalAmt )
	{
		this.creditCardTotalAmt = creditCardTotalAmt;
	}

	public void setDebitCardTotalAmt( BigDecimal debitCardTotalAmt )
	{
		this.debitCardTotalAmt = debitCardTotalAmt;
	}

	public void setHoldingCr3ditCard( Boolean holdingCr3ditCard )
	{
		this.holdingCr3ditCard = holdingCr3ditCard;
	}

	public void setHoldingDebitCard( Boolean holdingDebitCard )
	{
		this.holdingDebitCard = holdingDebitCard;
	}

	public void setHoldingPersonalLoan( BigDecimal holdingPersonalLoan )
	{
		this.holdingPersonalLoan = holdingPersonalLoan;
	}

	public void setJobSubType( String jobSubType )
	{
		this.jobSubType = jobSubType;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setRevolvingCr3dit( BigDecimal revolvingCr3dit )
	{
		this.revolvingCr3dit = revolvingCr3dit;
	}
}
