/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningUserCreatedParamBean extends BaseBean
{
	private Long signingContractId;

	private String name;

	private String mobileNumber;

	private String email;

	private Boolean isYouth;

	private Long validatedIdentityId;

	public SigningUserCreatedParamBean()
	{}

	public String getEmail()
	{
		return email;
	}

	public Boolean getIsYouth()
	{
		return isYouth;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public Long getSigningContractId()
	{
		return signingContractId;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setIsYouth( Boolean isYouth )
	{
		this.isYouth = isYouth;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

}
