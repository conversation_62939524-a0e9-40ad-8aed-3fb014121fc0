package com.megabank.olp.apply.persistence.bean.mixed;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class AttachmentListGetterParamBean extends BaseBean
{
	private String transmissionStatusCode;

	private String idNo;

	private String mobileNumber;

	private Date birthDate;

	private Date dateStart;

	private Date dateEnd;

	private String loanType;

	private String caseNo;

	private Long finalBranchBankId;

	public AttachmentListGetterParamBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return birthDate
	 */
	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	public Date getDateStart()
	{
		return dateStart;
	}

	public Long getFinalBranchBankId()
	{
		return finalBranchBankId;
	}

	/**
	 *
	 * @return idNo
	 */
	public String getIdNo()
	{
		return idNo;
	}

	public String getLoanType()
	{
		return loanType;
	}

	/**
	 *
	 * @return mobileNumber
	 */
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	/**
	 *
	 * @return transmissionStatus
	 */
	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	/**
	 *
	 * @param birthDate
	 */
	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	public void setFinalBranchBankId( Long finalBranchBankId )
	{
		this.finalBranchBankId = finalBranchBankId;
	}

	/**
	 *
	 * @param idNo
	 */
	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	/**
	 *
	 * @param mobileNumber
	 */
	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	/**
	 *
	 * @param transmissionStatusCode
	 */
	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}
}
