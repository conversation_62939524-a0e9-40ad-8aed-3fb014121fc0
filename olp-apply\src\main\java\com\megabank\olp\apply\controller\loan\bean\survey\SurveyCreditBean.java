package com.megabank.olp.apply.controller.loan.bean.survey;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class SurveyCreditBean extends BaseBean
{

	@NotNull
	private Boolean holdingCreditCard;

	private BigDecimal revovingCredit;

	private BigDecimal cashAdvance;

	private BigDecimal creditCardTotalAmt;

	@NotNull
	private Boolean holdingDebitCard;

	private BigDecimal debitCardTotalAmt;

	private BigDecimal holdingPersonalLoan;

	public SurveyCreditBean()
	{
		// default constructor
	}

	public BigDecimal getCashAdvance()
	{
		return cashAdvance;
	}

	public BigDecimal getCreditCardTotalAmt()
	{
		return creditCardTotalAmt;
	}

	public BigDecimal getDebitCardTotalAmt()
	{
		return debitCardTotalAmt;
	}

	public Boolean getHoldingCreditCard()
	{
		return holdingCreditCard;
	}

	public Boolean getHoldingDebitCard()
	{
		return holdingDebitCard;
	}

	public BigDecimal getHoldingPersonalLoan()
	{
		return holdingPersonalLoan;
	}

	public BigDecimal getRevovingCredit()
	{
		return revovingCredit;
	}

	public void setCashAdvance( BigDecimal cashAdvance )
	{
		this.cashAdvance = cashAdvance;
	}

	public void setCreditCardTotalAmt( BigDecimal creditCardTotalAmt )
	{
		this.creditCardTotalAmt = creditCardTotalAmt;
	}

	public void setDebitCardTotalAmt( BigDecimal debitCardTotalAmt )
	{
		this.debitCardTotalAmt = debitCardTotalAmt;
	}

	public void setHoldingCreditCard( Boolean holdingCreditCard )
	{
		this.holdingCreditCard = holdingCreditCard;
	}

	public void setHoldingDebitCard( Boolean holdingDebitCard )
	{
		this.holdingDebitCard = holdingDebitCard;
	}

	public void setHoldingPersonalLoan( BigDecimal holdingPersonalLoan )
	{
		this.holdingPersonalLoan = holdingPersonalLoan;
	}

	public void setRevovingCredit( BigDecimal revovingCredit )
	{
		this.revovingCredit = revovingCredit;
	}
}
