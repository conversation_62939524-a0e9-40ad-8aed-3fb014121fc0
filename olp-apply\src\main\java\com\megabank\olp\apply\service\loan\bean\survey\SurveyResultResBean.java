package com.megabank.olp.apply.service.loan.bean.survey;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class SurveyResultResBean extends BaseBean
{
	private boolean hasResult;

	@JsonProperty( "surveyResult" )
	private SurveyResultBean surveyResultBean;

	public SurveyResultResBean()
	{}

	public boolean getHasResult()
	{
		return hasResult;
	}

	public SurveyResultBean getSurveyResultBean()
	{
		return surveyResultBean;
	}

	public void setHasResult( boolean hasResult )
	{
		this.hasResult = hasResult;
	}

	public void setSurveyResultBean( SurveyResultBean surveyResultBean )
	{
		this.surveyResultBean = surveyResultBean;
	}

}
