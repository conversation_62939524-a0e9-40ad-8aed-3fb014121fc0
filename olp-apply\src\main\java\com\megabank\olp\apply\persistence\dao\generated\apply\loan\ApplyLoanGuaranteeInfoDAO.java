package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanGuaranteeInfoCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanGuaranteeInfoUpdatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeGuarantyReasonDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeRelationBorrowerTypeDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanGuaranteeInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanGuaranteeInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanGuaranteeInfoDAO extends BasePojoDAO<ApplyLoanGuaranteeInfo, Long>
{
	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private CodeRelationBorrowerTypeDAO codeRelationBorrowerTypeDAO;

	@Autowired
	private CodeGuarantyReasonDAO codeGuarantyReasonDAO;

	public Long create( ApplyLoanGuaranteeInfoCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notNull( paramBean.getCohabitationFlag() );
		Validate.notBlank( paramBean.getRelationBorrowerType() );

		ApplyLoanGuaranteeInfo pojo = new ApplyLoanGuaranteeInfo();
		pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );
		pojo.setCodeRelationBorrowerType( codeRelationBorrowerTypeDAO.read( paramBean.getRelationBorrowerType() ) );
		pojo.setCodeGuarantyReason( StringUtils
					.isBlank( paramBean.getGuarantyReasonCode() ) ? null : codeGuarantyReasonDAO.read( paramBean.getGuarantyReasonCode() ) );
		pojo.setOtherGuarantyReason( paramBean.getOtherGuarantyReason() );
		pojo.setCohabitationFlag( paramBean.getCohabitationFlag() );

		return super.createPojo( pojo );
	}

	public ApplyLoanGuaranteeInfo read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, ApplyLoanGuaranteeInfo.TABLENAME_CONSTANT );
	}

	public ApplyLoanGuaranteeInfo readToNull( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId );
	}

	public Long update( ApplyLoanGuaranteeInfoUpdatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notNull( paramBean.getCohabitationFlag() );
		Validate.notBlank( paramBean.getRelationBorrowerType() );

		ApplyLoanGuaranteeInfo pojo = read( paramBean.getLoanId() );
		pojo.setCodeRelationBorrowerType( codeRelationBorrowerTypeDAO.read( paramBean.getRelationBorrowerType() ) );
		pojo.setCodeGuarantyReason( StringUtils
					.isBlank( paramBean.getGuarantyReasonCode() ) ? null : codeGuarantyReasonDAO.read( paramBean.getGuarantyReasonCode() ) );
		pojo.setOtherGuarantyReason( paramBean.getOtherGuarantyReason() );
		pojo.setCohabitationFlag( paramBean.getCohabitationFlag() );

		return pojo.getLoanId();
	}

	@Override
	protected Class<ApplyLoanGuaranteeInfo> getPojoClass()
	{
		return ApplyLoanGuaranteeInfo.class;
	}
}
