package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeUserSubType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeUserSubTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeUserSubTypeDAO extends BasePojoDAO<CodeUserSubType, String>
{
	public CodeUserSubType read( String userSubType )
	{
		Validate.notBlank( userSubType );

		return getPojoByPK( userSubType, CodeUserSubType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeUserSubType> getPojoClass()
	{
		return CodeUserSubType.class;
	}
}
