package com.megabank.olp.apply.controller.management.bean.attachment;

import java.util.Date;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.validator.CheckDateRange;

@CheckDateRange( dateStart = "dateStart", dateEnd = "dateEnd" )
public class AttachmentListedArgBean extends BaseBean
{
	private String transmissionStatusCode;

	private String idNo;

	private Date birthDate;

	private String mobileNumber;

	private String caseNo;

	private Date dateStart;

	private Date dateEnd;

	@NotNull
	@Min( 1 )
	private Integer page;

	private Integer length;

	private String sortColumn;

	private String sortDirection;

	private String branchBankCode;

	public AttachmentListedArgBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return birthDate
	 */
	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	/**
	 *
	 * @return applyDateStart
	 */
	public Date getDateStart()
	{
		return dateStart;
	}

	/**
	 *
	 * @return idNo
	 */
	public String getIdNo()
	{
		return idNo;
	}

	/**
	 *
	 * @return length
	 */
	public Integer getLength()
	{
		return length;
	}

	/**
	 *
	 * @return mobileNumber
	 */
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	/**
	 *
	 * @return page
	 */
	public Integer getPage()
	{
		return page;
	}

	/**
	 *
	 * @return sortColumn
	 */
	public String getSortColumn()
	{
		return sortColumn;
	}

	/**
	 *
	 * @return sortDirection
	 */
	public String getSortDirection()
	{
		return sortDirection;
	}

	/**
	 *
	 * @return transmissionStatus
	 */
	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	/**
	 *
	 * @param birthDate
	 */
	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	/**
	 *
	 * @param idNo
	 */
	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	/**
	 *
	 * @param length
	 */
	public void setLength( Integer length )
	{
		this.length = length;
	}

	/**
	 *
	 * @param mobileNumber
	 */
	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	/**
	 *
	 * @param page
	 */
	public void setPage( Integer page )
	{
		this.page = page;
	}

	/**
	 *
	 * @param sortColumn
	 */
	public void setSortColumn( String sortColumn )
	{
		this.sortColumn = sortColumn;
	}

	/**
	 *
	 * @param sortDirection
	 */
	public void setSortDirection( String sortDirection )
	{
		this.sortDirection = sortDirection;
	}

	/**
	 *
	 * @param transmissionStatusCode
	 */
	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}
}
