package com.megabank.olp.apply.persistence.bean.generated.apply.survey;

import com.megabank.olp.base.bean.BaseBean;

public class ApplySurveyContactCreatedParamBean extends BaseBean
{
	private Long surveyId;

	private String name;

	private Long branchBankId;

	private String phoneCode;

	private String phoneNumber;

	private String phoneExt;

	private String email;

	private String mobileNumber;

	private String contactTimeCode;

	private String otherMsg;

	private String sexCode;

	public ApplySurveyContactCreatedParamBean()
	{
		// default constructor
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getPhoneCode()
	{
		return phoneCode;
	}

	public String getPhoneExt()
	{
		return phoneExt;
	}

	public String getPhoneNumber()
	{
		return phoneNumber;
	}

	public String getSexCode()
	{
		return sexCode;
	}

	public Long getSurveyId()
	{
		return surveyId;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setPhoneCode( String phoneCode )
	{
		this.phoneCode = phoneCode;
	}

	public void setPhoneExt( String phoneExt )
	{
		this.phoneExt = phoneExt;
	}

	public void setPhoneNumber( String phoneNumber )
	{
		this.phoneNumber = phoneNumber;
	}

	public void setSexCode( String sexCode )
	{
		this.sexCode = sexCode;
	}

	public void setSurveyId( Long surveyId )
	{
		this.surveyId = surveyId;
	}

}
