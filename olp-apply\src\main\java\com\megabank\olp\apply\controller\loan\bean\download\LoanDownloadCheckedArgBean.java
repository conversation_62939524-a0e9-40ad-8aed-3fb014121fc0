package com.megabank.olp.apply.controller.loan.bean.download;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class LoanDownloadCheckedArgBean extends BaseBean
{
	@NotBlank
	private String idNo;

	@NotNull
	private Date birthDate;

	public LoanDownloadCheckedArgBean()
	{
		// default constructor
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

}
