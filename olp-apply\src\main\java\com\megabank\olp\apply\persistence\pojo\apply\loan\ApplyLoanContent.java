package com.megabank.olp.apply.persistence.pojo.apply.loan;

import java.util.HashSet;
import java.util.Set;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddress;
import com.megabank.olp.apply.persistence.pojo.code.CodeCaseSource;
import com.megabank.olp.apply.persistence.pojo.code.CodeGracePeriod;
import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPeriod;
import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPurpose;
import com.megabank.olp.apply.persistence.pojo.code.CodeMortgageType;
import com.megabank.olp.apply.persistence.pojo.code.CodeNonPrivateUsageType;
import com.megabank.olp.apply.persistence.pojo.code.CodeNotification;
import com.megabank.olp.apply.persistence.pojo.code.CodePrivateUsageType;
import com.megabank.olp.base.bean.BaseBean;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

/**
 * The ApplyLoanContent is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_content" )
public class ApplyLoanContent extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_content";

	public static final String LOAN_ID_CONSTANT = "loanId";

	public static final String APPLY_ADDRESS_CONSTANT = "applyAddress";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String CODE_GRACE_PERIOD_CONSTANT = "codeGracePeriod";

	public static final String CODE_LOAN_PERIOD_CONSTANT = "codeLoanPeriod";

	public static final String CODE_LOAN_PURPOSE_CONSTANT = "codeLoanPurpose";

	public static final String CODE_MORTGAGE_TYPE_CONSTANT = "codeMortgageType";

	public static final String CODE_NON_PRIVATE_USAGE_TYPE_CONSTANT = "codeNonPrivateUsageType";

	public static final String NON_PRIVATE_USAGE_SUBTYPE_CONSTANT = "nonPrivateUsageSubType";

	public static final String CODE_NOTIFICATION_CONSTANT = "codeNotification";

	public static final String CODE_PRIVATE_USAGE_TYPE_CONSTANT = "codePrivateUsageType";

	public static final String CODE_CASE_SOURCE_CONSTANT = "codeCaseSource";

	public static final String LOAN_REQUEST_AMT_CONSTANT = "loanRequestAmt";

	public static final String OTHER_PURPOSE_CONSTANT = "otherPurpose";

	public static final String INCREASING_LOAN_CONSTANT = "increasingLoan";

	public static final String APPN_BANK_CODE_CONSTANT = "appnBankCode"; // J-110-0373 中鋼消貸線上申請暨對保作業

	public static final String APPN_DP_ACCT_CONSTANT = "appnDpAcct"; // 參考 otherDataBaseClientService.getPbAccount(...)

	public static final String RESTRICT_CONTR_CONSTANT = "restrictContr";

	public static final String APPLY_LOAN_SERVEDS_CONSTANT = "applyLoanServeds";

	public static final String APPLY_LOAN_RELATIONS_CONSTANT = "applyLoanRelations";

	private long loanId;

	private transient ApplyAddress applyAddress;

	private transient ApplyLoan applyLoan;

	private transient CodeGracePeriod codeGracePeriod;

	private transient CodeLoanPeriod codeLoanPeriod;

	private transient CodeLoanPurpose codeLoanPurpose;

	private transient CodeMortgageType codeMortgageType;

	private transient CodeNonPrivateUsageType codeNonPrivateUsageType;

	private transient CodeNotification codeNotification;

	private transient CodeCaseSource codeCaseSource;

	private transient CodePrivateUsageType codePrivateUsageType;

	private int loanRequestAmt;

	private String otherPurpose;

	private Boolean increasingLoan;

	private String appnBankCode;

	private String appnDpAcct; // ApplyService::getLoanApplyInfo( String loanType ) , otherDataBaseClientService.getPbAccount(...)有{實帳戶}科目

	private Boolean restrictContr; // J-111-0042 精銳認股案增加「是否綁約」

	private String nonPrivateUsageSubType;

	private String urlToIdentifyFraud;

	private transient Set<ApplyLoanServed> applyLoanServeds = new HashSet<>( 0 );

	private transient Set<ApplyLoanRelation> applyLoanRelations = new HashSet<>( 0 );

	public ApplyLoanContent()
	{}

	public ApplyLoanContent( ApplyLoan applyLoan, CodeLoanPeriod codeLoanPeriod, CodeLoanPurpose codeLoanPurpose, CodeNotification codeNotification,
							 CodeCaseSource codeCaseSource, int loanRequestAmt )
	{
		this.applyLoan = applyLoan;
		this.codeLoanPeriod = codeLoanPeriod;
		this.codeLoanPurpose = codeLoanPurpose;
		this.codeNotification = codeNotification;
		this.codeCaseSource = codeCaseSource;
		this.loanRequestAmt = loanRequestAmt;
	}

	public ApplyLoanContent( Long loanId )
	{
		this.loanId = loanId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "collateral_address_id" )
	public ApplyAddress getApplyAddress()
	{
		return applyAddress;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyLoanContent" )
	public Set<ApplyLoanRelation> getApplyLoanRelations()
	{
		return applyLoanRelations;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyLoanContent" )
	public Set<ApplyLoanServed> getApplyLoanServeds()
	{
		return applyLoanServeds;
	}

	@Column( name = "appn_bank_code", length = 3 )
	public String getAppnBankCode()
	{
		return appnBankCode;
	}

	@Column( name = "appn_dp_acct", length = 16 )
	public String getAppnDpAcct()
	{
		return appnDpAcct;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "grace_period_code" )
	public CodeGracePeriod getCodeGracePeriod()
	{
		return codeGracePeriod;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "load_period_code", nullable = false )
	public CodeLoanPeriod getCodeLoanPeriod()
	{
		return codeLoanPeriod;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_purpose_id", nullable = false )
	public CodeLoanPurpose getCodeLoanPurpose()
	{
		return codeLoanPurpose;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "mortgage_type" )
	public CodeMortgageType getCodeMortgageType()
	{
		return codeMortgageType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "non_private_usage_type" )
	public CodeNonPrivateUsageType getCodeNonPrivateUsageType()
	{
		return codeNonPrivateUsageType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "notification_code", nullable = false )
	public CodeNotification getCodeNotification()
	{
		return codeNotification;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "case_source_code" )
	public CodeCaseSource getCodeCaseSource()
	{
		return codeCaseSource;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "private_usage_type" )
	public CodePrivateUsageType getCodePrivateUsageType()
	{
		return codePrivateUsageType;
	}

	@Column( name = "increasing_loan", precision = 1, scale = 0 )
	public Boolean getIncreasingLoan()
	{
		return increasingLoan;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "applyLoan" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "loan_id", unique = true, nullable = false )
	public long getLoanId()
	{
		return loanId;
	}

	@Column( name = "loan_request_amt", nullable = false, precision = 8, scale = 0 )
	public int getLoanRequestAmt()
	{
		return loanRequestAmt;
	}

	@Column( name = "non_private_usage_subtype", length = 50 )
	public String getNonPrivateUsageSubType()
	{
		return nonPrivateUsageSubType;
	}

	@Column( name = "other_purpose" )
	public String getOtherPurpose()
	{
		return otherPurpose;
	}

	@Column( name = "restrict_contr", precision = 1, scale = 0 )
	public Boolean getRestrictContr()
	{
		return restrictContr;
	}

	@Column( name = "url_to_identify_fraud", length = 200 )
	public String getUrlToIdentifyFraud()
	{
		return urlToIdentifyFraud;
	}

	public void setApplyAddress( ApplyAddress applyAddress )
	{
		this.applyAddress = applyAddress;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setApplyLoanRelations( Set<ApplyLoanRelation> applyLoanRelations )
	{
		this.applyLoanRelations = applyLoanRelations;
	}

	public void setApplyLoanServeds( Set<ApplyLoanServed> applyLoanServeds )
	{
		this.applyLoanServeds = applyLoanServeds;
	}

	public void setAppnBankCode( String appnBankCode )
	{
		this.appnBankCode = appnBankCode;
	}

	public void setAppnDpAcct( String appnDpAcct )
	{
		this.appnDpAcct = appnDpAcct;
	}

	public void setCodeGracePeriod( CodeGracePeriod codeGracePeriod )
	{
		this.codeGracePeriod = codeGracePeriod;
	}

	public void setCodeLoanPeriod( CodeLoanPeriod codeLoanPeriod )
	{
		this.codeLoanPeriod = codeLoanPeriod;
	}

	public void setCodeLoanPurpose( CodeLoanPurpose codeLoanPurpose )
	{
		this.codeLoanPurpose = codeLoanPurpose;
	}

	public void setCodeMortgageType( CodeMortgageType codeMortgageType )
	{
		this.codeMortgageType = codeMortgageType;
	}

	public void setCodeNonPrivateUsageType( CodeNonPrivateUsageType codeNonPrivateUsageType )
	{
		this.codeNonPrivateUsageType = codeNonPrivateUsageType;
	}

	public void setCodeNotification( CodeNotification codeNotification )
	{
		this.codeNotification = codeNotification;
	}

	public void setCodeCaseSource( CodeCaseSource codeCaseSource )
	{
		this.codeCaseSource = codeCaseSource;
	}

	public void setCodePrivateUsageType( CodePrivateUsageType codePrivateUsageType )
	{
		this.codePrivateUsageType = codePrivateUsageType;
	}

	public void setIncreasingLoan( Boolean increasingLoan )
	{
		this.increasingLoan = increasingLoan;
	}

	public void setLoanId( long loanId )
	{
		this.loanId = loanId;
	}

	public void setLoanRequestAmt( int loanRequestAmt )
	{
		this.loanRequestAmt = loanRequestAmt;
	}

	public void setNonPrivateUsageSubType( String nonPrivateUsageSubType )
	{
		this.nonPrivateUsageSubType = nonPrivateUsageSubType;
	}

	public void setOtherPurpose( String otherPurpose )
	{
		this.otherPurpose = otherPurpose;
	}

	public void setRestrictContr( Boolean restrictContr )
	{
		this.restrictContr = restrictContr;
	}

	public void setUrlToIdentifyFraud( String urlToIdentifyFraud )
	{
		this.urlToIdentifyFraud = urlToIdentifyFraud;
	}
}