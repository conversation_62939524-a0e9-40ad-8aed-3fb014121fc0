package com.megabank.olp.apply.persistence.dto;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class CollateralCountDTO extends BaseBean
{

	private Long collateralId;

	private String providerName;

	public CollateralCountDTO()
	{
		// default constructor
	}

	public Long getCollateralId()
	{
		return collateralId;
	}

	public String getProviderName()
	{
		return providerName;
	}

	public void setCollateralId( Long collateralId )
	{
		this.collateralId = collateralId;
	}

	public void setProviderName( String providerName )
	{
		this.providerName = providerName;
	}

}
