package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeMortgageType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeMortgageTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeMortgageTypeDAO extends BasePojoDAO<CodeMortgageType, String>
{

	public CodeMortgageType read( String mortgageType )
	{
		Validate.notBlank( mortgageType );

		return getPojoByPK( mortgageType, CodeMortgageType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeMortgageType> getPojoClass()
	{
		return CodeMortgageType.class;
	}
}
