package com.megabank.olp.api.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.api.config.ApiConfig;
import com.megabank.olp.api.service.btt.ApplyService;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyCustInfoResultBean;
import com.megabank.olp.system.config.SystemConfig;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@SpringBootTest
@ContextConfiguration( classes = { ApiConfig.class, SystemConfig.class } )
public class ApplyServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private ApplyService service;

	@Test
	public void getApplyCustInfo()
	{
		String caseNo = "PA000001";

		ApplyCustInfoResultBean resultBean = service.getApplyCustInfo( caseNo );

		// logger.info( "resultBean:{}", resultBean );
	}

}
