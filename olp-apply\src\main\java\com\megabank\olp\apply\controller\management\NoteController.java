/**
 *
 */
package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.note.NoteSendArgBean;
import com.megabank.olp.apply.controller.management.bean.note.NotesListGetterArgBean;
import com.megabank.olp.apply.service.management.NoteService;
import com.megabank.olp.apply.service.management.bean.note.NoteCreatedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */
@RestController
@RequestMapping( "management/note" )
public class NoteController extends BaseController
{
	@Autowired
	private NoteService noteService;

	/**
	 * 取得案件/檔案註記
	 *
	 * @param pk
	 * @return
	 */
	@PostMapping( "listNotes" )
	public Map<String, Object> listNotes( @RequestBody @Validated NotesListGetterArgBean argBean )
	{
		return getResponseMap( noteService.getNoteList( argBean.getId(), argBean.getType() ) );
	}

	/**
	 * 送出案件/檔案註記
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "sendNote" )
	public Map<String, Object> sendNote( @RequestBody @Validated NoteSendArgBean argBean )
	{
		String action = "comment";

		NoteCreatedParamBean paramBean = new NoteCreatedParamBean();
		paramBean.setId( argBean.getId() );
		paramBean.setType( argBean.getNoteTypeEnum().getContext() );
		paramBean.setText( argBean.getText() );
		paramBean.setEmployeeName( argBean.getEmployeeName() );
		paramBean.setEmployeeId( argBean.getEmployeeId() );
		paramBean.setAction( action );

		return getResponseMap( noteService.createNote( paramBean ) );
	}
}
