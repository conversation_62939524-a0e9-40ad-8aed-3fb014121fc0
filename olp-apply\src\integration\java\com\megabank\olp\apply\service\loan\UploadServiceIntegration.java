package com.megabank.olp.apply.service.loan;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.upload.FileUploadedParamBean;
import com.megabank.olp.apply.service.loan.bean.upload.FileUploadedResBean;
import com.megabank.olp.apply.service.loan.bean.upload.ThumbnailResBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class UploadServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;
	
	private final String loanType = "personalloan";

	private final String idNo = "A123456789";

	private final Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );

	@Autowired
	private UploadService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void checkLoanApplyExisted()
	{

		boolean result = service.checkLoanApplyExisted( idNo, birthDate, loanType );

		logger.info( "result:{}", result );
	}

	@Test
	public void deleteFile()
	{
		Long uploadFileId = 1L;

		service.deleteFile( loanType, uploadFileId, "**********" );
	}

	@Test
	public void getAgreement()
	{
		// AgreementResBean resBean = service.getAgreement();
		//
		// logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getThankyouMessage()
	{
		MessageResBean resBean = service.getThankyouMessage();

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getThumbnails()
	{
		List<ThumbnailResBean> resBeans = service.getThumbnails( loanType, "**********" );

		logger.info( "resBeans:{}", resBeans );

	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	@Test
	public void submitFiles()
	{
		List<Long> uploadFileIds = Arrays.asList( 1L, 2L );
		String caseNo = "123123123";
		Boolean isAgreed = true;
		String finalBranchCode = "943";

		service.submitFiles( loanType, uploadFileIds, caseNo, isAgreed, finalBranchCode );
	}

	@Test
	public void uploadFile()
	{
		FileUploadedParamBean paramBean = getFileUploadedParamBean();

		FileUploadedResBean resBean = service.uploadFile( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	private FileUploadedParamBean getFileUploadedParamBean()
	{
		String attachmentType = "proof";
		String fileName = "file/input/image.jpg";
		String fileContent = "";

		FileUploadedParamBean paramBean = new FileUploadedParamBean();
		paramBean.setLoanType( loanType );
		paramBean.setAttachmentType( attachmentType );
		paramBean.setFileName( fileName );
		paramBean.setFileContent( fileContent );

		return paramBean;
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.OTHER_BANK.getContext(), IdentityTypeEnum.OTP.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}

}
