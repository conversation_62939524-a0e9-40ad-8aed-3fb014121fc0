/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.personalloan;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

public class ProcessStatusUpdatedArgBean extends BaseBean
{
	@NotBlank
	private String employeeId;

	@NotBlank
	private String employeeName;

	@JsonProperty( "id" )
	@NotNull
	private Long loanId;

	@JsonProperty( "status" )
	@NotBlank
	private String processStatus;

	public ProcessStatusUpdatedArgBean()
	{
		// default constructor
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getProcessStatus()
	{
		return processStatus;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setProcessStatus( String processStatus )
	{
		this.processStatus = processStatus;
	}

}
