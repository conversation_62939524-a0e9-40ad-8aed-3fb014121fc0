package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPlan;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeLoanPlanDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeLoanPlanDAO extends BasePojoDAO<CodeLoanPlan, String>
{
	public CodeLoanPlan read( String loanPlanCode )
	{
		Validate.notNull( loanPlanCode );

		// return getPojoByPK(loanPlanCode, CodeLoanPlan.TABLENAME_CONSTANT);
		return getPojoByPK( loanPlanCode );
	}

	@Override
	protected Class<CodeLoanPlan> getPojoClass()
	{
		return CodeLoanPlan.class;
	}

}
