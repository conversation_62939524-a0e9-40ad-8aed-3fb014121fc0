package com.megabank.olp.apply.controller.loan.bean.apply;

public class LoanApplyCreatedArgBean extends LoanApplyBaseArgBean
{
	private String refCaseNo;

	private String plan;

	private String introduceBrNo;

	public LoanApplyCreatedArgBean()
	{
		// default constructor
	}

	public String getIntroduceBrNo()
	{
		return introduceBrNo;
	}

	public String getPlan()
	{
		return plan;
	}

	public String getRefCaseNo()
	{
		return refCaseNo;
	}

	public void setIntroduceBrNo( String introduceBrNo )
	{
		this.introduceBrNo = introduceBrNo;
	}

	public void setPlan( String plan )
	{
		this.plan = plan;
	}

	public void setRefCaseNo( String refCaseNo )
	{
		this.refCaseNo = refCaseNo;
	}
}
