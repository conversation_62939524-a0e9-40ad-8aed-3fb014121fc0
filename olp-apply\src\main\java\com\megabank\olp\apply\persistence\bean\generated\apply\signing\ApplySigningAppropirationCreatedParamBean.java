/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class ApplySigningAppropirationCreatedParamBean extends BaseBean
{
	private String contractNo;

	private Long validatedIdentityId;

	private String bankAccount;

	private String townCode;

	private Date appropirationDate;

	private Integer repayment;

	private Date firstPaymentDate;

	private String borrowerContractSendingMethod;

	private String guarantorContractSendingMethod;

	private String rateAdjustInformMethod;

	private String address;

	private Date contractCheckDate;

	public ApplySigningAppropirationCreatedParamBean()
	{}

	public String getAddress()
	{
		return address;
	}

	public Date getAppropirationDate()
	{
		return appropirationDate;
	}

	public String getBankAccount()
	{
		return bankAccount;
	}

	public String getBorrowerContractSendingMethod()
	{
		return borrowerContractSendingMethod;
	}

	public Date getContractCheckDate()
	{
		return contractCheckDate;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public Date getFirstPaymentDate()
	{
		return firstPaymentDate;
	}

	public String getGuarantorContractSendingMethod()
	{
		return guarantorContractSendingMethod;
	}

	public String getRateAdjustInformMethod()
	{
		return rateAdjustInformMethod;
	}

	public Integer getRepayment()
	{
		return repayment;
	}

	public String getTownCode()
	{
		return townCode;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setAddress( String address )
	{
		this.address = address;
	}

	public void setAppropirationDate( Date appropirationDate )
	{
		this.appropirationDate = appropirationDate;
	}

	public void setBankAccount( String bankAccount )
	{
		this.bankAccount = bankAccount;
	}

	public void setBorrowerContractSendingMethod( String borrowerContractSendingMethod )
	{
		this.borrowerContractSendingMethod = borrowerContractSendingMethod;
	}

	public void setContractCheckDate( Date contractCheckDate )
	{
		this.contractCheckDate = contractCheckDate;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setFirstPaymentDate( Date firstPaymentDate )
	{
		this.firstPaymentDate = firstPaymentDate;
	}

	public void setGuarantorContractSendingMethod( String guarantorContractSendingMethod )
	{
		this.guarantorContractSendingMethod = guarantorContractSendingMethod;
	}

	public void setRateAdjustInformMethod( String rateAdjustInformMethod )
	{
		this.rateAdjustInformMethod = rateAdjustInformMethod;
	}

	public void setRepayment( Integer repayment )
	{
		this.repayment = repayment;
	}

	public void setTownCode( String townCode )
	{
		this.townCode = townCode;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

}
