package com.megabank.olp.api.utility;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ObjectConversionUtils
{
	/**
	 * 根據 field 相同的名稱轉換物件型態
	 */
	public static <T> T convert( Object source, Class<T> targetClass ) throws InstantiationException, IllegalAccessException
	{
		T target = targetClass.newInstance();
		Map<String, Object> sourceFieldValues = new HashMap<>();

		Field[] sourceFields = source.getClass().getDeclaredFields();

		for( Field sourceField : sourceFields )
		{
			sourceField.setAccessible( true );
			sourceFieldValues.put( sourceField.getName(), sourceField.get( source ) );
		}

		Field[] targetFields = targetClass.getDeclaredFields();

		for( Field targetField : targetFields )
		{
			targetField.setAccessible( true );
			if( sourceFieldValues.containsKey( targetField.getName() ) )
			{
				targetField.set( target, sourceFieldValues.get( targetField.getName() ) );
			}
		}

		return target;
	}
}
