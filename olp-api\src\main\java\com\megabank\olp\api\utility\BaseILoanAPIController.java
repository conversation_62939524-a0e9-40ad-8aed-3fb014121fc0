package com.megabank.olp.api.utility;

import org.springframework.beans.factory.annotation.Value;

import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.security.CommonSecurityUtils;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

public class BaseILoanAPIController extends BaseController
{
	@Value( "${iloan.secret.token}" )
	private String secretToken;

	@Value( "${iloan.key}" )
	private String iloanKey;

	public String getSecretToken()
	{
		return secretToken;
	}

	public void setSecretToken( String secretToken )
	{
		this.secretToken = secretToken;
	}

	protected void validAuth( String accessToken )
	{
		String text = CommonSecurityUtils.decrypt( accessToken, iloanKey );
		if( !secretToken.equals( text ) )
			throw new MyRuntimeException( SystemErrorEnum.ACCESS_DENIED );
	}
}
