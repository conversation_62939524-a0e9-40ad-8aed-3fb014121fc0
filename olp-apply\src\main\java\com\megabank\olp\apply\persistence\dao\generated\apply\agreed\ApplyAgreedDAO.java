package com.megabank.olp.apply.persistence.dao.generated.apply.agreed;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreed;
import com.megabank.olp.base.enums.ServiceTypeEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyAgreedDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyAgreedDAO extends BasePojoDAO<ApplyAgreed, Long>
{

	@SuppressWarnings( { "rawtypes", "unchecked" } )
	public List<ApplyAgreed> getApplyServiceAgreeds( String userType, String identityType, String serviceType )
	{
		Validate.notBlank( userType );
		Validate.notBlank( identityType );
		Validate.notBlank( serviceType );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "agreed.getApplyServiceAgreeds" );
		nativeQuery.setParameter( "userType", userType, String.class );
		nativeQuery.setParameter( "identityType", identityType, String.class );
		nativeQuery.setParameter( "serviceType", serviceType, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyAgreed.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( { "rawtypes", "unchecked" } )
	public List<ApplyAgreed> getContractServiceAgreeds( String userType, String serviceType )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "agreed.getContractServiceAgreeds" );
		nativeQuery.setParameter( "userType", userType, String.class );
		nativeQuery.setParameter( "serviceType", serviceType, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyAgreed.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( { "rawtypes", "unchecked" } )
	public List<ApplyAgreed> getRepaymentContractServiceAgreeds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "agreed.getRepaymentContractServiceAgreeds" );
		nativeQuery.setParameter( "contractType", "repayment", String.class );
		nativeQuery.setParameter( "serviceType", ServiceTypeEnum.SIGNING_CONTRACT.getContext(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyAgreed.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( { "rawtypes", "unchecked" } )
	public List<ApplyAgreed> getUploadServiceAgreeds( String identityType )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "agreed.getApplyServiceAgreeds" );
		nativeQuery.setParameter( "userType", null, String.class );
		nativeQuery.setParameter( "identityType", identityType, String.class );
		nativeQuery.setParameter( "serviceType", ServiceTypeEnum.UPLOAD.getContext(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyAgreed.class );

		return nativeQuery.getResultList();
	}

	public List<ApplyAgreed> getHouseContactAgreeds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "agreed.getApplyServiceAgreeds" );
		nativeQuery.setParameter( "userType", null, String.class );
		nativeQuery.setParameter( "identityType", null, String.class );
		nativeQuery.setParameter( "serviceType", ServiceTypeEnum.HOUSE_CONTACT.getContext(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyAgreed.class );

		return nativeQuery.getResultList();
	}

	public ApplyAgreed read( Long agreedId )
	{
		Validate.notNull( agreedId );

		return getPojoByPK( agreedId, ApplyAgreed.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<ApplyAgreed> getPojoClass()
	{
		return ApplyAgreed.class;
	}
}
