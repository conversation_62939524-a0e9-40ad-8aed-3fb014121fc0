package com.megabank.olp.apply.persistence.dao.generated.house;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.house.LoanTrialBasicCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.house.HouseLoanBasicInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseLoanBasicInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseLoanBasicInfoDAO extends BasePojoDAO<HouseLoanBasicInfo, Long>
{
	@Autowired
	private HouseLoanTrialInfoDAO houseLoanTrialInfoDAO;

	public Long create( LoanTrialBasicCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanTrialId() );

		HouseLoanBasicInfo pojo = new HouseLoanBasicInfo();
		pojo.setHouseLoanTrialInfo( houseLoanTrialInfoDAO.read( paramBean.getLoanTrialId() ) );
		pojo.setUserAge( paramBean.getUserAge() );
		pojo.setUserChildren( paramBean.getUserChildren() );
		pojo.setUserIncome( paramBean.getUserIncome() );
		pojo.setBuyHouseFrom( paramBean.getBuyHouseFrom() );
		pojo.setJobType( paramBean.getJobType() );
		pojo.setTitleType( paramBean.getTitleType() );
		pojo.setLoanPurpose( paramBean.getLoanPurpose() );
		pojo.setTotalPrice( paramBean.getTotalPrice() );
		pojo.setCreditcard( paramBean.getCreditcard() );
		pojo.setPay( paramBean.getPay() );
		pojo.setBorrow( paramBean.getBorrow() );
		pojo.setCashCard( paramBean.getCashCard() );
		pojo.setCashCardBalance( paramBean.getCashCardBalance() );
		pojo.setBalance( paramBean.getBalance() );

		return super.createPojo( pojo );
	}

	@Override
	protected Class<HouseLoanBasicInfo> getPojoClass()
	{
		return HouseLoanBasicInfo.class;
	}
}
