package com.megabank.olp.apply.service.management;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.download.FileDownloadedResBean;
import com.megabank.olp.apply.service.management.bean.survey.LoanSurveyDetailResBean;
import com.megabank.olp.apply.service.management.bean.survey.LoanSurveyExportedParamBean;
import com.megabank.olp.apply.service.management.bean.survey.LoanSurveyListedParamBean;
import com.megabank.olp.apply.service.management.bean.survey.LoanSurveyListedResBean;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class LoanSurveyServiceIntegration
{
	@Autowired
	private LoanSurveyService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void exportLoanSurvey()
	{
		LoanSurveyExportedParamBean paramBean = new LoanSurveyExportedParamBean();

		FileDownloadedResBean resBean = service.exportList( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getLoanSurveyDetail()
	{
		Long surveyId = 1L;

		LoanSurveyDetailResBean resBean = service.getLoanSurveyDetail( surveyId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listLoanSurvey()
	{
		LoanSurveyListedParamBean paramBean = new LoanSurveyListedParamBean();
		paramBean.setPage( 1 );

		LoanSurveyListedResBean resBean = service.listLoanSurvey( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void updateProcessStatus()
	{
		Long surveyId = 1L;
		String processCode = "processing";
		String employeeId = "developer-01";
		String employeeName = "developer";

		Long id = service.updateProcessStatus( surveyId, processCode, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	private LoanSurveyListedParamBean getLoanSurveyListParamBean()
	{
		String name = "王大明";
		String mobileNumber = "0912345678";
		Date applyDateStart = CommonDateUtils.getDate( 2020, 1, 1 );
		Date applyDateEnd = CommonDateUtils.getDate( 2020, 12, 31 );
		String sortColumn = "surveyDate";
		String sortDirection = "desc";
		int page = 1;
		int length = 10;

		LoanSurveyListedParamBean paramBean = new LoanSurveyListedParamBean();
		paramBean.setName( name );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setDateStart( applyDateStart );
		paramBean.setDateEnd( applyDateEnd );
		paramBean.setPage( page );
		paramBean.setLength( length );
		paramBean.setSortColumn( sortColumn );
		paramBean.setSortDirection( sortDirection );

		return paramBean;
	}
}
