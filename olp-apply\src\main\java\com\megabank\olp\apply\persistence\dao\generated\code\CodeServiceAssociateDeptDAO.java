package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeServiceAssociateDept;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeServiceAssociateDeptDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeServiceAssociateDeptDAO extends BasePojoDAO<CodeServiceAssociateDept, String>
{
	public CodeServiceAssociateDept read( String serviceAssociateDeptCode )
	{
		Validate.notBlank( serviceAssociateDeptCode );

		return getPojoByPK( serviceAssociateDeptCode, CodeServiceAssociateDept.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeServiceAssociateDept> getPojoClass()
	{
		return CodeServiceAssociateDept.class;
	}
}
