package com.megabank.olp.apply.persistence.dao.generated.house;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.house.ContactCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProcessDAO;
import com.megabank.olp.apply.persistence.pojo.house.HouseContactInfo;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseContactInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseContactInfoDAO extends BasePojoDAO<HouseContactInfo, Long>
{
	private static final String CONTACT_INFO_ID_CONSTANT = "contactInfoId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String CURRENT_TIME_CONSTANT = "currentTime";

	@Autowired
	private CodeProcessDAO codeProcessDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	public Long create( ContactCreatedParamBean paramBean, Boolean isBankCode )
	{
		Validate.notBlank( paramBean.getCaseNo() );
		Validate.notNull( paramBean.getCreatedDate() );
		Validate.notBlank( paramBean.getMobileNumber() );
		Validate.notNull( paramBean.getProcessCode() );

		if ( isBankCode )
			Validate.notNull( paramBean.getBranchBankCode() );
		else
			Validate.notNull( paramBean.getBranchBankId() );

		HouseContactInfo pojo = new HouseContactInfo();
		pojo.setCaseNo( paramBean.getCaseNo() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );

		if( isBankCode )
			pojo.setCodeBranchBank( codeBranchBankDAO.getPojoByBankCode( paramBean.getBranchBankCode() ) );
		else
			pojo.setCodeBranchBank( codeBranchBankDAO.read( paramBean.getBranchBankId() ) );

		pojo.setCodeProcess( codeProcessDAO.read( paramBean.getProcessCode() ) );
		pojo.setCreatedDate( paramBean.getCreatedDate() );
		pojo.setUpdatedDate( new Date() );
		pojo.setOtherMsg( paramBean.getOtherMsg() );
		pojo.setLoanPlanCode( paramBean.getLoanPlanCode() );

		return super.createPojo( pojo );
	}

	@SuppressWarnings( "rawtypes" )
	public Long getLatestId()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housecontact.getLatestId" );

		return ( Long )nativeQuery.uniqueResult();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedBankIds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housecontact.getBranchBankIds" );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedContactInfoIds( Long branchBankId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housecontact.getContactInfoIds" );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	public HouseContactInfo getPojoByCaseNoToNull( String caseNo )
	{
		Validate.notBlank( caseNo );

		NameValueBean condition = new NameValueBean( HouseContactInfo.CASE_NO_CONSTANT, caseNo );

		return getUniquePojoByProperty( condition );
	}

	public HouseContactInfo read( Long houseContactId )
	{
		Validate.notNull( houseContactId );

		return getPojoByPK( houseContactId, HouseContactInfo.TABLENAME_CONSTANT );
	}

	public Long updateBranchBank( Long houseContactId, Long branchBankId )
	{
		Validate.notNull( houseContactId );
		Validate.notNull( branchBankId );

		HouseContactInfo pojo = read( houseContactId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( branchBankId ) );
		pojo.setNotified( false );
		pojo.setUpdatedDate( new Date() );

		return pojo.getContactInfoId();

	}

	public int updateNotified( List<Long> contactInfoIds )
	{
		Validate.notEmpty( contactInfoIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housecontact.updateNotified" );
		nativeQuery.setParameterList( CONTACT_INFO_ID_CONSTANT, contactInfoIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long houseContactId )
	{
		Validate.notNull( houseContactId );

		HouseContactInfo pojo = read( houseContactId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getContactInfoId();
	}

	public Long updateProcess( Long houseContactId, String processCode )
	{
		Validate.notNull( houseContactId );
		Validate.notBlank( processCode );

		HouseContactInfo pojo = read( houseContactId );
		pojo.setCodeProcess( codeProcessDAO.read( processCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getContactInfoId();
	}

	@Override
	protected Class<HouseContactInfo> getPojoClass()
	{
		return HouseContactInfo.class;
	}

	public List<Long> getRepeatCasesIn1Day( String mobileNumber )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housecontact.getRepeatCasesIn1Day" );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, mobileNumber, String.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );

		return nativeQuery.getResultList();
	}

}
