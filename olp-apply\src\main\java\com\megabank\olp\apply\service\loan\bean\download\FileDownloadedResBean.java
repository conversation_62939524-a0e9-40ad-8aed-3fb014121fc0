package com.megabank.olp.apply.service.loan.bean.download;

import com.megabank.olp.base.bean.BaseBean;

public class FileDownloadedResBean extends BaseBean
{
	private transient String fileContent;

	private String fileName;

	public FileDownloadedResBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return fileContent
	 */
	public String getFileContent()
	{
		return fileContent;
	}

	/**
	 *
	 * @return fileName
	 */
	public String getFileName()
	{
		return fileName;
	}

	/**
	 *
	 * @param fileContent
	 */
	public void setFileContent( String fileContent )
	{
		this.fileContent = fileContent;
	}

	/**
	 *
	 * @param fileName
	 */
	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

}