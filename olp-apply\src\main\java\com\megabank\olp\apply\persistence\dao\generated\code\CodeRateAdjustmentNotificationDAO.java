package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeRateAdjustmentNotification;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeRateAdjustmentNotificationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeRateAdjustmentNotificationDAO extends BasePojoDAO<CodeRateAdjustmentNotification, String>
{
	public CodeRateAdjustmentNotification read( String notificationCode )
	{
		Validate.notBlank( notificationCode );

		return getPojoByPK( notificationCode, CodeRateAdjustmentNotification.TABLENAME_CONSTANT );
	}

	public CodeRateAdjustmentNotification readToNull( String notificationCode )
	{
		Validate.notNull( notificationCode );

		return getPojoByPK( notificationCode );
	}

	@Override
	protected Class<CodeRateAdjustmentNotification> getPojoClass()
	{
		return CodeRateAdjustmentNotification.class;
	}
}
