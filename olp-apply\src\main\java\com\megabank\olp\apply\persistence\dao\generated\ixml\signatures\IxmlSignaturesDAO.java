package com.megabank.olp.apply.persistence.dao.generated.ixml.signatures;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.pojo.ixml.signatures.IxmlSignatures;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.base.utility.text.CommonBase64Utils;

@Repository
public class IxmlSignaturesDAO extends BasePojoDAO<IxmlSignatures, Long>
{
	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	public Long create( Long loanId, String id, String recivedSignature ) throws UnsupportedEncodingException
	{
		Validate.notNull( loanId );
		Validate.notNull( recivedSignature );

		IxmlSignatures pojo = new IxmlSignatures();
		pojo.setLoanId( loanId );
		pojo.setIdNo( id );
		pojo.setCreatedDate( new Date() );
		pojo.setRecivedSignature( recivedSignature );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( TransmissionStatusEnum.NO.getContext() ) );

		// String sendSignature = recivedSignature.replace( "\\", "" ).replace( "\"", "\\\"" );
		String sendSignature = recivedSignature.replace( "\\", "" );
		String base64Signature = CommonBase64Utils.base64Encoder( sendSignature.getBytes( "UTF-8" ) );

		pojo.setSendContent( new ImmutableByteArray( base64Signature.getBytes() ) );

		return super.createPojo( pojo );
	}

	@SuppressWarnings( "unchecked" )
	public List<IxmlSignatures> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "ixml.signatures.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( IxmlSignatures.class );

		return nativeQuery.getResultList();
	}

	public boolean isUserHaveIxmlToday( String idNo )
	{
		Validate.notNull( idNo );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "ixml.signatures.isUserHaveIxmlToday" );
		nativeQuery.setParameter( "idNo", idNo, String.class );
		nativeQuery.setParameter( "currentTime", new Date(), Date.class );

		return !nativeQuery.getResultList().isEmpty();
	}

	public IxmlSignatures read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, IxmlSignatures.TABLENAME_CONSTANT );
	}

	public Long updateResend( Long signatureId )
	{
		Validate.notNull( signatureId );

		IxmlSignatures pojo = read( signatureId );
		pojo.setResend( pojo.getResend() + 1 );

		return pojo.getLoanId();
	}

	public Long updateTransmissionStatus( Long signatureId, String transmissionStatusCode )
	{
		Validate.notNull( signatureId );

		IxmlSignatures pojo = read( signatureId );

		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );

		return pojo.getLoanId();
	}

	@Override
	protected Class<IxmlSignatures> getPojoClass()
	{
		return IxmlSignatures.class;
	}
}
