package com.megabank.olp.api.persistence.dao.mixed;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class ApiRequestDAO extends BaseDAO
{

	public List<String> getRequestUrl( String clientAddress )
	{
		Validate.notBlank( clientAddress );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "request.getRequestUrl" );
		nativeQuery.setParameter( "clientAddress", clientAddress, String.class );

		return nativeQuery.getResultList();
	}

}
