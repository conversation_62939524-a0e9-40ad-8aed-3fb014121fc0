package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeEducationLevel;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeEducationLevelDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeEducationLevelDAO extends BasePojoDAO<CodeEducationLevel, String>
{

	public List<CodeEducationLevel> getList()
	{
		return getAllPojos();
	}

	public CodeEducationLevel read( String marriageStatusCode )
	{
		Validate.notBlank( marriageStatusCode );

		return getPojoByPK( marriageStatusCode, CodeEducationLevel.TABLENAME_CONSTANT );
	}

	public CodeEducationLevel readToNull( String marriageStatusCode )
	{
		Validate.notBlank( marriageStatusCode );

		return getPojoByPK( marriageStatusCode );
	}

	@Override
	protected Class<CodeEducationLevel> getPojoClass()
	{
		return CodeEducationLevel.class;
	}
}
