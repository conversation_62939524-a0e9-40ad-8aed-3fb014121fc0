package com.megabank.olp.apply.controller.management.bean.survey;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class LoanSurveyDetailGetterArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long surveyId;

	public LoanSurveyDetailGetterArgBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return surveyId
	 */
	public Long getSurveyId()
	{
		return surveyId;
	}

	/**
	 *
	 * @param surveyId
	 */
	public void setSurveyId( Long surveyId )
	{
		this.surveyId = surveyId;
	}
}
