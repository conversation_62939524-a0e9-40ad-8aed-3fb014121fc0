package com.megabank.olp.api.controller.eloan.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

import java.math.BigDecimal;

public class PayeeInfoBean extends BaseBean
{
	private BigDecimal payeeInfoType;

	private String payeeInfoAccountType;

	public PayeeInfoBean()
	{

	}

	public BigDecimal getPayeeInfoType()
	{
		return payeeInfoType;
	}

	public void setPayeeInfoType( BigDecimal payeeInfoType )
	{
		this.payeeInfoType = payeeInfoType;
	}

	public String getPayeeInfoAccountType()
	{
		return payeeInfoAccountType;
	}

	public void setPayeeInfoAccountType( String payeeInfoAccountType )
	{
		this.payeeInfoAccountType = payeeInfoAccountType;
	}
}
