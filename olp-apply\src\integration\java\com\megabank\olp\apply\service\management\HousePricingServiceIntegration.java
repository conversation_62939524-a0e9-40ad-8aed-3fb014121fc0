package com.megabank.olp.apply.service.management;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.download.FileDownloadedResBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HouseDataBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HouseEstimateDataBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingDetailResBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingExportedParamBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingListedParamBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingListedResBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingSendCaseParamBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HousePricingServiceIntegration
{
	@Autowired
	private HousePricingService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long result = service.create( getPricingSendCaseParamBean() );

		logger.info( "result:{}", result );
	}

	@Test
	public void exportList()
	{
		HousePricingExportedParamBean paramBean = new HousePricingExportedParamBean();

		FileDownloadedResBean resBean = service.exportList( paramBean );

		logger.info( "resBean:{}", resBean );

	}

	@Test
	public void getDetail()
	{
		Long housePricingId = 1L;

		HousePricingDetailResBean resBean = service.getDetail( housePricingId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listLoanTrial()
	{
		HousePricingListedParamBean paramBean = new HousePricingListedParamBean();
		paramBean.setPage( 1 );

		HousePricingListedResBean resBean = service.listHousePricing( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void updateProcessStatus()
	{
		Long housePricingId = 1L;
		String processCode = "processing";
		String employeeId = "developer-01";
		String employeeName = "developer";

		Long id = service.updateProcessStatus( housePricingId, processCode, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	private HousePricingSendCaseParamBean getPricingSendCaseParamBean()
	{
		HousePricingSendCaseParamBean paramBean = new HousePricingSendCaseParamBean();

		paramBean.setCaseNo( "HoPriTest001" );
		paramBean.setCreatedDate( new Date() );
		paramBean.setEmail( "" );
		paramBean.setMobileNumber( "**********" );
		paramBean.setBranchBankCode( "007" );

		HouseDataBean housedData = new HouseDataBean();
		housedData.setAddr( "爪洼路123號" );
		housedData.setbAge( 16 );
		housedData.setbArea( new BigDecimal( 77.2 ) );
		housedData.setHouseType( "2" );
		housedData.setCounty( "台北市" );
		housedData.setDistrict( "天龍區" );
		housedData.setFloors( 4 );
		housedData.setLevel( 2 );
		housedData.setLevelSelect( 0 );
		housedData.setParkingP( new BigDecimal( 5 ) );
		housedData.setParkingGty( 2 );
		housedData.setHouseParkingCode( "2" );
		paramBean.setHouseInfo( housedData );

		HouseEstimateDataBean estimateData = new HouseEstimateDataBean();
		estimateData.setAv750( new BigDecimal( 99.9 ) );
		paramBean.setEstimateInfo( estimateData );

		return paramBean;
	}
}
