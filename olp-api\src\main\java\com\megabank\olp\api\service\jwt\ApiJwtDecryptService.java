package com.megabank.olp.api.service.jwt;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.megabank.olp.base.service.jwt.decrypt.JwtService;

@Service
public class ApiJwtDecryptService extends JwtService
{
	@Value( "${eloan.secret.token}" )
	private String jwtSecretApi;

	@Override
	protected String getCurrentJwtSecret()
	{
		return jwtSecretApi;
	}
}
