package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class SurveyListDTO extends BaseBean
{
	private Long surveyId;

	private String caseNo;

	private String name;

	private String mobileNumber;

	private Date createdDate;

	private String processStatus;

	private String branchBank;

	private String notificationStatus;

	private String contactTime;

	private String loanPlanCode;

	private String loanPlanName;

	public SurveyListDTO()
	{
		// default constructor
	}

	public String getBranchBank()
	{
		return branchBank;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public String getContactTime()
	{
		return contactTime;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatus()
	{
		return notificationStatus;
	}

	public String getProcessStatus()
	{
		return processStatus;
	}

	public Long getSurveyId()
	{
		return surveyId;
	}

	public String getLoanPlanCode() {
		return loanPlanCode;
	}

	public String getLoanPlanName() {
		return loanPlanName;
	}

	public void setBranchBank(String branchBank )
	{
		this.branchBank = branchBank;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setContactTime( String contactTime )
	{
		this.contactTime = contactTime;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatus( String notificationStatus )
	{
		this.notificationStatus = notificationStatus;
	}

	public void setProcessStatus( String processStatus )
	{
		this.processStatus = processStatus;
	}

	public void setSurveyId( Long surveyId )
	{
		this.surveyId = surveyId;
	}

	public void setLoanPlanCode(String loanPlanCode) {
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanPlanName(String loanPlanName) {
		this.loanPlanName = loanPlanName;
	}
}