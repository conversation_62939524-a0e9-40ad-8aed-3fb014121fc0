package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContactInfoCreatedParamBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyLoanContactInfoDAOIntegration
{
	@Autowired
	private ApplyLoanContactInfoDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long loanId = 1L;
		String residenceStatusCode = "01";
		String houseStatusCode = "01";
		String homePhoneCode = "02";
		String homePhoneNumber = "********";
		String email = "<EMAIL>";
		String mobileNumber = "**********";

		ApplyLoanContactInfoCreatedParamBean paramBean = new ApplyLoanContactInfoCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setResidenceStatusCode( residenceStatusCode );
		paramBean.setHouseStatusCode( houseStatusCode );
		paramBean.setHomePhoneCode( homePhoneCode );
		paramBean.setHomePhoneNumber( homePhoneNumber );
		paramBean.setEmail( email );
		paramBean.setMobileNumber( mobileNumber );

		Long id = dao.create( paramBean );

		logger.info( "id:{}", id );
	}

}
