package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.megabank.olp.apply.persistence.dto.LoanCountDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.base.enums.LoanTypeEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyLoanDAOIntegration
{
	@Autowired
	private ApplyLoanDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long validatedIdentityId = 1L;
		String caseNo = "000001";
		String loanType = "personalloan";
		String applyStatusCode = "complete-agreed";
		String transmissionStatusCode = "no";
		String processCode = "unprocessed";

		ApplyLoanCreatedParamBean paramBean = new ApplyLoanCreatedParamBean();
		paramBean.setValidatedIdentityId( validatedIdentityId );
		paramBean.setCaseNo( caseNo );
		paramBean.setLoanType( loanType );
		paramBean.setApplyStatusCode( applyStatusCode );
		paramBean.setTransmissionStatusCode( transmissionStatusCode );
		paramBean.setProcessCode( processCode );
		paramBean.setIdentityFlag( 0 );

		Long id = dao.create( paramBean );

		logger.info( "id:{}", id );
	}

	@Test
	public void getNeedToNotifiedBankIds()
	{
		List<String> applyStatusCode = Arrays.asList( "complete-confirmed" );
		String loanType = "personalloan";

		List<Long> result = dao.getNeedToNotifiedBankIds( applyStatusCode, loanType, 0 );

		logger.info( "result:{}", result );
	}

	@Test
	public void getNeedToNotifiedLoanCountByBranch()
	{
		List<String> applyStatusCode = Arrays.asList( "complete-confirmed" );
		String loanType = "personalloan";
		Long finalBankBankId = 109L;

		List<LoanCountDTO> result = dao.getNeedToNotifiedLoanCountByBranch( applyStatusCode, loanType, finalBankBankId, 0 );

		logger.info( "result:{}", result );
	}

	@Test
	public void getPojoByCaseNo()
	{
		String caseNo = "123456";

		ApplyLoan pojo = dao.getPojoByCaseNo( caseNo );

		logger.info( "pojo:{}", pojo );
	}

	@Test
	public void getPojoByPkAndLoanType()
	{
		Long loanId = 1L;
		String loanType = LoanTypeEnum.HOUSE_LOAN.getContext();

		ApplyLoan pojo = dao.getPojoByPkAndLoanType( loanId, loanType );

		logger.info( "pojo:{}", pojo );
	}

	@Test
	public void read()
	{
		Long loanId = 111L;

		ApplyLoan pojo = dao.read( loanId );

		logger.info( "pojo:{}", pojo );
	}

	@Test
	public void updateRefCase()
	{
		Long loanId = 1L;
		String refCaseNo = "000002";

		Long id = dao.updateRefCase( loanId, refCaseNo );

		logger.info( "id:{}", id );
	}
	
	@Test
	public void updateNotified()
	{
		List<Long> l = Arrays.asList( new Long[] { 1L, 2L, 3L } );

		int result = dao.updateNotified( l );

		logger.info( "result:{}", result );
	}

}
