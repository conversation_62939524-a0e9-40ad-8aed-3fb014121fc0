package com.megabank.olp.apply.controller.management.bean.attachment;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.apply.utility.enums.TransmissionStatusUpdatedEnum;
import com.megabank.olp.base.bean.BaseBean;

public class TransmissionStatusUpdatedArgBean extends BaseBean
{
	@NotBlank
	private String employeeId;

	@NotBlank
	private String employeeName;

	@JsonProperty( "id" )
	@NotNull
	private Long attachmentId;

	@JsonProperty( "action" )
	@NotNull
	private TransmissionStatusUpdatedEnum transmissionStatusUpdatedEnum;

	public TransmissionStatusUpdatedArgBean()
	{
		// default constructor
	}

	public Long getAttachmentId()
	{
		return attachmentId;
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public TransmissionStatusUpdatedEnum getTransmissionStatusUpdatedEnum()
	{
		return transmissionStatusUpdatedEnum;
	}

	public void setAttachmentId( Long attachmentId )
	{
		this.attachmentId = attachmentId;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setTransmissionStatusUpdatedEnum( TransmissionStatusUpdatedEnum transmissionStatusUpdatedEnum )
	{
		this.transmissionStatusUpdatedEnum = transmissionStatusUpdatedEnum;
	}
}
