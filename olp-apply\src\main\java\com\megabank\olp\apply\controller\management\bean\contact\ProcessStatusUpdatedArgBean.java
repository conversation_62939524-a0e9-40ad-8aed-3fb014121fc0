/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.contact;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class ProcessStatusUpdatedArgBean extends BaseBean
{
	@NotBlank
	private String employeeId;

	@NotBlank
	private String employeeName;

	@JsonProperty( "id" )
	@NotNull
	private Long contactMeId;

	@JsonProperty( "status" )
	@NotNull
	private String processStatus;

	public ProcessStatusUpdatedArgBean()
	{
		// default constructor
	}

	public Long getContactMeId()
	{
		return contactMeId;
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public String getProcessStatus()
	{
		return processStatus;
	}

	public void setContactMeId( Long contactMeId )
	{
		this.contactMeId = contactMeId;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setProcessStatus( String processStatus )
	{
		this.processStatus = processStatus;
	}

}
