package com.megabank.olp.api.persistence.pojo.api;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApiRequestUrl is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "api_request_url", uniqueConstraints = @UniqueConstraint( columnNames = "request_url" ) )
public class ApiRequestUrl extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "api_request_url";

	public static final String REQUEST_URL_ID_CONSTANT = "requestUrlId";

	public static final String REQUEST_URL_CONSTANT = "requestUrl";

	public static final String API_REQUEST_URL_CLIENTS_CONSTANT = "apiRequestUrlClients";

	private Long requestUrlId;

	private String requestUrl;

	private transient Set<ApiRequestUrlClient> apiRequestUrlClients = new HashSet<>( 0 );

	public ApiRequestUrl()
	{}

	public ApiRequestUrl( Long requestUrlId )
	{
		this.requestUrlId = requestUrlId;
	}

	public ApiRequestUrl( String requestUrl )
	{
		this.requestUrl = requestUrl;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "apiRequestUrl" )
	public Set<ApiRequestUrlClient> getApiRequestUrlClients()
	{
		return apiRequestUrlClients;
	}

	@Column( name = "request_url", unique = true, nullable = false, length = 500 )
	public String getRequestUrl()
	{
		return requestUrl;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "request_url_id", unique = true, nullable = false )
	public Long getRequestUrlId()
	{
		return requestUrlId;
	}

	public void setApiRequestUrlClients( Set<ApiRequestUrlClient> apiRequestUrlClients )
	{
		this.apiRequestUrlClients = apiRequestUrlClients;
	}

	public void setRequestUrl( String requestUrl )
	{
		this.requestUrl = requestUrl;
	}

	public void setRequestUrlId( Long requestUrlId )
	{
		this.requestUrlId = requestUrlId;
	}
}