package com.megabank.olp.apply.service.loan.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

public class BankAccountResBean extends BaseBean
{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String bankAccount;

	private String bankCode;

	public String getBankAccount()
	{
		return bankAccount;
	}

	public String getBankCode()
	{
		return bankCode;
	}

	public void setBankAccount( String bankAccount )
	{
		this.bankAccount = bankAccount;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}
}
