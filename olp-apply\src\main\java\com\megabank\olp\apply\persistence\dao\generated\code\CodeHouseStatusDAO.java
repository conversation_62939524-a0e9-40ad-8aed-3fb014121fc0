package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeHouseStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeHouseStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeHouseStatusDAO extends BasePojoDAO<CodeHouseStatus, String>
{
	public List<CodeHouseStatus> getList()
	{
		return getAllPojos();
	}

	public CodeHouseStatus read( String houseStatusCode )
	{
		Validate.notBlank( houseStatusCode );

		return getPojoByPK( houseStatusCode, CodeHouseStatus.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeHouseStatus> getPojoClass()
	{
		return CodeHouseStatus.class;
	}
}
