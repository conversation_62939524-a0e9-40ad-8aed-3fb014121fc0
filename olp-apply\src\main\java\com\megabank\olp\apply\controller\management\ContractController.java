/**
 *
 */
package com.megabank.olp.apply.controller.management;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.signing.ContractCtrTypeCMgmtArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.ExpireInfoCreateArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.InterestInfoCreateArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.LoanConditionInfoBean;
import com.megabank.olp.apply.controller.management.bean.signing.LoanPurposeInfoBean;
import com.megabank.olp.apply.controller.management.bean.signing.PayeeInfoCreateArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.PaymentInfoCreateArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.RepaymentInfoCreateArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.SigningContractDiscardArgBean;
import com.megabank.olp.apply.controller.management.bean.signing.SigningContractSendArgBean;
import com.megabank.olp.apply.service.management.ContractService;
import com.megabank.olp.apply.service.management.bean.signing.ContractCreatedParamBean;
import com.megabank.olp.apply.service.management.bean.signing.ContractCtrTypeCMgmtParamBean;
import com.megabank.olp.apply.service.management.bean.signing.ExpireInfoCreateParamBean;
import com.megabank.olp.apply.service.management.bean.signing.InterestInfoCreateParamBean;
import com.megabank.olp.apply.service.management.bean.signing.LoanConditionDataBean;
import com.megabank.olp.apply.service.management.bean.signing.LoanPurposeDataBean;
import com.megabank.olp.apply.service.management.bean.signing.PayeeInfoCreateParamBean;
import com.megabank.olp.apply.service.management.bean.signing.PaymentInfoCreateParamBean;
import com.megabank.olp.apply.service.management.bean.signing.RepaymentInfoCreateParamBean;
import com.megabank.olp.apply.utility.ObjectConversionUtils;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.utility.web.CommonAppUtils;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft1ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft2ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft3ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft4ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft5ContentBean;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/signingcontract" )
public class ContractController extends BaseController
{
	@Autowired
	private ContractService service;

	/**
	 * 變更對保案件失效
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "discardSigningContract" )
	public Map<String, Object> discardSigningContract( @RequestBody @Validated SigningContractDiscardArgBean argBean )
	{
		return getResponseMap( service.setDiscard( argBean.getContractNo() ) );
	}

	@PostMapping( "sendContractCtrTypeC" )
	public Map<String, Object> sendContractCtrTypeC( @RequestBody @Validated ContractCtrTypeCMgmtArgBean argBean )
	{
		return getResponseMap( service.createCtrTypeCContract( mapCtrTypeCParamBean( argBean ) ) );
	}

	@PostMapping( "sendPaymentInfo" )
	public Map<String, Object> sendPaymentInfo( @RequestBody @Validated PaymentInfoCreateArgBean argBean ) throws Exception
	{
		PaymentInfoCreateParamBean paramBean = mapPaymentInfo( argBean );

		Long contractId = service.setPaymentInfo( paramBean );

		service.saveSigningContractPdf( paramBean );

		service.informCustomerAppropirated( paramBean );

		return getResponseMap( contractId );
	}

	/**
	 * 對保案件進件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "sendSigningContract" )
	public Map<String, Object> sendSigningContract( @RequestBody @Validated SigningContractSendArgBean argBean ) throws MyRuntimeException
	{
		String clientAddress = CommonAppUtils.getClientAddress( request );

		return getResponseMap( service.createSigningContract( mapCreatedParamBean( argBean ), clientAddress ) );
	}

	private ContractCreatedParamBean mapCreatedParamBean( SigningContractSendArgBean argBean )
	{
		ContractCreatedParamBean paramBean = new ContractCreatedParamBean();
		paramBean.setLoanType( argBean.getLoanType() );
		paramBean.setBranchCode( argBean.getBranchCode() );
		paramBean.setBorrowerBirthDate( argBean.getBorrowerBirthDate() );
		paramBean.setBorrowerId( argBean.getBorrowerId() );
		paramBean.setBorrowerMobileNumber( argBean.getBorrowerMobileNumber() );
		paramBean.setBorrowerName( argBean.getBorrowerName() );
		paramBean.setBorrowerEmail( argBean.getBorrowerEmail() );
		paramBean.setIsBorrowerYouth( argBean.getIsBorrowerYouth() );
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setContractVersion( argBean.getContractVersion() );
		paramBean.setCourtName( argBean.getCourtName() );
		paramBean.setExpiredDate( argBean.getExpiredDate() );
		paramBean.setGuaranteeAmt( argBean.getGuaranteeAmt() );
		paramBean.setGeneralGuaranteePlan( argBean.getGeneralGuaranteePlan() );
		paramBean.setGeneralGuaranteePlanInfo( argBean.getGeneralGuaranteePlanInfo() );
		paramBean.setJointGuaranteePlan( argBean.getJointGuaranteePlan() );
		paramBean.setJointGuaranteePlanInfo( argBean.getJointGuaranteePlanInfo() );
		paramBean.setProductCode( argBean.getProductCode() );
		paramBean.setRelatedPersonBirthDate( argBean.getRelatedPersonBirthDate() );
		paramBean.setRelatedPersonId( argBean.getRelatedPersonId() );
		paramBean.setRelatedPersonMobileNumber( argBean.getRelatedPersonMobileNumber() );
		paramBean.setRelatedPersonName( argBean.getRelatedPersonName() );
		paramBean.setRelatedPersonType( argBean.getRelatedPersonType() );
		paramBean.setRelatedPersonEmail( argBean.getRelatedPersonEmail() );
		paramBean.setLoanAccts( argBean.getLoanAccts() );
		paramBean.setLoanConditionDataBean( mapLoanConditionDataBean( argBean.getLoanConditionInfoBean() ) );
		paramBean.setLoanPlan( argBean.getLoanPlan() );
		paramBean.setGrpCntrNo( argBean.getGrpCntrNo() );
		paramBean.setGivenApprBegDate( argBean.getGivenApprBegDate() );
		paramBean.setGivenApprEndDate( argBean.getGivenApprEndDate() );
		paramBean.setPayeeBankCode( argBean.getPayeeBankCode() );
		paramBean.setPayeeBankAccountNo( argBean.getPayeeBankAccountNo() );
		paramBean.setPayeeBankAccountName( argBean.getPayeeBankAccountName() );
		paramBean.setPayeeTotalAmt( argBean.getPayeeTotalAmt() );
		paramBean.setPayeeRemittance( argBean.getPayeeRemittance() );
		paramBean.setPayeeSelfProvide( argBean.getPayeeSelfProvide() );
		paramBean.setBaseRate( argBean.getBaseRate() );
		paramBean.setRateList( argBean.getRateList() );
		paramBean.setIsRepayment( argBean.getIsRepayment() );
		paramBean.setRepamentList( argBean.getRepaymentList() );
		paramBean.setStaffRule( argBean.getStaffRule() );
		paramBean.setPayeeInfo( mapPayeeInfoBean( argBean.getPayeeInfo() ) );
		paramBean.setExpireInfo( mapExpireInfoBean( argBean.getExpireInfo() ) );
		paramBean.setRepaymentInfo( mapRepaymentInfoBean( argBean.getRepaymentInfo() ) );
		paramBean.setInterestInfo( mapInterestInfoBean( argBean.getInterestInfo() ) );
		paramBean.setGuaranteeType( argBean.getGuaranteeType() );
		paramBean.setWitness( argBean.getWitness() );
		paramBean.setBrNoTel( argBean.getBrNoTel() );
		paramBean.setBrNoFax( argBean.getBrNoFax() );
		paramBean.setRefSystemId( argBean.getRefSystemId() );
		paramBean.setProdKind( argBean.getProdKind() );
		paramBean.setLnDate( argBean.getLnDate() );
		paramBean.setConsentVer( argBean.getConsentVer() );
		paramBean.setCollateralBuildingAddr1( argBean.getCollateralBuildingAddr1() );
		paramBean.setCollateralBuildingAddr2( argBean.getCollateralBuildingAddr2() );
		paramBean.setMortgageMaxAmt1( argBean.getMortgageMaxAmt1() );
		paramBean.setMortgageMaxAmt2( argBean.getMortgageMaxAmt2() );
		paramBean.setFirstLoanDateYear( argBean.getFirstLoanDateYear() );
		paramBean.setFirstLoanDateMth( argBean.getFirstLoanDateMth() );
		paramBean.setFirstLoanDateDay( argBean.getFirstLoanDateDay() );
		paramBean.setFirstLoanAmt1( argBean.getFirstLoanAmt1() );
		paramBean.setFirstLoanAmt2( argBean.getFirstLoanAmt2() );
		paramBean.setCollateralContractTerms( argBean.getCollateralContractTerms() );
		paramBean.setUnregisteredBuildingDesc( argBean.getUnregisteredBuildingDesc() );
		paramBean.setHouseLoanContractNo( argBean.getHouseLoanContractNo() );
		paramBean.setCoTarget( argBean.getCoTarget() );
		paramBean.setCbAfftTerms( argBean.getCbAfftTerms() );
		paramBean.setCbAfftVersion( argBean.getCbAfftVersion() );

		try
		{
			paramBean.setCbAfft1ContentBean( argBean.getCbAfft1ContentBean() == null ? new CbAfft1ContentBean()
																					 : ObjectConversionUtils.convert( argBean.getCbAfft1ContentBean(),
																													  CbAfft1ContentBean.class ) );
			paramBean.setCbAfft2ContentBean( argBean.getCbAfft2ContentBean() == null ? new CbAfft2ContentBean()
																					 : ObjectConversionUtils.convert( argBean.getCbAfft2ContentBean(),
																													  CbAfft2ContentBean.class ) );
			paramBean.setCbAfft3ContentBean( argBean.getCbAfft3ContentBean() == null ? new CbAfft3ContentBean()
																					 : ObjectConversionUtils.convert( argBean.getCbAfft3ContentBean(),
																													  CbAfft3ContentBean.class ) );
			paramBean.setCbAfft4ContentBean( argBean.getCbAfft4ContentBean() == null ? new CbAfft4ContentBean()
																					 : ObjectConversionUtils.convert( argBean.getCbAfft4ContentBean(),
																													  CbAfft4ContentBean.class ) );
			paramBean.setCbAfft5ContentBean( argBean.getCbAfft5ContentBean() == null ? new CbAfft5ContentBean()
																					 : ObjectConversionUtils.convert( argBean.getCbAfft5ContentBean(),
																													  CbAfft5ContentBean.class ) );
		}
		catch( Exception ex )
		{
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
										  new String[]{ "cbAfft1Content ~ cbAfft5Content parameters mapping error" } );
		}

		return paramBean;
	}

	private ContractCtrTypeCMgmtParamBean mapCtrTypeCParamBean( ContractCtrTypeCMgmtArgBean argBean )
	{
		ContractCtrTypeCMgmtParamBean paramBean = new ContractCtrTypeCMgmtParamBean();
		paramBean.setLoanType( argBean.getLoanType() );
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setBankAcctCode( argBean.getBankAcctCode() );
		paramBean.setBankAcctNo( argBean.getBankAcctNo() );
		paramBean.setBorrowerIPAddr( argBean.getBorrowerIPAddr() );
		paramBean.setBorrowerIdentityType( argBean.getBorrowerIdentityType() );
		paramBean.setLoanBeginDate( argBean.getLoanBeginDate() );
		paramBean.setLoanEndDate( argBean.getLoanEndDate() );
		paramBean.setRateAdjustInformMethod( argBean.getRateAdjustInformMethod() );
		paramBean.setBorrowerSingingDate( argBean.getBorrowerSingingDate() );
		paramBean.setContractCheckDate( argBean.getContractCheckDate() );
		paramBean.setBorrowerAgreeCrossSelling( argBean.getBorrowerAgreeCrossSelling() );
		paramBean.setBranchCode( argBean.getBranchCode() );
		paramBean.setBorrowerId( argBean.getBorrowerId() );
		paramBean.setBorrowerBirthDate( argBean.getBorrowerBirthDate() );
		paramBean.setBorrowerName( argBean.getBorrowerName() );
		paramBean.setBorrowerMobileNumber( argBean.getBorrowerMobileNumber() );
		paramBean.setBorrowerEmail( argBean.getBorrowerEmail() );
		paramBean.setProductCode( argBean.getProductCode() );
		paramBean.setContractVersion( argBean.getContractVersion() );
		paramBean.setLoanAmt( argBean.getLoanAmt() );
		paramBean.setLoanPeriod( argBean.getLoanPeriod() );
		paramBean.setDrawDownType( argBean.getDrawDownType() );
		paramBean.setOneTimeFee( argBean.getOneTimeFee() );
		paramBean.setPreliminaryFee( argBean.getPreliminaryFee() );
		paramBean.setCreditCheckFee( argBean.getCreditCheckFee() );
		paramBean.setRepaymentMethod( argBean.getRepaymentMethod() );
		paramBean.setAdvancedRateDesc( argBean.getAdvancedRateDesc() );
		paramBean.setAdvancedAPR( argBean.getAdvancedAPR() );
		paramBean.setLimitedRateDesc( argBean.getLimitedRateDesc() );
		paramBean.setLimitedAPR( argBean.getLimitedAPR() );
		paramBean.setShowOption( argBean.getShowOption() );
		paramBean.setCourtName( argBean.getCourtName() );
		paramBean.setFile( argBean.getFile() );
		return paramBean;
	}

	private ExpireInfoCreateParamBean mapExpireInfoBean( ExpireInfoCreateArgBean argBean )
	{
		ExpireInfoCreateParamBean paramBean = new ExpireInfoCreateParamBean();

		paramBean.setExpireInfoType( argBean.getExpireInfoType() );
		paramBean.setStartYear1( argBean.getStartYear1() );
		paramBean.setStartMonth1( argBean.getStartMonth1() );
		paramBean.setStartDay1( argBean.getStartDay1() );
		paramBean.setExpireYear1( argBean.getExpireYear1() );
		paramBean.setExpireMonth1( argBean.getExpireMonth1() );
		paramBean.setExpireDay1( argBean.getExpireDay1() );
		paramBean.setDuration1( argBean.getDuration1() );
		paramBean.setStartYear2( argBean.getStartYear2() );
		paramBean.setStartMonth2( argBean.getStartMonth2() );
		paramBean.setStartDay2( argBean.getStartDay2() );
		paramBean.setExpireYear2( argBean.getExpireYear2() );
		paramBean.setExpireMonth2( argBean.getExpireMonth2() );
		paramBean.setExpireDay2( argBean.getExpireDay2() );
		paramBean.setDuration2( argBean.getDuration2() );
		paramBean.setStartYear3( argBean.getStartYear3() );
		paramBean.setStartMonth3( argBean.getStartMonth3() );
		paramBean.setStartDay3( argBean.getStartDay3() );
		paramBean.setExpireYear3( argBean.getExpireYear3() );
		paramBean.setExpireMonth3( argBean.getExpireMonth3() );
		paramBean.setExpireDay3( argBean.getExpireDay3() );
		paramBean.setDuration3( argBean.getDuration3() );
		paramBean.setDuration4( argBean.getDuration4() );
		paramBean.setMaxYear4( argBean.getMaxYear4() );
		paramBean.setExpireYear4( argBean.getExpireYear4() );
		paramBean.setExpireMonth4( argBean.getExpireMonth4() );
		paramBean.setExpireDay4( argBean.getExpireDay4() );
		paramBean.setOther5( argBean.getOther5() );

		return paramBean;
	}

	private InterestInfoCreateParamBean mapInterestInfoBean( InterestInfoCreateArgBean argBean )
	{
		InterestInfoCreateParamBean paramBean = new InterestInfoCreateParamBean();

		paramBean.setInterestInfoType( argBean.getInterestInfoType() );
		paramBean.setFormula1( argBean.getFormula1() );
		paramBean.setFormula2( argBean.getFormula2() );
		paramBean.setFirstPeriodFrom1( argBean.getFirstPeriodFrom1() );
		paramBean.setFirstPeriodTo1( argBean.getFirstPeriodTo1() );
		paramBean.setFirstPeriodRate1( argBean.getFirstPeriodRate1() );
		paramBean.setSecondPeriodFrom1( argBean.getSecondPeriodFrom1() );
		paramBean.setSecondPeriodTo1( argBean.getSecondPeriodTo1() );
		paramBean.setSecondPeriodRate1( argBean.getSecondPeriodRate1() );
		paramBean.setThirdPeriodFrom1( argBean.getThirdPeriodFrom1() );
		paramBean.setThirdPeriodTo1( argBean.getThirdPeriodTo1() );
		paramBean.setThirdPeriodRate1( argBean.getThirdPeriodRate1() );
		paramBean.setOther1( argBean.getOther1() );
		paramBean.setOther2( argBean.getOther2() );
		paramBean.setOther3( argBean.getOther3() );
		paramBean.setRate1( argBean.getRate1() );
		paramBean.setRate2_1( argBean.getRate2_1() );
		paramBean.setRate2_2( argBean.getRate2_2() );
		paramBean.setRate2_3_1( argBean.getRate2_3_1() );
		paramBean.setRate2_3_2( argBean.getRate2_3_2() );

		return paramBean;
	}

	private LoanConditionDataBean mapLoanConditionDataBean( LoanConditionInfoBean infoBean )
	{
		LoanConditionDataBean dataBean = new LoanConditionDataBean();
		dataBean.setCreditCheckFee( infoBean.getCreditCheckFee() );
		dataBean.setDrawDownType( infoBean.getDrawDownType() );
		dataBean.setLendingPlan( infoBean.getLendingPlan() );
		dataBean.setLoanAmt( infoBean.getLoanAmt() );
		dataBean.setLoanPeriod( infoBean.getLoanPeriod() );
		dataBean.setLoanPurposeDataBeans( mapLoanPurposeDataBeans( infoBean.getLoanPurposeInfoBeans() ) );
		dataBean.setOneTimeFee( infoBean.getOneTimeFee() );
		dataBean.setPreliminaryFee( infoBean.getPreliminaryFee() );
		dataBean.setCertFee( infoBean.getCertFee() );
		dataBean.setChangeFee( infoBean.getChangeFee() );
		dataBean.setReissueFee( infoBean.getReissueFee() );
		dataBean.setRenewFee( infoBean.getRenewFee() );
		dataBean.setRepaymentMethod( infoBean.getRepaymentMethod() );
		dataBean.setAdvancedRedemptionTitle( infoBean.getAdvancedRedemptionTitle() );
		dataBean.setAdvancedRedemptionDesc( infoBean.getAdvancedRedemptionDesc() );
		dataBean.setAdvancedRateTitle( infoBean.getAdvancedRateTitle() );
		dataBean.setAdvancedRateDesc( infoBean.getAdvancedRateDesc() );
		dataBean.setAdvancedApr( infoBean.getAdvancedApr() );
		dataBean.setLimitedRedemptionTitle( infoBean.getLimitedRedemptionTitle() );
		dataBean.setLimitedRedemptionDesc( infoBean.getLimitedRedemptionDesc() );
		dataBean.setLimitedRateTitle( infoBean.getLimitedRateTitle() );
		dataBean.setLimitedRateDesc( infoBean.getLimitedRateDesc() );
		dataBean.setLimitedApr( infoBean.getLimitedApr() );
		dataBean.setOtherInfoTitle( infoBean.getOtherInfoTitle() );
		dataBean.setOtherInfoDesc( infoBean.getOtherInfoDesc() );
		dataBean.setShowOption( infoBean.getShowOption() );

		return dataBean;
	}

	private List<LoanPurposeDataBean> mapLoanPurposeDataBeans( List<LoanPurposeInfoBean> loanPurposeInfoBeans )
	{
		List<LoanPurposeDataBean> dataBeans = new ArrayList<>();

		for( LoanPurposeInfoBean loanPurposeInfoBean : loanPurposeInfoBeans )
		{
			LoanPurposeDataBean dataBean = new LoanPurposeDataBean();
			dataBean.setLoanPurpose( loanPurposeInfoBean.getLoanPurpose() );
			dataBean.setIsChecked( loanPurposeInfoBean.getIsChecked() );

			dataBeans.add( dataBean );
		}

		return dataBeans;
	}

	private PayeeInfoCreateParamBean mapPayeeInfoBean( PayeeInfoCreateArgBean argBean )
	{
		PayeeInfoCreateParamBean paramBean = new PayeeInfoCreateParamBean();

		paramBean.setPayeeInfoType( argBean.getPayeeInfoType() );
		paramBean.setPayeeInfoAccountType( argBean.getPayeeInfoAccountType() );

		return paramBean;
	}

	private PaymentInfoCreateParamBean mapPaymentInfo( PaymentInfoCreateArgBean argBean )
	{
		PaymentInfoCreateParamBean paramBean = new PaymentInfoCreateParamBean();
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setPreliminaryFee( argBean.getPreliminaryFee() );
		paramBean.setCrChkFee( argBean.getCrChkFee() );
		paramBean.setPaymentInfoList( argBean.getPaymentInfoList() );

		return paramBean;
	}

	private RepaymentInfoCreateParamBean mapRepaymentInfoBean( RepaymentInfoCreateArgBean argBean )
	{
		RepaymentInfoCreateParamBean paramBean = new RepaymentInfoCreateParamBean();

		paramBean.setRepaymentInfoType( argBean.getRepaymentInfoType() );
		paramBean.setLimtedYear4( argBean.getLimtedYear4() );
		paramBean.setLimtedMonth4( argBean.getLimtedMonth4() );
		paramBean.setYear4( argBean.getYear4() );
		paramBean.setMonth4( argBean.getMonth4() );
		paramBean.setLimtedYear5( argBean.getLimtedYear5() );
		paramBean.setLimtedMonth5( argBean.getLimtedMonth5() );
		paramBean.setYear5( argBean.getYear5() );
		paramBean.setMonth5( argBean.getMonth5() );
		paramBean.setPeriod6( argBean.getPeriod6() );
		paramBean.setYear6( argBean.getYear6() );
		paramBean.setOther7( argBean.getOther7() );
		paramBean.setHouseRedemption( argBean.getHouseRedemption() );
		paramBean.setFirstRate( argBean.getFirstRate() );
		paramBean.setSecondRate( argBean.getSecondRate() );

		return paramBean;
	}
}
