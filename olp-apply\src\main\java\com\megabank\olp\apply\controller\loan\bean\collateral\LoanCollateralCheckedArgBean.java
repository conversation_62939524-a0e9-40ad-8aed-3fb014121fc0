package com.megabank.olp.apply.controller.loan.bean.collateral;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class LoanCollateralCheckedArgBean extends BaseBean
{
	@NotBlank
	private String idNo;

	public LoanCollateralCheckedArgBean()
	{
		// default constructor
	}

	public String getIdNo()
	{
		return idNo;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

}
