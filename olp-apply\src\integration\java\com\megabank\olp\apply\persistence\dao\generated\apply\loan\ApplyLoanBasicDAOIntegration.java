package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicCreatedParamBean;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyLoanBasicDAOIntegration
{
	@Autowired
	private ApplyLoanBasicDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long loanId = 29L;
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );

		ApplyLoanBasicCreatedParamBean paramBean = new ApplyLoanBasicCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setIdNo( idNo );
		paramBean.setBirthDate( birthDate );
		paramBean.setChildrenCount( 0 );

		Long id = dao.create( paramBean );

		logger.info( "id:{}", id );
	}

}
