package com.megabank.olp.apply.persistence.dao.mixed;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.mixed.ContactMeListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.ContactMeListDTO;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class ContactMeDAO extends BaseDAO
{
	private static final String CASE_NO_CONSTANT = "caseNo";

	private static final String DATE_END_CONSTANT = "dateEnd";

	private static final String DATE_START_CONSTANT = "dateStart";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String NAME_CONSTANT = "name";

	private static final String PROCESS_CODE_CONSTANT = "processCode";

	private static final String PROCESS_STATUS_CONSTANT = "processStatus";

	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String CONTACT_TIME_CODE_CONSTANT = "contactTimeCode";

	private static final String NOT_NOTIFIED_NAME_CONSTANT = "notNotifiedName";

	private static final String NOTIFIED_NAME_CONSTANT = "notifiedName";

	private static final String CONTACT_ME_ID_CONSTANT = "contactMeId";

	private static final String CREATED_DATE_CONSTANT = "createdDate";

	private static final String BRANCH_BANK_CONSTANT = "branchBank";

	private static final String NOTIFICATION_STATUS_CONSTANT = "notificationStatus";

	private static final String CONTACT_TIME_CONSTANT = "contactTime";

	private static final String LOAN_PLAN_CODE_CONSTANT = "loanPlanCode";

	private static final String LOAN_PLAN_NAME_CONSTANT = "loanPlanName";
	
	private static final String SEX_NAME_CONSTANT = "sexName";
	
	private static final String EMAIL_CONTANT = "email";
	
	private static final String OTHER_MESSAGE_CONSTANT = "otherMessage";

	private static final String INTRODUCER_BRANCH_BANK_ID_CONSTANT = "introducerBranchBankId";

	private static final String INTRODUCER_BRANCH_BANK_NAME_CONSTANT = "introducerBranchBankName";

	private static final String INTRODUCER_EMP_ID_CONSTANT = "introducerEmpId";

	private static final String PERMISSION_BRANCH_BANK_ID_CONSTANT = "permissionBranchBankId";

	private static final String PERMISSION_EMP_ID_CONSTANT = "permissionEmpId";

	public List<ContactMeListDTO> getList( ContactMeListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "contactme.getList" );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( CONTACT_TIME_CODE_CONSTANT, paramBean.getContactTimeCode(), String.class );
		nativeQuery.setParameter( INTRODUCER_BRANCH_BANK_ID_CONSTANT, paramBean.getIntroducerBranchBankId(), Long.class );
		nativeQuery.setParameter( INTRODUCER_EMP_ID_CONSTANT, paramBean.getIntroducerEmpId(), String.class );
		nativeQuery.setParameter( PERMISSION_BRANCH_BANK_ID_CONSTANT, paramBean.getPermissionBranchBankId(), Long.class );
		nativeQuery.setParameter( PERMISSION_EMP_ID_CONSTANT, paramBean.getPermissionEmpId(), String.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );
		
		nativeQuery.addScalar( CONTACT_ME_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( CONTACT_TIME_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCER_BRANCH_BANK_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCER_EMP_ID_CONSTANT, String.class );
		nativeQuery.addScalar( SEX_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( EMAIL_CONTANT, String.class );
		nativeQuery.addScalar( OTHER_MESSAGE_CONSTANT, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( ContactMeListDTO.class ) );

		return nativeQuery.getResultList();
	}

	public PagingBean<ContactMeListDTO> getPaging( ContactMeListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "contactme.getList" );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( CONTACT_TIME_CODE_CONSTANT, paramBean.getContactTimeCode(), String.class );
		nativeQuery.setParameter( INTRODUCER_BRANCH_BANK_ID_CONSTANT, paramBean.getIntroducerBranchBankId(), Long.class );
		nativeQuery.setParameter( INTRODUCER_EMP_ID_CONSTANT, paramBean.getIntroducerEmpId(), String.class );
		nativeQuery.setParameter( PERMISSION_BRANCH_BANK_ID_CONSTANT, paramBean.getPermissionBranchBankId(), Long.class );
		nativeQuery.setParameter( PERMISSION_EMP_ID_CONSTANT, paramBean.getPermissionEmpId(), String.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );
		
		nativeQuery.addScalar( CONTACT_ME_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( CONTACT_TIME_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCER_BRANCH_BANK_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( INTRODUCER_EMP_ID_CONSTANT, String.class );
		nativeQuery.addScalar( SEX_NAME_CONSTANT, String.class );
		nativeQuery.addScalar( EMAIL_CONTANT, String.class );
		nativeQuery.addScalar( OTHER_MESSAGE_CONSTANT, String.class );

		NativeQuery countQuery = getNamedSQLQueryByCount( "contactme.getList.count" );
		countQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		countQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		countQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		countQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		countQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		countQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		countQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		countQuery.setParameter( CONTACT_TIME_CODE_CONSTANT, paramBean.getContactTimeCode(), String.class );
		countQuery.setParameter( INTRODUCER_BRANCH_BANK_ID_CONSTANT, paramBean.getIntroducerBranchBankId(), Long.class );
		countQuery.setParameter( INTRODUCER_EMP_ID_CONSTANT, paramBean.getIntroducerEmpId(), String.class );
		countQuery.setParameter( PERMISSION_BRANCH_BANK_ID_CONSTANT, paramBean.getPermissionBranchBankId(), Long.class );
		countQuery.setParameter( PERMISSION_EMP_ID_CONSTANT, paramBean.getPermissionEmpId(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( ContactMeListDTO.class ) );

		return processPagination( nativeQuery, countQuery );
	}
}
