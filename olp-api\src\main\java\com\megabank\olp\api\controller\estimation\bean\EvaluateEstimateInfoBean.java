/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class EvaluateEstimateInfoBean extends BaseBean
{
	/**
	 * 預估每坪單價(萬)
	 */
	@Digits( integer = 8, fraction = 2 )
	private BigDecimal av750;

	/**
	 * 預估總價(萬)
	 */
	private BigDecimal estimateTotalPriceAV;

	public BigDecimal getAv750()
	{
		return av750;
	}

	public BigDecimal getEstimateTotalPriceAV()
	{
		return estimateTotalPriceAV;
	}

	public void setAv750( BigDecimal av750 )
	{
		this.av750 = av750;
	}

	public void setEstimateTotalPriceAV( BigDecimal estimateTotalPriceAV )
	{
		this.estimateTotalPriceAV = estimateTotalPriceAV;
	}
}
