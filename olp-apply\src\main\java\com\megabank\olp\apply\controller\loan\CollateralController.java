package com.megabank.olp.apply.controller.loan;

import java.io.IOException;
import java.util.Map;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.loan.bean.collateral.AgreementSubmittedArgBean;
import com.megabank.olp.apply.service.loan.CollateralService;
import com.megabank.olp.apply.service.loan.bean.collateral.AgreementSubmittedParamBean;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/apply/collateral" )
public class CollateralController extends BaseController
{
	@Autowired
	private CollateralService collateralService;

	/**
	 * 取得擔保品提供人同意事項
	 *
	 * @return
	 */
	@PostMapping( "getAgreement" )
	public Map<String, Object> getAgreement()
	{
		return getResponseMap( collateralService.getAgreement() );

	}

	/**
	 * 取得感謝頁內容
	 *
	 * @return
	 */
	@PostMapping( "getThankyouMessage" )
	public Map<String, Object> getThankyouMessage()
	{
		return getResponseMap( collateralService.getThankyouMessage() );

	}

	/**
	 * 送出擔保品提供人同意事項
	 *
	 * @param argBean
	 * @return
	 * @throws IOException
	 */
	@PostMapping( "submitAgreement" )
	public Map<String, Object> submitAgreement( @RequestBody @Validated AgreementSubmittedArgBean argBean ) throws IOException
	{
		Long validatedId = collateralService.saveApplyCollateral( mapAgreementParamBean( argBean ) );
		return getResponseMap( collateralService.submitAgreement( validatedId ) );

	}

	private AgreementSubmittedParamBean mapAgreementParamBean( AgreementSubmittedArgBean argBean )
	{
		validPaymentCondition( argBean.getIsCollateralFullPayment(), argBean.getGuranteeAmt(), argBean.getSignatory(), argBean.getTermNo() );

		AgreementSubmittedParamBean paramBean = new AgreementSubmittedParamBean();
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setIsCollateralFullPayment( argBean.getIsCollateralFullPayment() );
		paramBean.setCollateralAddressTownCode( argBean.getCollateralAddressTownCode() );
		paramBean.setCollateralAddressStreet( argBean.getCollateralAddressStreet() );
		paramBean.setCollateralAmt( argBean.getCollateralAmt() );
		paramBean.setLoanAmt( argBean.getLoanAmt() );
		paramBean.setWarrantee1( argBean.getWarrantee1() );
		paramBean.setWarrantee2( argBean.getWarrantee2() );
		paramBean.setWarrantee3( argBean.getWarrantee3() );
		paramBean.setGuranteeAmt( argBean.getGuranteeAmt() );
		paramBean.setSignatory( argBean.getSignatory() );
		paramBean.setTermNo( argBean.getTermNo() );
		paramBean.setBorrowerSignDate( argBean.getBorrowerSignDate() );
		paramBean.setLoanProduct1( argBean.getLoanProduct1() );
		paramBean.setLoanProduct2( argBean.getLoanProduct2() );
		paramBean.setLoanProduct3( argBean.getLoanProduct3() );
		paramBean.setLoanProduct4( argBean.getLoanProduct4() );
		paramBean.setLoanProduct5( argBean.getLoanProduct5() );

		return paramBean;
	}

	private void validPaymentCondition( Boolean isCollateralFullPayment, Long guranteeAmt, String signatory, String termNo )
	{
		if( BooleanUtils.isTrue( isCollateralFullPayment ) && guranteeAmt == null )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY, new String[]{ "guranteeAmt", "must not be null" } );
		else if( BooleanUtils.isFalse( isCollateralFullPayment ) && StringUtils.isAnyBlank( signatory, termNo ) )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY, new String[]{ "signatory, termNo", "must not be blank" } );

	}

}
