package com.megabank.olp.apply.controller.ixml.bean;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class LoginArgBean extends BaseBean
{
	@NotBlank
	private String id; // 身分證

	@NotBlank
	private String action;

	@NotBlank
	private String loanType;

	@NotBlank
	private String userType;

	public LoginArgBean()
	{
		// default constructor
	}

	public String getAction()
	{
		return action;
	}

	public String getId()
	{
		return id;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getUserType() { return userType; }

	public void setAction( String action )
	{
		this.action = action;
	}

	public void setId( String id )
	{
		this.id = id;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setUserType( String userSubType ) { this.userType = userSubType; }
}
