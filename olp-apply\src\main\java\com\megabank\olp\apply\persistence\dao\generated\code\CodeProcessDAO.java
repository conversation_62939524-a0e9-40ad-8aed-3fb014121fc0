package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeProcess;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeProcessDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeProcessDAO extends BasePojoDAO<CodeProcess, String>
{
	public CodeProcess read( String processCode )
	{
		Validate.notBlank( processCode );

		return getPojoByPK( processCode, CodeProcess.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeProcess> getPojoClass()
	{
		return CodeProcess.class;
	}
}
