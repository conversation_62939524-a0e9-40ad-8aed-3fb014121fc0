package com.megabank.olp.apply.persistence.dao.mixed;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;




import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.mixed.HouseContactListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.HouseContactListDTO;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class HouseContactDAO extends BaseDAO
{
	private static final String CASE_NO_CONSTANT = "caseNo";

	private static final String DATE_END_CONSTANT = "dateEnd";

	private static final String DATE_START_CONSTANT = "dateStart";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String NAME_CONSTANT = "name";

	private static final String PROCESS_CODE_CONSTANT = "processCode";

	private static final String PROCESS_STATUS_CONSTANT = "processStatus";

	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String CALL_BACK_TIME_CONSTANT = "callBackTime";

	private static final String NOT_NOTIFIED_NAME_CONSTANT = "notNotifiedName";

	private static final String NOTIFIED_NAME_CONSTANT = "notifiedName";

	private static final String CONTACT_INFO_ID_CONSTANT = "contactInfoId";

	private static final String CREATED_DATE_CONSTANT = "createdDate";

	private static final String BRANCH_BANK_CONSTANT = "branchBank";

	private static final String NOTIFICATION_STATUS_CONSTANT = "notificationStatus";

	private static final String OTHER_MSG_CONSTANT = "otherMsg";

	private static final String LOAN_PLAN_CODE_CONSTANT = "loanPlanCode";

	private static final String LOAN_PLAN_NAME_CONSTANT = "loanPlanName";

	public List<HouseContactListDTO> getList( HouseContactListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "housecontact.getList" );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getCreatedDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getCreatedDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, paramBean.getBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( CALL_BACK_TIME_CONSTANT, paramBean.getCallBackTime(), String.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.addScalar( CONTACT_INFO_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( CALL_BACK_TIME_CONSTANT, String.class );
		nativeQuery.addScalar( OTHER_MSG_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( HouseContactListDTO.class ) );

		return nativeQuery.getResultList();
	}

	public PagingBean<HouseContactListDTO> getPaging( HouseContactListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "housecontact.getList" );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getCreatedDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getCreatedDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, paramBean.getBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( CALL_BACK_TIME_CONSTANT, paramBean.getCallBackTime(), String.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.addScalar( CONTACT_INFO_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( CALL_BACK_TIME_CONSTANT, String.class );

		NativeQuery countQuery = getNamedSQLQueryByCount( "housecontact.getList.count" );
		countQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		countQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		countQuery.setParameter( DATE_START_CONSTANT, paramBean.getCreatedDateStart(), Date.class );
		countQuery.setParameter( DATE_END_CONSTANT, paramBean.getCreatedDateEnd(), Date.class );
		countQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		countQuery.setParameter( BRANCH_BANK_ID_CONSTANT, paramBean.getBranchBankId(), Long.class );
		countQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		countQuery.setParameter( CALL_BACK_TIME_CONSTANT, paramBean.getCallBackTime(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( HouseContactListDTO.class ) );

		return processPagination( nativeQuery, countQuery );

	}

}
