package com.megabank.olp.api.controller.open;

import java.io.IOException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.service.ixml.IxmlService;
import com.megabank.olp.base.layer.BaseController;

@RestController
@RequestMapping( "open/ixml" )
public class IxmlController extends BaseController
{
	@Autowired
	private IxmlService ixmlService;

	@PostMapping( "doCallback" )
	public void doCallback( HttpServletRequest request, HttpServletResponse response ) throws IOException
	{
		response.sendRedirect( ixmlService.doCallback( request ) );
	}
}
