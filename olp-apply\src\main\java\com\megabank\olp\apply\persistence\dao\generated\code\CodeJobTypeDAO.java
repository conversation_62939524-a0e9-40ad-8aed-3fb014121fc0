package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeJobType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeJobTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeJobTypeDAO extends BasePojoDAO<CodeJobType, String>
{
	public List<CodeJobType> getList()
	{
		return getAllPojos();
	}

	public CodeJobType read( String jobType )
	{
		Validate.notNull( jobType );

		return getPojoByPK( jobType, CodeJobType.TABLENAME_CONSTANT );
	}

	public CodeJobType readToNull( String jobType )
	{
		Validate.notNull( jobType );

		return getPojoByPK( jobType );
	}

	@Override
	protected Class<CodeJobType> getPojoClass()
	{
		return CodeJobType.class;
	}
}
