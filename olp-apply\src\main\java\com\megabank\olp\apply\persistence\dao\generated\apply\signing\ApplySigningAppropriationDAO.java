package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.util.Date;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.signing.ApplySigningAppropirationCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeRateAdjustmentNotificationDAO;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningAppropriation;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplySigningAppropirationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySigningAppropriationDAO extends BasePojoDAO<ApplySigningAppropriation, Long>
{
	@Autowired
	private ApplySigningBankAccountDAO bankAccountDAO;

	@Autowired
	private CodeRateAdjustmentNotificationDAO adjustmentNotificationDAO;

	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	public ApplySigningAppropriation create( ApplySigningAppropirationCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getRateAdjustInformMethod() );
		Validate.notBlank( paramBean.getBankAccount() );
		Validate.notBlank( paramBean.getContractNo() );

		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( paramBean.getContractNo() );
		ApplySigningAppropriation pojo = new ApplySigningAppropriation();
		pojo.setApplySigningContract( contract );
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setApplySigningBankAccount( bankAccountDAO.read( contract.getSigningContractId(), paramBean.getBankAccount() ) );
		pojo.setFirstPaymentDate( paramBean.getFirstPaymentDate() );
		pojo.setRepaymentDay( paramBean.getRepayment() );
		pojo.setAppropriationDate( paramBean.getAppropirationDate() );
		pojo.setCodeRateAdjustmentNotification( adjustmentNotificationDAO.read( paramBean.getRateAdjustInformMethod() ) );
		pojo.setContractCheckDate( paramBean.getContractCheckDate() );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );

		super.createPojo( pojo );

		return pojo;
	}

	public ApplySigningAppropriation update( ApplySigningAppropirationCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getRateAdjustInformMethod() );
		Validate.notBlank( paramBean.getBankAccount() );
		Validate.notBlank( paramBean.getContractNo() );

		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( paramBean.getContractNo() );
		ApplySigningAppropriation pojo = contract.getApplySigningAppropriation();
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setApplySigningBankAccount( bankAccountDAO.read( contract.getSigningContractId(), paramBean.getBankAccount() ) );
		pojo.setFirstPaymentDate( paramBean.getFirstPaymentDate() );
		pojo.setRepaymentDay( paramBean.getRepayment() );
		pojo.setAppropriationDate( paramBean.getAppropirationDate() );
		pojo.setCodeRateAdjustmentNotification( adjustmentNotificationDAO.read( paramBean.getRateAdjustInformMethod() ) );
		pojo.setContractCheckDate( paramBean.getContractCheckDate() );
		pojo.setUpdatedDate( new Date() );

		return pojo;
	}

	public ApplySigningAppropriation createForContractCtrTypeC( ApplySigningAppropirationCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getRateAdjustInformMethod() );
		Validate.notNull( paramBean.getBankAccount() );
		Validate.notBlank( paramBean.getContractNo() );

		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( paramBean.getContractNo() );
		ApplySigningAppropriation pojo = new ApplySigningAppropriation();
		pojo.setApplySigningContract( contract );
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setApplySigningBankAccount( bankAccountDAO.readForContractCtrTypeC( contract.getSigningContractId(), paramBean.getBankAccount() ) );
		pojo.setFirstPaymentDate( paramBean.getFirstPaymentDate() );
		pojo.setRepaymentDay( paramBean.getRepayment() );
		pojo.setAppropriationDate( paramBean.getAppropirationDate() );
		pojo.setCodeRateAdjustmentNotification( adjustmentNotificationDAO.read( paramBean.getRateAdjustInformMethod() ) );
		pojo.setContractCheckDate( paramBean.getContractCheckDate() );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );

		super.createPojo( pojo );

		return pojo;
	}

	@Override
	protected Class<ApplySigningAppropriation> getPojoClass()
	{
		return ApplySigningAppropriation.class;
	}
}
