package com.megabank.olp.apply.persistence.dao.generated.apply.survey;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.survey.ApplySurveyCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeJobSubTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProcessDAO;
import com.megabank.olp.apply.persistence.pojo.apply.survey.ApplySurvey;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplySurveyDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySurveyDAO extends BasePojoDAO<ApplySurvey, Long>
{
	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String SURVEY_ID_CONSTANT = "surveyId";

	private static final String NOTIFIED_CONSTANT = "notified";

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeJobSubTypeDAO codeJobSubTypeDAO;

	@Autowired
	private CodeProcessDAO codeProcessDAO;

	public Long create( ApplySurveyCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getAnnualIncome() );
		Validate.notNull( paramBean.getCashAdvance() );
		Validate.notNull( paramBean.getJobSubTypeId() );
		Validate.notNull( paramBean.getCreditCardTotalAmt() );
		Validate.notNull( paramBean.getDebitCardTotalAmt() );
		Validate.notNull( paramBean.getHoldingCreditCard() );
		Validate.notNull( paramBean.getHoldingDebitCard() );
		Validate.notNull( paramBean.getHoldingPersonalLoan() );
		Validate.notNull( paramBean.getRevovingCredit() );
		Validate.notBlank( paramBean.getRiskLevel() );
		Validate.notBlank( paramBean.getCaseNo() );
		Validate.notBlank( paramBean.getProcessCode() );

		ApplySurvey pojo = new ApplySurvey();
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setAnnualIncome( paramBean.getAnnualIncome() );
		pojo.setCaseNo( paramBean.getCaseNo() );
		pojo.setCashAdvance( paramBean.getCashAdvance() );
		pojo.setCodeJobSubType( codeJobSubTypeDAO.read( paramBean.getJobSubTypeId() ) );
		pojo.setRiskLevelCode( paramBean.getRiskLevel() );
		pojo.setCreditCardTotalAmt( paramBean.getCreditCardTotalAmt() );
		pojo.setDebitCardTotalAmt( paramBean.getDebitCardTotalAmt() );
		pojo.setHoldingCreditCard( paramBean.getHoldingCreditCard() );
		pojo.setHoldingDebitCard( paramBean.getHoldingDebitCard() );
		pojo.setHoldingPersonalLoan( paramBean.getHoldingPersonalLoan() );
		pojo.setRevovingCredit( paramBean.getRevovingCredit() );
		pojo.setCreatedDate( new Date() );
		pojo.setNotified( false );
		pojo.setCodeProcess( codeProcessDAO.read( paramBean.getProcessCode() ) );
		pojo.setLoanPlanCode( paramBean.getLoanPlanCode() );

		return super.createPojo( pojo );
	}

	public ApplySurvey getLatestData( Long validatedIdentityId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "survey.getLatestData" );
		nativeQuery.setParameter( "validatedIdentityId", validatedIdentityId, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplySurvey.class );

		return ( ApplySurvey )nativeQuery.uniqueResult();
	}

	public Long getLatestSurveyId()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "survey.getLatestSurveyId" );

		return ( Long )nativeQuery.uniqueResult();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedBankIds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "survey.getBranchBankIds" );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedSurveyIds( Long finalBranchBankId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "survey.getSurveyIds" );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, finalBranchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	public ApplySurvey read( Long surveyId )
	{
		Validate.notNull( surveyId );

		return getPojoByPK( surveyId, ApplySurvey.TABLENAME_CONSTANT );
	}

	public Long updateBranchBank( Long surveyId, Long branchBankId )
	{
		Validate.notNull( surveyId );
		Validate.notNull( branchBankId );

		ApplySurvey pojo = read( surveyId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( branchBankId ) );
		pojo.setNotified( false );

		return pojo.getSurveyId();

	}

	public Long updateCompleted( Long surveyId, Long finalBranchBankId )
	{
		Validate.notNull( surveyId );
		Validate.notNull( finalBranchBankId );

		ApplySurvey pojo = read( surveyId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( finalBranchBankId ) );
		pojo.setCompletedDate( new Date() );

		return pojo.getSurveyId();
	}

	public int updateNotified( List<Long> surveyIds )
	{
		Validate.notEmpty( surveyIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "survey.updateNotified" );
		nativeQuery.setParameterList( SURVEY_ID_CONSTANT, surveyIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long surveyId )
	{
		Validate.notNull( surveyId );

		ApplySurvey pojo = read( surveyId );
		pojo.setNotified( true );

		return pojo.getSurveyId();
	}

	public Long updateProcess( Long surveyId, String processCode )
	{
		Validate.notNull( surveyId );
		Validate.notBlank( processCode );

		ApplySurvey pojo = read( surveyId );
		pojo.setCodeProcess( codeProcessDAO.read( processCode ) );

		return pojo.getSurveyId();
	}

	@Override
	protected Class<ApplySurvey> getPojoClass()
	{
		return ApplySurvey.class;
	}
}
