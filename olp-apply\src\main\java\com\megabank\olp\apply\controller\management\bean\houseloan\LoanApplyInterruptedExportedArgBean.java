package com.megabank.olp.apply.controller.management.bean.houseloan;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.validator.CheckDateRange;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */
@CheckDateRange( dateStart = "dateStart", dateEnd = "dateEnd" )
public class LoanApplyInterruptedExportedArgBean extends BaseBean
{
	private String branchBankCode;

	private String notificationStatusCode;

	private String processStatusCode;

	private String idNo;

	private String name;

	private String mobileNumber;

	private Date dateStart;

	private Date dateEnd;

	private String sortColumn;

	private String sortDirection;

	public LoanApplyInterruptedExportedArgBean()
	{
		// default constructor
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	public Date getDateStart()
	{
		return dateStart;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatusCode()
	{
		return notificationStatusCode;
	}

	public String getProcessStatusCode()
	{
		return processStatusCode;
	}

	public String getSortColumn()
	{
		return sortColumn;
	}

	public String getSortDirection()
	{
		return sortDirection;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatusCode( String notificationStatusCode )
	{
		this.notificationStatusCode = notificationStatusCode;
	}

	public void setProcessStatusCode( String processStatusCode )
	{
		this.processStatusCode = processStatusCode;
	}

	public void setSortColumn( String sortColumn )
	{
		this.sortColumn = sortColumn;
	}

	public void setSortDirection( String sortDirection )
	{
		this.sortDirection = sortDirection;
	}

}
