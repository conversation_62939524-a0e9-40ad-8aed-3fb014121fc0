package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyPayeeInfo;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyPayeeInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyPayeeInfoDAO extends BasePojoDAO<ApplyPayeeInfo, Long>
{

	public Long create( String bankCode, String accountNo, String accountName, Integer totalAmt, Integer remittance, Integer selfProvide )
	{
		Validate.notBlank( bankCode );
		Validate.notBlank( accountNo );
		Validate.notNull( accountName );

		ApplyPayeeInfo pojo = new ApplyPayeeInfo();
		pojo.setBankCode( bankCode );
		pojo.setAccountNo( accountNo );
		pojo.setAccountName( accountName );
		pojo.setTotalAmt( totalAmt );
		pojo.setRemittance( remittance );
		pojo.setSelfProvide( selfProvide );
		// =====
		return super.createPojo( pojo );
	}

	public ApplyPayeeInfo getPojoByData( String bankCode, String accountNo )
	{
		NameValueBean bank_code = new NameValueBean( ApplyPayeeInfo.BANK_CODE_CONSTANT, bankCode );
		NameValueBean account_no = new NameValueBean( ApplyPayeeInfo.ACCOUNT_NO_CONSTANT, accountNo );
		NameValueBean[] conditions = new NameValueBean[]{ bank_code, account_no };

		return getUniquePojoByProperties( conditions );
	}

	public ApplyPayeeInfo read( Long payeeBankAccountId )
	{
		Validate.notNull( payeeBankAccountId );

		return getPojoByPK( payeeBankAccountId, ApplyPayeeInfo.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<ApplyPayeeInfo> getPojoClass()
	{
		return ApplyPayeeInfo.class;
	}

}
