package com.megabank.olp.apply.persistence.dao.generated.house;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.house.HousePricingCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProcessDAO;
import com.megabank.olp.apply.persistence.pojo.house.HousePricingInfo;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HousePricingInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HousePricingInfoDAO extends BasePojoDAO<HousePricingInfo, Long>
{
	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	private static final String PRICING_INFO_ID_CONSTANT = "pricingInfoId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	@Autowired
	private CodeProcessDAO codeProcessDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	public Long create( HousePricingCreatedParamBean paramBean )
	{
		Validate.notBlank( paramBean.getCaseNo() );
		Validate.notNull( paramBean.getCreatedDate() );
		Validate.notBlank( paramBean.getProcessCode() );
		Validate.notBlank( paramBean.getBranchBankCode() );

		HousePricingInfo pojo = new HousePricingInfo();
		pojo.setCaseNo( paramBean.getCaseNo() );
		pojo.setMobileNumber( paramBean.getMobileNumber() );
		pojo.setEmail( paramBean.getEmail() );
		pojo.setCodeBranchBank( codeBranchBankDAO.getPojoByBankCode( paramBean.getBranchBankCode() ) );
		pojo.setCodeProcess( codeProcessDAO.read( paramBean.getProcessCode() ) );
		pojo.setCreatedDate( paramBean.getCreatedDate() );
		pojo.setUpdatedDate( new Date() );

		return super.createPojo( pojo );
	}

	@SuppressWarnings( "rawtypes" )
	public Long getLatestId()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housepricing.getLatestId" );

		return ( Long )nativeQuery.uniqueResult();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedBankIds()
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housepricing.getBranchBankIds" );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToNotifiedPricingInfoIds( Long branchBankId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housepricing.getPricingInfoIds" );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );

		return nativeQuery.getResultList();
	}

	public HousePricingInfo getPojoByCaseNoToNull( String caseNo )
	{
		Validate.notBlank( caseNo );

		NameValueBean condition = new NameValueBean( HousePricingInfo.CASE_NO_CONSTANT, caseNo );

		return getUniquePojoByProperty( condition );
	}

	public HousePricingInfo read( Long housePricingInfoId )
	{
		Validate.notNull( housePricingInfoId );

		return getPojoByPK( housePricingInfoId, HousePricingInfo.TABLENAME_CONSTANT );
	}

	public Long updateBranchBank( Long housePricingInfoId, Long branchBankId )
	{
		Validate.notNull( housePricingInfoId );
		Validate.notNull( branchBankId );

		HousePricingInfo pojo = read( housePricingInfoId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( branchBankId ) );
		pojo.setNotified( false );
		pojo.setUpdatedDate( new Date() );

		return pojo.getPricingInfoId();
	}

	public int updateNotified( List<Long> pricingInfoIds )
	{
		Validate.notEmpty( pricingInfoIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "housepricing.updateNotified" );
		nativeQuery.setParameterList( PRICING_INFO_ID_CONSTANT, pricingInfoIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long housePricingInfoId )
	{
		Validate.notNull( housePricingInfoId );

		HousePricingInfo pojo = read( housePricingInfoId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getPricingInfoId();
	}

	public Long updateProcess( Long housePricingInfoId, String processCode )
	{
		Validate.notNull( housePricingInfoId );
		Validate.notBlank( processCode );

		HousePricingInfo pojo = read( housePricingInfoId );
		pojo.setCodeProcess( codeProcessDAO.read( processCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getPricingInfoId();
	}

	@Override
	protected Class<HousePricingInfo> getPojoClass()
	{
		return HousePricingInfo.class;
	}
}
