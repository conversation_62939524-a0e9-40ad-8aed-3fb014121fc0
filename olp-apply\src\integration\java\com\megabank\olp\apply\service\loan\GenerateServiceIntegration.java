package com.megabank.olp.apply.service.loan;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class GenerateServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;

	@Autowired
	private GenerateService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void generateLoanApplyPdf() throws IOException
	{
		Long loanId = 1L;

		// byte[] result = service.generateLoanApplyPdf( loanId, new Date() );

	}

	@Test
	public void generateSigningContractPdf() throws IOException
	{
		Long signingContractId = 3L;

		byte[] result = service.generateSigningContractPdf( signingContractId );

	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.OTHER_BANK.getContext(), IdentityTypeEnum.OTP.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}
}
