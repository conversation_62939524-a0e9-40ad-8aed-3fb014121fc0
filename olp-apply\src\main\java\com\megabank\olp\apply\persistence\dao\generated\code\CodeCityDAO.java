package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeCity;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeCityDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeCityDAO extends BasePojoDAO<CodeCity, String>
{
	public List<CodeCity> getList()
	{
		return getAllPojos();
	}

	public CodeCity read( String cityCode )
	{
		Validate.notBlank( cityCode );

		return getPojoByPK( cityCode, CodeCity.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeCity> getPojoClass()
	{
		return CodeCity.class;
	}
}
