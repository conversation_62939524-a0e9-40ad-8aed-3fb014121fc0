package com.megabank.olp.apply.persistence.dao.generated.apply.task;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.task.ApplyTask;
import com.megabank.olp.base.layer.BasePojoDAO;

@Repository
public class ApplyTaskDAO extends BasePojoDAO<ApplyTask, Long>
{
	public static final String TASK_ID_CONSTANT = "taskId";

	public static final String TASK_ACTION_CONSTANT = "taskAction";

	public List<ApplyTask> getPojosByTaskActionNullDoneTs( String taskAction )
	{
		/*
		 * 以下寫法所組出來的 SQL 是 done_ts = '' (不是 done_ts is null) 改成用 getNamedQuery(?)
		 *
		 * Validate.notNull( taskAction );
		 * NameValueBean cond_taskAction = new NameValueBean( ApplyTask.TASK_ACTION_CONSTANT, taskAction );
		 * NameValueBean cond_doneTs = new NameValueBean( ApplyTask.DONE_TS_CONSTANT, null );
		 * NameValueBean[] conditions = new NameValueBean[]{ cond_taskAction, cond_doneTs };
		 * return getPojosByProperties( conditions );
		 */
		Validate.notBlank( taskAction );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "task.getPojosByTaskActionNullDoneTs" );
		nativeQuery.setParameter( TASK_ACTION_CONSTANT, taskAction, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyTask.class );

		return nativeQuery.getResultList();
	}

	public ApplyTask read( Long taskId )
	{
		Validate.notNull( taskId );

		return getPojoByPK( taskId, ApplyTask.TABLENAME_CONSTANT );
	}

	public int updateDoneTs( Long taskId )
	{
		Validate.notNull( taskId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "task.updateDoneTs" );
		nativeQuery.setParameter( TASK_ID_CONSTANT, taskId, Long.class );

		return nativeQuery.executeUpdate();
	}

	public int updateProcTs( Long taskId )
	{
		Validate.notNull( taskId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "task.updateProcTs" );
		nativeQuery.setParameter( TASK_ID_CONSTANT, taskId, Long.class );

		return nativeQuery.executeUpdate();
	}

	@Override
	protected Class<ApplyTask> getPojoClass()
	{
		return ApplyTask.class;
	}
}
