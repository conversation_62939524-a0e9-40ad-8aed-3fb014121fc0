package com.megabank.olp.apply.controller.loan.bean.upload;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class MyDataCheckedArgBean extends BaseBean
{
	@NotBlank
	private String loanType;

	@NotBlank
	private String txId;

	public MyDataCheckedArgBean()
	{
		// default constructor
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getTxId()
	{
		return txId;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setTxId( String txId )
	{
		this.txId = txId;
	}

}
