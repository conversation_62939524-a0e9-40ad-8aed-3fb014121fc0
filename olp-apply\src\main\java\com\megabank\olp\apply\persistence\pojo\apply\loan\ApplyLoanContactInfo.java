package com.megabank.olp.apply.persistence.pojo.apply.loan;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddrText;
import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddress;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeHouseStatus;
import com.megabank.olp.apply.persistence.pojo.code.CodeResidenceStatus;
import com.megabank.olp.apply.persistence.pojo.code.CodeServiceAssociateDept;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyLoanContactInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_contact_info" )
public class ApplyLoanContactInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_contact_info";

	public static final String LOAN_ID_CONSTANT = "loanId";

	public static final String MAILING_ADDRESS_CONSTANT = "mailingAddress";

	public static final String HOME_ADDRESS_CONSTANT = "homeAddress";

	public static final String HOME_ADDR_TEXT_CONSTANT = "homeAddrText";

	public static final String MAILING_ADDR_TEXT_CONSTANT = "mailingAddrText";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String CODE_BRANCH_BANK_CONSTANT = "codeBranchBank";

	public static final String CODE_HOUSE_STATUS_CONSTANT = "codeHouseStatus";

	public static final String CODE_RESIDENCE_STATUS_CONSTANT = "codeResidenceStatus";

	public static final String CODE_SERVICE_ASSOCIATE_DEPT_CONSTANT = "codeServiceAssociateDept";

	public static final String RENT_CONSTANT = "rent";

	public static final String HOME_PHONE_CODE_CONSTANT = "homePhoneCode";

	public static final String HOME_PHONE_NUMBER_CONSTANT = "homePhoneNumber";

	public static final String EMAIL_CONSTANT = "email";

	public static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	public static final String SERVICE_ASSOCIATE_CONSTANT = "serviceAssociate";

	private long loanId;

	private transient ApplyAddress mailingAddress;

	private transient ApplyAddress homeAddress;

	private transient ApplyAddrText homeAddrText;

	private transient ApplyAddrText mailingAddrText;

	private transient ApplyLoan applyLoan;

	private transient CodeBranchBank codeBranchBank;

	private transient CodeHouseStatus codeHouseStatus;

	private transient CodeResidenceStatus codeResidenceStatus;

	private transient CodeServiceAssociateDept codeServiceAssociateDept;

	private Integer rent;

	private String homePhoneCode;

	private String homePhoneNumber;

	private String email;

	private String mobileNumber;

	private String serviceAssociate;

	public ApplyLoanContactInfo()
	{}

	public ApplyLoanContactInfo( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public ApplyLoanContactInfo( Long loanId )
	{
		this.loanId = loanId;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "branch_bank_id" )
	public CodeBranchBank getCodeBranchBank()
	{
		return codeBranchBank;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "house_status_code" )
	public CodeHouseStatus getCodeHouseStatus()
	{
		return codeHouseStatus;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "residence_status_code" )
	public CodeResidenceStatus getCodeResidenceStatus()
	{
		return codeResidenceStatus;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "service_associate_dept_code" )
	public CodeServiceAssociateDept getCodeServiceAssociateDept()
	{
		return codeServiceAssociateDept;
	}

	@Column( name = "email", length = 100 )
	public String getEmail()
	{
		return email;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "home_address_id" )
	public ApplyAddress getHomeAddress()
	{
		return homeAddress;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "home_addr_text_id" )
	public ApplyAddrText getHomeAddrText()
	{
		return homeAddrText;
	}

	@Column( name = "home_phone_code", length = 5 )
	public String getHomePhoneCode()
	{
		return homePhoneCode;
	}

	@Column( name = "home_phone_number", length = 12 )
	public String getHomePhoneNumber()
	{
		return homePhoneNumber;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "applyLoan" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "loan_id", unique = true, nullable = false )
	public long getLoanId()
	{
		return loanId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "mailing_address_id" )
	public ApplyAddress getMailingAddress()
	{
		return mailingAddress;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "mailing_addr_text_id" )
	public ApplyAddrText getMailingAddrText()
	{
		return mailingAddrText;
	}

	@Column( name = "mobile_number", length = 10 )
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	@Column( name = "rent", precision = 9, scale = 0 )
	public Integer getRent()
	{
		return rent;
	}

	@Column( name = "service_associate", length = 30 )
	public String getServiceAssociate()
	{
		return serviceAssociate;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setCodeBranchBank( CodeBranchBank codeBranchBank )
	{
		this.codeBranchBank = codeBranchBank;
	}

	public void setCodeHouseStatus( CodeHouseStatus codeHouseStatus )
	{
		this.codeHouseStatus = codeHouseStatus;
	}

	public void setCodeResidenceStatus( CodeResidenceStatus codeResidenceStatus )
	{
		this.codeResidenceStatus = codeResidenceStatus;
	}

	public void setCodeServiceAssociateDept( CodeServiceAssociateDept codeServiceAssociateDept )
	{
		this.codeServiceAssociateDept = codeServiceAssociateDept;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setHomeAddress( ApplyAddress homeAddress )
	{
		this.homeAddress = homeAddress;
	}

	public void setHomeAddrText( ApplyAddrText homeAddrText )
	{
		this.homeAddrText = homeAddrText;
	}

	public void setHomePhoneCode( String homePhoneCode )
	{
		this.homePhoneCode = homePhoneCode;
	}

	public void setHomePhoneNumber( String homePhoneNumber )
	{
		this.homePhoneNumber = homePhoneNumber;
	}

	public void setLoanId( long loanId )
	{
		this.loanId = loanId;
	}

	public void setMailingAddress( ApplyAddress mailingAddress )
	{
		this.mailingAddress = mailingAddress;
	}

	public void setMailingAddrText( ApplyAddrText mailingAddrText )
	{
		this.mailingAddrText = mailingAddrText;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setRent( Integer rent )
	{
		this.rent = rent;
	}

	public void setServiceAssociate( String serviceAssociate )
	{
		this.serviceAssociate = serviceAssociate;
	}
}