/**
 *
 */
package com.megabank.olp.apply.controller.loan.bean.signing;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractAccountArgBean extends BaseBean
{

	@NotBlank
	private String bankAccount;

	@NotBlank
	private String bankCode;

	@NotBlank
	private String bankBranchCode;

	@NotNull
	private Long singingContractId;

	public String getBankAccount()
	{
		return bankAccount;
	}

	public String getBankBranchCode()
	{
		return bankBranchCode;
	}

	public String getBankCode()
	{
		return bankCode;
	}

	public Long getSingingContractId()
	{
		return singingContractId;
	}

	public void setBankAccount( String bankAccount )
	{
		this.bankAccount = bankAccount;
	}

	public void setBankBranchCode( String bankBranchCode )
	{
		this.bankBranchCode = bankBranchCode;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setSingingContractId( Long singingContractId )
	{
		this.singingContractId = singingContractId;
	}
}
