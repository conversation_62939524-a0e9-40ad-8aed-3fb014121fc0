package com.megabank.olp.apply.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;
import com.megabank.olp.system.config.SystemConfig;

/**
 * @version 1.0
 * <AUTHOR>
 * @company Mega Bank
 * @copyright Copyright (c) 2019
 */

@Configuration
@ComponentScan( { "com.megabank.olp.apply.controller", "com.megabank.olp.apply.service", "com.megabank.olp.apply.persistence.dao" } )
@Import( { BaseServiceConfig.class, ClientServiceConfig.class, SystemConfig.class } )
public class ApplyConfig
{
	@Bean
	public ApplyServicePropertyBean servicePropertyBean()
	{
		return new ApplyServicePropertyBean();
	}
}
