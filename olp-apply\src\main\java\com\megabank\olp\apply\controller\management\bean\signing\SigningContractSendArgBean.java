package com.megabank.olp.apply.controller.management.bean.signing;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft1ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft2ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft3ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft4ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.CbAfft5ContentBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.RateInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean;

public class SigningContractSendArgBean extends BaseBean
{
	@NotBlank
	private String loanType;

	@NotBlank
	private String contractNo;

	@NotBlank
	private String contractVersion;

	@NotBlank
	private String courtName;

	@NotBlank
	private String branchCode;

	@NotBlank
	private String borrowerName;

	@NotBlank
	private String borrowerId;

	@NotNull
	private Date borrowerBirthDate;

	@NotBlank
	private String borrowerMobileNumber;

	@NotBlank
	private String borrowerEmail;

	private Boolean isBorrowerYouth;

	private String relatedPersonType;

	private String relatedPersonName;

	private String relatedPersonId;

	private Date relatedPersonBirthDate;

	private String relatedPersonMobileNumber;

	private String relatedPersonEmail;

	@NotBlank
	private String productCode;

	@NotNull
	private Date expiredDate;

	@JsonProperty( "loanConditionInfo" )
	@NotNull
	@Valid
	private LoanConditionInfoBean loanConditionInfoBean;

	@NotBlank
	private String generalGuaranteePlan;

	@NotBlank
	private String generalGuaranteePlanInfo;

	@NotBlank
	private String jointGuaranteePlan;

	@NotBlank
	private String jointGuaranteePlanInfo;

	private BigDecimal guaranteeAmt;

	private List<String> loanAccts;

	private String loanPlan;

	private String grpCntrNo;

	private Date givenApprBegDate;

	private Date givenApprEndDate;

	private String payeeBankCode;

	private String payeeBankAccountNo;

	private String payeeBankAccountName;

	private Integer payeeTotalAmt;

	private Integer payeeRemittance;

	private Integer payeeSelfProvide;

	private BigDecimal baseRate;

	private List<RateInfoBean> rateList;

	private Boolean isRepayment;

	private List<RepaymentBean> repaymentList;

	private Boolean staffRule;

	private PayeeInfoCreateArgBean payeeInfo;

	private ExpireInfoCreateArgBean expireInfo;

	private RepaymentInfoCreateArgBean repaymentInfo;

	private InterestInfoCreateArgBean interestInfo;

	private BigDecimal guaranteeType;

	private String witness;

	private String brNoTel;

	private String brNoFax;

	private Integer refSystemId;

	private String prodKind;

	private String lnDate;

	private String consentVer;

	private String collateralBuildingAddr1;

	private String collateralBuildingAddr2;

	private Integer mortgageMaxAmt1;

	private Integer mortgageMaxAmt2;

	private String firstLoanDateYear;

	private String firstLoanDateMth;

	private String firstLoanDateDay;

	private Integer firstLoanAmt1;

	private Integer firstLoanAmt2;

	private List<String> collateralContractTerms;

	private String unregisteredBuildingDesc;

	private String houseLoanContractNo;

	private String coTarget;

	private String cbAfftTerms;

	private CbAfft1ContentBean cbAfft1ContentBean;

	private CbAfft2ContentBean cbAfft2ContentBean;

	private CbAfft3ContentBean cbAfft3ContentBean;

	private CbAfft4ContentBean cbAfft4ContentBean;

	private CbAfft5ContentBean cbAfft5ContentBean;

	private String cbAfftVersion;

	public SigningContractSendArgBean()
	{}

	public BigDecimal getBaseRate()
	{
		return baseRate;
	}

	public Date getBorrowerBirthDate()
	{
		return borrowerBirthDate;
	}

	public String getBorrowerEmail()
	{
		return borrowerEmail;
	}

	public String getBorrowerId()
	{
		return borrowerId;
	}

	public String getBorrowerMobileNumber()
	{
		return borrowerMobileNumber;
	}

	public String getBorrowerName()
	{
		return borrowerName;
	}

	public String getBranchCode()
	{
		return branchCode;
	}

	public String getBrNoFax()
	{
		return brNoFax;
	}

	public String getBrNoTel()
	{
		return brNoTel;
	}

	public CbAfft1ContentBean getCbAfft1ContentBean()
	{
		return cbAfft1ContentBean;
	}

	public CbAfft2ContentBean getCbAfft2ContentBean()
	{
		return cbAfft2ContentBean;
	}

	public CbAfft3ContentBean getCbAfft3ContentBean()
	{
		return cbAfft3ContentBean;
	}

	public CbAfft4ContentBean getCbAfft4ContentBean()
	{
		return cbAfft4ContentBean;
	}

	public CbAfft5ContentBean getCbAfft5ContentBean()
	{
		return cbAfft5ContentBean;
	}

	public String getCbAfftTerms()
	{
		return cbAfftTerms;
	}

	public String getCbAfftVersion()
	{
		return cbAfftVersion;
	}

	public String getCollateralBuildingAddr1()
	{
		return collateralBuildingAddr1;
	}

	public String getCollateralBuildingAddr2()
	{
		return collateralBuildingAddr2;
	}

	public List<String> getCollateralContractTerms()
	{
		return collateralContractTerms;
	}

	public String getConsentVer()
	{
		return consentVer;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getContractVersion()
	{
		return contractVersion;
	}

	public String getCoTarget()
	{
		return coTarget;
	}

	public String getCourtName()
	{
		return courtName;
	}

	public Date getExpiredDate()
	{
		return expiredDate;
	}

	public ExpireInfoCreateArgBean getExpireInfo()
	{
		return expireInfo;
	}

	public Integer getFirstLoanAmt1()
	{
		return firstLoanAmt1;
	}

	public Integer getFirstLoanAmt2()
	{
		return firstLoanAmt2;
	}

	public String getFirstLoanDateDay()
	{
		return firstLoanDateDay;
	}

	public String getFirstLoanDateMth()
	{
		return firstLoanDateMth;
	}

	public String getFirstLoanDateYear()
	{
		return firstLoanDateYear;
	}

	public String getGeneralGuaranteePlan()
	{
		return generalGuaranteePlan;
	}

	public String getGeneralGuaranteePlanInfo()
	{
		return generalGuaranteePlanInfo;
	}

	public Date getGivenApprBegDate()
	{
		return givenApprBegDate;
	}

	public Date getGivenApprEndDate()
	{
		return givenApprEndDate;
	}

	public String getGrpCntrNo()
	{
		return grpCntrNo;
	}

	public BigDecimal getGuaranteeAmt()
	{
		return guaranteeAmt;
	}

	public BigDecimal getGuaranteeType()
	{
		return guaranteeType;
	}

	public String getHouseLoanContractNo()
	{
		return houseLoanContractNo;
	}

	public InterestInfoCreateArgBean getInterestInfo()
	{
		return interestInfo;
	}

	public Boolean getIsBorrowerYouth()
	{
		return isBorrowerYouth;
	}

	public Boolean getIsRepayment()
	{
		return isRepayment;
	}

	public String getJointGuaranteePlan()
	{
		return jointGuaranteePlan;
	}

	public String getJointGuaranteePlanInfo()
	{
		return jointGuaranteePlanInfo;
	}

	public String getLnDate()
	{
		return lnDate;
	}

	public List<String> getLoanAccts()
	{
		return loanAccts;
	}

	public LoanConditionInfoBean getLoanConditionInfoBean()
	{
		return loanConditionInfoBean;
	}

	public String getLoanPlan()
	{
		return loanPlan;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public Integer getMortgageMaxAmt1()
	{
		return mortgageMaxAmt1;
	}

	public Integer getMortgageMaxAmt2()
	{
		return mortgageMaxAmt2;
	}

	public String getPayeeBankAccountName()
	{
		return payeeBankAccountName;
	}

	public String getPayeeBankAccountNo()
	{
		return payeeBankAccountNo;
	}

	public String getPayeeBankCode()
	{
		return payeeBankCode;
	}

	public PayeeInfoCreateArgBean getPayeeInfo()
	{
		return payeeInfo;
	}

	public Integer getPayeeRemittance()
	{
		return payeeRemittance;
	}

	public Integer getPayeeSelfProvide()
	{
		return payeeSelfProvide;
	}

	public Integer getPayeeTotalAmt()
	{
		return payeeTotalAmt;
	}

	public String getProdKind()
	{
		return prodKind;
	}

	public String getProductCode()
	{
		return productCode;
	}

	public List<RateInfoBean> getRateList()
	{
		return rateList;
	}

	public Integer getRefSystemId()
	{
		return refSystemId;
	}

	public Date getRelatedPersonBirthDate()
	{
		return relatedPersonBirthDate;
	}

	public String getRelatedPersonEmail()
	{
		return relatedPersonEmail;
	}

	public String getRelatedPersonId()
	{
		return relatedPersonId;
	}

	public String getRelatedPersonMobileNumber()
	{
		return relatedPersonMobileNumber;
	}

	public String getRelatedPersonName()
	{
		return relatedPersonName;
	}

	public String getRelatedPersonType()
	{
		return relatedPersonType;
	}

	public RepaymentInfoCreateArgBean getRepaymentInfo()
	{
		return repaymentInfo;
	}

	public List<RepaymentBean> getRepaymentList()
	{
		return repaymentList;
	}

	public Boolean getStaffRule()
	{
		return staffRule;
	}

	public String getUnregisteredBuildingDesc()
	{
		return unregisteredBuildingDesc;
	}

	public String getWitness()
	{
		return witness;
	}

	public void setBaseRate( BigDecimal baseRate )
	{
		this.baseRate = baseRate;
	}

	public void setBorrowerBirthDate( Date borrowerBirthDate )
	{
		this.borrowerBirthDate = borrowerBirthDate;
	}

	public void setBorrowerEmail( String borrowerEmail )
	{
		this.borrowerEmail = borrowerEmail;
	}

	public void setBorrowerId( String borrowerId )
	{
		this.borrowerId = borrowerId;
	}

	public void setBorrowerMobileNumber( String borrowerMobileNumber )
	{
		this.borrowerMobileNumber = borrowerMobileNumber;
	}

	public void setBorrowerName( String borrowerName )
	{
		this.borrowerName = borrowerName;
	}

	public void setBranchCode( String branchCode )
	{
		this.branchCode = branchCode;
	}

	public void setBrNoFax( String brNoFax )
	{
		this.brNoFax = brNoFax;
	}

	public void setBrNoTel( String brNoTel )
	{
		this.brNoTel = brNoTel;
	}

	public void setCbAfft1ContentBean( CbAfft1ContentBean cbAfft1ContentBean )
	{
		this.cbAfft1ContentBean = cbAfft1ContentBean;
	}

	public void setCbAfft2ContentBean( CbAfft2ContentBean cbAfft2ContentBean )
	{
		this.cbAfft2ContentBean = cbAfft2ContentBean;
	}

	public void setCbAfft3ContentBean( CbAfft3ContentBean cbAfft3ContentBean )
	{
		this.cbAfft3ContentBean = cbAfft3ContentBean;
	}

	public void setCbAfft4ContentBean( CbAfft4ContentBean cbAfft4ContentBean )
	{
		this.cbAfft4ContentBean = cbAfft4ContentBean;
	}

	public void setCbAfft5ContentBean( CbAfft5ContentBean cbAfft5ContentBean )
	{
		this.cbAfft5ContentBean = cbAfft5ContentBean;
	}

	public void setCbAfftTerms( String cbAfftTerms )
	{
		this.cbAfftTerms = cbAfftTerms;
	}

	public void setCbAfftVersion( String cbAfftVersion )
	{
		this.cbAfftVersion = cbAfftVersion;
	}

	public void setCollateralBuildingAddr1( String collateralBuildingAddr1 )
	{
		this.collateralBuildingAddr1 = collateralBuildingAddr1;
	}

	public void setCollateralBuildingAddr2( String collateralBuildingAddr2 )
	{
		this.collateralBuildingAddr2 = collateralBuildingAddr2;
	}

	public void setCollateralContractTerms( List<String> collateralContractTerms )
	{
		this.collateralContractTerms = collateralContractTerms;
	}

	public void setConsentVer( String consentVer )
	{
		this.consentVer = consentVer;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setContractVersion( String contractVersion )
	{
		this.contractVersion = contractVersion;
	}

	public void setCoTarget( String coTarget )
	{
		this.coTarget = coTarget;
	}

	public void setCourtName( String courtName )
	{
		this.courtName = courtName;
	}

	public void setExpiredDate( Date expiredDate )
	{
		this.expiredDate = expiredDate;
	}

	public void setExpireInfo( ExpireInfoCreateArgBean expireInfo )
	{
		this.expireInfo = expireInfo;
	}

	public void setFirstLoanAmt1( Integer firstLoanAmt1 )
	{
		this.firstLoanAmt1 = firstLoanAmt1;
	}

	public void setFirstLoanAmt2( Integer firstLoanAmt2 )
	{
		this.firstLoanAmt2 = firstLoanAmt2;
	}

	public void setFirstLoanDateDay( String firstLoanDateDay )
	{
		this.firstLoanDateDay = firstLoanDateDay;
	}

	public void setFirstLoanDateMth( String firstLoanDateMth )
	{
		this.firstLoanDateMth = firstLoanDateMth;
	}

	public void setFirstLoanDateYear( String firstLoanDateYear )
	{
		this.firstLoanDateYear = firstLoanDateYear;
	}

	public void setGeneralGuaranteePlan( String generalGuaranteePlan )
	{
		this.generalGuaranteePlan = generalGuaranteePlan;
	}

	public void setGeneralGuaranteePlanInfo( String generalGuaranteePlanInfo )
	{
		this.generalGuaranteePlanInfo = generalGuaranteePlanInfo;
	}

	public void setGivenApprBegDate( Date givenApprBegDate )
	{
		this.givenApprBegDate = givenApprBegDate;
	}

	public void setGivenApprEndDate( Date givenApprEndDate )
	{
		this.givenApprEndDate = givenApprEndDate;
	}

	public void setGrpCntrNo( String grpCntrNo )
	{
		this.grpCntrNo = grpCntrNo;
	}

	public void setGuaranteeAmt( BigDecimal guaranteeAmt )
	{
		this.guaranteeAmt = guaranteeAmt;
	}

	public void setGuaranteeType( BigDecimal guaranteeType )
	{
		this.guaranteeType = guaranteeType;
	}

	public void setHouseLoanContractNo( String houseLoanContractNo )
	{
		this.houseLoanContractNo = houseLoanContractNo;
	}

	public void setInterestInfo( InterestInfoCreateArgBean interestInfo )
	{
		this.interestInfo = interestInfo;
	}

	public void setIsBorrowerYouth( Boolean isBorrowerYouth )
	{
		this.isBorrowerYouth = isBorrowerYouth;
	}

	public void setIsRepayment( Boolean isRepayment )
	{
		this.isRepayment = isRepayment;
	}

	public void setJointGuaranteePlan( String jointGuaranteePlan )
	{
		this.jointGuaranteePlan = jointGuaranteePlan;
	}

	public void setJointGuaranteePlanInfo( String jointGuaranteePlanInfo )
	{
		this.jointGuaranteePlanInfo = jointGuaranteePlanInfo;
	}

	public void setLnDate( String lnDate )
	{
		this.lnDate = lnDate;
	}

	public void setLoanAccts( List<String> loanAccts )
	{
		this.loanAccts = loanAccts;
	}

	public void setLoanConditionInfoBean( LoanConditionInfoBean loanConditionInfoBean )
	{
		this.loanConditionInfoBean = loanConditionInfoBean;
	}

	public void setLoanPlan( String loanPlan )
	{
		this.loanPlan = loanPlan;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setMortgageMaxAmt1( Integer mortgageMaxAmt1 )
	{
		this.mortgageMaxAmt1 = mortgageMaxAmt1;
	}

	public void setMortgageMaxAmt2( Integer mortgageMaxAmt2 )
	{
		this.mortgageMaxAmt2 = mortgageMaxAmt2;
	}

	public void setPayeeBankAccountName( String payeeBankAccountName )
	{
		this.payeeBankAccountName = payeeBankAccountName;
	}

	public void setPayeeBankAccountNo( String payeeBankAccountNo )
	{
		this.payeeBankAccountNo = payeeBankAccountNo;
	}

	public void setPayeeBankCode( String payeeBankCode )
	{
		this.payeeBankCode = payeeBankCode;
	}

	public void setPayeeInfo( PayeeInfoCreateArgBean payeeInfo )
	{
		this.payeeInfo = payeeInfo;
	}

	public void setPayeeRemittance( Integer payeeRemittance )
	{
		this.payeeRemittance = payeeRemittance;
	}

	public void setPayeeSelfProvide( Integer payeeSelfProvide )
	{
		this.payeeSelfProvide = payeeSelfProvide;
	}

	public void setPayeeTotalAmt( Integer payeeTotalAmt )
	{
		this.payeeTotalAmt = payeeTotalAmt;
	}

	public void setProdKind( String prodKind )
	{
		this.prodKind = prodKind;
	}

	public void setProductCode( String productCode )
	{
		this.productCode = productCode;
	}

	public void setRateList( List<RateInfoBean> rateList )
	{
		this.rateList = rateList;
	}

	public void setRefSystemId( Integer refSystemId )
	{
		this.refSystemId = refSystemId;
	}

	public void setRelatedPersonBirthDate( Date relatedPersonBirthDate )
	{
		this.relatedPersonBirthDate = relatedPersonBirthDate;
	}

	public void setRelatedPersonEmail( String relatedPersonEmail )
	{
		this.relatedPersonEmail = relatedPersonEmail;
	}

	public void setRelatedPersonId( String relatedPersonId )
	{
		this.relatedPersonId = relatedPersonId;
	}

	public void setRelatedPersonMobileNumber( String relatedPersonMobileNumber )
	{
		this.relatedPersonMobileNumber = relatedPersonMobileNumber;
	}

	public void setRelatedPersonName( String relatedPersonName )
	{
		this.relatedPersonName = relatedPersonName;
	}

	public void setRelatedPersonType( String relatedPersonType )
	{
		this.relatedPersonType = relatedPersonType;
	}

	public void setRepamentList( List<RepaymentBean> repaymentList )
	{
		this.repaymentList = repaymentList;
	}

	public void setRepaymentInfo( RepaymentInfoCreateArgBean repaymentInfo )
	{
		this.repaymentInfo = repaymentInfo;
	}

	public void setRepaymentList( List<RepaymentBean> repaymentList )
	{
		this.repaymentList = repaymentList;
	}

	public void setStaffRule( Boolean staffRule )
	{
		this.staffRule = staffRule;
	}

	public void setUnregisteredBuildingDesc( String unregisteredBuildingDesc )
	{
		this.unregisteredBuildingDesc = unregisteredBuildingDesc;
	}

	public void setWitness( String witness )
	{
		this.witness = witness;
	}
}
