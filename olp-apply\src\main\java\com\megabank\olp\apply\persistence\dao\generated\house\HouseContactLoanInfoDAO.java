package com.megabank.olp.apply.persistence.dao.generated.house;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.house.ContactLoanCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.house.HouseContactLoanInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseContactLoanInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseContactLoanInfoDAO extends BasePojoDAO<HouseContactLoanInfo, Long>
{
	@Autowired
	private HouseContactInfoDAO contactInfoDAO;

	public Long create( ContactLoanCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getContactId() );

		HouseContactLoanInfo pojo = new HouseContactLoanInfo();
		pojo.setHouseContactInfo( contactInfoDAO.read( paramBean.getContactId() ) );
		pojo.setLoanBalance( paramBean.getLoanBalance() );
		pojo.setCounty( paramBean.getCounty() );
		pojo.setDistrict( paramBean.getDistrict() );
		pojo.setAddress( paramBean.getAddress() );
		pojo.setLoanPurpose( paramBean.getLoanPurpose() );

		return super.createPojo( pojo );
	}

	@Override
	protected Class<HouseContactLoanInfo> getPojoClass()
	{
		return HouseContactLoanInfo.class;
	}
}
