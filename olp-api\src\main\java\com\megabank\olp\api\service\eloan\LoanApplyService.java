/**
 *
 */
package com.megabank.olp.api.service.eloan;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.management.apply.ApplyBranchBankUpdatedClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.ApplyLoanDiscardedClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyBranchBankUpdatedArgBean;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyLoanDiscardedArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
public class LoanApplyService extends BaseApiService
{

	private static final String ELOAN_CONSTANT = "eloan";

	@Autowired
	private ApplyBranchBankUpdatedClient applyBranchBankUpdatedClient;

	@Autowired
	private ApplyLoanDiscardedClient applyLoanDiscardedClient;

	public void discardLoan( String caseNo )
	{
		String employeeId = ELOAN_CONSTANT;
		String employeeName = ELOAN_CONSTANT;

		ApplyLoanDiscardedArgBean argBean = new ApplyLoanDiscardedArgBean();
		argBean.setCaseNo( caseNo );
		argBean.setEmployeeId( employeeId );
		argBean.setEmployeeName( employeeName );

		applyLoanDiscardedClient.send( argBean, new JwtArgBean() );

	}

	public void updateBranch( String caseNo, String branchBankCode )
	{
		String employeeId = ELOAN_CONSTANT;
		String employeeName = ELOAN_CONSTANT;

		ApplyBranchBankUpdatedArgBean argBean = new ApplyBranchBankUpdatedArgBean();
		argBean.setCaseNo( caseNo );
		argBean.setBranchBankCode( branchBankCode );
		argBean.setEmployeeId( employeeId );
		argBean.setEmployeeName( employeeName );

		applyBranchBankUpdatedClient.send( argBean, new JwtArgBean() );
	}

}
