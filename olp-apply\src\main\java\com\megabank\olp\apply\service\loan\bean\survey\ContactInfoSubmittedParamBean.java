package com.megabank.olp.apply.service.loan.bean.survey;

import com.megabank.olp.base.bean.BaseBean;

public class ContactInfoSubmittedParamBean extends BaseBean
{
	private String name;

	private String branchBankCode;

	private String phoneCode;

	private String phoneNumber;

	private String phoneExt;

	private String email;

	private String mobileNumber;

	private String contactTimeCode;

	private String otherMsg;

	private String sexCode;

	public ContactInfoSubmittedParamBean()
	{
		// default constructor
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getPhoneCode()
	{
		return phoneCode;
	}

	public String getPhoneExt()
	{
		return phoneExt;
	}

	public String getPhoneNumber()
	{
		return phoneNumber;
	}

	public String getSexCode()
	{
		return sexCode;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setPhoneCode( String phoneCode )
	{
		this.phoneCode = phoneCode;
	}

	public void setPhoneExt( String phoneExt )
	{
		this.phoneExt = phoneExt;
	}

	public void setPhoneNumber( String phoneNumber )
	{
		this.phoneNumber = phoneNumber;
	}

	public void setSexCode( String sexCode )
	{
		this.sexCode = sexCode;
	}

}
