package com.megabank.olp.apply.controller.ixml.bean;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class QueryVerifyResultArgBean extends BaseBean
{
	@NotBlank
	private String verifyNo;

	@NotBlank
	private String id;

	@NotBlank
	private String token;

	@NotBlank
	private String loanType;

	public QueryVerifyResultArgBean()
	{}

	public String getId()
	{
		return id;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getToken()
	{
		return token;
	}

	public String getVerifyNo()
	{
		return verifyNo;
	}

	public void setId( String id )
	{
		this.id = id;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setToken( String token )
	{
		this.token = token;
	}

	public void setVerifyNo( String verifyNo )
	{
		this.verifyNo = verifyNo;
	}

}
