/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import java.util.Date;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningRateCreatedParamBean extends BaseBean
{
	private Long signingContractId;

	public SigningRateCreatedParamBean()
	{}

	private Long rateId;

	private String rateType;

	private int rateBgn;

	private int rateEnd;

	private double rate;

	private Date createTime;

	private Date updateTime;

	public Long getSigningContractId() {
		return signingContractId;
	}

	public void setSigningContractId(Long signingContractId) {
		this.signingContractId = signingContractId;
	}

	public Long getRateId() {
		return rateId;
	}

	public void setRateId(Long rateId) {
		this.rateId = rateId;
	}

	public String getRateType() {
		return rateType;
	}

	public void setRateType(String rateType) {
		this.rateType = rateType;
	}

	public int getRateBgn() {
		return rateBgn;
	}

	public void setRateBgn(int rateBgn) {
		this.rateBgn = rateBgn;
	}

	public int getRateEnd() {
		return rateEnd;
	}

	public void setRateEnd(int rateEnd) {
		this.rateEnd = rateEnd;
	}

	public double getRate() {
		return rate;
	}

	public void setRate(double rate) {
		this.rate = rate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
