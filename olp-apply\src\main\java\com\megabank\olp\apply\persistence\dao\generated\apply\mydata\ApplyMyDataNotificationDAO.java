package com.megabank.olp.apply.persistence.dao.generated.apply.mydata;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.mydata.ApplyMyDataNotification;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.enums.OrderEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyMyDataNotificationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyMyDataNotificationDAO extends BasePojoDAO<ApplyMyDataNotification, Long>
{
	@Autowired
	private ApplyMyDataDAO applyMyDataDAO;

	public Long create( Long myDataId, int waitSec )
	{
		Validate.notNull( myDataId );

		ApplyMyDataNotification pojo = new ApplyMyDataNotification();
		pojo.setApplyMyData( applyMyDataDAO.read( myDataId ) );
		pojo.setWaitSec( waitSec );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public List<ApplyMyDataNotification> getPojosByMyDataId( Long myDataId )
	{
		Validate.notNull( myDataId );

		NameValueBean condition = new NameValueBean( ApplyMyDataNotification.APPLY_MY_DATA_CONSTANT, applyMyDataDAO.read( myDataId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( ApplyMyDataNotification.CREATED_DATE_CONSTANT, OrderEnum.DESCEND ) };

		return getPojosByPropertyOrderBy( condition, orderBeans );
	}

	@Override
	protected Class<ApplyMyDataNotification> getPojoClass()
	{
		return ApplyMyDataNotification.class;
	}
}
