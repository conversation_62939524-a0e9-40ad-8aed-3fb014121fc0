package com.megabank.olp.apply.service.loan;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.collateral.AgreementResBean;
import com.megabank.olp.apply.service.loan.bean.collateral.AgreementSubmittedParamBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class CollateralServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;
	
	@Autowired
	private CollateralService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void checkLoanCollateralExisted()
	{
		String idNo = "A123456789";

		boolean result = service.checkLoanCollateralExisted( idNo );

		logger.info( "result:{}", result );
	}

	@Test
	public void getAgreement()
	{
		AgreementResBean resBean = service.getAgreement();

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getThankyouMessage()
	{
		MessageResBean resBean = service.getThankyouMessage();

		logger.info( "resBean:{}", resBean );
	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	@Test
	public void submitAgreement() throws IOException
	{
		Long result = service.submitAgreement( 220L );

		logger.info( "result:{}", result );
	}

	private AgreementSubmittedParamBean getAgreementSubmittedParamBean()
	{
		AgreementSubmittedParamBean paramBean = new AgreementSubmittedParamBean();
		paramBean.setEmail( "<EMAIL>" );
		paramBean.setIsCollateralFullPayment( true );
		paramBean.setCollateralAddressTownCode( "6302" );
		paramBean.setCollateralAddressStreet( "00路00號" );
		paramBean.setCollateralAmt( 100L );
		paramBean.setLoanAmt( 100L );
		paramBean.setWarrantee1( "warrantee-1" );
		paramBean.setWarrantee2( "warrantee-2" );
		paramBean.setWarrantee3( "warrantee-3" );
		paramBean.setGuranteeAmt( 100L );
		paramBean.setSignatory( "簽約人" );
		paramBean.setTermNo( "001" );
		paramBean.setBorrowerSignDate( new Date() );
		paramBean.setLoanProduct1( "loan-product-1" );
		paramBean.setLoanProduct2( "loan-product-2" );
		paramBean.setLoanProduct3( "loan-product-3" );
		paramBean.setLoanProduct4( "loan-product-4" );
		paramBean.setLoanProduct5( "loan-product-5" );

		return paramBean;
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.SKIP.getContext(), IdentityTypeEnum.OTHER_BANK.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}

}