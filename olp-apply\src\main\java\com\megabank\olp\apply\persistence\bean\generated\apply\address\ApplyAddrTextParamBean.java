package com.megabank.olp.apply.persistence.bean.generated.apply.address;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyAddrTextParamBean extends BaseBean
{
	private String addrPostalCode;

	private String addr;

	public ApplyAddrTextParamBean()
	{
		// default constructor
	}

	public ApplyAddrTextParamBean( String addrPostalCode, String addr )
	{
		this.addrPostalCode = addrPostalCode;
		this.addr = addr;
	}

	public String getAddr()
	{
		return addr;
	}

	public String getAddrPostalCode()
	{
		return addrPostalCode;
	}

	public void setAddr( String addr )
	{
		this.addr = addr;
	}

	public void setAddrPostalCode( String addrPostalCode )
	{
		this.addrPostalCode = addrPostalCode;
	}
}
