/**
 *
 */
package com.megabank.olp.apply.service.management;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
class NotificationServiceIntegration
{
	@Autowired
	private NotificationService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void sendHouseLoanAttachment()
	{
		service.sendHouseLoanAttachment();
	}

	@Test
	public void sendHouseLoanCompleted()
	{
		service.sendHouseLoanCompleted();
	}

	@Test
	public void sendHouseLoanPromotion()
	{
		service.sendHouseLoanPromotion();
	}

	@Test
	public void sendLoanCollateral()
	{
		service.sendLoanCollateral();
	}

	@Test
	public void sendPersonalLoanAttachment()
	{
		service.sendPersonalLoanAttachment();
	}

	@Test
	public void sendPersonalLoanCompleted()
	{
		service.sendPersonalLoanCompleted();
	}

	@Test
	public void sendPersonalLoanPromotion()
	{
		service.sendPersonalLoanPromotion();
	}

	@Test
	public void sendSigningContract()
	{
		service.sendSigningContract();
	}

}
