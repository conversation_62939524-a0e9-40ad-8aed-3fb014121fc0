package com.megabank.olp.apply.persistence.bean.generated.apply.contact;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyContactMeCreatedParamBean extends BaseBean
{
	private String caseNo;

	private String name;

	private Long branchBankId;

	private String phoneCode;

	private String phoneNumber;

	private String phoneExt;

	private String mobileNumber;

	private String email;

	private String contactTimeCode;

	private String otherMsg;

	private String processCode;

	private Long finalBranchBankId;

	private String sexCode;

	private String loanPlanCode;

	private Long introducerBranchBankId;

	private String introducerEmpId;

	public ApplyContactMeCreatedParamBean()
	{
		// default constructor
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	public String getEmail()
	{
		return email;
	}

	public Long getFinalBranchBankId()
	{
		return finalBranchBankId;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getPhoneCode()
	{
		return phoneCode;
	}

	public String getPhoneExt()
	{
		return phoneExt;
	}

	public String getPhoneNumber()
	{
		return phoneNumber;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public String getSexCode()
	{
		return sexCode;
	}

	public String getLoanPlanCode() {
		return loanPlanCode;
	}

	public void setBranchBankId(Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setFinalBranchBankId( Long finalBranchBankId )
	{
		this.finalBranchBankId = finalBranchBankId;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setPhoneCode( String phoneCode )
	{
		this.phoneCode = phoneCode;
	}

	public void setPhoneExt( String phoneExt )
	{
		this.phoneExt = phoneExt;
	}

	public void setPhoneNumber( String phoneNumber )
	{
		this.phoneNumber = phoneNumber;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

	public void setSexCode( String sexCode )
	{
		this.sexCode = sexCode;
	}

	public void setLoanPlanCode(String loanPlanCode) {
		this.loanPlanCode = loanPlanCode;
	}

	public Long getIntroducerBranchBankId() {
		return introducerBranchBankId;
	}

	public void setIntroducerBranchBankId(Long introducerBranchBankId) {
		this.introducerBranchBankId = introducerBranchBankId;
	}

	public String getIntroducerEmpId() {
		return introducerEmpId;
	}

	public void setIntroducerEmpId(String introducerEmpId) {
		this.introducerEmpId = introducerEmpId;
	}
}
