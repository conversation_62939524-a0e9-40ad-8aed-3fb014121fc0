package com.megabank.olp.api.filter;

import java.io.IOException;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.megabank.olp.api.service.client.ApiClientService;
import com.megabank.olp.base.enums.CommonErrorEnum;
import com.megabank.olp.base.utility.web.CommonAppUtils;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class ApiClientAddressFilter extends OncePerRequestFilter
{
	@Autowired
	private ApiClientService apiClientService;

	private final Logger theLogger = LogManager.getLogger( getClass() );

	@Override
	protected void doFilterInternal( HttpServletRequest request, HttpServletResponse response, FilterChain filterChain )
		throws ServletException, IOException
	{
		theLogger.traceEntry();

		String clientAddress = CommonAppUtils.getClientAddress( request );
		String requestUrl = request.getServletPath();

		boolean isLegalAddress = apiClientService.checkLegalAddress( requestUrl, clientAddress );

		if( !isLegalAddress )
		{
			CommonAppUtils.forward2Error( request, response, CommonErrorEnum.ILLEGAL_ADDRESS, new String[]{ clientAddress, requestUrl } );

			return;
		}

		filterChain.doFilter( request, response );
	}

}
