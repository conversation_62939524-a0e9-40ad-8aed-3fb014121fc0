package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.apply.persistence.pojo.code.CodeList;

/**
 * The CodeListDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeListDAO extends BasePojoDAO<CodeList, Long>
{
	public void create( String codeType, String codeValue, String codeDesc, int order )
	{
		Validate.notNull( codeType );
		Validate.notNull( codeValue );

		CodeList pojo = new CodeList();
		pojo.setCodeType( codeType );
		pojo.setCodeValue( codeValue );
		pojo.setCodeDesc( codeDesc );
		pojo.setCodeDisplayOrder( order );
		pojo.setCodeModeifyTime( new Date() );

		super.createPojo( pojo );
	}
	
	public int updateAllDisable( String codeType , boolean disable)
	{
		List<CodeList> codeLists = this.getPojosByCodeType(codeType);
		List<Long> ids = new ArrayList<Long>();
		for(CodeList codeList :codeLists) {
			ids.add( codeList.getCodeId() );
			if(ids.size()>1000) {
				updateDisable(ids, disable);
				ids = new ArrayList<Long>();
			}
		}
		return updateDisable(ids, disable);
	}
	
	public int updateAllDisable( List<Long> updateIds , boolean disable)
	{
		List<Long> ids = new ArrayList<Long>();
		for(Long id :updateIds) {
			ids.add( id );
			if(ids.size()>1000) {
				updateDisable(ids, disable);
				ids = new ArrayList<Long>();
			}
		}
		return updateDisable(ids, disable);
	}
	
	private int updateDisable( List<Long> ids, boolean disable)
	{
		if(ids.size()>0) {
			Validate.notEmpty( ids );

			NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "codelist.updateBranch" );
			nativeQuery.setParameterList( CodeList.CODE_ID_CONSTANT, ids, Long.class );
			nativeQuery.setParameter( CodeList.CODE_DISABLED_CONSTANT, disable, Integer.class );

			return nativeQuery.executeUpdate();
		}
		return 0;
	}
	

	public List<CodeList> getPojosByCodeTypeAll( String codeType )
	{

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( CodeList.CODE_TYPE_CONSTANT, codeType ) };

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( CodeList.CODE_DISPLAY_ORDER_CONSTANT ) };

		return this.getPojosByPropertiesOrderBy( conditions, orderBeans );
	}
	
	public List<CodeList> getPojosByCodeType( String codeType )
	{

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( CodeList.CODE_TYPE_CONSTANT, codeType ),
														  new NameValueBean( CodeList.CODE_DISABLED_CONSTANT, false ) };

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( CodeList.CODE_DISPLAY_ORDER_CONSTANT ) };

		return this.getPojosByPropertiesOrderBy( conditions, orderBeans );
	}
	
	public List<String> getStringByCodeType( String codeType )
	{

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( CodeList.CODE_TYPE_CONSTANT, codeType ),
														  new NameValueBean( CodeList.CODE_DISABLED_CONSTANT, false ) };

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( CodeList.CODE_DISPLAY_ORDER_CONSTANT ) };
		
		List<CodeList> codeLists = this.getPojosByPropertiesOrderBy( conditions, orderBeans );
		
		List<String> codeTypeString= new ArrayList<String>();
		for(CodeList codeList :codeLists) {
			codeTypeString.add( codeList.getCodeValue() );
		}

		return codeTypeString;
	}
	
	public CodeList getPojosByCodeTypeAndcodeValue( String codeType ,String codeValue)
	{

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( CodeList.CODE_TYPE_CONSTANT, codeType ),
		                                                  new NameValueBean( CodeList.CODE_VALUE_CONSTANT, codeValue )};

		return this.getUniquePojoByProperties( conditions );
	}

	@Override
	protected Class<CodeList> getPojoClass()
	{
		return CodeList.class;
	}
}
