package com.megabank.olp.apply.service.management;

import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.download.FileDownloadedResBean;
import com.megabank.olp.apply.service.management.bean.contact.ContactMeDetailResBean;
import com.megabank.olp.apply.service.management.bean.contact.ContactMeExportedParamBean;
import com.megabank.olp.apply.service.management.bean.contact.ContactMeListedParamBean;
import com.megabank.olp.apply.service.management.bean.contact.ContactMeListedResBean;
import com.megabank.olp.apply.service.management.bean.contact.ReassignBranchBankResBean;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ContactMeServiceIntegration
{
	@Autowired
	private ContactMeService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void exportList()
	{
		ContactMeExportedParamBean paramBean = new ContactMeExportedParamBean();
		
		Boolean headOffice = true;

		FileDownloadedResBean resBean = service.exportList( paramBean, headOffice );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getDetail()
	{
		Long contactMeId = 1L;

		ContactMeDetailResBean resBean = service.getDetail( contactMeId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getReassignBranchBank()
	{
		Long contactMeId = 1L;

		List<ReassignBranchBankResBean> resBean = service.getReassignBranchBank( contactMeId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listContactMe()
	{
		ContactMeListedParamBean paramBean = new ContactMeListedParamBean();
		paramBean.setPage( 1 );

		ContactMeListedResBean resBean = service.listContactMe( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void updateProcessStatus()
	{
		Long contactMeId = 1L;
		String processCode = "processing";
		String employeeId = "developer-01";
		String employeeName = "developer";

		Long id = service.updateProcessStatus( contactMeId, processCode, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	private ContactMeListedParamBean getContactMeListParamBean()
	{
		String name = "王大明";
		String mobileNumber = "**********";
		Date createdDateStart = CommonDateUtils.getDate( 2020, 1, 1 );
		Date createdDateEnd = CommonDateUtils.getDate( 2020, 12, 31 );
		String sortColumn = "surveyDate";
		String sortDirection = "desc";
		int page = 1;
		int length = 10;

		ContactMeListedParamBean paramBean = new ContactMeListedParamBean();
		paramBean.setName( name );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setDateStart( createdDateStart );
		paramBean.setDateEnd( createdDateEnd );
		paramBean.setPage( page );
		paramBean.setLength( length );
		paramBean.setSortColumn( sortColumn );
		paramBean.setSortDirection( sortDirection );

		return paramBean;
	}
}
