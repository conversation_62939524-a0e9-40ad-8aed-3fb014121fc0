package com.megabank.olp.api.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;
import com.megabank.olp.system.filter.MyFirstFilter;

/**
 * @version 1.0
 * <AUTHOR>
 * @company Mega Bank
 * @copyright Copyright (c) 2019
 */

@Configuration
@ComponentScan( { "com.megabank.olp.api.controller", "com.megabank.olp.api.filter", "com.megabank.olp.api.service",
				  "com.megabank.olp.api.persistence.dao", "com.megabank.olp.api.filter", "com.megabank.olp.api.security" } )
@Import( { BaseServiceConfig.class, ClientServiceConfig.class } )
public class ApiConfig
{

}
