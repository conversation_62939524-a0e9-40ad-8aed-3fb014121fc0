package com.megabank.olp.apply.controller.loan.bean.signing;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class SigningContractUpdateInfoArgBean extends BaseBean
{
	@NotBlank
	private String contractNo;

	@NotBlank
	private String verifiedEmail;

	public String getContractNo()
	{
		return contractNo;
	}

	public String getVerifiedEmail()
	{
		return verifiedEmail;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setVerifiedEmail( String verifiedEmail )
	{
		this.verifiedEmail = verifiedEmail;
	}
}
