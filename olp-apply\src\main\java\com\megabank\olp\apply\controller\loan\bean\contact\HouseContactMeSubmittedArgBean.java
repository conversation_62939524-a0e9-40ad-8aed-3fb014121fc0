package com.megabank.olp.apply.controller.loan.bean.contact;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactBasicDataBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactLoanDataBean;
import com.megabank.olp.base.bean.BaseBean;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

public class HouseContactMeSubmittedArgBean extends BaseBean
{
	@NotBlank
	private String name;

	private BigDecimal loanBalance;

	@NotBlank
	private String mobileNumber;

	private String county;

	private String district;

	private String address;

	private String loanPurpose;

	private String callBackTime;

	private Long branchBankId;

	private String loanPlanCode;

	private String otherMsg;

	public HouseContactMeSubmittedArgBean()
	{
	}

	public String getName()
	{
		return name;
	}

	public BigDecimal getLoanBalance()
	{
		return loanBalance;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getCounty()
	{
		return county;
	}

	public String getDistrict()
	{
		return district;
	}

	public String getAddress()
	{
		return address;
	}

	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public String getCallBackTime()
	{
		return callBackTime;
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public void setName(String name )
	{
		this.name = name;
	}

	public void setLoanBalance( BigDecimal loanBalance )
	{
		this.loanBalance = loanBalance;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setCounty( String county )
	{
		this.county = county;
	}

	public void setDistrict( String district )
	{
		this.district = district;
	}

	public void setAddress( String address )
	{
		this.address = address;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setCallBackTime( String callBackTime )
	{
		this.callBackTime = callBackTime;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setLoanPlanCode(String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}
}
