package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanOccupationCreatedParamBean;
import com.megabank.olp.base.config.BasePersistenceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyLoanOccupationDAOIntegration
{
	@Autowired
	private ApplyLoanOccupationDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long loanId = 1L;
		Long jobSubTypeId = 1L;
		String companyName = "公司名稱";
		int annualIncome = 60;
		int seniorityYear = 3;
		int seniorityMonth = 6;
		String titleType = "01";
		String companyTaxNo = "********";
		String companyPhoneCode = "02";
		String companyPhoneNumber = "********";
		String companyPhoneExt = "11";

		ApplyLoanOccupationCreatedParamBean paramBean = new ApplyLoanOccupationCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setJobSubTypeId( jobSubTypeId );
		paramBean.setCompanyName( companyName );
		paramBean.setAnnualIncome( annualIncome );
		paramBean.setSeniorityYear( seniorityYear );
		paramBean.setSeniorityMonth( seniorityMonth );
		paramBean.setTitleType( titleType );
		paramBean.setCompanyTaxNo( companyTaxNo );
		paramBean.setCompanyPhoneCode( companyPhoneCode );
		paramBean.setCompanyPhoneNumber( companyPhoneNumber );
		paramBean.setCompanyPhoneExt( companyPhoneExt );

		Long id = dao.create( paramBean );

		logger.info( "id:{}", id );
	}

}
