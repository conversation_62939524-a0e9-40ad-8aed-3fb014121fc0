package com.megabank.olp.apply.controller.loan.bean.upload;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class MyDataUrlGettedArgBean extends BaseBean
{
	@NotBlank
	private String loanType;

	public MyDataUrlGettedArgBean()
	{
		// default constructor
	}

	public String getLoanType()
	{
		return loanType;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

}
