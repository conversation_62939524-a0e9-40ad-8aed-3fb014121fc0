package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class CbAfft4ContentBean extends BaseBean
{
	private String cbAfft4_1;

	private String cbAfft4_2_year;

	private String cbAfft4_2_mth;

	private String cbAfft4_2_day;

	private Integer cbAfft4_3;

	private String cbAfft4_4;

	private BigDecimal cbAfft4_5;

	private BigDecimal cbAfft4_6;

	private BigDecimal cbAfft4_7;

	public CbAfft4ContentBean()
	{}

	public String getCbAfft4_1()
	{
		return cbAfft4_1;
	}

	public void setCbAfft4_1( String cbAfft4_1 )
	{
		this.cbAfft4_1 = cbAfft4_1;
	}

	public String getCbAfft4_2_year()
	{
		return cbAfft4_2_year;
	}

	public void setCbAfft4_2_year( String cbAfft4_2_year )
	{
		this.cbAfft4_2_year = cbAfft4_2_year;
	}

	public String getCbAfft4_2_mth()
	{
		return cbAfft4_2_mth;
	}

	public void setCbAfft4_2_mth( String cbAfft4_2_mth )
	{
		this.cbAfft4_2_mth = cbAfft4_2_mth;
	}

	public String getCbAfft4_2_day()
	{
		return cbAfft4_2_day;
	}

	public void setCbAfft4_2_day( String cbAfft4_2_day )
	{
		this.cbAfft4_2_day = cbAfft4_2_day;
	}

	public Integer getCbAfft4_3()
	{
		return cbAfft4_3;
	}

	public void setCbAfft4_3( Integer cbAfft4_3 )
	{
		this.cbAfft4_3 = cbAfft4_3;
	}

	public String getCbAfft4_4()
	{
		return cbAfft4_4;
	}

	public void setCbAfft4_4( String cbAfft4_4 )
	{
		this.cbAfft4_4 = cbAfft4_4;
	}

	public BigDecimal getCbAfft4_5()
	{
		return cbAfft4_5;
	}

	public void setCbAfft4_5( BigDecimal cbAfft4_5 )
	{
		this.cbAfft4_5 = cbAfft4_5;
	}

	public BigDecimal getCbAfft4_6()
	{
		return cbAfft4_6;
	}

	public void setCbAfft4_6( BigDecimal cbAfft4_6 )
	{
		this.cbAfft4_6 = cbAfft4_6;
	}

	public BigDecimal getCbAfft4_7()
	{
		return cbAfft4_7;
	}

	public void setCbAfft4_7( BigDecimal cbAfft4_7 )
	{
		this.cbAfft4_7 = cbAfft4_7;
	}
}
