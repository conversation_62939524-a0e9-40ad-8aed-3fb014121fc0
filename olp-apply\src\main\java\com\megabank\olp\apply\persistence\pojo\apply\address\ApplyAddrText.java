package com.megabank.olp.apply.persistence.pojo.apply.address;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyAddrText is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_addr_text" )
public class ApplyAddrText extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_addr_text";

	public static final String ADDR_TEXT_ID_CONSTANT = "addrTextId";

	public static final String ADDR_POSTAL_CODE_CONSTANT = "addrPostalCode";

	public static final String ADDR_CONSTANT = "addr";

	private Long addrTextId;

	private String addrPostalCode;

	private String addr;

	public ApplyAddrText()
	{}

	public ApplyAddrText( Long addrTextId )
	{
		this.addrTextId = addrTextId;
	}

	public ApplyAddrText( String addr )
	{
		this.addr = addr;
	}

	@Column( name = "addr", nullable = false )
	public String getAddr()
	{
		return addr;
	}

	@Column( name = "addr_postal_code", length = 6 )
	public String getAddrPostalCode()
	{
		return addrPostalCode;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "addr_text_id", unique = true, nullable = false )
	public Long getAddrTextId()
	{
		return addrTextId;
	}

	public void setAddr( String addr )
	{
		this.addr = addr;
	}

	public void setAddrPostalCode( String addrPostalCode )
	{
		this.addrPostalCode = addrPostalCode;
	}

	public void setAddrTextId( Long addrTextId )
	{
		this.addrTextId = addrTextId;
	}
}