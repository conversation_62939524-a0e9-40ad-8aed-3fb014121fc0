package com.megabank.olp.apply.persistence.pojo.apply.loan;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.apply.persistence.pojo.code.CodeRelationType;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyLoanRelation is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_relation" )
public class ApplyLoanRelation extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_relation";

	public static final String LOAN_RELATION_ID_CONSTANT = "loanRelationId";

	public static final String APPLY_LOAN_CONTENT_CONSTANT = "applyLoanContent";

	public static final String CODE_RELATION_TYPE_CONSTANT = "codeRelationType";

	public static final String RELATION_NAME_CONSTANT = "relationName";

	public static final String RELATION_ID_NO_CONSTANT = "relationIdNo";

	private Long loanRelationId;

	private transient ApplyLoanContent applyLoanContent;

	private transient CodeRelationType codeRelationType;

	private String relationName;

	private String relationIdNo;

	public ApplyLoanRelation()
	{}

	public ApplyLoanRelation( Long loanRelationId )
	{
		this.loanRelationId = loanRelationId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_id", nullable = false )
	public ApplyLoanContent getApplyLoanContent()
	{
		return applyLoanContent;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "relation_type", nullable = false )
	public CodeRelationType getCodeRelationType()
	{
		return codeRelationType;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "loan_relation_id", unique = true, nullable = false )
	public Long getLoanRelationId()
	{
		return loanRelationId;
	}

	@Column( name = "relation_id_no", nullable = false, length = 10 )
	public String getRelationIdNo()
	{
		return relationIdNo;
	}

	@Column( name = "relation_name", nullable = false )
	public String getRelationName()
	{
		return relationName;
	}

	public void setApplyLoanContent( ApplyLoanContent applyLoanContent )
	{
		this.applyLoanContent = applyLoanContent;
	}

	public void setCodeRelationType( CodeRelationType codeRelationType )
	{
		this.codeRelationType = codeRelationType;
	}

	public void setLoanRelationId( Long loanRelationId )
	{
		this.loanRelationId = loanRelationId;
	}

	public void setRelationIdNo( String relationIdNo )
	{
		this.relationIdNo = relationIdNo;
	}

	public void setRelationName( String relationName )
	{
		this.relationName = relationName;
	}
}