/**
 *
 */
package com.megabank.olp.api.controller.eloan.bean.apply;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class LoanDiscardedArgBean extends BaseBean
{
	@NotBlank
	private String caseNo;

	public LoanDiscardedArgBean()
	{
		// default constructor
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

}
