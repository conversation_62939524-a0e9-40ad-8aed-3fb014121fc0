package com.megabank.olp.api.controller.iloan.bean.signing;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoBean;
import com.megabank.olp.base.bean.BaseBean;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

public class PaymentInfoCreateArgILoanBean extends BaseBean {

	@NotBlank
	private String contractNo;

	@NotNull
	private Integer preliminaryFee;

	@NotNull
	@JsonProperty("kreditCheckFee")
	private Integer crChkFee;

	@NotNull
	private List<PaymentInfoBean> paymentInfoList;

	public PaymentInfoCreateArgILoanBean() {
	}

	public String getContractNo() {
		return contractNo;
	}

	public Integer getCrChkFee() {
		return crChkFee;
	}

	public List<PaymentInfoBean> getPaymentInfoList() {
		return paymentInfoList;
	}

	public Integer getPreliminaryFee() {
		return preliminaryFee;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	public void setCrChkFee(Integer crChkFee) {
		this.crChkFee = crChkFee;
	}

	public void setPaymentInfoList(List<PaymentInfoBean> paymentInfoList) {
		this.paymentInfoList = paymentInfoList;
	}

	public void setPreliminaryFee(Integer preliminaryFee) {
		this.preliminaryFee = preliminaryFee;
	}


}
