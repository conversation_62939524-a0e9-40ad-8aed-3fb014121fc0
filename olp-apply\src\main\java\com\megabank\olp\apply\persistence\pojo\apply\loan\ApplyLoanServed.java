package com.megabank.olp.apply.persistence.pojo.apply.loan;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.apply.persistence.pojo.code.CodeRepresentativeType;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyLoanServed is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_served" )
public class ApplyLoanServed extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_served";

	public static final String LOAN_SERVED_ID_CONSTANT = "loanServedId";

	public static final String APPLY_LOAN_CONTENT_CONSTANT = "applyLoanContent";

	public static final String CODE_REPRESENTATIVE_TYPE_CONSTANT = "codeRepresentativeType";

	public static final String COMPANY_NAME_CONSTANT = "companyName";

	public static final String TAX_NO_CONSTANT = "taxNo";

	public static final String SERVED_TITLE_CONSTANT = "servedTitle";

	public static final String COMMENT_CONSTANT = "comment";

	private Long loanServedId;

	private transient ApplyLoanContent applyLoanContent;

	private transient CodeRepresentativeType codeRepresentativeType;

	private String companyName;

	private String taxNo;

	private String servedTitle;

	private String comment;

	public ApplyLoanServed()
	{}

	public ApplyLoanServed( ApplyLoanContent applyLoanContent, CodeRepresentativeType codeRepresentativeType, String companyName, String servedTitle )
	{
		this.applyLoanContent = applyLoanContent;
		this.codeRepresentativeType = codeRepresentativeType;
		this.companyName = companyName;
		this.servedTitle = servedTitle;
	}

	public ApplyLoanServed( Long loanServedId )
	{
		this.loanServedId = loanServedId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_id", nullable = false )
	public ApplyLoanContent getApplyLoanContent()
	{
		return applyLoanContent;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "representative_type", nullable = false )
	public CodeRepresentativeType getCodeRepresentativeType()
	{
		return codeRepresentativeType;
	}

	@Column( name = "comment" )
	public String getComment()
	{
		return comment;
	}

	@Column( name = "company_name", nullable = false )
	public String getCompanyName()
	{
		return companyName;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "loan_served_id", unique = true, nullable = false )
	public Long getLoanServedId()
	{
		return loanServedId;
	}

	@Column( name = "served_title", nullable = false )
	public String getServedTitle()
	{
		return servedTitle;
	}

	@Column( name = "tax_no", length = 8 )
	public String getTaxNo()
	{
		return taxNo;
	}

	public void setApplyLoanContent( ApplyLoanContent applyLoanContent )
	{
		this.applyLoanContent = applyLoanContent;
	}

	public void setCodeRepresentativeType( CodeRepresentativeType codeRepresentativeType )
	{
		this.codeRepresentativeType = codeRepresentativeType;
	}

	public void setComment( String comment )
	{
		this.comment = comment;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public void setLoanServedId( Long loanServedId )
	{
		this.loanServedId = loanServedId;
	}

	public void setServedTitle( String servedTitle )
	{
		this.servedTitle = servedTitle;
	}

	public void setTaxNo( String taxNo )
	{
		this.taxNo = taxNo;
	}
}