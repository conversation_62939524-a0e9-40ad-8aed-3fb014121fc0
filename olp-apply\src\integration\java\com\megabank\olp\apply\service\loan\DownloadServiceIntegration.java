package com.megabank.olp.apply.service.loan;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.dao.generated.apply.collateral.ApplyCollateralDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningBankAccountDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningContractDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningEddaDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeIdentityTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeListDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeOtherBankDAO;
import com.megabank.olp.apply.persistence.pojo.apply.collateral.ApplyCollateral;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningBankAccount;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningEdda;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeList;
import com.megabank.olp.apply.service.loan.bean.download.FileDownloadedResBean;
import com.megabank.olp.apply.service.loan.bean.download.LoanResBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.exception.MyUtilityException;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.base.utility.date.CommonDateUtils;
import com.megabank.olp.base.utility.date.CommonTimeUtils;
import com.megabank.olp.base.utility.text.CommonHtml2PdfUtils;

@SpringBootTest
@Transactional( propagation = Propagation.REQUIRES_NEW )
@ContextConfiguration( classes = ApplyConfig.class )
public class DownloadServiceIntegration
{

	public static void byteArrayToFile( byte[] buffer, String dest )
	{
		File file = new File( FilenameUtils.getFullPath( dest ) );

		if( !file.exists() )
			file.mkdirs();

		try (FileOutputStream outputStream = new FileOutputStream( dest ))
		{
			outputStream.write( buffer );
		}
		catch( IOException exception )
		{
			throw new MyUtilityException( exception );
		}
	}

	@Autowired
	private DownloadService service;

	@Autowired
	private ResourceLoader resourceLoader;

	@Autowired
	private CodeOtherBankDAO codeOtherBankDAO;

	@Autowired
	private ApplySigningEddaDAO signingEddaDAO;

	@Autowired
	private CodeIdentityTypeDAO codeIdentityTypeDAO;

	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	private ApplySigningBankAccountDAO bankAccountDAO;

	@Autowired
	private CodeListDAO codeListDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private ApplyCollateralDAO applyCollateralDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private GenerateService generateService;

	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;

	private final Logger logger = LogManager.getLogger( getClass() );

	private byte[] regularFontProgram;

	private byte[] boldFontProgram;

	@Test
	public void downloadAttachment()
	{
		Long attachmentId = 1L;

		FileDownloadedResBean resBean = service.downloadAttachment( attachmentId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void downloadCollateralPdf()
	{
		Long collateralId = 1L;

		FileDownloadedResBean resBean = service.downloadCollateralPdf( collateralId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void downloadContract() throws IOException
	{
		// ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( "************-007" );
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( "070111203406-001" );
		byte[] pdfContent = generateService.generateSigningContractPdf( contract );
		ByteArrayOutputStream fileContent = new ByteArrayOutputStream( pdfContent.length );
		fileContent.write( pdfContent, 0, pdfContent.length );

		DownloadServiceIntegration
					.byteArrayToFile( fileContent.toByteArray(),
									  "file/output/" + CommonDateStringUtils.transDate2String( new Date(), "yyyyMMddHHmmss" ) + ".pdf" );
	}

	@Test
	public void downloadContractEdda() throws IOException
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		StringBuilder builder = new StringBuilder();
		String[] inputs = new String[]{ "src/main/resources/pdf/contract/page-2.html", "src/main/resources/pdf/contract/page-Ach1.html",
										"src/main/resources/pdf/contract/page-Ach2.html" };

		for( String input : inputs )
		{
			File thisFile = new File( input );
			InputStream inputStream = null;
			inputStream = new FileInputStream( thisFile );

			// InputStream inputStream = resourceLoader.getResource( "classpath:" + input ).getInputStream();

			Map<String, Object> map = new HashMap<>();
			ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( "************-006" );
			ApplySigningEdda applySigningEDDA = signingEddaDAO.readLastest( contract.getSigningContractId() );
			ApplySigningBankAccount bankAccount = bankAccountDAO.getContractBankAccounts( contract.getSigningContractId() ).get( 0 );

			String signingDate = CommonDateStringUtils.transDate2String( applySigningEDDA.getRcAdate(), "TTT/MM/dd" );
			String year = signingDate.split( "/" )[ 0 ];
			String month = signingDate.split( "/" )[ 1 ];
			String date = signingDate.split( "/" )[ 2 ];

			map.put( "repayment", "<span style='color:red'>" + "21" + "</span>" );
			map.put( "firstPaymentDate", "<span style='color:red'>" + "2022/07/21)" + "</span>" );
			map.put( "bankAcctNo", "<span style='color:red'>" + bankAccount.getBankAccount() + "</span>" );

			map.put( "year", "<span style='color:red'>" + year + "</span>" );
			map.put( "month", "<span style='color:red'>" + month + "</span>" );
			map.put( "date", "<span style='color:red'>" + date + "</span>" );

			map.put( "contractNo", "<span style='color:red'>" + contract.getContractNo() + "</span>" );
			map.put( "checkDate", "<span style='color:red'>" + CommonDateStringUtils.transDate2String( new Date() ) + "</span>" );
			map.put( "borrowerEddaChecked", "checked" );
			map.put( "addEddaFlag", "checked" );

			map.put( "borrowerName", "<span style='color:red'>" + "測試一" + "</span>" );
			map.put( "borrowerId", "<span style='color:red'>" + applySigningEDDA.getAid() + "</span>" );
			map.put( "borrowerIpAddress", "<span style='color:red'>*************:43960</span>" );
			map.put( "borrowerIdentityType", "<span style='color:red'>" + codeIdentityTypeDAO.read( "deposit" ).getName() + "</span>" );
			map.put( "borrowerSigningDate",
					 "<span style='color:red'>" + CommonDateStringUtils.transDate2String( new Date(), CommonTimeUtils.TIME_PATTERN_01 ) + "</span>" );

			map.put( "bankName", "<span style='color:red'>" + getEddaBankName( bankAccount.getBankCode() ) + "</span>" );
			map.put( "bankNo", "<span style='color:red'>" + bankAccount.getBankAccount() + "</span>" );
			map.put( "bankCode", "<span style='color:red'>" + bankAccount.getBankCode() + "</span>" );

			map.put( "branchName", "<span style='color:red'>"
				+ getEddaBranchName( bankAccount.getBankCode(), bankAccount.getBankAccount(), bankAccount.getBankBranchCode() ) + "</span>" );
			map.put( "branchCode", "<span style='color:red'>" + bankAccount.getBankBranchCode() + "</span>" );

			map.put( "contractVersion", "<span style='color:red'>" + contract.getContractVersion() + "</span>" );

			String tempHtml = IOUtils.toString( inputStream, StandardCharsets.UTF_8 );
			builder.append( CommonHtml2PdfUtils.replaceHtml( tempHtml, "\\{(\\w+)\\}", map ) );
		}

		// ByteArrayOutputStream fileContent = CommonHtml2PdfUtils.convertString2OutputStream( builder.toString(), getRegularFontProgram(),
		// getBoldFontProgram(),
		// new WatermarkingEventHandler( getRegularFontProgram(),
		// "兆豐銀行 TEST 2022/07/07 16:57:15" ,
		// 20f, 45f ) );
		//
		// DownloadServiceIntegration.byteArrayToFile(fileContent.toByteArray() , "file/output/"+ CommonDateStringUtils.transDate2String(
		// new Date(), "yyyyMMddHHmmss" ) +".pdf");
	}

	@Test
	public void downloadEncryptPdf()
	{
		Long loanId = 1L;
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );

		FileDownloadedResBean resBean = service.downloadEncryptApplyPdf( loanId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void downloadIxml() throws IOException
	{
		byte[] pdfContent = generateService.generateIxmlAuthorizePdf( 29909L );
		ByteArrayOutputStream fileContent = new ByteArrayOutputStream( pdfContent.length );
		fileContent.write( pdfContent, 0, pdfContent.length );

		DownloadServiceIntegration
					.byteArrayToFile( fileContent.toByteArray(),
									  "file/output/" + CommonDateStringUtils.transDate2String( new Date(), "yyyyMMddHHmmss" ) + ".pdf" );
	}

	@Test
	public void downloadLoanApplyPdf() throws IOException
	{
		// ApplyLoan applyLoan = applyLoanDAO.read( 29479L );
		ApplyLoan applyLoan = applyLoanDAO.read( 296L );
		byte[] pdfContent = generateService.generateLoanApplyPdf( applyLoan );
		ByteArrayOutputStream fileContent = new ByteArrayOutputStream( pdfContent.length );
		fileContent.write( pdfContent, 0, pdfContent.length );

		DownloadServiceIntegration
					.byteArrayToFile( fileContent.toByteArray(),
									  "file/output/" + CommonDateStringUtils.transDate2String( new Date(), "yyyyMMddHHmmss" ) + ".pdf" );
	}

	@Test
	public void downloadLoanCollateral() throws IOException
	{
		ApplyCollateral applyCollateral = applyCollateralDAO.getPojoByIdentityId( 227L );
		byte[] pdfContent = generateService.generateLoanCollateralPdf( applyCollateral );

		ByteArrayOutputStream fileContent = new ByteArrayOutputStream( pdfContent.length );
		fileContent.write( pdfContent, 0, pdfContent.length );

		DownloadServiceIntegration
					.byteArrayToFile( fileContent.toByteArray(),
									  "file/output/" + CommonDateStringUtils.transDate2String( new Date(), "yyyyMMddHHmmss" ) + ".pdf" );
	}

	@Test
	public void downloadUnEncryptPdf()
	{
		Long loanId = 1L;

		FileDownloadedResBean resBean = service.downloadUnEncryptApplyPdf( loanId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getApplyList()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );

		List<LoanResBean> resBean = service.getApplyList();

		logger.info( "resBean:{}", resBean );
	}

	public String getEddaBankName( String bankCode )
	{
		if( bankCode == null )
			return "________";

		CodeList codeList = codeListDAO.getPojosByCodeTypeAndcodeValue( CodeList.EDDA_BANK_CODE, bankCode );

		String bankName = codeList != null ? codeList.getCodeDesc() : "";

		int index = bankName.indexOf( "銀行" );
		if( index > -1 )
			bankName = bankName.substring( 0, index );
		else
		{
			index = bankName.indexOf( "合作社" );
			if( index > -1 )
				bankName = bankName.substring( 0, index );
		}

		return bankName;
	}

	public String getEddaBranchName( String bankCode, String bankAccNo, String bankBranchCode )
	{
		String branchName = "________";
		if( bankBranchCode != null )
		{
			CodeList codeList = codeListDAO.getPojosByCodeTypeAndcodeValue( CodeList.EDDA_BANK_BRANCH_CODE, bankBranchCode );
			branchName = codeList != null ? codeList.getCodeDesc() : "";
		}
		else if( bankCode.equals( "017" ) )
		{
			String branchCode = bankAccNo.substring( 0, 3 );
			CodeBranchBank codeBranchBank = codeBranchBankDAO.getPojoByBankCode( branchCode );
			branchName = codeBranchBank.getName();
		}
		int index = branchName.indexOf( "分行" );
		if( index > -1 )
			branchName = branchName.substring( 0, index );
		return branchName;
	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	private synchronized byte[] getBoldFontProgram() throws IOException
	{
		String fontPath = "classpath:fonts/NotoSansCJKtc-Bold.otf";

		if( boldFontProgram != null && boldFontProgram.length > 0 )
			return boldFontProgram;

		try (InputStream inputStream = resourceLoader.getResource( fontPath ).getInputStream();)
		{
			boldFontProgram = IOUtils.toByteArray( inputStream );

			return boldFontProgram;
		}
	}

	private synchronized byte[] getRegularFontProgram() throws IOException
	{
		String fontPath = "classpath:fonts/NotoSansCJKtc-Regular.otf";

		if( regularFontProgram != null && regularFontProgram.length > 0 )
			return regularFontProgram;

		try (InputStream inputStream = resourceLoader.getResource( fontPath ).getInputStream();)
		{
			regularFontProgram = IOUtils.toByteArray( inputStream );

			return regularFontProgram;
		}
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.OTHER_BANK.getContext(), IdentityTypeEnum.OTP.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}
}
