/**
 *
 */
package com.megabank.olp.api.controller.eloan;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.controller.eloan.bean.signing.BankAccountInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.ContractCtrTypeCCreateAPIArgBean;
import com.megabank.olp.api.controller.eloan.bean.signing.GuaranteeInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.LendingPlanInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.LoanConditionInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.LoanPurposeInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoCreateArgBean;
import com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoCreateParamBean;
import com.megabank.olp.api.controller.eloan.bean.signing.SigningContractCreateArgBean;
import com.megabank.olp.api.controller.eloan.bean.signing.SigningContractDiscardArgBean;
import com.megabank.olp.api.service.eloan.SigningContractService;
import com.megabank.olp.api.service.eloan.bean.BankAccountDataBean;
import com.megabank.olp.api.service.eloan.bean.ContractCtrTypeCCreatedAPIParamBean;
import com.megabank.olp.api.service.eloan.bean.GuaranteeDataBean;
import com.megabank.olp.api.service.eloan.bean.LendingPlanDataBean;
import com.megabank.olp.api.service.eloan.bean.LoanConditionDataBean;
import com.megabank.olp.api.service.eloan.bean.LoanPurposeDataBean;
import com.megabank.olp.api.service.eloan.bean.SigningContractCreatedParamBean;
import com.megabank.olp.api.utility.BaseELoanAPIController;
import com.megabank.olp.base.enums.RecipientSystemEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "eloan/signingcontract" )
public class SigningContractController extends BaseELoanAPIController
{
	@Autowired
	private SigningContractService service;

	@PostMapping( "discardContract" )
	public Map<String, Object> discardContract( @RequestHeader( value = "AccessToken" ) String accessToken,
												@RequestBody @Validated SigningContractDiscardArgBean argBean )
	{
		validAuth( accessToken );

		return getResponseMap( service.discardContract( argBean.getContractNo() ) );
	}

	@PostMapping( "sendSigningContract" )
	public Map<String, Object> sendContract( @RequestHeader( value = "AccessToken" ) String accessToken,
											 @RequestBody @Validated SigningContractCreateArgBean argBean )
	{
		validAuth( accessToken );

		checkSigningContractCreateArgBean( argBean );

		return getResponseMap( service.createContract( mapCreatedParamBean( argBean ) ) );
	}

	@PostMapping( "sendContractCtrTypeC" )
	public Map<String, Object> sendContractCtrTypeC( @RequestHeader( value = "AccessToken" ) String accessToken,
													 @RequestBody @Validated ContractCtrTypeCCreateAPIArgBean argBean )
	{
		validAuth( accessToken );

		return getResponseMap( service.createCtrTypeCContract( mapCtrTypeCParamBean( argBean ) ) );
	}

	@PostMapping( "sendPaymentInfo" )
	public Map<String, Object> sendPaymentInfo( @RequestHeader( value = "AccessToken" ) String accessToken,
												@RequestBody @Validated PaymentInfoCreateArgBean argBean )
	{
		validAuth( accessToken );

		return getResponseMap( service.createPaymentInfo( mapPaymentInfoBean( argBean ) ) );
	}

	private void checkSigningContractCreateArgBean( SigningContractCreateArgBean argBean )
	{
		if( StringUtils.isNotBlank( argBean.getRelatedPersonType() )
			&& ( StringUtils.isAnyBlank( argBean.getRelatedPersonId(), argBean.getRelatedPersonName(), argBean.getRelatedPersonMobileNumber(),
										 argBean.getRelatedPersonEmail() )
				|| argBean.getRelatedPersonBirthDate() == null ) )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
										  new String[]{ "relatedPersonId, relatedPersonName, relatedPersonMobileNumber, relatedPersonBirthDate, relatedPersonEmail",
														"must not be null" } );

		if( argBean.getLoanConditionInfoBean().getLendingPlanInfoBean().getAdvancedApr() == null
			&& argBean.getLoanConditionInfoBean().getLendingPlanInfoBean().getLimitedApr() == null )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
										  new String[]{ "advancedAPR, limitedAPR", "at least one field not null" } );

		if( "Y".equals( argBean.getIsRepayment() ) && ( argBean.getRepaymentList() == null || argBean.getRepaymentList().size() == 0 ) )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
										  new String[]{ "when isRepayment is 'Y' repaymentList", "must not be null" } );
	}

	private List<BankAccountDataBean> mapBankAccountDataBeans( List<BankAccountInfoBean> bankAccountInfoBeans )
	{
		List<BankAccountDataBean> dataBeans = new ArrayList<>();
		for( BankAccountInfoBean bankAccountInfoBean : bankAccountInfoBeans )
		{
			BankAccountDataBean dataBean = new BankAccountDataBean();
			dataBean.setBankCode( bankAccountInfoBean.getBankCode() );
			dataBean.setAccount( bankAccountInfoBean.getAccount() );

			dataBeans.add( dataBean );
		}

		return dataBeans;
	}

	private SigningContractCreatedParamBean mapCreatedParamBean( SigningContractCreateArgBean argBean )
	{
		SigningContractCreatedParamBean paramBean = new SigningContractCreatedParamBean();
		paramBean.setBranchCode( argBean.getBranchCode() );
		paramBean.setBorrowerId( argBean.getBorrowerId() );
		paramBean.setBorrowerBirthDate( argBean.getBorrowerBirthDate() );
		paramBean.setBorrowerMobileNumber( argBean.getBorrowerMobileNumber() );
		paramBean.setBorrowerEmail( argBean.getBorrowerEmail() );
		paramBean.setBorrowerName( argBean.getBorrowerName() );
		paramBean.setIsBorrowerYouth( argBean.getIsBorrowerYouth() );
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setContractVersion( argBean.getContractVersion() );
		paramBean.setCourtName( argBean.getCourtName() );
		paramBean.setExpiredDate( argBean.getExpiredDate() );
		paramBean.setProductCode( argBean.getProductCode() );
		paramBean.setRelatedPersonBirthDate( argBean.getRelatedPersonBirthDate() );
		paramBean.setRelatedPersonId( argBean.getRelatedPersonId() );
		paramBean.setRelatedPersonMobileNumber( argBean.getRelatedPersonMobileNumber() );
		paramBean.setRelatedPersonName( argBean.getRelatedPersonName() );
		paramBean.setRelatedPersonEmail( argBean.getRelatedPersonEmail() );
		paramBean.setRelatedPersonType( argBean.getRelatedPersonType() );
		paramBean.setLoanConditionDataBean( mapLoanConditionInfoBean( argBean.getLoanConditionInfoBean() ) );
		paramBean.setBankAccountDataBeans( mapBankAccountDataBeans( argBean.getBankAccountInfoBeans() ) );
		paramBean.setGuaranteeDataBean( mapGuaranteeDataBean( argBean.getGuaranteeInfoBean() ) );
		paramBean.setLoanPlan( argBean.getLoanPlan() );
		paramBean.setGrpCntrNo( argBean.getGrpCntrNo() );
		paramBean.setGivenApprBegDate( argBean.getGivenApprBegDate() );
		paramBean.setGivenApprEndDate( argBean.getGivenApprEndDate() );
		paramBean.setPayeeBankCode( argBean.getPayeeBankCode() );
		paramBean.setPayeeBankAccountNo( argBean.getPayeeBankAccountNo() );
		paramBean.setPayeeBankAccountName( argBean.getPayeeBankAccountName() );
		paramBean.setPayeeTotalAmt( argBean.getPayeeTotalAmt() );
		paramBean.setPayeeRemittance( argBean.getPayeeRemittance() );
		paramBean.setPayeeSelfProvide( argBean.getPayeeSelfProvide() );
		paramBean.setBaseRate( argBean.getBaseRate() );
		paramBean.setRateList( argBean.getRateList() );
		paramBean.setIsRepayment( argBean.getIsRepayment() );
		paramBean.setRepaymentList( argBean.getRepaymentList() );
		paramBean.setStaffRule( argBean.getStaffRule() != null && StringUtils.equals( argBean.getStaffRule(), "Y" ) ? true : false );
		paramBean.setPayeeInfo( argBean.getPayeeInfo() );
		paramBean.setExpireInfo( argBean.getExpireInfo() );
		paramBean.setRepaymentInfo( argBean.getRepaymentInfo() );
		paramBean.setInterestInfo( argBean.getInterestInfo() );
		paramBean.setGuaranteeType( argBean.getGuaranteeType() );
		paramBean.setWitness( argBean.getWitness() );
		paramBean.setBrNoTel( argBean.getBrNoTel() );
		paramBean.setBrNoFax( argBean.getBrNoFax() );
		paramBean.setRefSystemId( RecipientSystemEnum.ELOAN.getSystemId() );
		paramBean.setProdKind( argBean.getProdKind() );
		paramBean.setLnDate( argBean.getLnDate() );
		paramBean.setConsentVer( argBean.getConsentVer() );
		paramBean.setCollateralBuildingAddr1( argBean.getCollateralBuildingAddr1() );
		paramBean.setCollateralBuildingAddr2( argBean.getCollateralBuildingAddr2() );
		paramBean.setMortgageMaxAmt1( argBean.getMortgageMaxAmt1() );
		paramBean.setMortgageMaxAmt2( argBean.getMortgageMaxAmt2() );
		paramBean.setFirstLoanDateYear( argBean.getFirstLoanDateYear() );
		paramBean.setFirstLoanDateMth( argBean.getFirstLoanDateMth() );
		paramBean.setFirstLoanDateDay( argBean.getFirstLoanDateDay() );
		paramBean.setFirstLoanAmt1( argBean.getFirstLoanAmt1() );
		paramBean.setFirstLoanAmt2( argBean.getFirstLoanAmt2() );
		paramBean.setCollateralContractTerms( argBean.getCollateralContractTerms() );
		paramBean.setUnregisteredBuildingDesc( argBean.getUnregisteredBuildingDesc() );
		paramBean.setHouseLoanContractNo( argBean.getHouseLoanContractNo() );
		paramBean.setCoTarget( argBean.getCoTarget() );
		paramBean.setCbAfftTerms( argBean.getCbAfftTerms() );
		paramBean.setCbAfft1ContentBean( argBean.getCbAfft1ContentBean() );
		paramBean.setCbAfft2ContentBean( argBean.getCbAfft2ContentBean() );
		paramBean.setCbAfft3ContentBean( argBean.getCbAfft3ContentBean() );
		paramBean.setCbAfft4ContentBean( argBean.getCbAfft4ContentBean() );
		paramBean.setCbAfft5ContentBean( argBean.getCbAfft5ContentBean() );
		paramBean.setCbAfftVersion( argBean.getCbAfftVersion() );

		return paramBean;
	}

	private ContractCtrTypeCCreatedAPIParamBean mapCtrTypeCParamBean( ContractCtrTypeCCreateAPIArgBean argBean )
	{
		ContractCtrTypeCCreatedAPIParamBean paramBean = new ContractCtrTypeCCreatedAPIParamBean();
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setBankAcctCode( argBean.getBankAcctCode() );
		paramBean.setBankAcctNo( argBean.getBankAcctNo() );
		paramBean.setBorrowerIPAddr( argBean.getBorrowerIPAddr() );
		paramBean.setBorrowerIdentityType( argBean.getBorrowerIdentityType() );
		paramBean.setLoanBeginDate( argBean.getLoanBeginDate() );
		paramBean.setLoanEndDate( argBean.getLoanEndDate() );
		paramBean.setRateAdjustInformMethod( argBean.getRateAdjustInformMethod() );
		paramBean.setBorrowerSingingDate( argBean.getBorrowerSingingDate() );
		paramBean.setContractCheckDate( argBean.getContractCheckDate() );
		paramBean.setBorrowerAgreeCrossSelling( argBean.getBorrowerAgreeCrossSelling() );
		paramBean.setBranchCode( argBean.getBranchCode() );
		paramBean.setBorrowerId( argBean.getBorrowerId() );
		paramBean.setBorrowerBirthDate( argBean.getBorrowerBirthDate() );
		paramBean.setBorrowerName( argBean.getBorrowerName() );
		paramBean.setBorrowerMobileNumber( argBean.getBorrowerMobileNumber() );
		paramBean.setBorrowerEmail( argBean.getBorrowerEmail() );
		paramBean.setProductCode( argBean.getProductCode() );
		paramBean.setContractVersion( argBean.getContractVersion() );
		paramBean.setLoanAmt( argBean.getLoanAmt() );
		paramBean.setLoanPeriod( argBean.getLoanPeriod() );
		paramBean.setDrawDownType( argBean.getDrawDownType() );
		paramBean.setOneTimeFee( argBean.getOneTimeFee() );
		paramBean.setPreliminaryFee( argBean.getPreliminaryFee() );
		paramBean.setCreditCheckFee( argBean.getCreditCheckFee() );
		paramBean.setRepaymentMethod( argBean.getRepaymentMethod() );
		paramBean.setAdvancedRateDesc( argBean.getAdvancedRateDesc() );
		paramBean.setAdvancedAPR( argBean.getAdvancedAPR() );
		paramBean.setLimitedRateDesc( argBean.getLimitedRateDesc() );
		paramBean.setLimitedAPR( argBean.getLimitedAPR() );
		paramBean.setShowOption( argBean.getShowOption() );
		paramBean.setCourtName( argBean.getCourtName() );
		paramBean.setFile( argBean.getFile() );

		return paramBean;
	}

	private GuaranteeDataBean mapGuaranteeDataBean( GuaranteeInfoBean infoBean )
	{
		GuaranteeDataBean dataBean = new GuaranteeDataBean();
		dataBean.setGeneralGuaranteePlan( infoBean.getGeneralGuaranteePlan() );
		dataBean.setGeneralGuaranteePlanInfo( infoBean.getGeneralGuaranteePlanInfo() );
		dataBean.setGuaranteeAmt( infoBean.getGuaranteeAmt() );
		dataBean.setJointGuaranteePlan( infoBean.getJointGuaranteePlan() );
		dataBean.setJointGuaranteePlanInfo( infoBean.getJointGuaranteePlanInfo() );

		return dataBean;
	}

	private LendingPlanDataBean mapLendingPlanData( LendingPlanInfoBean infoBean )
	{
		LendingPlanDataBean dataBean = new LendingPlanDataBean();
		dataBean.setAdvancedRedemptionTitle( infoBean.getAdvancedRedemptionTitle() );
		dataBean.setAdvancedRedemptionDesc( infoBean.getAdvancedRedemptionDesc() );
		dataBean.setAdvancedRateTitle( infoBean.getAdvancedRateTitle() );
		dataBean.setAdvancedRateDesc( infoBean.getAdvancedRateDesc() );
		dataBean.setAdvancedApr( infoBean.getAdvancedApr() );
		dataBean.setLimitedRedemptionTitle( infoBean.getLimitedRedemptionTitle() );
		dataBean.setLimitedRedemptionDesc( infoBean.getLimitedRedemptionDesc() );
		dataBean.setLimitedRateTitle( infoBean.getLimitedRateTitle() );
		dataBean.setLimitedRateDesc( infoBean.getLimitedRateDesc() );
		dataBean.setLimitedApr( infoBean.getLimitedApr() );
		dataBean.setOtherInfoTitle( infoBean.getOtherInfoTitle() );
		dataBean.setOtherInfoDesc( infoBean.getOtherInfoDesc() );
		dataBean.setShowOption( infoBean.getShowOption() );

		return dataBean;
	}

	private LoanConditionDataBean mapLoanConditionInfoBean( LoanConditionInfoBean infoBean )
	{
		LoanConditionDataBean conditionDataBean = new LoanConditionDataBean();
		conditionDataBean.setLoanAmt( infoBean.getLoanAmt() );
		conditionDataBean.setLoanPeriod( infoBean.getLoanPeriod() );
		conditionDataBean.setLoanPurposeDataBeans( mapLoanPurposeData( infoBean.getLoanPurposeInfoBeans() ) );
		conditionDataBean.setOneTimeFee( infoBean.getOneTimeFee() );
		conditionDataBean.setCreditCheckFee( infoBean.getCreditCheckFee() );
		conditionDataBean.setRenewFee( infoBean.getRenewFee() );
		conditionDataBean.setChangeFee( infoBean.getChangeFee() );
		conditionDataBean.setCertFee( infoBean.getCertFee() );
		conditionDataBean.setReissueFee( infoBean.getReissueFee() );
		conditionDataBean.setPreliminaryFee( infoBean.getPreliminaryFee() );
		conditionDataBean.setRepaymentMethod( infoBean.getRepaymentMethod() );
		conditionDataBean.setDrawDownType( infoBean.getDrawDownType() );
		conditionDataBean.setLendingPlan( infoBean.getLendingPlan() );
		conditionDataBean.setLendingPlanDataBean( mapLendingPlanData( infoBean.getLendingPlanInfoBean() ) );

		return conditionDataBean;
	}

	private List<LoanPurposeDataBean> mapLoanPurposeData( List<LoanPurposeInfoBean> infoBeans )
	{
		List<LoanPurposeDataBean> dataBeans = new ArrayList<>();

		for( LoanPurposeInfoBean infoBean : infoBeans )
		{
			LoanPurposeDataBean dataBean = new LoanPurposeDataBean();
			dataBean.setLoanPurposeName( infoBean.getLoanPurposeName() );
			dataBean.setIsChecked( infoBean.getIsChecked() );

			dataBeans.add( dataBean );
		}

		return dataBeans;
	}

	private PaymentInfoCreateParamBean mapPaymentInfoBean( PaymentInfoCreateArgBean argBean )
	{
		PaymentInfoCreateParamBean paramBean = new PaymentInfoCreateParamBean();
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setPreliminaryFee( argBean.getPreliminaryFee() );
		paramBean.setCrChkFee( argBean.getCrChkFee() );
		paramBean.setPaymentInfoList( argBean.getPaymentInfoList() );

		return paramBean;
	}
}
