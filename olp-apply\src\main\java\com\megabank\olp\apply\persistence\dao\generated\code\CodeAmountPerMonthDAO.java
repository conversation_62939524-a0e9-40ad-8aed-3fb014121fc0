package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeAmountPerMonth;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeAmountPerMonthDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeAmountPerMonthDAO extends BasePojoDAO<CodeAmountPerMonth, String>
{
	public CodeAmountPerMonth read( String amountPerMonthCode )
	{
		Validate.notBlank( amountPerMonthCode );

		return getPojoByPK( amountPerMonthCode, CodeAmountPerMonth.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeAmountPerMonth> getPojoClass()
	{
		return CodeAmountPerMonth.class;
	}
}
