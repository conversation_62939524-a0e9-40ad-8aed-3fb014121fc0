package com.megabank.olp.apply.persistence.pojo.house;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseContactLoanInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_contact_loan_info" )
public class HouseContactLoanInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_contact_loan_info";

	public static final String CONTACT_INFO_ID_CONSTANT = "contactInfoId";

	public static final String HOUSE_CONTACT_INFO_CONSTANT = "houseContactInfo";

	public static final String LOAN_PURPOSE_CONSTANT = "loanPurpose";

	public static final String LOAN_BALANCE_CONSTANT = "loanBalance";

	public static final String COUNTY_CONSTANT = "county";

	public static final String DISTRICT_CONSTANT = "district";

	public static final String ADDRESS_CONSTANT = "address";

	private long contactInfoId;

	private transient HouseContactInfo houseContactInfo;

	private String loanPurpose;

	private BigDecimal loanBalance;

	private String county;

	private String district;

	private String address;

	public HouseContactLoanInfo()
	{}

	public HouseContactLoanInfo( HouseContactInfo houseContactInfo )
	{
		this.houseContactInfo = houseContactInfo;
	}

	public HouseContactLoanInfo( Long contactInfoId )
	{
		this.contactInfoId = contactInfoId;
	}

	@Column( name = "address" )
	public String getAddress()
	{
		return address;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "houseContactInfo" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "contact_info_id", unique = true, nullable = false )
	public long getContactInfoId()
	{
		return contactInfoId;
	}

	@Column( name = "county" )
	public String getCounty()
	{
		return county;
	}

	@Column( name = "district" )
	public String getDistrict()
	{
		return district;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public HouseContactInfo getHouseContactInfo()
	{
		return houseContactInfo;
	}

	@Column( name = "loan_balance", precision = 11 )
	public BigDecimal getLoanBalance()
	{
		return loanBalance;
	}

	@Column( name = "loan_purpose" )
	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public void setAddress( String address )
	{
		this.address = address;
	}

	public void setContactInfoId( long contactInfoId )
	{
		this.contactInfoId = contactInfoId;
	}

	public void setCounty( String county )
	{
		this.county = county;
	}

	public void setDistrict( String district )
	{
		this.district = district;
	}

	public void setHouseContactInfo( HouseContactInfo houseContactInfo )
	{
		this.houseContactInfo = houseContactInfo;
	}

	public void setLoanBalance( BigDecimal loanBalance )
	{
		this.loanBalance = loanBalance;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}
}