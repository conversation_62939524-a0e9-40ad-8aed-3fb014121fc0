package com.megabank.olp.apply.persistence.pojo.house;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;

import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeProcess;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseLoanTrialInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_loan_trial_info", uniqueConstraints = @UniqueConstraint( columnNames = "case_no" ) )
public class HouseLoanTrialInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_loan_trial_info";

	public static final String LOAN_TRIAL_INFO_ID_CONSTANT = "loanTrialInfoId";

	public static final String CODE_BRANCH_BANK_CONSTANT = "codeBranchBank";

	public static final String CODE_PROCESS_CONSTANT = "codeProcess";

	public static final String CASE_NO_CONSTANT = "caseNo";

	public static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	public static final String EMAIL_CONSTANT = "email";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String APPLY_NOTES_CONSTANT = "applyNotes";

	public static final String HOUSE_LOAN_BASIC_INFO_CONSTANT = "houseLoanBasicInfo";

	public static final String HOUSE_LOAN_INFO_CONSTANT = "houseLoanInfo";

	private Long loanTrialInfoId;

	private transient CodeBranchBank codeBranchBank;

	private transient CodeProcess codeProcess;

	private String caseNo;

	private String mobileNumber;

	private String email;

	private boolean notified;

	private Date updatedDate;

	private Date createdDate;

	private transient Set<ApplyNote> applyNotes = new HashSet<>( 0 );

	private transient HouseLoanBasicInfo houseLoanBasicInfo;

	private transient HouseLoanInfo houseLoanInfo;

	public HouseLoanTrialInfo()
	{}

	public HouseLoanTrialInfo( CodeBranchBank codeBranchBank, CodeProcess codeProcess, String caseNo, boolean notified, Date updatedDate,
							   Date createdDate )
	{
		this.codeBranchBank = codeBranchBank;
		this.codeProcess = codeProcess;
		this.caseNo = caseNo;
		this.notified = notified;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
	}

	public HouseLoanTrialInfo( Long loanTrialInfoId )
	{
		this.loanTrialInfoId = loanTrialInfoId;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "houseLoanTrialInfo" )
	public Set<ApplyNote> getApplyNotes()
	{
		return applyNotes;
	}

	@Column( name = "case_no", unique = true, nullable = false, length = 20 )
	public String getCaseNo()
	{
		return caseNo;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "branch_bank_id", nullable = false )
	public CodeBranchBank getCodeBranchBank()
	{
		return codeBranchBank;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "process_code", nullable = false )
	public CodeProcess getCodeProcess()
	{
		return codeProcess;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "email", length = 50 )
	public String getEmail()
	{
		return email;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "houseLoanTrialInfo" )
	public HouseLoanBasicInfo getHouseLoanBasicInfo()
	{
		return houseLoanBasicInfo;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "houseLoanTrialInfo" )
	public HouseLoanInfo getHouseLoanInfo()
	{
		return houseLoanInfo;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "loan_trial_info_id", unique = true, nullable = false )
	public Long getLoanTrialInfoId()
	{
		return loanTrialInfoId;
	}

	@Column( name = "mobile_number", length = 10 )
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	public void setApplyNotes( Set<ApplyNote> applyNotes )
	{
		this.applyNotes = applyNotes;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCodeBranchBank( CodeBranchBank codeBranchBank )
	{
		this.codeBranchBank = codeBranchBank;
	}

	public void setCodeProcess( CodeProcess codeProcess )
	{
		this.codeProcess = codeProcess;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setHouseLoanBasicInfo( HouseLoanBasicInfo houseLoanBasicInfo )
	{
		this.houseLoanBasicInfo = houseLoanBasicInfo;
	}

	public void setHouseLoanInfo( HouseLoanInfo houseLoanInfo )
	{
		this.houseLoanInfo = houseLoanInfo;
	}

	public void setLoanTrialInfoId( Long loanTrialInfoId )
	{
		this.loanTrialInfoId = loanTrialInfoId;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}
}