package com.megabank.olp.apply.service.loan.bean.upload;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class ThumbnailResBean extends BaseBean
{

	private Long fileId;

	private String attachmentType;

	private String fileName;

	private String fileType;

	@JsonProperty( "thumbnail" )
	private transient String compressFileContent;

	public ThumbnailResBean()
	{
		// default constructor
	}

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public String getCompressFileContent()
	{
		return compressFileContent;
	}

	public Long getFileId()
	{
		return fileId;
	}

	public String getFileName()
	{
		return fileName;
	}

	public String getFileType()
	{
		return fileType;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setCompressFileContent( String compressFileContent )
	{
		this.compressFileContent = compressFileContent;
	}

	public void setFileId( Long fileId )
	{
		this.fileId = fileId;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setFileType( String fileType )
	{
		this.fileType = fileType;
	}
}
