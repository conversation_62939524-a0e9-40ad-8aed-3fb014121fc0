package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningEdda;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplySigningEddaDAOIntegration
{
	@Autowired
	private ApplySigningEddaDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void read()
	{
		Long contractId = 1L;
		Long eddaId = 1L;

		ApplySigningEdda signingEdda = dao.read( eddaId,contractId );

		logger.info( "ApplySigningEdda:{}", signingEdda );
	}

}
