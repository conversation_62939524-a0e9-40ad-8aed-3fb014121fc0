package com.megabank.olp.apply.persistence.dao.generated.house;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.house.ContactLoanCreatedParamBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HouseContactRequestInfoDAOIntegration
{
	@Autowired
	private HouseContactLoanInfoDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		ContactLoanCreatedParamBean paramBean = new ContactLoanCreatedParamBean();
		paramBean.setContactId( 2L );

		Long userInfo = dao.create( paramBean );

		logger.info( "HouseContactInfo Long:{}", userInfo );
	}

}
