/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.mydata;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class ApplyMyDataCreatedParamBean extends BaseBean
{
	private Long loanId;

	private Long validatedIdentityId;

	private String serviceId;

	private String txId;

	private String myDataStatusCode;

	private String transmissionStatusCode;

	public ApplyMyDataCreatedParamBean()
	{}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getMyDataStatusCode()
	{
		return myDataStatusCode;
	}

	public String getServiceId()
	{
		return serviceId;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public String getTxId()
	{
		return txId;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setMyDataStatusCode( String myDataStatusCode )
	{
		this.myDataStatusCode = myDataStatusCode;
	}

	public void setServiceId( String serviceId )
	{
		this.serviceId = serviceId;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setTxId( String txId )
	{
		this.txId = txId;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

}
