package com.megabank.olp.apply.persistence.pojo.apply.signing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplySigningBankAccount is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_signing_bank_account" )
public class ApplySigningBankAccount extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_signing_bank_account";

	public static final String BANK_ACCOUNT_ID_CONSTANT = "bankAccountId";

	public static final String APPLY_SIGNING_CONTRACT_CONSTANT = "applySigningContract";

	public static final String BANK_ACCOUNT_CONSTANT = "bankAccount";

	public static final String APPLY_SIGNING_APPROPRIATIONS_CONSTANT = "applySigningAppropriations";

	public static final String BANK_CODE_CONSTANT = "bankCode";

	public static final String BANK_BRANCH_CODE_CONSTANT = "bankBranchCode";

	public static final String DISCARD_CONSTANT = "discard";

	private Long bankAccountId;

	private transient ApplySigningContract applySigningContract;

	private String bankAccount;

	private String bankCode;

	private Boolean discard;

	private String bankBranchCode;

	private transient Set<ApplySigningAppropriation> applySigningAppropriations = new HashSet<>( 0 );

	public ApplySigningBankAccount()
	{}

	public ApplySigningBankAccount( ApplySigningContract applySigningContract, String bankAccount, String bankCode )
	{
		this.applySigningContract = applySigningContract;
		this.bankAccount = bankAccount;
		this.bankCode = bankCode;
	}

	public ApplySigningBankAccount( Long bankAccountId )
	{
		this.bankAccountId = bankAccountId;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applySigningBankAccount" )
	public Set<ApplySigningAppropriation> getApplySigningAppropriations()
	{
		return applySigningAppropriations;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "signing_contract_id", nullable = false )
	public ApplySigningContract getApplySigningContract()
	{
		return applySigningContract;
	}

	@Column( name = "bank_account", nullable = false, length = 20 )
	public String getBankAccount()
	{
		return bankAccount;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "bank_account_id", unique = true, nullable = false )
	public Long getBankAccountId()
	{
		return bankAccountId;
	}

	@Column( name = "bank_branch_code", nullable = true, length = 7 )
	public String getBankBranchCode()
	{
		return bankBranchCode;
	}

	@Column( name = "bank_code", nullable = false, length = 3 )
	public String getBankCode()
	{
		return bankCode;
	}

	@Column( name = "discard" )
	public Boolean getDiscard()
	{
		return discard;
	}

	public void setApplySigningAppropriations( Set<ApplySigningAppropriation> applySigningAppropriations )
	{
		this.applySigningAppropriations = applySigningAppropriations;
	}

	public void setApplySigningContract( ApplySigningContract applySigningContract )
	{
		this.applySigningContract = applySigningContract;
	}

	public void setBankAccount( String bankAccount )
	{
		this.bankAccount = bankAccount;
	}

	public void setBankAccountId( Long bankAccountId )
	{
		this.bankAccountId = bankAccountId;
	}

	public void setBankBranchCode( String bankBranchCode )
	{
		this.bankBranchCode = bankBranchCode;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setDiscard( Boolean discard )
	{
		this.discard = discard;
	}
}