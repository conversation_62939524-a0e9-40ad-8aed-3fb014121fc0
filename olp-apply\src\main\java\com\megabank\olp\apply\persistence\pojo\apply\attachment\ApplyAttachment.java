package com.megabank.olp.apply.persistence.pojo.apply.attachment;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.apply.persistence.pojo.code.CodeAttachmentType;
import com.megabank.olp.apply.persistence.pojo.code.CodeTransmissionStatus;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The ApplyAttachment is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_attachment" )
public class ApplyAttachment extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_attachment";

	public static final String ATTACHMENT_ID_CONSTANT = "attachmentId";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String CODE_ATTACHMENT_TYPE_CONSTANT = "codeAttachmentType";

	public static final String CODE_TRANSMISSION_STATUS_CONSTANT = "codeTransmissionStatus";

	public static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	public static final String RESEND_CONSTANT = "resend";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String FILE_NAME_CONSTANT = "fileName";

	public static final String FILE_SIZE_CONSTANT = "fileSize";

	public static final String FILE_CONTENT_CONSTANT = "fileContent";

	public static final String COMPRESS_FILE_CONTENT_CONSTANT = "compressFileContent";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String APPLY_NOTES_CONSTANT = "applyNotes";

	private Long attachmentId;

	private transient ApplyLoan applyLoan;

	private transient CodeAttachmentType codeAttachmentType;

	private transient CodeTransmissionStatus codeTransmissionStatus;

	private long validatedIdentityId;

	private int resend;

	private boolean notified;

	private String fileName;

	private long fileSize;

	private transient ImmutableByteArray fileContent;

	private transient ImmutableByteArray compressFileContent;

	private Date updatedDate;

	private Date createdDate;

	private transient Set<ApplyNote> applyNotes = new HashSet<>( 0 );

	public ApplyAttachment()
	{}

	public ApplyAttachment( ApplyLoan applyLoan, CodeAttachmentType codeAttachmentType, CodeTransmissionStatus codeTransmissionStatus,
							long validatedIdentityId, int resend, boolean notified, String fileName, long fileSize, ImmutableByteArray fileContent,
							Date updatedDate, Date createdDate )
	{
		this.applyLoan = applyLoan;
		this.codeAttachmentType = codeAttachmentType;
		this.codeTransmissionStatus = codeTransmissionStatus;
		this.validatedIdentityId = validatedIdentityId;
		this.resend = resend;
		this.notified = notified;
		this.fileName = fileName;
		this.fileSize = fileSize;
		this.fileContent = fileContent;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
	}

	public ApplyAttachment( Long attachmentId )
	{
		this.attachmentId = attachmentId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_id", nullable = false )
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyAttachment" )
	public Set<ApplyNote> getApplyNotes()
	{
		return applyNotes;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "attachment_id", unique = true, nullable = false )
	public Long getAttachmentId()
	{
		return attachmentId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "attachment_type", nullable = false )
	public CodeAttachmentType getCodeAttachmentType()
	{
		return codeAttachmentType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "transmission_status_code", nullable = false )
	public CodeTransmissionStatus getCodeTransmissionStatus()
	{
		return codeTransmissionStatus;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "compress_file_content" ) ) } )
	public ImmutableByteArray getCompressFileContent()
	{
		return compressFileContent;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "file_content", nullable = false ) ) } )
	public ImmutableByteArray getFileContent()
	{
		return fileContent;
	}

	@Column( name = "file_name", nullable = false, length = 300 )
	public String getFileName()
	{
		return fileName;
	}

	@Column( name = "file_size", nullable = false )
	public long getFileSize()
	{
		return fileSize;
	}

	@Column( name = "resend", nullable = false, precision = 5, scale = 0 )
	public int getResend()
	{
		return resend;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "validated_identity_id", nullable = false )
	public long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setApplyNotes( Set<ApplyNote> applyNotes )
	{
		this.applyNotes = applyNotes;
	}

	public void setAttachmentId( Long attachmentId )
	{
		this.attachmentId = attachmentId;
	}

	public void setCodeAttachmentType( CodeAttachmentType codeAttachmentType )
	{
		this.codeAttachmentType = codeAttachmentType;
	}

	public void setCodeTransmissionStatus( CodeTransmissionStatus codeTransmissionStatus )
	{
		this.codeTransmissionStatus = codeTransmissionStatus;
	}

	public void setCompressFileContent( ImmutableByteArray compressFileContent )
	{
		this.compressFileContent = compressFileContent;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setFileContent( ImmutableByteArray fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setFileSize( long fileSize )
	{
		this.fileSize = fileSize;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setResend( int resend )
	{
		this.resend = resend;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

	public void setValidatedIdentityId( long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}
}