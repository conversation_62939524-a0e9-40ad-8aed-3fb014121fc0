package com.megabank.olp.apply.persistence.dao.generated.house;

import java.math.BigDecimal;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.house.HouseEstimateInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseEstimateInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseEstimateInfoDAO extends BasePojoDAO<HouseEstimateInfo, Long>
{
	@Autowired
	private HousePricingInfoDAO housePricingInfoDAO;

	public Long create( Long housePricingInfoId, BigDecimal av750 )
	{
		Validate.notNull( housePricingInfoId );

		HouseEstimateInfo pojo = new HouseEstimateInfo();
		pojo.setHousePricingInfo( housePricingInfoDAO.read( housePricingInfoId ) );
		pojo.setAv750( av750 );

		return super.createPojo( pojo );
	}

	@Override
	protected Class<HouseEstimateInfo> getPojoClass()
	{
		return HouseEstimateInfo.class;
	}
}
