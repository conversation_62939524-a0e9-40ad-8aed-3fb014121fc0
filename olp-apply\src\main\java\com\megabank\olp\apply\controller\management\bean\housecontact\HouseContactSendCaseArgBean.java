/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.housecontact;

import java.util.Date;

import javax.validation.Valid;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseContactSendCaseArgBean extends BaseBean
{
	private String caseNo;

	private String mobileNumber;

	private String branchBankCode;

	private Date createdDate;

	@Valid
	private HouseContactBasicInfoBean basicInfo;

	@Valid
	private HouseContactLoanInfoBean loanInfo;

	private String otherMsg;

	public HouseContactBasicInfoBean getBasicInfo()
	{
		return basicInfo;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public HouseContactLoanInfoBean getLoanInfo()
	{
		return loanInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public void setBasicInfo(HouseContactBasicInfoBean basicInfo )
	{
		this.basicInfo = basicInfo;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setLoanInfo( HouseContactLoanInfoBean loanInfo )
	{
		this.loanInfo = loanInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}
}
