package com.megabank.olp.api.controller.iloan.bean.signing;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.api.controller.eloan.bean.signing.LendingPlanInfoBean;
import com.megabank.olp.api.controller.eloan.bean.signing.LoanPurposeInfoBean;
import com.megabank.olp.base.bean.BaseBean;

public class LoanConditionInfoIloanBean extends BaseBean
{
	@NotNull
	private Integer loanAmt;

	@NotNull
	private Integer loanPeriod;

	@JsonProperty( "loanPurposeInfo" )
	@NotEmpty
	@Valid
	private List<LoanPurposeInfoBean> loanPurposeInfoBeans;

	@NotBlank
	private String drawDownType;

	@NotNull
	private Integer oneTimeFee;

	@NotNull
	private Integer preliminaryFee;

	@JsonProperty( "kreditCheckFee" )
	@NotNull
	private Integer creditCheckFee;

	private String repaymentMethod;

	@NotBlank
	private String lendingPlan;

	@JsonProperty( "lendingPlanInfo" )
	@NotNull
	@Valid
	private LendingPlanInfoBean lendingPlanInfoBean;

	public LoanConditionInfoIloanBean()
	{
	}

	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public String getDrawDownType()
	{
		return drawDownType;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public String getLendingPlan()
	{
		return lendingPlan;
	}

	public void setLendingPlan( String lendingPlan )
	{
		this.lendingPlan = lendingPlan;
	}

	public LendingPlanInfoBean getLendingPlanInfoBean()
	{
		return lendingPlanInfoBean;
	}

	public void setLendingPlanInfoBean( LendingPlanInfoBean lendingPlanInfoBean )
	{
		this.lendingPlanInfoBean = lendingPlanInfoBean;
	}

	public Integer getLoanAmt()
	{
		return loanAmt;
	}

	public void setLoanAmt( Integer loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public List<LoanPurposeInfoBean> getLoanPurposeInfoBeans()
	{
		return loanPurposeInfoBeans;
	}

	public void setLoanPurposeInfoBeans( List<LoanPurposeInfoBean> loanPurposeInfoBeans )
	{
		this.loanPurposeInfoBeans = loanPurposeInfoBeans;
	}

	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}

}
