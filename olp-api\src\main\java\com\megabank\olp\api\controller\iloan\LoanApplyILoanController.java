package com.megabank.olp.api.controller.iloan;

import java.util.Map;

import com.megabank.olp.api.controller.eloan.bean.apply.BranchUpdatedArgBean;
import com.megabank.olp.api.controller.eloan.bean.apply.LoanDiscardedArgBean;
import com.megabank.olp.api.controller.iloan.bean.apply.TransferCaseArgILoanBean;
import com.megabank.olp.api.service.iloan.LoanApplyILoanService;
import com.megabank.olp.api.utility.BaseILoanAPIController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping( "iloan/apply" )
public class LoanApplyILoanController extends BaseILoanAPIController
{
	@Autowired
	private LoanApplyILoanService service;

	@PostMapping( "discardLoan" )
	public Map<String, Object> discardLoan( @RequestHeader( value = "AccessToken" ) String accessToken,
	                                        @RequestBody @Validated LoanDiscardedArgBean argBean )
	{
		validAuth( accessToken );

		service.discardLoan( argBean.getCaseNo() );

		return getResponseMap();
	}

	@PostMapping( "updateBranch" )
	public Map<String, Object> updateBranch( @RequestHeader( value = "AccessToken" ) String accessToken,
	                                         @RequestBody @Validated BranchUpdatedArgBean argBean )
	{
		validAuth( accessToken );

		service.updateBranch( argBean.getCaseNo(), argBean.getBranchCode() );

		return getResponseMap();
	}

	@PostMapping( "transferCase" )
	public Map<String, Object> transferCase( @RequestHeader( value = "AccessToken" ) String accessToken,
											 @RequestBody @Validated TransferCaseArgILoanBean argBean )
	{
		validAuth( accessToken );

		service.transferCase( argBean.getCaseNo() );

		return getResponseMap();
	}
}
