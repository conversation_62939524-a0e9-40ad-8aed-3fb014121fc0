package com.megabank.olp.apply.persistence.dao.generated.apply.agreed;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreedItem;
import com.megabank.olp.base.config.BasePersistenceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyAgreedItemDAOIntegration
{
	@Autowired
	private ApplyAgreedItemDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getPojosByAgreedId()
	{
		Long agreedId = 1L;
		List<ApplyAgreedItem> result = dao.getPojosByAgreedId( agreedId );

		logger.info( "result:{}", result );
	}

}
