package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningEddaCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningEddaUpdateParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningRateCreatedParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningBankAccount;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningRate;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningUser;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.enums.OrderEnum;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderArgBean;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderResultBean;

/**
 * The ApplySigningEddaDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySigningRateDAO extends BasePojoDAO<ApplySigningRate, Long>
{
	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	@Qualifier( "clientObjectMapper" )
	protected ObjectMapper mapper;

	public Long create( SigningRateCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getSigningContractId() );
		Validate.notBlank( paramBean.getRateType() );
		Validate.notNull( paramBean.getRateBgn() );
		Validate.notNull( paramBean.getRateEnd() );
		Validate.notNull( paramBean.getRate() );

		ApplySigningRate pojo = new ApplySigningRate();
		pojo.setApplySigningContract( applySigningContractDAO.read( paramBean.getSigningContractId() ) );
		pojo.setRateType( paramBean.getRateType() );
		pojo.setRateBgn( paramBean.getRateBgn() );
		pojo.setRateEnd( paramBean.getRateEnd() );
		pojo.setRate( paramBean.getRate() );
		pojo.setCreateTime( new Date() );

		return super.createPojo( pojo );
	}


	public SigningEddaCreatedParamBean mapCreate( EddaSenderArgBean paramBean )
	{
		return mapper.convertValue( paramBean, SigningEddaCreatedParamBean.class );
	}

	public SigningEddaUpdateParamBean mapUpdate( EddaSenderResultBean paramBean )
	{
		return mapper.convertValue( paramBean, SigningEddaUpdateParamBean.class );
	}

	public ApplySigningRate read( Long rateId, Long contractId )
	{
		Validate.notNull( contractId );

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( ApplySigningRate.RATE_ID_CONSTANT, rateId ),
														  new NameValueBean( ApplySigningRate.APPLY_SIGNING_CONTRACT_CONSTANT,
																			 applySigningContractDAO.read( contractId ) ) };

		return getUniquePojoByProperties( conditions, ApplySigningRate.TABLENAME_CONSTANT );
	}
	
	public List<ApplySigningRate> getContractRates( Long contractId )
	{
		Validate.notNull( contractId );

		ApplySigningContract applySigningContract = applySigningContractDAO.read( contractId );

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( ApplySigningUser.APPLY_SIGNING_CONTRACT_CONSTANT, applySigningContract ) };

		return getPojosByPropertiesOrderBy( conditions, new OrderBean[ 0 ] );
	}

	@Override
	protected Class<ApplySigningRate> getPojoClass()
	{
		return ApplySigningRate.class;
	}
}
