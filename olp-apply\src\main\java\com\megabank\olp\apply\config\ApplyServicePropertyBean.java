package com.megabank.olp.apply.config;

import org.springframework.beans.factory.annotation.Value;

import com.megabank.olp.base.bean.BaseBean;

/**
 * test-010
 *
 * @version 1.0
 * <AUTHOR>
 * @company Mega Bank
 * @copyright Copyright (c) 2019
 */

public class ApplyServicePropertyBean extends BaseBean
{

	@Value( "${service.pdf.template.collateral.hidden}" )
	private boolean collateralHidden;

	@Value( "${service.pdf.template.personalloan.hidden}" )
	private boolean personalloanHidden;

	@Value( "${service.pdf.template.houseloan.hidden}" )
	private boolean houseloanHidden;

	@Value( "${service.pdf.template.personalloan.borrower}" )
	private String personalLoanBorrowerPdfTemplate;

	@Value( "${service.pdf.template.personalloan.guarantor}" )
	private String personalLoanGuarantorPdfTemplate;

	@Value( "${service.pdf.template.personalloan.relation}" )
	private String personalLoanRelationPdfTemplate;

	@Value( "${service.pdf.template.personalloan.agreement}" )
	private String personalLoanAgreementPdfTemplate;

	@Value( "${service.pdf.template.houseloan.borrower}" )
	private String houseLoanBorrowerPdfTemplate;

	@Value( "${service.pdf.template.houseloan.guarantor}" )
	private String houseLoanGuarantorPdfTemplate;

	@Value( "${service.pdf.template.houseloan.relation}" )
	private String houseLoanRelationPdfTemplate;

	@Value( "${service.pdf.template.signing.ixml1}" )
	private String signingPdfTemplateIxml1;

	@Value( "${service.pdf.template.signing.ixml2}" )
	private String signingPdfTemplateIxml2;

	@Value( "${service.pdf.template.signing.page1}" )
	private String signingPdfTemplatePage1;

	@Value( "${service.pdf.template.signing.page2}" )
	private String signingPdfTemplatePage2;

	@Value( "${service.pdf.template.signing.page2-1}" )
	private String signingPdfTemplatePage2_1;

	@Value( "${service.pdf.template.signing.page3}" )
	private String signingPdfTemplatePage3;

	@Value( "${service.pdf.template.signing.page4}" )
	private String signingPdfTemplatePage4;

	@Value( "${service.pdf.template.signing.page5}" )
	private String signingPdfTemplatePage5;

	@Value( "${service.pdf.template.signing.page6}" )
	private String signingPdfTemplatePage6;

	@Value( "${service.pdf.template.signing.page7}" )
	private String signingPdfTemplatePage7;

	@Value( "${service.pdf.template.signing.page8}" )
	private String signingPdfTemplatePage8;

	@Value( "${service.pdf.template.signing.page8_co70647919_c101}" )
	private String signingPdfTemplatePage8_co70647919_c101;

	@Value( "${service.pdf.template.signing.debit_auth_co70647919_c101}" )
	private String signingPdfTemplateDebit_auth_co70647919_c101;

	@Value( "${service.pdf.template.collateral.full-payment}" )
	private String collateralFullPaymentPdfTemplate;

	@Value( "${service.pdf.template.collateral.non-full-payment}" )
	private String collateralNonFullPaymentPdfTemplate;

	@Value( "${apply.url.download}" )
	private String downloadUrl;

	@Value( "${apply.url.signing}" )
	private String signingUrl;

	@Value( "${apply.url.houseSigning}" )
	private String houseSigning;

	@Value( "${apply.url.upload}" )
	private String uploadUrl;

	@Value( "${mailserver.from}" )
	private String mailServerFrom;

	@Value( "${mailserver.url}" )
	private String mailServerUrl;

	@Value( "${url.contract}" )
	private String contractUrl;

	@Value( "${mydata.returnUrl}" )
	private String myDataReturnUrl;

	@Value( "${service.pdf.template.personalloan.chinaSteel.parent}" )
	private String personalLoanChinaSteelParentBorrowerPdfTemplate;

	@Value( "${service.pdf.template.personalloan.chinaSteel.subsidiary}" )
	private String personalLoanChinaSteelSubsidiaryBorrowerPdfTemplate;

	@Value( "${service.pdf.template.personalloan.co70647919_c101}" )
	private String personalLoanBorrowerCo70647919_c101_PdfTemplate;

	@Value( "${service.pdf.template.signing.pageAch1}" )
	private String signingPdfTemplatePageAch1;

	@Value( "${service.pdf.template.signing.pageAch2}" )
	private String signingPdfTemplatePageAch2;

	@Value( "${service.pdf.template.signing.pageRate}" )
	private String signingPdfTemplatePageRate;

	@Value( "${service.pdf.template.signing.pageReceipt}" )
	private String signingPdfTemplatePageReceipt;

	@Value( "${service.pdf.template.signing.hidden}" )
	private boolean hiddenItextLoanSigning;

	@Value( "${ixml.callback}" )
	private String ixmlCallback;

	@Value( "${inet.url}" )
	private String inetUrl;

	@Value( "${inet.datasource}" )
	private String datasource;

	@Value( "${ixml.url}" )
	private String ixmlUrl;

	@Value( "${ixml.params.businessNo}" )
	private String businessNo;

	@Value( "${ixml.params.apiVersion}" )
	private String apiVersion;

	@Value( "${ixml.params.hashKeyNo}" )
	private String hashKeyNo;

	@Value( "${ixml.params.errCodeType}" )
	private String errCodeType;

	@Value( "${ixml.params.hashKey}" )
	private String hashKey;

	public ApplyServicePropertyBean()
	{}

	public String getApiVersion()
	{
		return apiVersion;
	}

	public String getBusinessNo()
	{
		return businessNo;
	}

	public String getCollateralFullPaymentPdfTemplate()
	{
		return collateralFullPaymentPdfTemplate;
	}

	public boolean getCollateralHidden()
	{
		return collateralHidden;
	}

	public String getCollateralNonFullPaymentPdfTemplate()
	{
		return collateralNonFullPaymentPdfTemplate;
	}

	public String getContractUrl()
	{
		return contractUrl;
	}

	public String getDatasource()
	{
		return datasource;
	}

	public String getDownloadUrl()
	{
		return downloadUrl;
	}

	public String getErrCodeType()
	{
		return errCodeType;
	}

	public String getHashKey()
	{
		return hashKey;
	}

	public String getHashKeyNo()
	{
		return hashKeyNo;
	}

	public String getHouseLoanBorrowerPdfTemplate()
	{
		return houseLoanBorrowerPdfTemplate;
	}

	public String getHouseLoanGuarantorPdfTemplate()
	{
		return houseLoanGuarantorPdfTemplate;
	}

	public boolean getHouseloanHidden()
	{
		return houseloanHidden;
	}

	public String getHouseLoanRelationPdfTemplate()
	{
		return houseLoanRelationPdfTemplate;
	}

	public String getHouseSigning()
	{
		return houseSigning;
	}

	public String getInetUrl()
	{
		return inetUrl;
	}

	public String getIxmlCallback()
	{
		return ixmlCallback;
	}

	public String getIxmlUrl()
	{
		return ixmlUrl;
	}

	public String getMailServerFrom()
	{
		return mailServerFrom;
	}

	public String getMailServerUrl()
	{
		return mailServerUrl;
	}

	public String getMyDataReturnUrl()
	{
		return myDataReturnUrl;
	}

	public String getPersonalLoanAgreementPdfTemplate()
	{
		return personalLoanAgreementPdfTemplate;
	}

	public String getPersonalLoanBorrowerCo70647919_c101_PdfTemplate()
	{
		return personalLoanBorrowerCo70647919_c101_PdfTemplate;
	}

	public String getPersonalLoanBorrowerPdfTemplate()
	{
		return personalLoanBorrowerPdfTemplate;
	}

	public String getPersonalLoanChinaSteelParentBorrowerPdfTemplate()
	{
		return personalLoanChinaSteelParentBorrowerPdfTemplate;
	}

	public String getPersonalLoanChinaSteelSubsidiaryBorrowerPdfTemplate()
	{
		return personalLoanChinaSteelSubsidiaryBorrowerPdfTemplate;
	}

	public String getPersonalLoanGuarantorPdfTemplate()
	{
		return personalLoanGuarantorPdfTemplate;
	}

	public boolean getPersonalloanHidden()
	{
		return personalloanHidden;
	}

	public String getPersonalLoanRelationPdfTemplate()
	{
		return personalLoanRelationPdfTemplate;
	}

	public String getSigningPdfTemplateDebit_auth_co70647919_c101()
	{
		return signingPdfTemplateDebit_auth_co70647919_c101;
	}

	public String getSigningPdfTemplateIxml1()
	{
		return signingPdfTemplateIxml1;
	}

	public String getSigningPdfTemplateIxml2()
	{
		return signingPdfTemplateIxml2;
	}

	public String getSigningPdfTemplatePage1()
	{
		return signingPdfTemplatePage1;
	}

	public String getSigningPdfTemplatePage2()
	{
		return signingPdfTemplatePage2;
	}

	public String getSigningPdfTemplatePage2_1()
	{
		return signingPdfTemplatePage2_1;
	}

	public String getSigningPdfTemplatePage3()
	{
		return signingPdfTemplatePage3;
	}

	public String getSigningPdfTemplatePage4()
	{
		return signingPdfTemplatePage4;
	}

	public String getSigningPdfTemplatePage5()
	{
		return signingPdfTemplatePage5;
	}

	public String getSigningPdfTemplatePage6()
	{
		return signingPdfTemplatePage6;
	}

	public String getSigningPdfTemplatePage7()
	{
		return signingPdfTemplatePage7;
	}

	public String getSigningPdfTemplatePage8()
	{
		return signingPdfTemplatePage8;
	}

	public String getSigningPdfTemplatePage8_co70647919_c101()
	{
		return signingPdfTemplatePage8_co70647919_c101;
	}

	public String getSigningPdfTemplatePageAch1()
	{
		return signingPdfTemplatePageAch1;
	}

	public String getSigningPdfTemplatePageAch2()
	{
		return signingPdfTemplatePageAch2;
	}

	public String getSigningPdfTemplatePageRate()
	{
		return signingPdfTemplatePageRate;
	}

	public String getSigningPdfTemplatePageReceipt()
	{
		return signingPdfTemplatePageReceipt;
	}

	public String getSigningUrl()
	{
		return signingUrl;
	}

	public String getUploadUrl()
	{
		return uploadUrl;
	}

	public boolean isHiddenItextLoanSigning()
	{
		return hiddenItextLoanSigning;
	}

	public void setApiVersion( String apiVersion )
	{
		this.apiVersion = apiVersion;
	}

	public void setBusinessNo( String businessNo )
	{
		this.businessNo = businessNo;
	}

	public void setCollateralFullPaymentPdfTemplate( String collateralFullPaymentPdfTemplate )
	{
		this.collateralFullPaymentPdfTemplate = collateralFullPaymentPdfTemplate;
	}

	public void setCollateralHidden( boolean collateralHidden )
	{
		this.collateralHidden = collateralHidden;
	}

	public void setCollateralNonFullPaymentPdfTemplate( String collateralNonFullPaymentPdfTemplate )
	{
		this.collateralNonFullPaymentPdfTemplate = collateralNonFullPaymentPdfTemplate;
	}

	public void setContractUrl( String contractUrl )
	{
		this.contractUrl = contractUrl;
	}

	public void setDatasource( String datasource )
	{
		this.datasource = datasource;
	}

	public void setDownloadUrl( String downloadUrl )
	{
		this.downloadUrl = downloadUrl;
	}

	public void setErrCodeType( String errCodeType )
	{
		this.errCodeType = errCodeType;
	}

	public void setHashKey( String hashKey )
	{
		this.hashKey = hashKey;
	}

	public void setHashKeyNo( String hashKeyNo )
	{
		this.hashKeyNo = hashKeyNo;
	}

	public void setHouseLoanBorrowerPdfTemplate( String houseLoanBorrowerPdfTemplate )
	{
		this.houseLoanBorrowerPdfTemplate = houseLoanBorrowerPdfTemplate;
	}

	public void setHouseLoanGuarantorPdfTemplate( String houseLoanGuarantorPdfTemplate )
	{
		this.houseLoanGuarantorPdfTemplate = houseLoanGuarantorPdfTemplate;
	}

	public void setHouseloanHidden( boolean houseloanHidden )
	{
		this.houseloanHidden = houseloanHidden;
	}

	public void setHouseLoanRelationPdfTemplate( String houseLoanRelationPdfTemplate )
	{
		this.houseLoanRelationPdfTemplate = houseLoanRelationPdfTemplate;
	}

	public void setHouseSigning( String houseSigning )
	{
		this.houseSigning = houseSigning;
	}

	public void setInetUrl( String inetUrl )
	{
		this.inetUrl = inetUrl;
	}

	public void setIxmlCallback( String ixmlCallback )
	{
		this.ixmlCallback = ixmlCallback;
	}

	public void setIxmlUrl( String ixmlUrl )
	{
		this.ixmlUrl = ixmlUrl;
	}

	public void setMailServerFrom( String mailServerFrom )
	{
		this.mailServerFrom = mailServerFrom;
	}

	public void setMailServerUrl( String mailServerUrl )
	{
		this.mailServerUrl = mailServerUrl;
	}

	public void setMyDataReturnUrl( String myDataReturnUrl )
	{
		this.myDataReturnUrl = myDataReturnUrl;
	}

	public void setPersonalLoanAgreementPdfTemplate( String personalLoanAgreementPdfTemplate )
	{
		this.personalLoanAgreementPdfTemplate = personalLoanAgreementPdfTemplate;
	}

	public void setPersonalLoanBorrowerCo70647919_c101_PdfTemplate( String personalLoanBorrowerCo70647919_c101_PdfTemplate )
	{
		this.personalLoanBorrowerCo70647919_c101_PdfTemplate = personalLoanBorrowerCo70647919_c101_PdfTemplate;
	}

	public void setPersonalLoanBorrowerPdfTemplate( String personalLoanBorrowerPdfTemplate )
	{
		this.personalLoanBorrowerPdfTemplate = personalLoanBorrowerPdfTemplate;
	}

	public void setPersonalLoanChinaSteelParentBorrowerPdfTemplate( String personalLoanChinaSteelParentBorrowerPdfTemplate )
	{
		this.personalLoanChinaSteelParentBorrowerPdfTemplate = personalLoanChinaSteelParentBorrowerPdfTemplate;
	}

	public void setPersonalLoanChinaSteelSubsidiaryBorrowerPdfTemplate( String personalLoanChinaSteelSubsidiaryBorrowerPdfTemplate )
	{
		this.personalLoanChinaSteelSubsidiaryBorrowerPdfTemplate = personalLoanChinaSteelSubsidiaryBorrowerPdfTemplate;
	}

	public void setPersonalLoanGuarantorPdfTemplate( String personalLoanGuarantorPdfTemplate )
	{
		this.personalLoanGuarantorPdfTemplate = personalLoanGuarantorPdfTemplate;
	}

	public void setPersonalloanHidden( boolean personalloanHidden )
	{
		this.personalloanHidden = personalloanHidden;
	}

	public void setPersonalLoanRelationPdfTemplate( String personalLoanRelationPdfTemplate )
	{
		this.personalLoanRelationPdfTemplate = personalLoanRelationPdfTemplate;
	}

	public void setSigningPdfTemplateDebit_auth_co70647919_c101( String signingPdfTemplateDebit_auth_co70647919_c101 )
	{
		this.signingPdfTemplateDebit_auth_co70647919_c101 = signingPdfTemplateDebit_auth_co70647919_c101;
	}

	public void setSigningPdfTemplateIxml1( String signingPdfTemplateIxml1 )
	{
		this.signingPdfTemplateIxml1 = signingPdfTemplateIxml1;
	}

	public void setSigningPdfTemplateIxml2( String signingPdfTemplateIxml2 )
	{
		this.signingPdfTemplateIxml2 = signingPdfTemplateIxml2;
	}

	public void setSigningPdfTemplatePage1( String signingPdfTemplatePage1 )
	{
		this.signingPdfTemplatePage1 = signingPdfTemplatePage1;
	}

	public void setSigningPdfTemplatePage2( String signingPdfTemplatePage2 )
	{
		this.signingPdfTemplatePage2 = signingPdfTemplatePage2;
	}

	public void setSigningPdfTemplatePage2_1( String signingPdfTemplatePage2_1 )
	{
		this.signingPdfTemplatePage2_1 = signingPdfTemplatePage2_1;
	}

	public void setSigningPdfTemplatePage3( String signingPdfTemplatePage3 )
	{
		this.signingPdfTemplatePage3 = signingPdfTemplatePage3;
	}

	public void setSigningPdfTemplatePage4( String signingPdfTemplatePage4 )
	{
		this.signingPdfTemplatePage4 = signingPdfTemplatePage4;
	}

	public void setSigningPdfTemplatePage5( String signingPdfTemplatePage5 )
	{
		this.signingPdfTemplatePage5 = signingPdfTemplatePage5;
	}

	public void setSigningPdfTemplatePage6( String signingPdfTemplatePage6 )
	{
		this.signingPdfTemplatePage6 = signingPdfTemplatePage6;
	}

	public void setSigningPdfTemplatePage7( String signingPdfTemplatePage7 )
	{
		this.signingPdfTemplatePage7 = signingPdfTemplatePage7;
	}

	public void setSigningPdfTemplatePage8( String signingPdfTemplatePage8 )
	{
		this.signingPdfTemplatePage8 = signingPdfTemplatePage8;
	}

	public void setSigningPdfTemplatePage8_co70647919_c101( String signingPdfTemplatePage8_co70647919_c101 )
	{
		this.signingPdfTemplatePage8_co70647919_c101 = signingPdfTemplatePage8_co70647919_c101;
	}

	public void setSigningPdfTemplatePageAch1( String signingPdfTemplatePageAch1 )
	{
		this.signingPdfTemplatePageAch1 = signingPdfTemplatePageAch1;
	}

	public void setSigningPdfTemplatePageAch2( String signingPdfTemplatePageAch2 )
	{
		this.signingPdfTemplatePageAch2 = signingPdfTemplatePageAch2;
	}

	public void setSigningUrl( String signingUrl )
	{
		this.signingUrl = signingUrl;
	}

	public void setUploadUrl( String uploadUrl )
	{
		this.uploadUrl = uploadUrl;
	}
}