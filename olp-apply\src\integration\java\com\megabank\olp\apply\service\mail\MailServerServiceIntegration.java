package com.megabank.olp.apply.service.mail;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class MailServerServiceIntegration
{
	@Autowired
	private MailServerService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void send()
	{
		String to = "<EMAIL>";
		String subject = "測試mail主旨";
		String text = "測試mail內文";

		boolean result = service.send( to, subject, text );

		logger.info( "result:{}", result );
	}

}