package com.megabank.olp.apply.persistence.dao.generated.identity;

import com.megabank.olp.apply.persistence.pojo.identity.IdentityIloanWhitelist;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

/**
 * The IdentityIloanWhitelistDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class IdentityIloanWhitelistDAO extends BasePojoDAO<IdentityIloanWhitelist, Long>
{
	public IdentityIloanWhitelist getPojoByIdNo( String idNo )
	{
		Validate.notNull( idNo );

		NameValueBean condition = new NameValueBean( IdentityIloanWhitelist.ID_NO_CONSTANT, idNo );

		return getUniquePojoByProperty( condition );
	}


	public IdentityIloanWhitelist read( Long iloanWhitelistId )
	{
		Validate.notNull( iloanWhitelistId );

		return getPojoByPK( iloanWhitelistId, IdentityIloanWhitelist.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<IdentityIloanWhitelist> getPojoClass()
	{
		return IdentityIloanWhitelist.class;
	}
}
