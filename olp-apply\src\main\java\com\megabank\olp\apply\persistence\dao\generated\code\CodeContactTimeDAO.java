package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeContactTime;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeContactTimeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeContactTimeDAO extends BasePojoDAO<CodeContactTime, String>
{

	public CodeContactTime read( String contactTimeCode )
	{
		Validate.notBlank( contactTimeCode );

		return getPojoByPK( contactTimeCode, CodeContactTime.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeContactTime> getPojoClass()
	{
		return CodeContactTime.class;
	}
}
