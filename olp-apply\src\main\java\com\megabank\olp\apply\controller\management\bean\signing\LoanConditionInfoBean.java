/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.signing;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class LoanConditionInfoBean extends BaseBean
{
	@NotNull
	private Integer loanAmt;

	@NotNull
	private Integer loanPeriod;

	@JsonProperty( "loanPurposeInfo" )
	@NotEmpty
	@Valid
	private List<LoanPurposeInfoBean> loanPurposeInfoBeans;

	@NotBlank
	private String drawDownType;

	@NotNull
	private Integer oneTimeFee;

	@NotNull
	private Integer preliminaryFee;

	@NotNull
	private Integer creditCheckFee;

	private BigDecimal renewFee;

	private BigDecimal changeFee;

	private BigDecimal certFee;

	private BigDecimal reissueFee;

	private String repaymentMethod;

	@NotBlank
	private String lendingPlan;

	@NotBlank
	private String advancedRedemptionTitle;

	@NotBlank
	private String advancedRedemptionDesc;

	@NotBlank
	private String advancedRateTitle;

	@NotBlank
	private String advancedRateDesc;

	private BigDecimal advancedApr;

	@NotBlank
	private String limitedRedemptionTitle;

	@NotBlank
	private String limitedRedemptionDesc;

	@NotBlank
	private String limitedRateTitle;

	@NotBlank
	private String limitedRateDesc;

	private BigDecimal limitedApr;

	@NotBlank
	private String showOption;

	private String otherInfoTitle;

	private String otherInfoDesc;

	public LoanConditionInfoBean()
	{}

	public Integer getLoanAmt()
	{
		return loanAmt;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public List<LoanPurposeInfoBean> getLoanPurposeInfoBeans()
	{
		return loanPurposeInfoBeans;
	}

	public String getDrawDownType()
	{
		return drawDownType;
	}

	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	public BigDecimal getRenewFee()
	{
		return renewFee;
	}

	public BigDecimal getChangeFee()
	{
		return changeFee;
	}

	public BigDecimal getCertFee()
	{
		return certFee;
	}

	public BigDecimal getReissueFee()
	{
		return reissueFee;
	}

	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	public String getLendingPlan()
	{
		return lendingPlan;
	}

	public String getAdvancedRedemptionTitle()
	{
		return advancedRedemptionTitle;
	}

	public String getAdvancedRedemptionDesc()
	{
		return advancedRedemptionDesc;
	}

	public String getAdvancedRateTitle()
	{
		return advancedRateTitle;
	}

	public String getAdvancedRateDesc()
	{
		return advancedRateDesc;
	}

	public BigDecimal getAdvancedApr()
	{
		return advancedApr;
	}

	public String getLimitedRedemptionTitle()
	{
		return limitedRedemptionTitle;
	}

	public String getLimitedRedemptionDesc()
	{
		return limitedRedemptionDesc;
	}

	public String getLimitedRateTitle()
	{
		return limitedRateTitle;
	}

	public String getLimitedRateDesc()
	{
		return limitedRateDesc;
	}

	public BigDecimal getLimitedApr()
	{
		return limitedApr;
	}

	public String getShowOption()
	{
		return showOption;
	}

	public String getOtherInfoTitle()
	{
		return otherInfoTitle;
	}

	public String getOtherInfoDesc()
	{
		return otherInfoDesc;
	}

	public void setLoanAmt( Integer loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanPurposeInfoBeans( List<LoanPurposeInfoBean> loanPurposeInfoBeans )
	{
		this.loanPurposeInfoBeans = loanPurposeInfoBeans;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public void setRenewFee( BigDecimal renewFee )
	{
		this.renewFee = renewFee;
	}

	public void setChangeFee( BigDecimal changeFee )
	{
		this.changeFee = changeFee;
	}

	public void setCertFee( BigDecimal certFee )
	{
		this.certFee = certFee;
	}

	public void setReissueFee( BigDecimal reissueFee )
	{
		this.reissueFee = reissueFee;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}

	public void setLendingPlan( String lendingPlan )
	{
		this.lendingPlan = lendingPlan;
	}

	public void setAdvancedRedemptionTitle( String advancedRedemptionTitle )
	{
		this.advancedRedemptionTitle = advancedRedemptionTitle;
	}

	public void setAdvancedRedemptionDesc( String advancedRedemptionDesc )
	{
		this.advancedRedemptionDesc = advancedRedemptionDesc;
	}

	public void setAdvancedRateTitle( String advancedRateTitle )
	{
		this.advancedRateTitle = advancedRateTitle;
	}

	public void setAdvancedRateDesc( String advancedRateDesc )
	{
		this.advancedRateDesc = advancedRateDesc;
	}

	public void setAdvancedApr( BigDecimal advancedApr )
	{
		this.advancedApr = advancedApr;
	}

	public void setLimitedRedemptionTitle( String limitedRedemptionTitle )
	{
		this.limitedRedemptionTitle = limitedRedemptionTitle;
	}

	public void setLimitedRedemptionDesc( String limitedRedemptionDesc )
	{
		this.limitedRedemptionDesc = limitedRedemptionDesc;
	}

	public void setLimitedRateTitle( String limitedRateTitle )
	{
		this.limitedRateTitle = limitedRateTitle;
	}

	public void setLimitedRateDesc( String limitedRateDesc )
	{
		this.limitedRateDesc = limitedRateDesc;
	}

	public void setLimitedApr( BigDecimal limitedApr )
	{
		this.limitedApr = limitedApr;
	}

	public void setShowOption( String showOption )
	{
		this.showOption = showOption;
	}

	public void setOtherInfoTitle( String otherInfoTitle )
	{
		this.otherInfoTitle = otherInfoTitle;
	}

	public void setOtherInfoDesc( String otherInfoDesc )
	{
		this.otherInfoDesc = otherInfoDesc;
	}
}
