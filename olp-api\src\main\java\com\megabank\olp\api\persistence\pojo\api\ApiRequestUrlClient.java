package com.megabank.olp.api.persistence.pojo.api;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApiRequestUrlClient is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "api_request_url_client" )
public class ApiRequestUrlClient extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "api_request_url_client";

	public static final String REQUEST_URL_CLIENT_ID_CONSTANT = "requestUrlClientId";

	public static final String API_REQUEST_URL_CONSTANT = "apiRequestUrl";

	public static final String CLIENT_ADDRESS_CONSTANT = "clientAddress";

	private Long requestUrlClientId;

	private transient ApiRequestUrl apiRequestUrl;

	private String clientAddress;

	public ApiRequestUrlClient()
	{}

	public ApiRequestUrlClient( Long requestUrlClientId )
	{
		this.requestUrlClientId = requestUrlClientId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "request_url_id", nullable = false )
	public ApiRequestUrl getApiRequestUrl()
	{
		return apiRequestUrl;
	}

	@Column( name = "client_address", nullable = false, length = 50 )
	public String getClientAddress()
	{
		return clientAddress;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "request_url_client_id", unique = true, nullable = false )
	public Long getRequestUrlClientId()
	{
		return requestUrlClientId;
	}

	public void setApiRequestUrl( ApiRequestUrl apiRequestUrl )
	{
		this.apiRequestUrl = apiRequestUrl;
	}

	public void setClientAddress( String clientAddress )
	{
		this.clientAddress = clientAddress;
	}

	public void setRequestUrlClientId( Long requestUrlClientId )
	{
		this.requestUrlClientId = requestUrlClientId;
	}
}