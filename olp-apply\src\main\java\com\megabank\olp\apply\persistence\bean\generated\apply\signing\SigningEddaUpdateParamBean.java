/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningEddaUpdateParamBean extends BaseBean
{
	private Long eddaId;
	
	private Long signingContractId;

	private String aDate;

	private String rc;

	private String effDate;

	private String note;

	private String seq;

	private String cNo;

	private String reservedField;

	private String pBankNote;

	private String rcDesc;

	public SigningEddaUpdateParamBean()
	{}

	public Long getEddaId()
	{
		return eddaId;
	}
	
	public Long getSigningContractId()
	{
		return signingContractId;
	}
	
	public String getaDate()
	{
		return aDate;
	}

	public String getcNo()
	{
		return cNo;
	}

	public String getEffDate()
	{
		return effDate;
	}

	public String getNote()
	{
		return note;
	}

	public String getpBankNote()
	{
		return pBankNote;
	}

	public String getRc()
	{
		return rc;
	}

	public String getRcDesc()
	{
		return rcDesc;
	}

	public String getReservedField()
	{
		return reservedField;
	}

	public String getSeq()
	{
		return seq;
	}

	public void setEddaId( Long eddaId )
	{
		this.eddaId = eddaId;
	}
	
	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}
	
	public void setaDate( String aDate )
	{
		this.aDate = aDate;
	}

	public void setcNo( String cNo )
	{
		this.cNo = cNo;
	}

	public void setEffDate( String effDate )
	{
		this.effDate = effDate;
	}

	public void setNote( String note )
	{
		this.note = note;
	}

	public void setpBankNote( String pBankNote )
	{
		this.pBankNote = pBankNote;
	}

	public void setRc( String rc )
	{
		this.rc = rc;
	}

	public void setRcDesc( String rcDesc )
	{
		this.rcDesc = rcDesc;
	}

	public void setReservedField( String reservedField )
	{
		this.reservedField = reservedField;
	}

	public void setSeq( String seq )
	{
		this.seq = seq;
	}

}
