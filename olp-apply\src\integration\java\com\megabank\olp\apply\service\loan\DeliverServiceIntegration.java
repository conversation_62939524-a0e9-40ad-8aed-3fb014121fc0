package com.megabank.olp.apply.service.loan;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.megabank.olp.base.enums.RecipientSystemEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.dao.generated.ixml.applyRecord.IxmlApplyRecordDAO;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class DeliverServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;
	
	@Autowired
	private DeliverService service;

	@Autowired
	private IxmlApplyRecordDAO ixmlApplyRecordDAO;

	private final Logger logger = LogManager.getLogger( getClass() );
	

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	@Test
	public void retryIxmlCert()
	{
		service.retryIxmlCert();
	}

	@Disabled
	@Test
	public void retrySubmitAttachment()
	{
		service.retrySubmitAttachment( RecipientSystemEnum.ELOAN.getSystemId() );
		service.retrySubmitAttachment( RecipientSystemEnum.ILOAN.getSystemId() );
	}

	@Disabled
	@Test
	public void retrySubmitLoan()
	{
		service.retrySubmitLoan( RecipientSystemEnum.ELOAN.getSystemId() );
		service.retrySubmitLoan( RecipientSystemEnum.ILOAN.getSystemId() );
	}

	@Disabled
	@Test
	public void retrySubmitSigningContract()
	{
		service.retrySubmitSigningContract( RecipientSystemEnum.ELOAN.getSystemId() );
		service.retrySubmitSigningContract( RecipientSystemEnum.ILOAN.getSystemId() );
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.SKIP.getContext(), IdentityTypeEnum.OTHER_BANK.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}

}
