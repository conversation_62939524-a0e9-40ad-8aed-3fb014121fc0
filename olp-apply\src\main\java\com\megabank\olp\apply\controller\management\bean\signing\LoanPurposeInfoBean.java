package com.megabank.olp.apply.controller.management.bean.signing;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class LoanPurposeInfoBean extends BaseBean
{
	@NotBlank
	private String loanPurpose;

	@NotNull
	private Boolean isChecked;

	public LoanPurposeInfoBean()
	{}

	public Boolean getIsChecked()
	{
		return isChecked;
	}

	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public void setIsChecked( Boolean isChecked )
	{
		this.isChecked = isChecked;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}
}
