/**
 *
 */
package com.megabank.olp.apply.service.loan;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.megabank.olp.base.enums.ProductCodeEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.apply.SigningContractFillInfoParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.SigningContractResBean;
import com.megabank.olp.apply.service.loan.bean.signing.AgreementResBean;
import com.megabank.olp.apply.service.loan.bean.signing.SigningContractBasicResBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class SigningContractServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;
	
	@Autowired
	private SigningContractService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void checkSigningContractExisted()
	{
		String idNo = "A*********";
		Date birth = CommonDateUtils.getDate( 1990, 1, 1 );
		boolean result = service.checkSigningContractExisted( idNo, birth, ProductCodeEnum.HOUSE_LOAN.getContext() );

		logger.info( "result:{}", result );
	}

	@Test
	public void getAgreement()
	{
		String contractNo = "PA000001";

		AgreementResBean resBean = service.getAgreement( contractNo, "housesigningcontract" );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getSigningContractBasicInfo()
	{
		String contractNo = "PA000001";
		SigningContractBasicResBean resBean = service.getSigningContractBasicInfo( contractNo );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getSigningContracts()
	{
		List<SigningContractResBean> result = service.getSigningContracts( "PA" );

		logger.info( "result:{}", result );
	}

	@Test
	public void getThankyouMessage()
	{
		String contractNo = "PA000004";

		MessageResBean resBean = service.getThankyouMessage( contractNo );

		logger.info( "resBean:{}", resBean );
	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	@Test
	public void saveContract() throws Exception
	{
		SigningContractFillInfoParamBean argBean = getSigningContractFillInfoParamBean();
		String result = service.saveContract( argBean );
		logger.info( "result:{}", result );
	}

	private SigningContractFillInfoParamBean getSigningContractFillInfoParamBean()
	{
		SigningContractFillInfoParamBean paramBean = new SigningContractFillInfoParamBean();
		paramBean.setAppropriationDate( CommonDateUtils.getDate( 2020, 06, 06 ) );
		paramBean.setBankAcctCode( "017" );
		paramBean.setBankAcctNo( "*********" );
		paramBean.setContractNo( "PA000001" );
		// paramBean.setMailAddressStreet( "考試路123巷" );
		// paramBean.setMailAddressTownCode( "110" );
		paramBean.setBorrowerContractSendingMethodCode( "01" );
		paramBean.setRateAdjustInformMethodCode( "01" );
		paramBean.setFirstPaymentDate( CommonDateUtils.getDate( 2020, 06, 03 ) );
		paramBean.setRepayment( "03" );

		return paramBean;
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A*********";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.OTHER_BANK.getContext(), IdentityTypeEnum.OTP.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}
}
