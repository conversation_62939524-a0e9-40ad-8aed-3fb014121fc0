/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanEvaluateArgBean extends BaseBean
{
	@NotBlank
	private String caseID;

	private String mobileNumber;

	private String email;

	@NotNull
	private Date createdDate;

	@JsonProperty( "contactBranchCode" )
	@NotBlank
	private String branchBankCode;

	@Valid
	private EvaluateHouseInfoBean houseInfo;

	@Valid
	private EvaluateEstimateInfoBean estimateInfo;

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseID()
	{
		return caseID;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getEmail()
	{
		return email;
	}

	public EvaluateEstimateInfoBean getEstimateInfo()
	{
		return estimateInfo;
	}

	public EvaluateHouseInfoBean getHouseInfo()
	{
		return houseInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseID( String caseID )
	{
		this.caseID = caseID;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setEstimateInfo( EvaluateEstimateInfoBean estimateInfo )
	{
		this.estimateInfo = estimateInfo;
	}

	public void setHouseInfo( EvaluateHouseInfoBean houseInfo )
	{
		this.houseInfo = houseInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}
}
