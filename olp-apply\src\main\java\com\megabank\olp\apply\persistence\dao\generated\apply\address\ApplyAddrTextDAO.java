package com.megabank.olp.apply.persistence.dao.generated.apply.address;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.address.ApplyAddrTextParamBean;
import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddrText;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyAddrTextDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyAddrTextDAO extends BasePojoDAO<ApplyAddrText, Long>
{

	public Long create( ApplyAddrTextParamBean paramBean )
	{
		Validate.notBlank( paramBean.getAddr() );

		ApplyAddrText pojo = new ApplyAddrText();
		pojo.setAddrPostalCode( paramBean.getAddrPostalCode() );
		pojo.setAddr( paramBean.getAddr() );

		return super.createPojo( pojo );
	}

	public ApplyAddrText read( Long addressId )
	{
		Validate.notNull( addressId );

		return getPojoByPK( addressId, ApplyAddrText.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<ApplyAddrText> getPojoClass()
	{
		return ApplyAddrText.class;
	}
}
