package com.megabank.olp.apply.persistence.pojo.apply.mydata;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.code.CodeMyDataStatus;
import com.megabank.olp.apply.persistence.pojo.code.CodeTransmissionStatus;
import com.megabank.olp.apply.persistence.pojo.temp.TempMyDataFile;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyMyData is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_my_data" )
public class ApplyMyData extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_my_data";

	public static final String MY_DATA_ID_CONSTANT = "myDataId";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String CODE_MY_DATA_STATUS_CONSTANT = "codeMyDataStatus";

	public static final String CODE_TRANSMISSION_STATUS_CONSTANT = "codeTransmissionStatus";

	public static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	public static final String SERVICE_ID_CONSTANT = "serviceId";

	public static final String TXID_CONSTANT = "txid";

	public static final String ERROR_CODE_CONSTANT = "errorCode";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String APPLY_MY_DATA_NOTIFICATIONS_CONSTANT = "applyMyDataNotifications";

	public static final String TEMP_MY_DATA_FILES_CONSTANT = "tempMyDataFiles";

	private Long myDataId; // J-110-0093 於[olp-common]application.yml 與 application-uat.yml 與 application-prod.yml 開啟 MyData功能

	private transient ApplyLoan applyLoan;

	private transient CodeMyDataStatus codeMyDataStatus;

	private transient CodeTransmissionStatus codeTransmissionStatus;

	private long validatedIdentityId;

	private String serviceId;

	private String txid;

	private String errorCode;

	private Date updatedDate;

	private Date createdDate;

	private boolean notified;

	private transient Set<ApplyMyDataNotification> applyMyDataNotifications = new HashSet<>( 0 );

	private transient Set<TempMyDataFile> tempMyDataFiles = new HashSet<>( 0 );

	public ApplyMyData()
	{}

	public ApplyMyData( ApplyLoan applyLoan, CodeMyDataStatus codeMyDataStatus, CodeTransmissionStatus codeTransmissionStatus,
						long validatedIdentityId, String serviceId, String txid, Date updatedDate, Date createdDate, boolean notified )
	{
		this.applyLoan = applyLoan;
		this.codeMyDataStatus = codeMyDataStatus;
		this.codeTransmissionStatus = codeTransmissionStatus;
		this.validatedIdentityId = validatedIdentityId;
		this.serviceId = serviceId;
		this.txid = txid;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
		this.notified = notified;
	}

	public ApplyMyData( Long myDataId )
	{
		this.myDataId = myDataId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_id", nullable = false )
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyMyData" )
	public Set<ApplyMyDataNotification> getApplyMyDataNotifications()
	{
		return applyMyDataNotifications;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "my_data_status_code", nullable = false )
	public CodeMyDataStatus getCodeMyDataStatus()
	{
		return codeMyDataStatus;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "transmission_status_code", nullable = false )
	public CodeTransmissionStatus getCodeTransmissionStatus()
	{
		return codeTransmissionStatus;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "error_code", length = 20 )
	public String getErrorCode()
	{
		return errorCode;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "my_data_id", unique = true, nullable = false )
	public Long getMyDataId()
	{
		return myDataId;
	}

	@Column( name = "service_id", nullable = false, length = 50 )
	public String getServiceId()
	{
		return serviceId;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyMyData" )
	public Set<TempMyDataFile> getTempMyDataFiles()
	{
		return tempMyDataFiles;
	}

	@Column( name = "txid", nullable = false, length = 100 )
	public String getTxid()
	{
		return txid;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "validated_identity_id", nullable = false )
	public long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setApplyMyDataNotifications( Set<ApplyMyDataNotification> applyMyDataNotifications )
	{
		this.applyMyDataNotifications = applyMyDataNotifications;
	}

	public void setCodeMyDataStatus( CodeMyDataStatus codeMyDataStatus )
	{
		this.codeMyDataStatus = codeMyDataStatus;
	}

	public void setCodeTransmissionStatus( CodeTransmissionStatus codeTransmissionStatus )
	{
		this.codeTransmissionStatus = codeTransmissionStatus;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setErrorCode( String errorCode )
	{
		this.errorCode = errorCode;
	}

	public void setMyDataId( Long myDataId )
	{
		this.myDataId = myDataId;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setServiceId( String serviceId )
	{
		this.serviceId = serviceId;
	}

	public void setTempMyDataFiles( Set<TempMyDataFile> tempMyDataFiles )
	{
		this.tempMyDataFiles = tempMyDataFiles;
	}

	public void setTxid( String txid )
	{
		this.txid = txid;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

	public void setValidatedIdentityId( long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}
}