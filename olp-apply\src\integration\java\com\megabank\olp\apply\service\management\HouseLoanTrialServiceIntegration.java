package com.megabank.olp.apply.service.management;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.download.FileDownloadedResBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanDataBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialBasicDataBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialDetailResBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialExportedParamBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialListedParamBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialListedResBean;
import com.megabank.olp.apply.service.management.bean.houseloantrial.HouseLoanTrialSendCaseParamBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HouseLoanTrialServiceIntegration
{
	@Autowired
	private HouseLoanTrialService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		Long result = service.create( getLoanTrialSendCaseParamBean() );

		logger.info( "result:{}", result );
	}

	@Test
	public void exportList()
	{
		HouseLoanTrialExportedParamBean paramBean = new HouseLoanTrialExportedParamBean();

		FileDownloadedResBean resBean = service.exportList( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getDetail()
	{
		Long loanTrialId = 1L;

		HouseLoanTrialDetailResBean resBean = service.getDetail( loanTrialId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void listLoanTrial()
	{
		HouseLoanTrialListedParamBean paramBean = new HouseLoanTrialListedParamBean();
		paramBean.setPage( 1 );

		HouseLoanTrialListedResBean resBean = service.listLoanTrial( paramBean );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void updateProcessStatus()
	{
		Long loanTrialId = 1L;
		String processCode = "processing";
		String employeeId = "developer-01";
		String employeeName = "developer";

		Long id = service.updateProcessStatus( loanTrialId, processCode, employeeId, employeeName );

		logger.info( "id:{}", id );
	}

	private HouseLoanTrialSendCaseParamBean getLoanTrialSendCaseParamBean()
	{
		HouseLoanTrialSendCaseParamBean paramBean = new HouseLoanTrialSendCaseParamBean();

		paramBean.setCaseNo( "HoLoanTrialTest001" );
		paramBean.setCreatedDate( new Date() );
		paramBean.setEmail( "<EMAIL>" );
		paramBean.setMobileNumber( "" );
		paramBean.setBranchBankCode( "007" );

		HouseLoanTrialBasicDataBean basicInfo = new HouseLoanTrialBasicDataBean();
		basicInfo.setBalance( false );
		basicInfo.setBorrow( false );
		basicInfo.setBuyHouseFrom( 1 );
		basicInfo.setCashCard( true );
		basicInfo.setCashCardBalance( true );
		basicInfo.setCreditcard( true );
		basicInfo.setLoanPurpose( 0 );
		basicInfo.setPay( true );
		basicInfo.setTotalPrice( new BigDecimal( 2163.55 ) );
		basicInfo.setUserAge( 36 );
		basicInfo.setUserChildren( 0 );
		basicInfo.setUserIncome( 123 );
		basicInfo.setJobType( "資訊及通訊業" );
		basicInfo.setTitleType( "工程師" );
		paramBean.setBasicInfo( basicInfo );

		HouseLoanDataBean loanInfo = new HouseLoanDataBean();
		loanInfo.setRate( new BigDecimal( 1.4 ) );
		loanInfo.setTopLoanCredit( new BigDecimal( 1900 ) );
		paramBean.setLoanInfo( loanInfo );

		return paramBean;
	}
}
