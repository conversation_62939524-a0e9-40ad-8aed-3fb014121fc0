package com.megabank.olp.apply.controller.management.bean.houseloan;

import java.util.Date;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.validator.CheckDateRange;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */
@CheckDateRange( dateStart = "dateStart", dateEnd = "dateEnd" )
public class LoanApplyCompletedListedArgBean extends BaseBean
{
	private String branchBankCode;

	private String notificationStatusCode;

	private String transmissionStatusCode;

	private String idNo;

	private String name;

	private String mobileNumber;

	private Date dateStart;

	private Date dateEnd;

	@NotNull
	@Min( 1 )
	private Integer page;

	private Integer length;

	private String sortColumn;

	private String sortDirection;

	private Boolean discard;

	public LoanApplyCompletedListedArgBean()
	{
		// default constructor
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	public Date getDateStart()
	{
		return dateStart;
	}

	public Boolean getDiscard()
	{
		return discard;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public Integer getLength()
	{
		return length;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatusCode()
	{
		return notificationStatusCode;
	}

	public Integer getPage()
	{
		return page;
	}

	public String getSortColumn()
	{
		return sortColumn;
	}

	public String getSortDirection()
	{
		return sortDirection;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	public void setDiscard( Boolean discard )
	{
		this.discard = discard;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLength( Integer length )
	{
		this.length = length;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatusCode( String notificationStatusCode )
	{
		this.notificationStatusCode = notificationStatusCode;
	}

	public void setPage( Integer page )
	{
		this.page = page;
	}

	public void setSortColumn( String sortColumn )
	{
		this.sortColumn = sortColumn;
	}

	public void setSortDirection( String sortDirection )
	{
		this.sortDirection = sortDirection;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

}
