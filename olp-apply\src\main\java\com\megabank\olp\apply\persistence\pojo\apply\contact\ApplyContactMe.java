package com.megabank.olp.apply.persistence.pojo.apply.contact;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;

import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.apply.persistence.pojo.code.*;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyContactMe is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_contact_me", uniqueConstraints = @UniqueConstraint( columnNames = "case_no" ) )
public class ApplyContactMe extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_contact_me";

	public static final String CONTACT_ME_ID_CONSTANT = "contactMeId";

	public static final String CODE_BRANCH_BANK_BY_FINAL_BRANCH_BANK_ID_CONSTANT = "codeBranchBankByFinalBranchBankId";

	public static final String CODE_BRANCH_BANK_BY_BRANCH_BANK_ID_CONSTANT = "codeBranchBankByBranchBankId";

	public static final String CODE_CONTACT_TIME_CONSTANT = "codeContactTime";

	public static final String CODE_PROCESS_CONSTANT = "codeProcess";

	public static final String CODE_SEX_CONSTANT = "codeSex";

	public static final String NAME_CONSTANT = "name";

	public static final String CASE_NO_CONSTANT = "caseNo";

	public static final String PHONE_CODE_CONSTANT = "phoneCode";

	public static final String PHONE_NUMBER_CONSTANT = "phoneNumber";

	public static final String PHONE_EXT_CONSTANT = "phoneExt";

	public static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	public static final String EMAIL_CONSTANT = "email";

	public static final String OTHER_MSG_CONSTANT = "otherMsg";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String APPLY_NOTES_CONSTANT = "applyNotes";

	public static final String LOAN_PLAN_CODE_CONSTANT = "loanPlanCode";

	private Long contactMeId;

	private transient CodeBranchBank codeBranchBankByFinalBranchBankId;

	private transient CodeBranchBank codeBranchBankByBranchBankId;

	private transient CodeContactTime codeContactTime;

	private transient CodeProcess codeProcess;

	private transient CodeSex codeSex;

	private String name;

	private String caseNo;

	private String phoneCode;

	private String phoneNumber;

	private String phoneExt;

	private String mobileNumber;

	private String email;

	private String otherMsg;

	private boolean notified;

	private Date updatedDate;

	private Date createdDate;

	private transient Set<ApplyNote> applyNotes = new HashSet<>( 0 );

	private String loanPlanCode;

	private String introducerEmpId;

	private Long introducerBranchBankId;

	public ApplyContactMe()
	{}

	public ApplyContactMe( CodeBranchBank codeBranchBankByBranchBankId, CodeContactTime codeContactTime, CodeProcess codeProcess, CodeSex codeSex,
						   String name, String caseNo, String mobileNumber, boolean notified, Date updatedDate, Date createdDate, String loanPlanCode )
	{
		this.codeBranchBankByBranchBankId = codeBranchBankByBranchBankId;
		this.codeContactTime = codeContactTime;
		this.codeProcess = codeProcess;
		this.codeSex = codeSex;
		this.name = name;
		this.caseNo = caseNo;
		this.mobileNumber = mobileNumber;
		this.notified = notified;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
		this.loanPlanCode = loanPlanCode;
	}

	public ApplyContactMe( Long contactMeId )
	{
		this.contactMeId = contactMeId;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "applyContactMe" )
	public Set<ApplyNote> getApplyNotes()
	{
		return applyNotes;
	}

	@Column( name = "case_no", unique = true, nullable = false, length = 30 )
	public String getCaseNo()
	{
		return caseNo;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "branch_bank_id", nullable = false )
	public CodeBranchBank getCodeBranchBankByBranchBankId()
	{
		return codeBranchBankByBranchBankId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "final_branch_bank_id" )
	public CodeBranchBank getCodeBranchBankByFinalBranchBankId()
	{
		return codeBranchBankByFinalBranchBankId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "contact_time_code", nullable = false )
	public CodeContactTime getCodeContactTime()
	{
		return codeContactTime;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "process_code", nullable = false )
	public CodeProcess getCodeProcess()
	{
		return codeProcess;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "sex_code", nullable = false )
	public CodeSex getCodeSex()
	{
		return codeSex;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "contact_me_id", unique = true, nullable = false )
	public Long getContactMeId()
	{
		return contactMeId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "email", length = 50 )
	public String getEmail()
	{
		return email;
	}

	@Column( name = "mobile_number", nullable = false, length = 10 )
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	@Column( name = "name", nullable = false, length = 100 )
	public String getName()
	{
		return name;
	}

	@Column( name = "other_msg" )
	public String getOtherMsg()
	{
		return otherMsg;
	}

	@Column( name = "phone_code", length = 5 )
	public String getPhoneCode()
	{
		return phoneCode;
	}

	@Column( name = "phone_ext", length = 6 )
	public String getPhoneExt()
	{
		return phoneExt;
	}

	@Column( name = "phone_number", length = 10 )
	public String getPhoneNumber()
	{
		return phoneNumber;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	@Column( name = "loan_plan_code", nullable = false, length = 20 )
	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	@Column( name = "introducer_emp_id", length = 10 )
	public String getIntroducerEmpId()
	{
		return introducerEmpId;
	}

	@Column( name = "introducer_branch_bank_id" )
	public Long getIntroducerBranchBankId()
	{
		return introducerBranchBankId;
	}

	public void setApplyNotes( Set<ApplyNote> applyNotes )
	{
		this.applyNotes = applyNotes;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCodeBranchBankByBranchBankId( CodeBranchBank codeBranchBankByBranchBankId )
	{
		this.codeBranchBankByBranchBankId = codeBranchBankByBranchBankId;
	}

	public void setCodeBranchBankByFinalBranchBankId( CodeBranchBank codeBranchBankByFinalBranchBankId )
	{
		this.codeBranchBankByFinalBranchBankId = codeBranchBankByFinalBranchBankId;
	}

	public void setCodeContactTime( CodeContactTime codeContactTime )
	{
		this.codeContactTime = codeContactTime;
	}

	public void setCodeProcess( CodeProcess codeProcess )
	{
		this.codeProcess = codeProcess;
	}

	public void setCodeSex( CodeSex codeSex )
	{
		this.codeSex = codeSex;
	}

	public void setContactMeId( Long contactMeId )
	{
		this.contactMeId = contactMeId;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setPhoneCode( String phoneCode )
	{
		this.phoneCode = phoneCode;
	}

	public void setPhoneExt( String phoneExt )
	{
		this.phoneExt = phoneExt;
	}

	public void setPhoneNumber( String phoneNumber )
	{
		this.phoneNumber = phoneNumber;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setIntroducerEmpId( String introducerEmpId )
	{
		this.introducerEmpId = introducerEmpId;
	}

	public void setIntroducerBranchBankId( Long introducerBranchBankId )
	{
		this.introducerBranchBankId = introducerBranchBankId;
	}
}