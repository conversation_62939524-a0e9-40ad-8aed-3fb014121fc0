package com.megabank.olp.apply.persistence.pojo.temp;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.youthStartUp.ApplyYouthStartUp;
import com.megabank.olp.apply.persistence.pojo.code.CodeAttachmentType;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The TempUploadFile is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "temp_upload_file" )
public class TempUploadFile extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "temp_upload_file";

	public static final String UPLOAD_FILE_ID_CONSTANT = "uploadFileId";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String APPLY_YOUTH_START_UP_CONSTANT = "applyYouthStartUp";

	public static final String CODE_ATTACHMENT_TYPE_CONSTANT = "codeAttachmentType";

	public static final String FILE_NAME_CONSTANT = "fileName";

	public static final String FILE_SIZE_CONSTANT = "fileSize";

	public static final String FILE_CONTENT_CONSTANT = "fileContent";

	public static final String COMPRESS_FILE_CONTENT_CONSTANT = "compressFileContent";

	public static final String PROCESSED_CONSTANT = "processed";

	public static final String OTHER_BANK_ID_CONSTANT = "otherBankId";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	private Long uploadFileId;

	private transient ApplyLoan applyLoan;

	private transient ApplyYouthStartUp applyYouthStartUp;

	private transient CodeAttachmentType codeAttachmentType;

	private String fileName;

	private long fileSize;

	private transient ImmutableByteArray fileContent;

	private transient ImmutableByteArray compressFileContent;

	private boolean processed;

	private Long otherBankId;

	private Date createdDate;

	public TempUploadFile()
	{}

	public TempUploadFile( Long uploadFileId )
	{
		this.uploadFileId = uploadFileId;
	}

	public TempUploadFile( String fileName, long fileSize, ImmutableByteArray fileContent, boolean processed, Date createdDate )
	{
		this.fileName = fileName;
		this.fileSize = fileSize;
		this.fileContent = fileContent;
		this.processed = processed;
		this.createdDate = createdDate;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_id" )
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "youth_start_up_id" )
	public ApplyYouthStartUp getApplyYouthStartUp()
	{
		return applyYouthStartUp;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "attachment_type" )
	public CodeAttachmentType getCodeAttachmentType()
	{
		return codeAttachmentType;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "compress_file_content" ) ) } )
	public ImmutableByteArray getCompressFileContent()
	{
		return compressFileContent;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "file_content", nullable = false ) ) } )
	public ImmutableByteArray getFileContent()
	{
		return fileContent;
	}

	@Column( name = "file_name", nullable = false, length = 300 )
	public String getFileName()
	{
		return fileName;
	}

	@Column( name = "file_size", nullable = false )
	public long getFileSize()
	{
		return fileSize;
	}

	@Column( name = "other_bank_id" )
	public Long getOtherBankId()
	{
		return otherBankId;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "upload_file_id", unique = true, nullable = false )
	public Long getUploadFileId()
	{
		return uploadFileId;
	}

	@Column( name = "processed", nullable = false, precision = 1, scale = 0 )
	public boolean isProcessed()
	{
		return processed;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setApplyYouthStartUp( ApplyYouthStartUp applyYouthStartUp )
	{
		this.applyYouthStartUp = applyYouthStartUp;
	}

	public void setCodeAttachmentType( CodeAttachmentType codeAttachmentType )
	{
		this.codeAttachmentType = codeAttachmentType;
	}

	public void setCompressFileContent( ImmutableByteArray compressFileContent )
	{
		this.compressFileContent = compressFileContent;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setFileContent( ImmutableByteArray fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setFileSize( long fileSize )
	{
		this.fileSize = fileSize;
	}

	public void setOtherBankId( Long otherBankId )
	{
		this.otherBankId = otherBankId;
	}

	public void setProcessed( boolean processed )
	{
		this.processed = processed;
	}

	public void setUploadFileId( Long uploadFileId )
	{
		this.uploadFileId = uploadFileId;
	}
}