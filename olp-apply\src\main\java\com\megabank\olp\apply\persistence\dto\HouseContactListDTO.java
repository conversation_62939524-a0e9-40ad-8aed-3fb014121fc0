package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class HouseContactListDTO extends BaseBean
{
	private Long contactInfoId;

	private String caseNo;

	private String name;

	private String mobileNumber;

	private Date createdDate;

	private String processStatus;

	private String branchBank;

	private String notificationStatus;

	private String callBackTime;

	private String otherMsg;

	private String loanPlanCode;

	private String loanPlanName;

	public HouseContactListDTO()
	{
		// default constructor
	}

	public String getBranchBank()
	{
		return branchBank;
	}

	public String getCallBackTime()
	{
		return callBackTime;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Long getContactInfoId()
	{
		return contactInfoId;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatus()
	{
		return notificationStatus;
	}

	public String getProcessStatus()
	{
		return processStatus;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public String getLoanPlanName()
	{
		return loanPlanName;
	}

	public void setBranchBank(String branchBank )
	{
		this.branchBank = branchBank;
	}

	public void setCallBackTime( String callBackTime )
	{
		this.callBackTime = callBackTime;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setContactInfoId( Long contactInfoId )
	{
		this.contactInfoId = contactInfoId;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatus( String notificationStatus )
	{
		this.notificationStatus = notificationStatus;
	}

	public void setProcessStatus( String processStatus )
	{
		this.processStatus = processStatus;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanPlanName( String loanPlanName )
	{
		this.loanPlanName = loanPlanName;
	}
}