package com.megabank.olp.apply.persistence.dao.generated.house;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.generated.house.ContactCreatedParamBean;
import com.megabank.olp.apply.utility.enums.ProcessStatusEnum;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HouseContactInfoDAOIntegration
{
	@Autowired
	private HouseContactInfoDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void create()
	{
		ContactCreatedParamBean createBean = new ContactCreatedParamBean();
		createBean.setCaseNo( "testHouse001" );
		createBean.setCreatedDate( new Date() );
		createBean.setMobileNumber( "**********" );
		createBean.setProcessCode( ProcessStatusEnum.UNPROCESSED.getContext() );
		createBean.setBranchBankCode( "201" );

		Long userInfo = dao.create( createBean, false );

		logger.info( "HouseContactInfo Long:{}", userInfo );
	}

}
