package com.megabank.olp.apply.service.inet;

import com.megabank.olp.apply.utility.enums.InetReturnTypeEnum;
import com.megabank.olp.apply.utility.enums.InetRptTemplateEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;

import java.util.Arrays;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class InetServiceIntegration
{
	@Autowired
	private InetService service;

	@Test
	public void test001()
	{
		service.send( InetRptTemplateEnum.HOUSE_LOAN_CONTRACT.getContext(), InetReturnTypeEnum.PDF.getContext(), Arrays.asList( 46 ) );
	}
}
