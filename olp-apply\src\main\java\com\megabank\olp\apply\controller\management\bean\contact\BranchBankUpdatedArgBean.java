/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.contact;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class BranchBankUpdatedArgBean extends BaseBean
{
	@NotBlank
	private String employeeId;

	@NotBlank
	private String employeeName;

	@JsonProperty( "id" )
	@NotNull
	private Long contactMeId;

	@NotNull
	private Long branchBankId;

	public BranchBankUpdatedArgBean()
	{
		// default constructor
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public Long getContactMeId()
	{
		return contactMeId;
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setContactMeId( Long contactMeId )
	{
		this.contactMeId = contactMeId;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

}
