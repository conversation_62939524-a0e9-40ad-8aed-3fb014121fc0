package com.megabank.olp.apply.persistence.bean.generated.apply.survey;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class ApplySurveyCreatedParamBean extends BaseBean
{
	private Long validatedIdentityId;

	private String caseNo;

	private String riskLevel;

	private Long jobSubTypeId;

	private Integer annualIncome;

	private Boolean holdingCreditCard;

	private BigDecimal revovingCredit;

	private BigDecimal cashAdvance;

	private BigDecimal creditCardTotalAmt;

	private Boolean holdingDebitCard;

	private BigDecimal debitCardTotalAmt;

	private BigDecimal holdingPersonalLoan;

	private String processCode;

	private String loanPlanCode;

	public ApplySurveyCreatedParamBean()
	{}

	public Integer getAnnualIncome()
	{
		return annualIncome;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public BigDecimal getCashAdvance()
	{
		return cashAdvance;
	}

	public BigDecimal getCreditCardTotalAmt()
	{
		return creditCardTotalAmt;
	}

	public BigDecimal getDebitCardTotalAmt()
	{
		return debitCardTotalAmt;
	}

	public Boolean getHoldingCreditCard()
	{
		return holdingCreditCard;
	}

	public Boolean getHoldingDebitCard()
	{
		return holdingDebitCard;
	}

	public BigDecimal getHoldingPersonalLoan()
	{
		return holdingPersonalLoan;
	}

	public Long getJobSubTypeId()
	{
		return jobSubTypeId;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public BigDecimal getRevovingCredit()
	{
		return revovingCredit;
	}

	public String getRiskLevel()
	{
		return riskLevel;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public String getLoanPlanCode() {
		return loanPlanCode;
	}

	public void setAnnualIncome(Integer annualIncome )
	{
		this.annualIncome = annualIncome;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCashAdvance( BigDecimal cashAdvance )
	{
		this.cashAdvance = cashAdvance;
	}

	public void setCreditCardTotalAmt( BigDecimal creditCardTotalAmt )
	{
		this.creditCardTotalAmt = creditCardTotalAmt;
	}

	public void setDebitCardTotalAmt( BigDecimal debitCardTotalAmt )
	{
		this.debitCardTotalAmt = debitCardTotalAmt;
	}

	public void setHoldingCreditCard( Boolean holdingCreditCard )
	{
		this.holdingCreditCard = holdingCreditCard;
	}

	public void setHoldingDebitCard( Boolean holdingDebitCard )
	{
		this.holdingDebitCard = holdingDebitCard;
	}

	public void setHoldingPersonalLoan( BigDecimal holdingPersonalLoan )
	{
		this.holdingPersonalLoan = holdingPersonalLoan;
	}

	public void setJobSubTypeId( Long jobSubTypeId )
	{
		this.jobSubTypeId = jobSubTypeId;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

	public void setRevovingCredit( BigDecimal revovingCredit )
	{
		this.revovingCredit = revovingCredit;
	}

	public void setRiskLevel( String riskLevel )
	{
		this.riskLevel = riskLevel;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

	public void setLoanPlanCode(String loanPlanCode) {
		this.loanPlanCode = loanPlanCode;
	}
}
