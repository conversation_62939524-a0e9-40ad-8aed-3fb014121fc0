package com.megabank.olp.apply.controller.management.bean.houseloantrial;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class HouseLoanTrialDetailGetterArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long houseLoanTrialId;

	public HouseLoanTrialDetailGetterArgBean()
	{
		// default constructor
	}

	public Long getHouseLoanTrialId()
	{
		return houseLoanTrialId;
	}

	public void setHouseContactId( Long houseLoanTrialId )
	{
		this.houseLoanTrialId = houseLoanTrialId;
	}

}
