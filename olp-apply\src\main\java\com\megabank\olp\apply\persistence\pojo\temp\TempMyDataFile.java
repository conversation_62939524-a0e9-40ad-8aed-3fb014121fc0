package com.megabank.olp.apply.persistence.pojo.temp;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import com.megabank.olp.apply.persistence.pojo.apply.mydata.ApplyMyData;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The TempMyDataFile is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "temp_my_data_file" )
public class TempMyDataFile extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "temp_my_data_file";

	public static final String MY_DATA_FILE_ID_CONSTANT = "myDataFileId";

	public static final String APPLY_MY_DATA_CONSTANT = "applyMyData";

	public static final String FILE_TYPE_CONSTANT = "fileType";

	public static final String FILE_CONTENT_CONSTANT = "fileContent";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	private Long myDataFileId;

	private transient ApplyMyData applyMyData;

	private String fileType;

	private transient String fileContent;

	private Date createdDate;

	public TempMyDataFile()
	{}

	public TempMyDataFile( Long myDataFileId )
	{
		this.myDataFileId = myDataFileId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "my_data_id", nullable = false )
	public ApplyMyData getApplyMyData()
	{
		return applyMyData;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "file_content", nullable = false )
	public String getFileContent()
	{
		return fileContent;
	}

	@Column( name = "file_type", nullable = false, length = 20 )
	public String getFileType()
	{
		return fileType;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "my_data_file_id", unique = true, nullable = false )
	public Long getMyDataFileId()
	{
		return myDataFileId;
	}

	public void setApplyMyData( ApplyMyData applyMyData )
	{
		this.applyMyData = applyMyData;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setFileContent( String fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileType( String fileType )
	{
		this.fileType = fileType;
	}

	public void setMyDataFileId( Long myDataFileId )
	{
		this.myDataFileId = myDataFileId;
	}
}