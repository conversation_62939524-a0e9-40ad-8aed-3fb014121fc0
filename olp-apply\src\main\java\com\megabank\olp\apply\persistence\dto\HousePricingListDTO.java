package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class HousePricingListDTO extends BaseBean
{
	private Long pricingInfoId;

	private String caseNo;

	private String email;

	private String mobileNumber;

	private Date createdDate;

	private String processStatus;

	private String branchBank;

	private String notificationStatus;

	public HousePricingListDTO()
	{
		// default constructor
	}

	public String getBranchBank()
	{
		return branchBank;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getNotificationStatus()
	{
		return notificationStatus;
	}

	public Long getPricingInfoId()
	{
		return pricingInfoId;
	}

	public String getProcessStatus()
	{
		return processStatus;
	}

	public void setBranchBank( String branchBank )
	{
		this.branchBank = branchBank;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setNotificationStatus( String notificationStatus )
	{
		this.notificationStatus = notificationStatus;
	}

	public void setPricingInfoId( Long pricingInfoId )
	{
		this.pricingInfoId = pricingInfoId;
	}

	public void setProcessStatus( String processStatus )
	{
		this.processStatus = processStatus;
	}

}