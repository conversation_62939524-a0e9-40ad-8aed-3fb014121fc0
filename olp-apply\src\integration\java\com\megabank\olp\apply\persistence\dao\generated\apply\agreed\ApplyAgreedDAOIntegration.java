package com.megabank.olp.apply.persistence.dao.generated.apply.agreed;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreed;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyAgreedDAOIntegration
{
	@Autowired
	private ApplyAgreedDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getApplyServiceAgreeds()
	{
		String userType = "borrower"; // guarantor | borrower
		String identityType = "none"; // moica | none
		String loanType = "personalloan"; // personalloan | houseloan

		List<ApplyAgreed> result = dao.getApplyServiceAgreeds( userType, identityType, loanType );

		logger.info( "result:{}", result );
		logger.info( "size:{}", result.size() );
	}

	@Test
	public void getContractServiceAgreeds()
	{
		String userType = "borrower"; // guarantor | borrower

		List<ApplyAgreed> result = dao.getContractServiceAgreeds( userType, "housesigningcontract" );
		logger.info( "result:{}", result );
		logger.info( "size:{}", result.size() );
	}

	@Test
	public void getUploadServiceAgreeds()
	{
		// List<ApplyAgreed> result = dao.getUploadServiceAgreeds();
		// logger.info( "result:{}", result );
		// logger.info( "size:{}", result.size() );
	}

}
