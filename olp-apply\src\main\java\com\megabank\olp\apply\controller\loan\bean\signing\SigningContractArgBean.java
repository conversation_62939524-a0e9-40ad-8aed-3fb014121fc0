/**
 *
 */
package com.megabank.olp.apply.controller.loan.bean.signing;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractArgBean extends BaseBean
{

	@NotBlank
	private String contractNo;

	public SigningContractArgBean()
	{
		// default constructor
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

}
