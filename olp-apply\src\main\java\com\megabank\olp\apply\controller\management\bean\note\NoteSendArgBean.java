/**
 *
 */
package com.megabank.olp.apply.controller.management.bean.note;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.enums.NoteTypeEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

public class NoteSendArgBean extends BaseBean
{
	@NotBlank
	private String employeeId;

	@NotBlank
	private String employeeName;

	@NotNull
	private Long id;

	@JsonProperty( "type" )
	@NotNull
	private NoteTypeEnum noteTypeEnum;

	@NotBlank
	private String text;

	public NoteSendArgBean()
	{
		// default constructor
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public Long getId()
	{
		return id;
	}

	public NoteTypeEnum getNoteTypeEnum()
	{
		return noteTypeEnum;
	}

	public String getText()
	{
		return text;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setId( Long id )
	{
		this.id = id;
	}

	public void setNoteTypeEnum( NoteTypeEnum noteTypeEnum )
	{
		this.noteTypeEnum = noteTypeEnum;
	}

	public void setText( String text )
	{
		this.text = text;
	}
}
