package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeMyDataStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeMyDataStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeMyDataStatusDAO extends BasePojoDAO<CodeMyDataStatus, String>
{
	public CodeMyDataStatus read( String myDataStatusCode )
	{
		Validate.notBlank( myDataStatusCode );

		return getPojoByPK( myDataStatusCode, CodeMyDataStatus.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeMyDataStatus> getPojoClass()
	{
		return CodeMyDataStatus.class;
	}
}
