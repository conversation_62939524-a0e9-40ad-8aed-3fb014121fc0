package com.megabank.olp.api.controller.mydata;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.controller.open.bean.DataTransferArgBean;
import com.megabank.olp.api.service.mydata.MyDataService;
import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MydataResultBean;

@RestController
@RequestMapping( "mydata" )
public class MyDataType1Controller extends BaseController
{
	@Autowired
	private MyDataService service;

	// MEGAPLOAN-194 偉康模式一資料傳遞API
	@PostMapping( "dataTransfer" )
	public Map<String, Object> dataTransfer( @RequestBody @Validated DataTransferArgBean argBean )
	{
		/*
		 * curl -X POST -H "Content-type: application/json" -d
		 * "{\"transactionId\":\"abcdefgh\",\"txId\":\"wxyz\",\"file\":\"abcdefgh\",\"status\":\"200\"}"
		 * http://127.0.0.1:9005/mydata/dataTransfer
		 * https://192.168.70.21/service/api/mydata/dataTransfer
		 */
		MydataResultBean bean = service.dataTransfer( argBean );
		
		Map<String, Object> map = new HashMap<>();
		
		if ( bean != null ) {
			map.put( "status", bean.getStatus() );
			map.put( "msg", bean.getMsg() );
			if ( StringUtils.isNotBlank(bean.getErrorCode() ) ) 
			{
				map.put( "errorCode", bean.getErrorCode() );
			}
		} else {
			map.put( "status", 200 );
			map.put( "msg", "Success" );
		}
		
		return map;
	}

}
