package com.megabank.olp.apply.persistence.pojo.apply.signing;

import com.megabank.olp.base.bean.BaseBean;
import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import static jakarta.persistence.GenerationType.IDENTITY;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The ApplySigningRate is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_signing_rate" )
public class ApplySigningRate extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_signing_rate";

	public static final String RATE_ID_CONSTANT = "rateId";

	public static final String APPLY_SIGNING_CONTRACT_CONSTANT = "applySigningContract";

	public static final String RATE_TYPE_CONSTANT = "rateType";

	public static final String RATE_BGN_CONSTANT = "rateBgn";

	public static final String RATE_End_CONSTANT = "rateEnd";

	public static final String RATE_CONSTANT = "rate";

	public static final String CREATE_TIME_CONSTANT = "createTime";

	public static final String UPDATE_TIME_CONSTANT = "updateTime";

	private Long rateId;

	private transient ApplySigningContract applySigningContract;

	private String rateType;

	private int rateBgn;

	private int rateEnd;

	private double rate;

	private Date createTime;

	private Date updateTime;

	public ApplySigningRate()
	{}

	public ApplySigningRate( Long rateId )
	{
		this.rateId = rateId;
	}

	public ApplySigningRate( ApplySigningContract applySigningContract)
	{
		this.applySigningContract = applySigningContract;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "rate_id", unique = true, nullable = false )
	public Long getRateId()
	{
		return this.rateId;
	}

	public void setRateId( Long rateId )
	{
		this.rateId = rateId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "signing_contract_id", nullable = false )
	public ApplySigningContract getApplySigningContract()
	{
		return applySigningContract;
	}

	public void setApplySigningContract( ApplySigningContract applySigningContract )
	{
		this.applySigningContract = applySigningContract;
	}

	@Column( name = "rate_Type", nullable = false, length = 1 )
	public String getRateType()
	{
		return this.rateType;
	}

	public void setRateType( String rateType )
	{
		this.rateType = rateType;
	}

	@Column( name = "rate_Bgn", nullable = false, precision = 5, scale = 0 )
	public int getRateBgn()
	{
		return this.rateBgn;
	}

	public void setRateBgn( int rateBgn )
	{
		this.rateBgn = rateBgn;
	}

	@Column( name = "rate_End", nullable = false, precision = 5, scale = 0 )
	public int getRateEnd()
	{
		return this.rateEnd;
	}

	public void setRateEnd( int rateEnd )
	{
		this.rateEnd = rateEnd;
	}
	
	@Column( name = "rate", nullable = false, precision = 5, scale = 3 )
	public double getRate()
	{
		return this.rate;
	}

	public void setRate( double rate )
	{
		this.rate = rate;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "createTime", length = 23 )
	public Date getCreateTime()
	{
		return this.createTime;
	}

	public void setCreateTime( Date createTime )
	{
		this.createTime = createTime;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updateTime", length = 23 )
	public Date getUpdateTime()
	{
		return this.updateTime;
	}

	public void setUpdateTime( Date updateTime )
	{
		this.updateTime = updateTime;
	}
}