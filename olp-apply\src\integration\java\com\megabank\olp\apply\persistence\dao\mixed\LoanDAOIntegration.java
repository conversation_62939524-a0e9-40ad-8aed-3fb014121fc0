package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.mixed.LoanListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.ApplyListDTO;
import com.megabank.olp.apply.persistence.dto.LoanListDTO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.bean.threadlocal.PagingThreadLocalBean;
import com.megabank.olp.base.threadlocal.PagingThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class LoanDAOIntegration
{
	@Autowired
	private PagingThreadLocal pagingThreadLocal;
	
	@Autowired
	private LoanDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getApplyCountInDays()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		int dayDiff = 0;

		long result = dao.getApplyCountInDays( idNo, birthDate, null, dayDiff );

		logger.info( "result:{}", result );
	}

	@Test
	public void getApplyList()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		String mobileNumber = "**********";

		List<ApplyListDTO> dtos = dao.getApplyList( idNo, birthDate, null );

		logger.info( "dtos:{}", dtos );
	}

	@Test
	public void getCompletedIdentityIdsIn7Days()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		String loanType = "personalloan";

		List<Long> result = dao.getCompletedIdentityIdsIn7Days( idNo, birthDate, loanType );

		logger.info( "result:{}", result );
	}

	@Test
	public void getLatestLoanIdInDays()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		String loanType = "houseloan";
		String mobileNumber = "**********";
		int dayDiff = 180;

		Long result = dao.getLatestLoanIdInDays( idNo, birthDate, mobileNumber, loanType, dayDiff );

		logger.info( "result:{}", result );
	}

	@Test
	public void getList()
	{
		// LoanListGetterParamBean paramBean = getPersonalLoanListGetterParamBean();
		LoanListGetterParamBean paramBean = new LoanListGetterParamBean();

		List<String> applyStatusCodes = Arrays.asList( "complete-agreed", "complete-confirmed" );
		paramBean.setApplyStatusCodes( applyStatusCodes );
		paramBean.setSecondDiff( 0 );

		List<LoanListDTO> result = dao.getList( paramBean );

		logger.info( "result:{}", result );
		logger.info( "size:{}", result.size() );
	}

	@Test
	public void getPaging()
	{
		PagingThreadLocalBean localBean = new PagingThreadLocalBean();
		localBean.setStart( 0 );
		localBean.setLength( 10 );
		localBean.setSortColumn( "createdDate" );
		localBean.setSortDirection( "asc" );
		pagingThreadLocal.set( localBean );

		LoanListGetterParamBean paramBean = new LoanListGetterParamBean();
		// List<String> applyStatusCodes = Arrays.asList( "complete_agreed" );
		List<String> applyStatusCodes = Arrays.asList( "complete-confirmed", "complete-agreed", "complete-basic" );
		paramBean.setApplyStatusCodes( applyStatusCodes );
		paramBean.setSecondDiff( 0 );
		paramBean.setIntroduceEmpId( null );
		paramBean.setIntroduceBrNo( null );

		PagingBean<LoanListDTO> result = dao.getPaging( paramBean );

		logger.info( "data:{}", result.getData() );
		logger.info( "recordsFiltered:{}", result.getRecordsFiltered() );
		logger.info( "recordsTotal:{}", result.getRecordsTotal() );

	}

	@Test
	public void getPojosByBorrowerInfo()
	{
		String caseNo = "PA000001";
		String idNo = "A123456789";
		String name = "王大明";
		String mobileNumber = "**********";
		String loanType = "personalloan";

		List<ApplyLoan> pojos = dao.getPojosByBorrowerInfo( caseNo, name, idNo, mobileNumber, loanType );

		logger.info( "pojos:{}", pojos );
	}

	private LoanListGetterParamBean getPersonalLoanListGetterParamBean()
	{
		String transmissionStatus = "no";
		List<String> applyStatus = Arrays.asList( "complete" );
		String idNo = "A123456789";
		String name = "王大明";
		String mobileNumber = "**********";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		Date applyDateStart = CommonDateUtils.getDate( 2020, 1, 1 );
		Date applyDateEnd = CommonDateUtils.getDate( 2020, 12, 31 );
		String loanType = "personalloan";

		LoanListGetterParamBean paramBean = new LoanListGetterParamBean();
		// paramBean.setTransmissionStatus( transmissionStatus );
		// paramBean.setApplyStatus( applyStatus );
		paramBean.setDateStart( applyDateStart );
		paramBean.setDateEnd( applyDateEnd );
		paramBean.setIdNo( idNo );
		paramBean.setName( name );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setLoanType( loanType );

		return paramBean;
	}

}
