package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class PaymentInfoBean extends BaseBean
{
	private String bankCode;

	private String bankName;

	private String repaymentProductType;

	private String repaymentProduct;

	private String bankAcctNo;

	private BigDecimal repaymentAmt;

	private String accountName;

	public PaymentInfoBean()
	{}

	public String getAccountName()
	{
		return accountName;
	}

	public String getBankAcctNo()
	{
		return bankAcctNo;
	}

	public String getBankCode()
	{
		return bankCode;
	}

	public String getBankName()
	{
		return bankName;
	}

	public BigDecimal getRepaymentAmt()
	{
		return repaymentAmt;
	}

	public String getRepaymentProduct()
	{
		return repaymentProduct;
	}

	public String getRepaymentProductType()
	{
		return repaymentProductType;
	}

	public void setAccountName( String accountName )
	{
		this.accountName = accountName;
	}

	public void setBankAcctNo( String bankAcctNo )
	{
		this.bankAcctNo = bankAcctNo;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setBankName( String bankName )
	{
		this.bankName = bankName;
	}

	public void setRepaymentAmt( BigDecimal repaymentAmt )
	{
		this.repaymentAmt = repaymentAmt;
	}

	public void setRepaymentProduct( String repaymentProduct )
	{
		this.repaymentProduct = repaymentProduct;
	}

	public void setRepaymentProductType( String repaymentProductType )
	{
		this.repaymentProductType = repaymentProductType;
	}

}
