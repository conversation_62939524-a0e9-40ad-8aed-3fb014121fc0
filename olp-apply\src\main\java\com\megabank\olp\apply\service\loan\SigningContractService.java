/**
 *
 */
package com.megabank.olp.apply.service.loan;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractFillInfoArgBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractUpdateInfoArgBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.ApplyCollateralContractAttachmentCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.ApplySigningAppropirationCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningEddaUpdateParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.signing.SigningUserUpdateParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.agreed.ApplyAgreedDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.agreed.ApplyAgreedItemDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplyCollateralContractAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplyCollateralContractDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplyHouseSigningContractDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplyPayeeInfoDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningAppropriationDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningBankAccountDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningContractDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningEddaDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningRepaymentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningUserDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBusinessDayDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProdKindDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeUserSubTypeDAO;
import com.megabank.olp.apply.persistence.dao.mixed.SigningContractDAO;
import com.megabank.olp.apply.persistence.dto.SigningContractListDTO;
import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreed;
import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreedItem;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyCollateralContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyCollateralContractAttachment;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyHouseSigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyPayeeInfo;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningAppropriation;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningBankAccount;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningEdda;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningRate;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningRepayment;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningUser;
import com.megabank.olp.apply.persistence.pojo.code.CodeBusinessDay;
import com.megabank.olp.apply.persistence.pojo.code.CodeProdKind;
import com.megabank.olp.apply.service.inet.InetService;
import com.megabank.olp.apply.service.inet.bean.PackagedInetResBean;
import com.megabank.olp.apply.service.loan.bean.apply.AgreedItemBean;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.apply.SigningContractAccountResBean;
import com.megabank.olp.apply.service.loan.bean.apply.SigningContractFillInfoParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.SigningContractResBean;
import com.megabank.olp.apply.service.loan.bean.signing.AgreementResBean;
import com.megabank.olp.apply.service.loan.bean.signing.AppropriationBean;
import com.megabank.olp.apply.service.loan.bean.signing.BankAccountResBean;
import com.megabank.olp.apply.service.loan.bean.signing.ContractAgreementBean;
import com.megabank.olp.apply.service.loan.bean.signing.ProjectDescBean;
import com.megabank.olp.apply.service.loan.bean.signing.SigningContractBasicResBean;
import com.megabank.olp.apply.service.loan.bean.signing.SigningUserInfoBean;
import com.megabank.olp.apply.utility.ApplyLoanUtils;
import com.megabank.olp.apply.utility.BaseApplyService;
import com.megabank.olp.apply.utility.enums.ApplyErrorEnum;
import com.megabank.olp.apply.utility.enums.BillhunterMailTypeEnum;
import com.megabank.olp.apply.utility.enums.IncreaseHouseloanVariousTermsTitleEnum;
import com.megabank.olp.apply.utility.enums.InetResponseStatusEnum;
import com.megabank.olp.apply.utility.enums.InetReturnTypeEnum;
import com.megabank.olp.apply.utility.enums.InetRptTemplateEnum;
import com.megabank.olp.apply.utility.enums.SigningContractSendStatusEnum;
import com.megabank.olp.apply.utility.enums.SigningContractTypeEnum;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.enums.AgreedTypeEnum;
import com.megabank.olp.base.enums.AttachTypeEnum;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.ProductCodeEnum;
import com.megabank.olp.base.enums.UserSubTypeEnum;
import com.megabank.olp.base.enums.UserTypeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.utility.CommonStringUtils;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.base.utility.date.CommonDateUtils;
import com.megabank.olp.base.utility.text.CommonBase64Utils;
import com.megabank.olp.base.utility.text.CommonHtml2PdfUtils;
import com.megabank.olp.client.sender.billhunter.bean.AttachmentFileBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderArgBean;
import com.megabank.olp.client.sender.billhunter.bean.ToMailInfoBean;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderArgBean;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderResultBean;
import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoResultBean;
import com.megabank.olp.client.service.billhunter.BillhunterSenderService;
import com.megabank.olp.client.service.common.UserClientService;
import com.megabank.olp.client.service.eDDA.EddaSenderService;
import com.megabank.olp.client.service.sms.SmsSenderService;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Transactional( propagation = Propagation.REQUIRES_NEW )
public class SigningContractService extends BaseApplyService
{
	private static final String AP_NAME = "olp-apply";

	private static final String UNCHECKED_BOX_STRING = "☐";

	private static final String CHECKED_BOX_STRING = "☑";

	private static final String NO_BREAKING_HTML_SPACE_10 = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";

	private static final String NO_BREAKING_HTML_SPACE_4 = "&nbsp;&nbsp;&nbsp;&nbsp;";

	@Autowired
	private ApplySigningBankAccountDAO bankAccountDAO;

	@Autowired
	private GenerateService generateService;

	@Autowired
	private UserClientService userClientService;

	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	private ApplyHouseSigningContractDAO applyHouseSigningContractDAO;

	@Autowired
	private ApplyAgreedDAO applyAgreedDAO;

	@Autowired
	private ApplyAgreedItemDAO applyAgreedItemDAO;

	@Autowired
	private ApplySigningUserDAO signingUserDAO;

	@Autowired
	private SigningContractDAO signingContractDAO;

	@Autowired
	private ApplySigningAppropriationDAO applySigningAppropriationDAO;

	@Autowired
	ApplyPayeeInfoDAO applyPayeeInfoDAO;

	@Autowired
	private DeliverService deliverService;

	@Autowired
	private SmsSenderService smsSenderService;

	@Autowired
	private BillhunterSenderService billhunterSenderService;

	@Autowired
	private CodeUserSubTypeDAO codeUserSubTypeDAO;

	@Autowired
	private CodeBusinessDayDAO codeBusinessDayDAO;

	@Autowired
	private EddaSenderService eDDASenderservice;

	@Autowired
	private ApplySigningEddaDAO signingEddaDAO;

	@Autowired
	private SystemService systemService;

	@Autowired
	private ApplySigningRepaymentDAO applySigningRepaymentDAO;

	@Autowired
	private InetService inetService;

	@Autowired
	private CodeProdKindDAO codeProdKindDAO;

	@Autowired
	private ApplyCollateralContractDAO applyCollateralContractDAO;

	@Autowired
	private ApplyCollateralContractAttachmentDAO applyCollateralContractAttachmentDAO;

	/**
	 * 存客戶對保填寫資料
	 * J-111-0227 eDDA驗證
	 *
	 * @param paramBean
	 * @return
	 */
	public String checkEDDA( SigningContractFillInfoParamBean paramBean ) throws Exception
	{
		String result = "";
		String contractNo = paramBean.getContractNo();
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		// 借款人資料
		SigningUserInfoBean borrowerInfo = getUserInfo( contract.getApplySigningUsers(), UserTypeEnum.BORROWER.getContext() );

		sendEDDA( paramBean, contract, borrowerInfo );

		return result;
	}

	/**
	 * 檢查客戶是否有半年內的對保案件
	 *
	 * @param idNo
	 * @param birthDate
	 * @return
	 */
	public boolean checkSigningContractExisted( String idNo, Date birthDate, String productCode )
	{
		List<Long> identityIds = userClientService.getIdentitiyIdsResult( idNo, birthDate );

		if( identityIds.isEmpty() )
			return false;

		List<SigningContractListDTO> signingContracts = signingContractDAO.getContractList( identityIds,
																							Stream.of( productCode ).collect( Collectors.toList() ) );

		return !signingContracts.isEmpty();
	}

	public void createApplyCollateralContractAttachments( SigningContractFillInfoArgBean argBean ) throws IOException
	{

		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( argBean.getContractNo() );
		Long signingContractId = contract.getSigningContractId();
		boolean isApplyCollateralContractExist = Optional.ofNullable( contract.getApplyCollateralContract() ).isPresent();
		boolean isHouseloan = LoanTypeEnum.HOUSE_LOAN.getContext().equals( contract.getCodeLoanType().getLoanType() );

		// 排除非房貸增貸
		if( !isHouseloan || !isApplyCollateralContractExist )
			return;

		IdentityInfoResultBean currentIdentityInfo = userClientService.getCurrentIdentityInfoResult();
		IdentityInfoResultBean contractIdentityInfo = getSigningUserIdentityInfo( contract, currentIdentityInfo.getIdNo(),
																				  currentIdentityInfo.getBirthDate() );
		String signingUserType = contractIdentityInfo.getUserType();

		// 排除共借人與保證人
		if( !UserTypeEnum.BORROWER.getContext().equals( signingUserType ) || !( contract.getApplySigningUsers().size() > 1 ) )
		{
			saveApplyCollateralContractAttachment( InetRptTemplateEnum.HOUSE_LOAN_VARIOUS_AGREEMENT, signingContractId, currentIdentityInfo );
			saveApplyCollateralContractAttachment( InetRptTemplateEnum.HOUSE_LOAN_CENTRAL_BANK_AFFIDAVIT, signingContractId, currentIdentityInfo );
		}
	}

	/**
	 * 寫入他行帳號
	 *
	 * @param signingContractId
	 * @param bankCode
	 * @param bankBranchCode
	 * @param bankAccount
	 * @return
	 */
	public SigningContractAccountResBean createBankAccount( Long signingContractId, String bankCode, String bankBranchCode, String bankAccount )
	{
		SigningContractAccountResBean signingContractAccountResBean = new SigningContractAccountResBean();

		signingContractAccountResBean.setContractId( signingContractId );

		// 要先作廢他行帳戶(不檢查是否有寫入他行帳戶，直接作廢)
		signingContractAccountResBean.setDiscardBankAccountResult( bankAccountDAO.discardOtherBankAccount( signingContractId ) );

		// 新增他行帳戶
		signingContractAccountResBean
					.setInsertBankAccountResult( bankAccountDAO.createOtherBankAccount( signingContractId, bankCode, bankBranchCode, bankAccount ) );

		return signingContractAccountResBean;
	}

	/**
	 * 送件eloan
	 *
	 * @param contract
	 * @param pdfContent
	 */
	public void deliverSigningContract( ApplySigningContract contract, byte[] pdfContent )
	{
		boolean submitSuccess = deliverService.submitSigningContract( contract, pdfContent );

		if( submitSuccess )
			submitSuccess = deliverService.submitSigningContractAttachment( contract );

		String sendStatusCode = submitSuccess ? SigningContractSendStatusEnum.COMPLETED.getContext()
											  : SigningContractSendStatusEnum.EXCEPTION.getContext();

		applySigningContractDAO.updateSendStatus( contract.getSigningContractId(), sendStatusCode );

		handleCollateralContractSubmission( contract );
	}

	/**
	 * 取得同意事項
	 *
	 * @param contractNo
	 * @return
	 */
	public AgreementResBean getAgreement( String contractNo, String serviceType )
	{
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		List<ApplyAgreed> agreedList = applyAgreedDAO.getContractServiceAgreeds( null, serviceType );

		return mapAgreementResBean( agreedList, contract );
	}

	/**
	 * 取得對保撥款日
	 *
	 * @return
	 */
	public List<String> getAppropirationDateListByContractNo( String contractNo )
	{
		List<String> raw_list = common_codeService___getAppropirationDateList();
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		if( contract == null || contract.getGivenApprBegDate() == null || contract.getGivenApprEndDate() == null )
			return raw_list;
		// ==========================
		List<String> codeBusinessDays = new ArrayList<>();
		for( String dateStr : raw_list )
		{
			Date dt_dateStr = CommonDateStringUtils.transString2Date( dateStr, "yyyy/MM/dd" );
			if( CommonDateUtils.cmpDate( dt_dateStr, ">=", contract.getGivenApprBegDate() )
				&& CommonDateUtils.cmpDate( dt_dateStr, "<=", contract.getGivenApprEndDate() ) )
				codeBusinessDays.add( dateStr );
		}
		return codeBusinessDays;
	}

	/**
	 * 發電文取得該帳戶的撥款帳戶清單
	 *
	 * @return
	 */
	public List<String> getBankAccount( String contractNo )
	{
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		checkIsSameUser( contract );

		List<ApplySigningBankAccount> bankAccounts = bankAccountDAO.getContractBankAccounts( contract.getSigningContractId() );

		List<String> result = new ArrayList<>();
		for( ApplySigningBankAccount applySigningBankAccount : bankAccounts )
			result.add( applySigningBankAccount.getBankAccount() );

		return result;
	}

	/**
	 * 發電文取得該他行驗證的驗證帳戶
	 *
	 * @return
	 */
	public List<BankAccountResBean> getOtherBankAccount( String contractNo )
	{
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		checkIsSameUser( contract );

		List<ApplySigningBankAccount> bankAccounts = bankAccountDAO.getContractBankAccounts( contract.getSigningContractId() );

		List<BankAccountResBean> resBeans = new ArrayList<>();
		for( ApplySigningBankAccount applySigningBankAccount : bankAccounts )
		{
			BankAccountResBean resBean = new BankAccountResBean();
			resBean.setBankCode( applySigningBankAccount.getBankCode() );
			resBean.setBankAccount( applySigningBankAccount.getBankAccount() );

			resBeans.add( resBean );
		}
		return resBeans;
	}

	/**
	 * 取得產品種類
	 *
	 * @return
	 */
	public List<String> getProdKindByCaseType( String caseType )
	{
		List<CodeProdKind> codeProdKinds = codeProdKindDAO.getPojosByCaseType( caseType );

		List<String> result = new ArrayList<>();

		for( CodeProdKind codeProdKind : codeProdKinds )
			result.add( codeProdKind.getProdKindId() );

		return result;
	}

	/**
	 * 取得代償同意事項
	 *
	 * @param contractNo
	 * @return
	 */
	public AgreementResBean getRepaymentAgreement( String contractNo )
	{
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		List<ApplyAgreed> agreedList = applyAgreedDAO.getRepaymentContractServiceAgreeds();

		return mapAgreementResBean( agreedList, contract );
	}

	/**
	 * 取得代償資料
	 *
	 * @param contractNo
	 * @return
	 */
	public List<ApplySigningRepayment> getRepaymentInfo( String contractNo )
	{
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		List<ApplySigningRepayment> repaymentList = applySigningRepaymentDAO.getPojoByContractId( contract.getSigningContractId() );

		return repaymentList;
	}

	/**
	 * 取得契約基本資料
	 *
	 * @param contractNo
	 * @return
	 */
	public SigningContractBasicResBean getSigningContractBasicInfo( String contractNo )
	{
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		String signingUserType = getSigningUserIdentityInfo( contract ).getUserType();

		return mapBasicResBean( contract, signingUserType );
	}

	/**
	 * 取得指定簽約案件的本行帳號
	 *
	 * @param contractId
	 * @return
	 */
	public boolean getSigningContractInnerBankAccount( Long contractId )
	{
		boolean result = false;

		List<ApplySigningBankAccount> bankAccounts = bankAccountDAO.getContractInnerBankAccount( contractId );

		if( bankAccounts != null && bankAccounts.size() > 0 )
			result = true;

		return result;
	}

	/**
	 * 取得最新一筆有效且符合eDDA登入條件之案件
	 *
	 * @param idNo
	 * @param birthDate
	 * @return
	 */
	public SigningContractResBean getSigningContractLatestOne( String idNo, Date birthDate, String productCode )
	{

		List<Long> identityIds = userClientService.getIdentitiyIdsResult( idNo, birthDate );

		if( identityIds.isEmpty() )
			throw new MyRuntimeException( ApplyErrorEnum.NO_SIGNING_CONTRACT );

		List<SigningContractListDTO> signingContractListDtos = signingContractDAO
					.getContractListByIdentityIdsAndType( identityIds, Stream.of( productCode ).collect( Collectors.toList() ) );

		SigningContractResBean resBean = new SigningContractResBean();

		if( !signingContractListDtos.isEmpty() )
			for( SigningContractListDTO signingContractListDto : signingContractListDtos )
		{
			boolean haveAccount = getSigningContractInnerBankAccount( signingContractListDto.getSigningContractId() );

			if( haveAccount )// 有本行帳號就代表此案不符合eDDA登入條件
				break;

			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( signingContractListDto.getValidatedIdentityId() );

			Date deadline = getDeadline( signingContractListDto.getExpiredDate(), signingContractListDto.getAppropriationDate(),
										 signingContractListDto.getUserCount() );

			String signingContractType = checkContractType( signingContractListDto.getSigningContractType(),
															signingContractListDto.getSigningContractTypeName(), signingContractListDto.getDiscard(),
															deadline );

			if( SigningContractTypeEnum.EXPIRED.getName().equals( signingContractType ) )// 若最新一筆未簽約案件已失效，則不回傳該筆案件資訊
				break;

			if( !UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
				// 若登入者不為借款人，則不回傳該筆案件資訊
				break;

			resBean.setContractId( signingContractListDto.getSigningContractId() );
			resBean.setContractNo( signingContractListDto.getContractNo() );
			resBean.setLoanType( signingContractListDto.getLoanType() );
			resBean.setContractType( identityInfo.getUserType() );
			resBean.setName( signingContractListDto.getName() );
			resBean.setDeadline( deadline );
			resBean.setSigningContractType( signingContractType );

			break;
		}

		return resBean;
	}

	/**
	 * 取得貸款案件
	 *
	 * @return
	 */
	public List<SigningContractResBean> getSigningContracts( String productCode )
	{
		IdentityInfoResultBean currentIdentityInfo = userClientService.getCurrentIdentityInfoResult();

		List<Long> identityIds = userClientService.getIdentitiyIdsResult( currentIdentityInfo.getIdNo(), currentIdentityInfo.getBirthDate() );

		if( identityIds.isEmpty() )
			throw new MyRuntimeException( ApplyErrorEnum.NO_SIGNING_CONTRACT );

		List<SigningContractListDTO> signingContractListDtos = signingContractDAO
					.getContractList( identityIds, Stream.of( productCode ).collect( Collectors.toList() ) );

		List<SigningContractResBean> resBeans = new ArrayList<>();

		for( SigningContractListDTO signingContractListDto : signingContractListDtos )
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( signingContractListDto.getValidatedIdentityId() );

			Date deadline = getDeadline( signingContractListDto.getExpiredDate(), signingContractListDto.getAppropriationDate(),
										 signingContractListDto.getUserCount() );

			SigningContractResBean resBean = new SigningContractResBean();
			resBean.setContractNo( signingContractListDto.getContractNo() );
			resBean.setLoanType( signingContractListDto.getLoanType() );
			resBean.setContractType( identityInfo.getUserType() );
			resBean.setName( signingContractListDto.getName() );
			resBean.setDeadline( deadline );
			resBean.setSigningContractType( checkContractType( signingContractListDto.getSigningContractType(),
															   signingContractListDto.getSigningContractTypeName(),
															   signingContractListDto.getDiscard(), deadline ) );
			resBean.setIsAppropiration( signingContractListDto.getIsAppropiration() );
			resBean.setInetResponseStatus( signingContractListDto.getInetResponseStatus() );
			Optional.ofNullable( signingContractListDto.getVerifiedEmail() ).ifPresentOrElse( verifiedEmail -> {}, () -> {
				resBean.setRequiresEmailVerification( true );
				resBean.setSignatoryEmail( signingContractListDto.getEmail() );
			} );

			resBeans.add( resBean );
		}

		return resBeans;
	}

	/**
	 * 取得感謝頁訊息
	 *
	 * @param contractNo
	 * @return
	 */
	public MessageResBean getThankyouMessage( String contractNo )
	{
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		checkIsSameUser( contract );

		MessageResBean resBean = new MessageResBean();
		resBean.setTitle( "您已完成線上簽約對保" );
		resBean.setContent( getThankyouContent( contract ) );

		return resBean;
	}

	/**
	 * 存客戶對保填寫資料
	 *
	 * @param paramBean
	 * @return
	 * @throws Exception
	 */
	public String saveContract( SigningContractFillInfoParamBean paramBean ) throws Exception
	{
		String result = "";

		String contractNo = paramBean.getContractNo();
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		IdentityInfoResultBean currentIdentityInfo = userClientService.getCurrentIdentityInfoResult();

		IdentityInfoResultBean contractIdentityInfo = getSigningUserIdentityInfo( contract, currentIdentityInfo.getIdNo(),
																				  currentIdentityInfo.getBirthDate() );

		checkSigningContract( contract );

		String signingUserType = contractIdentityInfo.getUserType();

		userClientService.updateUserSubType( contractIdentityInfo.getUserSubType() );

		Long contractIdentityId = contractIdentityInfo.getValidatedIdentityId();

		if( UserTypeEnum.BORROWER.getContext().equals( signingUserType ) )
		{
			contract.setApplySigningAppropriation( createAppropiration( currentIdentityInfo.getValidatedIdentityId(), contractNo, paramBean ) );
			if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
				setHouseSigningContractExpireInfo( contract.getApplyHouseSigningContract(), paramBean.getAppropriationDate(),
												   contract.getLoanPeriod(), contract.getCodeProdKind() );
		}

		updateSigningUser( contract.getSigningContractId(), contractIdentityId, currentIdentityInfo.getValidatedIdentityId(), signingUserType,
						   paramBean );

		// 借款人資料
		SigningUserInfoBean borrowerInfo = getUserInfo( contract.getApplySigningUsers(), UserTypeEnum.BORROWER.getContext() );

		// J-111-0227 判斷為借款人且是否需要發動eDDA
		// 檢查eDDA帳號是否正確
		if( UserTypeEnum.BORROWER.getContext().equals( signingUserType ) )
		{
			applySigningContractDAO.updateACH( contractNo, paramBean.getIsNeedACH() );
			if( !paramBean.getBankAcctCode().equals( "017" ) )
			{
				checkEDDAAccount( paramBean );

				if( paramBean.getIsNeedACH() != null )
					if( paramBean.getIsNeedACH() )
						checkEDDA( paramBean );
			}
		}

		if( UserTypeEnum.BORROWER.getContext().equals( signingUserType ) && contract.getApplySigningUsers().size() > 1 )
		{
			notifyBorrowerToWaitGuarantor( borrowerInfo, contract );

			notifyGuarantorToSign( borrowerInfo, contract );

			applySigningContractDAO.updateSigningContractType( contractNo, SigningContractTypeEnum.WAITING_GUARANTOR.getContext() );
		}
		else
		{
			byte[] pdfContent = null;
			if( ProductCodeEnum.PERSONAL_LOAN.getContext().equals( contract.getProductCode() ) && !propertyBean.isHiddenItextLoanSigning() )
				pdfContent = generateSigningContractPdf( contract );
			else if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
			{
				// 因 transaction 鎖表，因此不在此處向 inet server 取得 pdf
			}

			if( contract.getApplySigningUsers().size() > 1 )
			{
				notifyBorrowerGuarantorComplete( borrowerInfo, contract );
				notifyGuarantorComplete( contract );
			}
			else
				notifyBorrowerComplete( borrowerInfo, contract );

			applySigningContractDAO.updateCompleted( contractNo, SigningContractTypeEnum.COMPLETED.getContext(),
													 SigningContractSendStatusEnum.NO.getContext() );

			if( ProductCodeEnum.PERSONAL_LOAN.getContext().equals( contract.getProductCode() ) )
			{
				if( !propertyBean.isHiddenItextLoanSigning() )
				{
					applySigningContractDAO.updatePdfContent( contractNo, pdfContent );
					deliverSigningContract( contract, pdfContent );
				}
				if( propertyBean.isHiddenItextLoanSigning() )
					try
				{
					generateService.saveTempSigningRates( contract );
				}
					catch( Exception ex )
				{
					systemService.saveExceptionLog( SystemErrorEnum.INTERNAL.getCode(), ex, null, AP_NAME, "產生對保契約書攤還表失敗" );

				}
			}
		}

		updateApplyCollateralContract( contract, paramBean );

		return result;
	}

	public String saveSigningContractPdf( SigningContractFillInfoArgBean paramBean ) throws Exception
	{
		String contractNo = paramBean.getContractNo();
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() )
			|| ( ProductCodeEnum.PERSONAL_LOAN.getContext().equals( contract.getProductCode() ) && propertyBean.isHiddenItextLoanSigning() ) )
		{
			IdentityInfoResultBean currentIdentityInfo = userClientService.getCurrentIdentityInfoResult();

			IdentityInfoResultBean contractIdentityInfo = getSigningUserIdentityInfo( contract, currentIdentityInfo.getIdNo(),
																					  currentIdentityInfo.getBirthDate() );

			String signingUserType = contractIdentityInfo.getUserType();

			if( !UserTypeEnum.BORROWER.getContext().equals( signingUserType ) || !( contract.getApplySigningUsers().size() > 1 ) )
			{
				// 建PDF
				byte[] pdfContent = null;
				PackagedInetResBean resBean = null;
				// inet pdf
				if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
					resBean = inetService.getPdf( InetRptTemplateEnum.HOUSE_LOAN_CONTRACT.getContext(), InetReturnTypeEnum.PDF.getContext(),
												  Arrays.asList( contract.getSigningContractId() ) );

				if( ProductCodeEnum.PERSONAL_LOAN.getContext().equals( contract.getProductCode() ) )
					resBean = inetService.getPdf( InetRptTemplateEnum.PERSONAL_LOAN_CONTRACT.getContext(), InetReturnTypeEnum.PDF.getContext(),
												  Arrays.asList( contract.getSigningContractId() ) );

				applySigningContractDAO.updateInetResponseStat( contractNo, resBean.getStat() );

				if( resBean.getStat() == InetResponseStatusEnum.SUCCESS.getCode() )
				{
					pdfContent = resBean.getPdfContent().getData();
					applySigningContractDAO.updatePdfContent( contractNo, pdfContent );

					deliverSigningContract( contract, pdfContent );
				}
			}
		}

		return "";
	}

	public boolean saveVerifiedEmail( SigningContractUpdateInfoArgBean argBean )
	{
		String contractNo = argBean.getContractNo();
		String verifiedEmail = argBean.getVerifiedEmail();

		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );
		IdentityInfoResultBean signingUserIdentityInfo = getSigningUserIdentityInfo( contract );
		signingUserDAO.updateVerifiedEmail( contract.getSigningContractId(), signingUserIdentityInfo.getValidatedIdentityId(), verifiedEmail );

		return true;
	}

	private void addGuarantorAgreedBeans( List<ContractAgreementBean> guarantorAgreedBeans, ApplyAgreed agreed, boolean isGuaranteeContract,
										  Map<String, Object> map )
	{
		int itemSize = agreed.getApplyAgreedItems().size();

		if( isGuaranteeContract && itemSize == 0 )
			guarantorAgreedBeans.add( mapContractAgreementBean( agreed, map ) );

	}

	/**
	 * 檢查契約是否已失效
	 *
	 * @param contractType
	 * @param contractTypeName
	 * @param discard
	 * @param deadline
	 * @return
	 */
	private String checkContractType( String contractType, String contractTypeName, Integer discard, Date deadline )
	{
		if( discard != null && discard == 1 )
			return SigningContractTypeEnum.DISCARD.getName();

		if( SigningContractTypeEnum.COMPLETED.getContext().equals( contractType ) )
			return contractTypeName;

		Date current = new Date();

		return CommonDateUtils.getStartDate( deadline ).getTime() < CommonDateUtils.getStartDate( current )
					.getTime() ? SigningContractTypeEnum.EXPIRED.getName() : contractTypeName;
	}

	private void checkedBoxSetter( Map<String, Object> placeHolderMap, String termsCheckBoxName, String checkedTermsList )
	{
		Arrays.stream( CommonStringUtils.split( checkedTermsList, ',', false ) )
					.forEach( checkedTerms -> placeHolderMap.put( termsCheckBoxName + checkedTerms, CHECKED_BOX_STRING ) );
	}

	private void checkEDDAAccount( SigningContractFillInfoParamBean argBean )
	{
		String contractNo = argBean.getContractNo();
		ApplySigningContract contract = applySigningContractDAO.getPojoByContractNo( contractNo );

		List<ApplySigningBankAccount> bankAccounts = bankAccountDAO.getContractBankAccounts( contract.getSigningContractId() );

		List<BankAccountResBean> resBeans = new ArrayList<>();

		// 判斷對保走他行認證isNeedACH必填
		if( !argBean.getBankAcctCode().equals( "017" ) && argBean.getIsNeedACH() == null )
			throw new MyRuntimeException( ApplyErrorEnum.NEED_TO_CHECK );

		// 判斷ACH Flag且ACH帳號為空時，自動塞ACH帳號
		if( argBean.getIsNeedACH() != null )
		{
			// 檢查他行帳號,入帳還款帳號相同
			for( ApplySigningBankAccount applySigningBankAccount : bankAccounts )
				if( !argBean.getBankAcctCode().equals( applySigningBankAccount.getBankCode() )
					|| !argBean.getBankAcctNo().equals( applySigningBankAccount.getBankAccount() ) )
					throw new MyRuntimeException( ApplyErrorEnum.eDDA_Bank_Error );

			if( argBean.getIsNeedACH() )
			{
				// 檢查入帳/還款帳號相同
				if( argBean.getBankACHAcctCode().isEmpty() || argBean.getBankACHAcctNo().isEmpty() )
				{
					argBean.setBankACHAcctCode( argBean.getBankAcctCode() );
					argBean.setBankACHAcctNo( argBean.getBankAcctNo() );
				}
				if( !argBean.getBankAcctCode().equals( argBean.getBankACHAcctCode() )
					|| !argBean.getBankAcctNo().equals( argBean.getBankACHAcctNo() ) )
					throw new MyRuntimeException( ApplyErrorEnum.eDDA_Bank_Error );
			}
		}
	}

	private void checkIsSameUser( ApplySigningContract contract )
	{
		if( contract == null )
			throw new MyRuntimeException( ApplyErrorEnum.NO_SIGNING_CONTRACT );

		for( ApplySigningUser user : contract.getApplySigningUsers() )
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );

			if( getIdNo().equals( identityInfo.getIdNo() ) && getBirthDate().getTime() == identityInfo.getBirthDate().getTime() )
				return;
		}

		throw new MyRuntimeException( ApplyErrorEnum.USER_NOT_OWN_SIGNING_CONTRACT );
	}

	private void checkSigningContract( ApplySigningContract contract )
	{
		if( contract.isDiscard()
			|| CommonDateUtils.getStartDate( getDeadline( contract ) ).getTime() < CommonDateUtils.getStartDate( new Date() ).getTime() )
			throw new MyRuntimeException( ApplyErrorEnum.INVALID_CONTRACT );

		if( SigningContractTypeEnum.COMPLETED.getContext().equals( contract.getCodeSigningContractType().getSigningContractType() ) )
			throw new MyRuntimeException( ApplyErrorEnum.CONTRACT_ALREADY_COMPLETED );

	}

	/**
	 * 取得對保撥款日
	 *
	 * @return
	 */
	private List<String> common_codeService___getAppropirationDateList()
	{
		List<CodeBusinessDay> codeBusinessDays = common_codeService___getCodeBusinessDays();

		return codeBusinessDays.stream().map( codeBusinessDay -> CommonDateStringUtils.transDate2String( codeBusinessDay.getBusinessDayCode() ) )
					.collect( Collectors.toList() );
	}

	private List<CodeBusinessDay> common_codeService___getCodeBusinessDays()
	{
		boolean isBusinessHours = Calendar.getInstance().get( Calendar.HOUR_OF_DAY ) < 14;

		if( isBusinessHours )
			return codeBusinessDayDAO.get5PojosAfterToday();

		return codeBusinessDayDAO.get5PojosAfterDate( DateUtils.addDays( new Date(), 1 ) );
	}

	private String convertIntAmtToString( Integer intAmt )
	{
		return Optional.ofNullable( intAmt ).map( String::valueOf ).map( CommonStringUtils::addComma ).orElse( null );
	}

	private String convertRateToString( BigDecimal rate )
	{
		return Optional.ofNullable( rate ).map( BigDecimal::stripTrailingZeros ).map( BigDecimal::toPlainString ).orElse( NO_BREAKING_HTML_SPACE_4 );
	}

	private ApplySigningAppropriation createAppropiration( Long validatedIdentityId, String contractNo, SigningContractFillInfoParamBean paramBean )
	{
		ApplySigningAppropirationCreatedParamBean createdParamBean = new ApplySigningAppropirationCreatedParamBean();
		createdParamBean.setValidatedIdentityId( validatedIdentityId );
		createdParamBean.setContractNo( contractNo );
		createdParamBean.setAppropirationDate( paramBean.getAppropriationDate() );
		createdParamBean.setBankAccount( paramBean.getBankAcctNo() );
		createdParamBean.setRepayment( Integer.parseInt( paramBean.getRepayment() ) );
		createdParamBean.setRateAdjustInformMethod( paramBean.getRateAdjustInformMethodCode() );
		createdParamBean.setFirstPaymentDate( paramBean.getFirstPaymentDate() );
		createdParamBean.setContractCheckDate( paramBean.getContractCheckDate() );

		return applySigningAppropriationDAO.create( createdParamBean );
	}

	private byte[] generateSigningContractPdf( ApplySigningContract contract ) throws IOException
	{
		return generateService.generateSigningContractPdf( contract );
	}

	private Map<String, Object> getAgreementMap( ApplySigningContract contract )
	{
		Map<String, Object> map = new HashMap<>();
		map.put( "courtName", contract.getCourtName() );

		String guaranteeAmt = contract.getGuaranteeAmt() == null ? "&nbsp;&nbsp;&nbsp;&nbsp;"
																 : contract.getGuaranteeAmt().setScale( 0, BigDecimal.ROUND_DOWN ).toString();
		map.put( "guaranteeAmt", guaranteeAmt );

		map.put( "loanPeriodYear", contract.getLoanPeriod() / 12 );

		boolean isHouseloan = LoanTypeEnum.HOUSE_LOAN.getContext().equals( contract.getCodeLoanType().getLoanType() );
		boolean isApplyCollateralContractExist = Optional.ofNullable( contract.getApplyCollateralContract() ).isPresent();

		// 房貸
		if( isHouseloan )
		{
			ApplyHouseSigningContract houseContract = applyHouseSigningContractDAO.getPojoBySigningContractId( contract.getSigningContractId() );
			map.put( "brNoTel", houseContract.getBrNoTel() );
			map.put( "brNoFax", houseContract.getBrNoFax() );
		}

		// 房貸增貸
		if( isHouseloan && isApplyCollateralContractExist )
		{
			ApplyCollateralContract collateralContract = contract.getApplyCollateralContract();
			// 共用
			map.put( "checkedBox", CHECKED_BOX_STRING );
			map.put( "uncheckedBox", UNCHECKED_BOX_STRING );

			// 增貸同意書
			map.put( "collateralBuildingAddr1", collateralContract.getCollateralBuildingAddr1() );
			map.put( "collateralBuildingAddr2", collateralContract.getCollateralBuildingAddr2() );
			map.put( "mortgageMaxAmt1", convertIntAmtToString( collateralContract.getMortgageMaxAmt1() ) );
			map.put( "mortgageMaxAmt2", convertIntAmtToString( collateralContract.getMortgageMaxAmt2() ) );
			map.put( "firstLoanDateYear", collateralContract.getFirstLoanDateYear() );
			map.put( "firstLoanDateMth", collateralContract.getFirstLoanDateMth() );
			map.put( "firstLoanDateDay", collateralContract.getFirstLoanDateDay() );
			map.put( "firstLoanAmt1", convertIntAmtToString( collateralContract.getFirstLoanAmt1() ) );
			map.put( "firstLoanAmt2", convertIntAmtToString( collateralContract.getFirstLoanAmt2() ) );
			map.put( "mortgageSettingDebtDesc", "" );
			map.put( "houseLoanContractNo", collateralContract.getHouseLoanContractNo() );

			// 擔保品使用狀況承諾書
			uncheckedBoxSetter( map, "collateralContractTermsCheck", 4 );
			checkedBoxSetter( map, "collateralContractTermsCheck", collateralContract.getCollateralContractTerms() );
			map.put( "unregisteredBuildingDesc",
					 StringUtils.isBlank( collateralContract.getUnregisteredBuildingDesc() ) ? NO_BREAKING_HTML_SPACE_10
																							 : collateralContract.getUnregisteredBuildingDesc() );

			// 借款支用書
			LocalDate currentDate = LocalDate.now();
			map.put( "signingDateYear", currentDate.getYear() );
			map.put( "signingDateMth", currentDate.getMonthValue() );
			map.put( "signingDateDay", currentDate.getDayOfMonth() );
			map.put( "contractNo", contract.getContractNo() );
			map.put( "loanAmt", convertIntAmtToString( contract.getLoanAmt() ) );

			// 央行管制切結書-購地貸款適用 (屬有「住」字樣「購置住宅貸款」或屬中央銀行管制「購地貸款」)
			uncheckedBoxSetter( map, "cbAfft1_5_check", 4 );
			checkedBoxSetter( map, "cbAfft1_5_check", collateralContract.getCbAfft1_5() );
			uncheckedBoxSetter( map, "cbAfft1_16_check", 4 );
			checkedBoxSetter( map, "cbAfft1_16_check", collateralContract.getCbAfft1_16() );
			map.put( "cbAfft1_1", collateralContract.getCbAfft1_1() );
			map.put( "cbAfft1_2", collateralContract.getCbAfft1_2() );
			map.put( "cbAfft1_3_year", collateralContract.getCbAfft1_3_year() );
			map.put( "cbAfft1_3_mth", collateralContract.getCbAfft1_3_mth() );
			map.put( "cbAfft1_3_day", collateralContract.getCbAfft1_3_day() );
			map.put( "cbAfft1_4", convertIntAmtToString( collateralContract.getCbAfft1_4() ) );
			map.put( "cbAfft1_6", collateralContract.getCbAfft1_6() );
			map.put( "cbAfft1_7", convertRateToString( collateralContract.getCbAfft1_7() ) );
			map.put( "cbAfft1_8", convertIntAmtToString( collateralContract.getCbAfft1_8() ) );
			map.put( "cbAfft1_9_year", collateralContract.getCbAfft1_9_year() );
			map.put( "cbAfft1_9_mth", collateralContract.getCbAfft1_9_mth() );
			map.put( "cbAfft1_9_day", collateralContract.getCbAfft1_9_day() );
			map.put( "cbAfft1_10_year", collateralContract.getCbAfft1_10_year() );
			map.put( "cbAfft1_10_mth", collateralContract.getCbAfft1_10_mth() );
			map.put( "cbAfft1_10_day", collateralContract.getCbAfft1_10_day() );
			map.put( "cbAfft1_10_no", collateralContract.getCbAfft1_10_no() );
			map.put( "cbAfft1_11", convertIntAmtToString( collateralContract.getCbAfft1_11() ) );
			map.put( "cbAfft1_12", convertIntAmtToString( collateralContract.getCbAfft1_12() ) );
			map.put( "cbAfft1_13", convertIntAmtToString( collateralContract.getCbAfft1_13() ) );
			map.put( "cbAfft1_14", convertIntAmtToString( collateralContract.getCbAfft1_14() ) );
			map.put( "cbAfft1_15", convertIntAmtToString( collateralContract.getCbAfft1_15() ) );

			// 央行管制切結書-購置住宅貸款 (自然人特定地區第二戶購屋貸款換屋者使用)
			map.put( "cbAfft2_1", collateralContract.getCbAfft2_1() );
			map.put( "cbAfft2_2", collateralContract.getCbAfft2_2() );
			map.put( "cbAfft2_3_year", collateralContract.getCbAfft2_3_year() );
			map.put( "cbAfft2_3_mth", collateralContract.getCbAfft2_3_mth() );
			map.put( "cbAfft2_3_day", collateralContract.getCbAfft2_3_day() );
			map.put( "cbAfft2_4", convertIntAmtToString( collateralContract.getCbAfft2_4() ) );
			map.put( "cbAfft2_5", convertIntAmtToString( collateralContract.getCbAfft2_5() ) );
			map.put( "cbAfft2_6", convertIntAmtToString( collateralContract.getCbAfft2_6() ) );
			map.put( "cbAfft2_7", convertIntAmtToString( collateralContract.getCbAfft2_7() ) );
			map.put( "cbAfft2_8", convertIntAmtToString( collateralContract.getCbAfft2_8() ) );
			map.put( "cbAfft2_9", convertIntAmtToString( collateralContract.getCbAfft2_9() ) );
			map.put( "cbAfft2_10", convertRateToString( collateralContract.getCbAfft2_10() ) );

			// 央行管制切結書-購置住宅貸款適用排除條件者 (受限戶建物用途含「住」字供營業使用)
			map.put( "cbAfft3_1", collateralContract.getCbAfft3_1() );
			map.put( "cbAfft3_2", collateralContract.getCbAfft3_2() );
			map.put( "cbAfft3_3_year", collateralContract.getCbAfft3_3_year() );
			map.put( "cbAfft3_3_mth", collateralContract.getCbAfft3_3_mth() );
			map.put( "cbAfft3_3_day", collateralContract.getCbAfft3_3_day() );
			map.put( "cbAfft3_4", convertIntAmtToString( collateralContract.getCbAfft3_4() ) );
			map.put( "cbAfft3_5", convertRateToString( collateralContract.getCbAfft3_5() ) );

			// 央行管制切結書-非購屋(或購地)貸款適用資金用途用於投資、週轉…等
			map.put( "cbAfft4_1", collateralContract.getCbAfft4_1() );
			map.put( "cbAfft4_2_year", collateralContract.getCbAfft4_2_year() );
			map.put( "cbAfft4_2_mth", collateralContract.getCbAfft4_2_mth() );
			map.put( "cbAfft4_2_day", collateralContract.getCbAfft4_2_day() );
			map.put( "cbAfft4_3", convertIntAmtToString( collateralContract.getCbAfft4_3() ) );
			map.put( "cbAfft4_4", collateralContract.getCbAfft4_4() );
			map.put( "cbAfft4_5", convertRateToString( collateralContract.getCbAfft4_5() ) );
			map.put( "cbAfft4_6", convertRateToString( collateralContract.getCbAfft4_6() ) );
			map.put( "cbAfft4_7", convertRateToString( collateralContract.getCbAfft4_7() ) );

			// 央行管制切結書-工業區閒置土地抵押貸款適用資金用途用於購置、投資、週轉…等
			uncheckedBoxSetter( map, "cbAfft5_5_check", 3 );
			checkedBoxSetter( map, "cbAfft5_5_check", collateralContract.getCbAfft5_5() );
			map.put( "cbAfft5_1", collateralContract.getCbAfft5_1() );
			map.put( "cbAfft5_2", collateralContract.getCbAfft5_2() );
			map.put( "cbAfft5_3_year", collateralContract.getCbAfft5_3_year() );
			map.put( "cbAfft5_3_mth", collateralContract.getCbAfft5_3_mth() );
			map.put( "cbAfft5_3_day", collateralContract.getCbAfft5_3_day() );
			map.put( "cbAfft5_4", convertIntAmtToString( collateralContract.getCbAfft5_4() ) );
			map.put( "cbAfft5_6", convertRateToString( collateralContract.getCbAfft5_6() ) );
			map.put( "cbAfft5_7", convertIntAmtToString( collateralContract.getCbAfft5_7() ) );
			map.put( "cbAfft5_8", convertIntAmtToString( collateralContract.getCbAfft5_8() ) );
			map.put( "cbAfft5_9", convertIntAmtToString( collateralContract.getCbAfft5_9() ) );
			map.put( "cbAfft5_10", convertIntAmtToString( collateralContract.getCbAfft5_10() ) );
			map.put( "cbAfft5_11", convertIntAmtToString( collateralContract.getCbAfft5_11() ) );
			map.put( "cbAfft5_12", collateralContract.getCbAfft5_12() );
			map.put( "cbAfft5_13", convertRateToString( collateralContract.getCbAfft5_13() ) );
			map.put( "cbAfft5_14", convertRateToString( collateralContract.getCbAfft5_14() ) );
			map.put( "cbAfft5_15", convertRateToString( collateralContract.getCbAfft5_15() ) );
		}

		return map;
	}

	private BigDecimal getAnnualPercentageRate( ApplySigningContract contract )
	{
		if( contract.getShowOption() == 2 )
			return contract.getLimitedApr();

		return contract.getAdvancedApr();
	}

	/**
	 * 取得對保有效日
	 *
	 * @param contract
	 * @return
	 */
	private Date getDeadline( ApplySigningContract contract )
	{
		if( contract.getApplySigningAppropriation() == null )
			return contract.getExpiredDate();

		return getDeadline( contract.getExpiredDate(), contract.getApplySigningAppropriation().getAppropriationDate(),
							contract.getApplySigningUsers().size() );

	}

	/**
	 * 取得對保有效日
	 *
	 * @param expiredDate
	 * @param appropriationDate
	 * @param userCount
	 * @return
	 */
	private Date getDeadline( Date expiredDate, Date appropriationDate, int userCount )
	{
		if( appropriationDate == null )
			return expiredDate;

		if( userCount > 1 && expiredDate.getTime() >= appropriationDate.getTime() )
			return DateUtils.addDays( appropriationDate, -1 );

		return expiredDate;
	}

	private List<String> getLoanPurposeList( String loanPurpose )
	{
		List<String> list = new ArrayList<>();

		if( StringUtils.isBlank( loanPurpose ) )
			return list;

		String[] strs = StringUtils.split( loanPurpose, "\\|" );

		if( strs == null )
			return list;

		for( String str : strs )
		{
			String[] substr = StringUtils.split( str, "," );

			if( substr == null || substr.length == 0 )
				continue;

			if( ( substr.length == 1 ) || ( substr.length > 1 && "1".equals( substr[ 1 ] ) ) )
				list.add( substr[ 0 ] );
		}

		return list;
	}

	private String getPdfFileNameByInetRptTemplate( InetRptTemplateEnum inetRptTemplateEnum )
	{
		switch( inetRptTemplateEnum )
		{
			case HOUSE_LOAN_VARIOUS_AGREEMENT:
				return "線上對保各項約據.pdf";
			case HOUSE_LOAN_CENTRAL_BANK_AFFIDAVIT:
				return "線上借款人央行管制切結書.pdf";
			default:
				return "";
		}
	}

	private IdentityInfoResultBean getSigningUserIdentityInfo( ApplySigningContract contract )
	{
		return getSigningUserIdentityInfo( contract, getIdNo(), getBirthDate() );
	}

	private IdentityInfoResultBean getSigningUserIdentityInfo( ApplySigningContract contract, String idNo, Date birthDate )
	{
		if( contract == null )
			throw new MyRuntimeException( ApplyErrorEnum.NO_SIGNING_CONTRACT );

		for( ApplySigningUser user : contract.getApplySigningUsers() )
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );

			if( idNo.equals( identityInfo.getIdNo() ) && birthDate.getTime() == identityInfo.getBirthDate().getTime() )
				return identityInfo;
		}

		throw new MyRuntimeException( ApplyErrorEnum.USER_NOT_OWN_SIGNING_CONTRACT );
	}

	/**
	 * 取得對保完成感謝頁內容
	 *
	 * @param contractId
	 * @return
	 */
	private String getThankyouCompletedContent( Long contractId )
	{
		return "感謝您已完成兆豐銀行貸款線上簽約。";
	}

	/**
	 * 取得對保未完成感謝頁內容
	 *
	 * @param contract
	 * @return
	 */
	private String getThankyouContent( ApplySigningContract contract )
	{
		if( SigningContractTypeEnum.COMPLETED.getContext().equals( contract.getCodeSigningContractType().getSigningContractType() ) )
			return getThankyouCompletedContent( contract.getSigningContractId() );

		String url = "";
		if( ProductCodeEnum.PERSONAL_LOAN.getContext().equals( contract.getProductCode() ) )
			url = propertyBean.getSigningUrl();
		else if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
			url = propertyBean.getHouseSigning();

		return "感謝您完成兆豐銀行貸款線上簽約，您的契約編號是" + contract.getContractNo() + "，因本案另有保證人，請將契約編號 " + contract.getContractNo()
			+ "，通知保證人，請其盡速於預定撥款日一日之前，於網站點選<a href=\"" + url + "\" rel=\"noopener\" target=\"_blank\">簽約撥款</a>辦理線上對保，謝謝!";

	}

	private SigningUserInfoBean getUserInfo( Set<ApplySigningUser> users, String userType )
	{
		SigningUserInfoBean userInfo = new SigningUserInfoBean();

		for( ApplySigningUser user : users )
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );

			if( userType.equals( identityInfo.getUserType() ) )
			{
				userInfo.setIdNo( identityInfo.getIdNo() );
				userInfo.setName( user.getName() );
				userInfo.setMobileNumber( user.getMobileNumber() );
				userInfo.setMlAddr( getValidEmailToUse( user ) );
			}
		}

		return userInfo;
	}

	private String getValidEmailToUse( ApplySigningUser user )
	{
		return Optional.ofNullable( user.getVerifiedEmail() ).orElse( user.getEmail() );
	}

	private void handleCollateralContractSubmission( ApplySigningContract contract )
	{
		if( !isHouseloan( contract ) )
			return;

		List<ApplyCollateralContractAttachment> attachmentList = applyCollateralContractAttachmentDAO
					.getPojosBySigningContractIdAndInetResponseStatus( contract.getSigningContractId(), InetResponseStatusEnum.SUCCESS.getCode() );

		for( ApplyCollateralContractAttachment attachment : attachmentList )
		{
			boolean isSubmitSuccess = deliverService.submitCollateralContractAttachment( attachment );
			String transmissionStatusCode = isSubmitSuccess ? TransmissionStatusEnum.COMPLETED.getContext()
															: TransmissionStatusEnum.EXCEPTION.getContext();
			applyCollateralContractAttachmentDAO.updateTransmissionStatus( attachment.getAttachmentId(), transmissionStatusCode );

		}
	}

	private List<ContractAgreementBean> handleVariousTermsOfIncreaseHouseloan( List<ContractAgreementBean> commonAgreedBeans, ApplyAgreed agreed,
																			   Map<String, Object> agreementMap, ApplySigningContract contract )
	{
		IncreaseHouseloanVariousTermsTitleEnum titleEnum = IncreaseHouseloanVariousTermsTitleEnum.getEnum( agreed.getTitle() );
		Set<ApplySigningRate> applySigningRates = contract.getApplySigningRates();
		CodeProdKind codeProdKind = contract.getCodeProdKind();
		String consentVer = contract.getApplyCollateralContract().getConsentVer();

		if( titleEnum == null )
			return commonAgreedBeans;

		switch( titleEnum )
		{
			case LOAN_INCREASE_LIMIT:
				if( "1".equals( consentVer ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case LOAN_INCREASE_REPAY:
				if( "2".equals( consentVer ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case LOAN_DISBURSE:
				if( codeProdKind != null && Arrays.asList( "03", "31" ).contains( codeProdKind.getProdKindId() ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case STAFF_SPOUSE_LIMIT:
				if( applySigningRates != null
					&& applySigningRates.stream().map( ApplySigningRate::getRateType ).anyMatch( Arrays.asList( "M2", "N2", "M3", "MR" )::contains ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case MEGA_EMPLOYEE_1500W:
				if( applySigningRates != null && applySigningRates.stream().map( ApplySigningRate::getRateType ).anyMatch( "7D"::equals ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			default:
				commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
		}

		return commonAgreedBeans;
	}

	private boolean hasBlanksInAgreement( ApplyAgreed agreed )
	{
		IncreaseHouseloanVariousTermsTitleEnum titleEnum = IncreaseHouseloanVariousTermsTitleEnum.getEnum( agreed.getTitle() );
		return titleEnum == IncreaseHouseloanVariousTermsTitleEnum.LOAN_INCREASE_LIMIT
			|| titleEnum == IncreaseHouseloanVariousTermsTitleEnum.LOAN_INCREASE_REPAY;
	}

	private List<ContractAgreementBean> increaseHouseloanCommonAgreedBeansAppender( List<ContractAgreementBean> commonAgreedBeans, ApplyAgreed agreed,
																					Map<String, Object> agreementMap, ApplySigningContract contract )
	{
		String cbAfftTerms = contract.getApplyCollateralContract().getCbAfftTerms();
		AgreedTypeEnum agreedTypeEnum = AgreedTypeEnum.getEnum( agreed.getAgreedType() );

		// 不用檢核顯示條件的項目
		if( agreedTypeEnum == null )
		{
			commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
			return commonAgreedBeans;
		}

		switch( agreedTypeEnum )
		{
			// 各項約據
			case HOUSELOAN_VARIOUS:
				return handleVariousTermsOfIncreaseHouseloan( commonAgreedBeans, agreed, agreementMap, contract );
			// 央行切結書
			case CB_AFFT_1:
				if( "1".equals( cbAfftTerms ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case CB_AFFT_2:
				if( "2".equals( cbAfftTerms ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case CB_AFFT_3:
				if( "3".equals( cbAfftTerms ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case CB_AFFT_4:
				if( "4".equals( cbAfftTerms ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
			case CB_AFFT_5:
				if( "5".equals( cbAfftTerms ) )
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, agreementMap, contract ) );
				break;
		}

		return commonAgreedBeans;
	}

	private boolean isHouseloan( ApplySigningContract contract )
	{
		return ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() );
	}

	private boolean isIncreaseHouseLoanAgreement( ApplyAgreed agreed )
	{
		AgreedTypeEnum typeEnum = AgreedTypeEnum.getEnum( agreed.getAgreedType() );
		return typeEnum != null && EnumSet.of( AgreedTypeEnum.HOUSELOAN_VARIOUS, AgreedTypeEnum.CB_AFFT_1, AgreedTypeEnum.CB_AFFT_2,
											   AgreedTypeEnum.CB_AFFT_3, AgreedTypeEnum.CB_AFFT_4, AgreedTypeEnum.CB_AFFT_5 )
					.contains( typeEnum );
	}

	private AgreementResBean mapAgreementResBean( List<ApplyAgreed> agreedList, ApplySigningContract contract )
	{
		String userType = getSigningUserIdentityInfo( contract ).getUserType();
		boolean isGuaranteeContract = contract.getApplySigningUsers().size() > 1;
		boolean isHouseloan = LoanTypeEnum.HOUSE_LOAN.getContext().equals( contract.getCodeLoanType().getLoanType() );
		boolean isApplyCollateralContractExist = Optional.ofNullable( contract.getApplyCollateralContract() ).isPresent();

		List<ContractAgreementBean> borrowerAgreedBeans = new ArrayList<>();
		List<ContractAgreementBean> guarantorAgreedBeans = new ArrayList<>();
		List<ContractAgreementBean> commonAgreedBeans = new ArrayList<>();

		Map<String, Object> map = getAgreementMap( contract );

		for( ApplyAgreed agreed : agreedList )
		{
			if( isIncreaseHouseLoanAgreement( agreed ) && !isApplyCollateralContractExist )
				continue;

			if( agreed.getCodeUserType() == null )
			{
				if( isHouseloan && isApplyCollateralContractExist )
					increaseHouseloanCommonAgreedBeansAppender( commonAgreedBeans, agreed, map, contract );
				else
					// 非房貸增貸 & 信貸
					commonAgreedBeans.add( mapContractAgreementBean_with_loanPlanCode( agreed, map, contract ) );
			}
			else
			{
				String agreedUserType = agreed.getCodeUserType().getUserType();

				if( UserTypeEnum.BORROWER.getContext().equals( userType ) && UserTypeEnum.BORROWER.getContext().equals( agreedUserType ) )
					borrowerAgreedBeans.add( mapContractAgreementBean( agreed ) );
				else if( UserTypeEnum.BORROWER.getContext().equals( userType ) && UserTypeEnum.GUARANTOR.getContext().equals( agreedUserType ) )
					addGuarantorAgreedBeans( guarantorAgreedBeans, agreed, isGuaranteeContract, map );
				else if( UserTypeEnum.GUARANTOR.getContext().equals( userType ) && UserTypeEnum.GUARANTOR.getContext().equals( agreedUserType ) )
					guarantorAgreedBeans.add( mapContractAgreementBean( agreed, map ) );
			}
		}
		AgreementResBean resBean = new AgreementResBean();
		resBean.setBorrowerAgreedBeans( borrowerAgreedBeans );
		resBean.setGuarantorAgreedBeans( guarantorAgreedBeans );
		resBean.setCommonAgreedBeans( commonAgreedBeans );

		return resBean;
	}

	private AppropriationBean mapAppropriationBean( ApplySigningAppropriation appropriation, ApplySigningUser borrowInfo )
	{
		AppropriationBean resultBean = new AppropriationBean();
		resultBean.setBankAcctNo( appropriation.getApplySigningBankAccount().getBankAccount() );
		resultBean.setAppropriationDate( CommonDateStringUtils.transDate2String( appropriation.getAppropriationDate() ) );
		resultBean.setRepayment( String.format( "%02d", appropriation.getRepaymentDay() ) );
		resultBean.setFirstPaymentDate( CommonDateStringUtils.transDate2String( appropriation.getFirstPaymentDate() ) );
		resultBean.setRateAdjustInformMethodCode( appropriation.getCodeRateAdjustmentNotification().getRateAdjustmentNotificaitonCode() );
		resultBean.setRateAdjustInformMethod( appropriation.getCodeRateAdjustmentNotification().getName() );

		if( borrowInfo == null )
			return resultBean;

		if( borrowInfo.getCodeContractNotification() != null )
		{
			resultBean.setBorrowerContractSendingMethodCode( borrowInfo.getCodeContractNotification().getContractNotificationCode() );
			resultBean.setBorrowerContractSendingMethod( borrowInfo.getCodeContractNotification().getName() );
		}

		if( borrowInfo.getCodeTown() != null )
		{
			resultBean.setBorrowerAddressCityCode( borrowInfo.getCodeTown().getCodeCity().getCityCode() );
			resultBean.setBorrowerAddressTownCode( borrowInfo.getCodeTown().getTownCode() );
			resultBean.setBorrowerAddressStreet( borrowInfo.getNotificationAddress() );
		}

		return resultBean;
	}

	private SigningContractBasicResBean mapBasicResBean( ApplySigningContract contract, String signingUserType )
	{
		SigningContractBasicResBean resBean = new SigningContractBasicResBean();
		resBean.setContractUrl( propertyBean.getContractUrl() );
		resBean.setLoanAmt( contract.getLoanAmt() );
		resBean.setLoanPeriod( contract.getLoanPeriod() );
		resBean.setLoanPurpose( getLoanPurposeList( contract.getLoanPurpose() ) );
		resBean.setOneTimeFee( contract.getOneTimeFee() );
		resBean.setPreliminaryFee( contract.getPreliminaryFee() );
		resBean.setCreditCheckFee( contract.getCreditCheckFee() );
		resBean.setDrawDownType( contract.getDrawDownType() );
		resBean.setRepaymentMethod( contract.getRepaymentMethod() );
		resBean.setLendingPlan( contract.getLendingPlan() );
		resBean.setAnnualPercentageRate( getAnnualPercentageRate( contract ) );
		resBean.setProjectDescBeans( mapProjectDescBeans( contract ) );
		resBean.setPlan( contract.getLoanPlanCode() );
		resBean.setLnDate( contract.getLnDate() );

		if( contract.getCodeProdKind() != null )
			resBean.setProdKind( contract.getCodeProdKind().getProdKindId() );

		if( contract.getPayeeInfoId() != null )
		{
			ApplyPayeeInfo applyPayeeInfo = applyPayeeInfoDAO.read( contract.getPayeeInfoId() );
			resBean.setPayeeBankCode( applyPayeeInfo.getBankCode() );
			resBean.setPayeeBankAccountNo( applyPayeeInfo.getAccountNo() );
			resBean.setPayeeBankAccountName( applyPayeeInfo.getAccountName() );
			resBean.setPayeeTotalAmt( applyPayeeInfo.getTotalAmt() );
			resBean.setPayeeRemittance( applyPayeeInfo.getRemittance() );
			resBean.setPayeeSelfProvide( applyPayeeInfo.getSelfProvide() );
		}

		ApplySigningUser borrowInfo = null;
		for( ApplySigningUser user : contract.getApplySigningUsers() )
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );

			if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			{
				borrowInfo = user;
				resBean.setIsSignatoryYouth( BooleanUtils.isTrue( user.getIsYouth() ) );
				resBean.setName( user.getName() );
				resBean.setId( maskIdNo( identityInfo.getIdNo() ) );
			}
			else
			{
				resBean.setGuaranteeName( user.getName() );
				resBean.setGuaranteeId( maskIdNo( identityInfo.getIdNo() ) );
				resBean.setGuaranteeType( codeUserSubTypeDAO.read( identityInfo.getUserSubType() ).getName() );
				resBean.setGuaranteeAmt( contract.getGuaranteeAmt() );

				setGuaranteePlan( resBean, contract, identityInfo.getUserSubType() );
			}
		}

		if( UserTypeEnum.BORROWER.getContext().equals( signingUserType ) )
		{
			resBean.setContractType( UserTypeEnum.BORROWER.getContext() );
			resBean.setAppropriationBean( null );
		}
		else
		{
			resBean.setContractType( UserTypeEnum.GUARANTOR.getContext() );
			resBean.setAppropriationBean( mapAppropriationBean( contract.getApplySigningAppropriation(), borrowInfo ) );
			resBean.setContractCheckDate( CommonDateStringUtils.transDate2String( contract.getApplySigningAppropriation().getContractCheckDate() ) );
		}

		return resBean;
	}

	private ContractAgreementBean mapContractAgreementBean( ApplyAgreed agreed )
	{
		return mapContractAgreementBean( agreed, null );
	}

	private ContractAgreementBean mapContractAgreementBean( ApplyAgreed agreed, Map<String, Object> map )
	{
		List<ApplyAgreedItem> itemList = applyAgreedItemDAO.getPojosByAgreedId( agreed.getAgreedId() );

		Boolean isGroup = false;
		List<AgreedItemBean> itemBeans = new ArrayList<>();
		for( ApplyAgreedItem item : itemList )
		{
			if( StringUtils.isNotBlank( item.getAgreedGroup() ) )
				isGroup = true;

			AgreedItemBean itemBean = new AgreedItemBean();
			itemBean.setItemId( item.getAgreedItemId() );
			itemBean.setText( replaceContent( item.getContent(), map ) );
			itemBean.setRequired( item.isNeedToCheck() );
			itemBean.setValue( item.getItemValue() );
			itemBean.setType( item.getItemType() );

			itemBeans.add( itemBean );
		}

		ContractAgreementBean agreedBean = new ContractAgreementBean();
		agreedBean.setTitle( agreed.getTitle() );
		agreedBean.setContent( replaceContent( agreed.getContent(), map ) );
		agreedBean.setIsGroup( isGroup );
		agreedBean.setAgreedItemBeans( itemBeans );
		agreedBean.setIsReadRequired( isIncreaseHouseLoanAgreement( agreed ) );
		agreedBean.setHasBlankToFillIn( hasBlanksInAgreement( agreed ) );

		return agreedBean;
	}

	private ContractAgreementBean mapContractAgreementBean_with_loanPlanCode( ApplyAgreed agreed, Map<String, Object> map,
																			  ApplySigningContract contract )
	{
		ContractAgreementBean bean = mapContractAgreementBean( agreed, map );

		// 原條文裡面包含 ...第三條之第(四)項...第五條之第(四)、(五)...，所以改用 "<p>(四)" 來做 indexOf 比對
		// J-112-0206 配合團貸案之個別商議條款調整為可由分行於簽報書自行選擇(非團貸案件一律不顯示個別商議條款)，精銳科技也比照辦理
		if( contract != null && ApplyLoanUtils.is_staffRule_N( contract.getStaffRule() ) && StringUtils.equals( "個別商議條款", bean.getTitle() )
			&& bean.getContent().indexOf( "<p>(五)" ) > 0 )

		{
			String content = bean.getContent().substring( 0, bean.getContent().indexOf( "<p>(五)" ) );
			bean.setContent( content );
		}
		/*
		 * // J-112-0206 配合團貸案之個別商議條款調整為可由分行於簽報書自行選擇(非團貸案件一律不顯示個別商議條款)，
		 * // 精銳科技也比照辦理，註解掉精銳科技原有之[(四)甲方同意於離職時，應清償本專案借款餘欠。]相關邏輯
		 * if( contract != null && ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( contract.getLoanPlanCode() )
		 * && StringUtils.equals( "個別商議條款", bean.getTitle() ) && bean.getContent().indexOf( "<p>(四)" ) < 0 )
		 *
		 * {
		 * String added_item = "<p>(四)" + ApplyLoanUtils.get_signing_contract_term_7_4th_co70647919_C101_value() + "</p>";
		 * bean.setContent( bean.getContent() + added_item );
		 * }
		 */

		return bean;
	}

	private List<ProjectDescBean> mapProjectDescBeans( ApplySigningContract contract )
	{
		List<ProjectDescBean> projects = new ArrayList<>();

		ProjectDescBean rate = new ProjectDescBean();
		ProjectDescBean redemption = new ProjectDescBean();
		if( contract.getShowOption() == 2 )
		{
			rate.setTitle( contract.getLimitedRateTitle() );
			rate.setInfoDesc( contract.getLimitedRateDesc() );
			redemption.setTitle( contract.getLimitedRedemptionTitle() );
			redemption.setInfoDesc( contract.getLimitedRedemptionDesc() );
		}
		else
		{
			rate.setTitle( contract.getAdvancedRateTitle() );
			rate.setInfoDesc( contract.getAdvancedRateDesc() );
			redemption.setTitle( contract.getAdvancedRedemptionTitle() );
			redemption.setInfoDesc( contract.getAdvancedRedemptionDesc() );
		}

		projects.add( rate );
		projects.add( redemption );

		if( contract.getOtherInfoTitle() != null )
		{
			ProjectDescBean otherInfo = new ProjectDescBean();
			otherInfo.setTitle( contract.getOtherInfoTitle() );
			otherInfo.setInfoDesc( contract.getOtherInfoDesc() );
			projects.add( otherInfo );
		}

		return projects;
	}

	/**
	 * 遮罩身分證字號 A123***789
	 *
	 * @param idNo
	 * @return
	 */
	private String maskIdNo( String idNo )
	{
		return idNo.substring( 0, 4 ) + "***" + idNo.substring( 7, 10 );
	}

	/**
	 * 借款人對保完成通知(無保證人對保契約完成)
	 *
	 * @param borrowerInfo
	 * @param contract
	 */
	private void notifyBorrowerComplete( SigningUserInfoBean borrowerInfo, ApplySigningContract contract ) throws MyRuntimeException
	{
		BillhunterMailTypeEnum mailTypeEnum = null;
		if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
			mailTypeEnum = BillhunterMailTypeEnum.HOUSE_LOAN_SIGNING_COMPLETED;
		else
			mailTypeEnum = BillhunterMailTypeEnum.PERSONAL_LOAN_SIGNING_COMPLETED;

		notifyUserComplete( borrowerInfo, contract, mailTypeEnum );
	}

	/**
	 * 借款人對保完成通知(有保證人對保契約完成)
	 *
	 * @param userInfo
	 * @param contract
	 */
	private void notifyBorrowerGuarantorComplete( SigningUserInfoBean userInfo, ApplySigningContract contract ) throws MyRuntimeException
	{
		BillhunterMailTypeEnum mailTypeEnum = null;
		if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
			mailTypeEnum = BillhunterMailTypeEnum.HOUSE_LOAN_SIGNING_COMPLETED;
		else
			mailTypeEnum = BillhunterMailTypeEnum.PERSONAL_LOAN_SIGNING_COMPLETED;

		String timeStamp = CommonDateStringUtils.transDate2String( new Date(), "yyyy年MM月dd月HH時mm分" );

		String mailMsg = "<p><span style=\"font-size:16px;\">親愛的兆豐銀行貴賓您好：<br />本案保證人已於" + timeStamp
			+ "完成兆豐銀行貸款線上簽約，本行將進行後續撥款相關作業，如需任何服務，敬請洽詢貸款服務專員，謝謝！</span></p>";

		String smsMsg = "本案保證人已於" + timeStamp + "完成兆豐銀行貸款線上簽約，本行將進行後續撥款相關作業，如需任何服務，敬請洽詢貸款服務專員，謝謝！";

		notifySms( contract.getContractNo(), contract.getCodeBranchBank().getBankCode(), userInfo.getMobileNumber(), userInfo.getIdNo(), smsMsg );

		notifyMail( userInfo, mailTypeEnum, mailMsg );
	}

	/**
	 * 借款人對保完成待保證人對保通知(對保契約未完成)
	 *
	 * @param borrowerInfo
	 * @param contract
	 */
	private void notifyBorrowerToWaitGuarantor( SigningUserInfoBean borrowerInfo, ApplySigningContract contract ) throws MyRuntimeException
	{
		String url = propertyBean.getSigningUrl();
		String contractNo = contract.getContractNo();

		String mailMsg = "<p><span style=\"font-size:16px;\">親愛的兆豐銀行貴賓您好：<br /><br />感謝您完成貸款線上簽約，您的契約編號是" + contractNo + "，因本案另有保證人，請將契約編號"
			+ contractNo + "通知保證人，請其盡速於預定撥款日一日之前，於網站點選<a href=\"" + url + "\" rel=\"noopener\" target=\"_blank\">簽約撥款</a>辦理線上對保，謝謝!</span></p>";

		String smsMsg = "感謝您完成貸款線上簽約，您的契約編號是" + contractNo + "，因本案另有保證人，請將契約編號" + contractNo + "通知保證人，請其盡速於預定撥款日一日之前，於點選以下連結辦理線上對保，謝謝!" + url;

		BillhunterMailTypeEnum mailTypeEnum = null;
		if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
			mailTypeEnum = BillhunterMailTypeEnum.HOUSE_LOAN_SIGNING_ONLY_BORROWER_COMPLETED;
		else
			mailTypeEnum = BillhunterMailTypeEnum.PERSONAL_LOAN_SIGNING_ONLY_BORROWER_COMPLETED;

		notifySms( contractNo, contract.getCodeBranchBank().getBankCode(), borrowerInfo.getMobileNumber(), borrowerInfo.getIdNo(), smsMsg );

		notifyMail( borrowerInfo, mailTypeEnum, mailMsg );

	}

	/**
	 * 保證人對保完成通知(對保契約完成)
	 *
	 * @param contract
	 */
	private void notifyGuarantorComplete( ApplySigningContract contract ) throws MyRuntimeException
	{
		SigningUserInfoBean guarantorInfo = getUserInfo( contract.getApplySigningUsers(), UserTypeEnum.GUARANTOR.getContext() );

		BillhunterMailTypeEnum mailTypeEnum = null;
		if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
			mailTypeEnum = BillhunterMailTypeEnum.HOUSE_LOAN_SIGNING_COMPLETED;
		else
			mailTypeEnum = BillhunterMailTypeEnum.PERSONAL_LOAN_SIGNING_COMPLETED;

		notifyUserComplete( guarantorInfo, contract, mailTypeEnum );
	}

	/**
	 * 借款人對保完成，通知保證人進行對保(對保契約未完成)
	 *
	 * @param borrowerInfo
	 * @param contract
	 */
	private void notifyGuarantorToSign( SigningUserInfoBean borrowerInfo, ApplySigningContract contract ) throws MyRuntimeException
	{
		String url = propertyBean.getSigningUrl();

		SigningUserInfoBean guarantorInfo = getUserInfo( contract.getApplySigningUsers(), UserTypeEnum.GUARANTOR.getContext() );

		String smsMsg = "您好，借款人" + borrowerInfo.getName() + "前所申請之兆豐銀行貸款已完成簽約，案件編號為" + contract.getContractNo() + "，謹通知您盡速點選以下連結辦理保證人線上簽約對保 " + url;

		notifySms( contract.getContractNo(), contract.getCodeBranchBank().getBankCode(), guarantorInfo.getMobileNumber(), guarantorInfo.getIdNo(),
				   smsMsg );

		String mailMsg = "<p><span style=\"font-size:16px;\">親愛的兆豐銀行貴賓您好：<br /><br />借款人" + borrowerInfo.getName() + "前所申請之兆豐銀行信用貸款已完成簽約，案件編號為"
			+ contract.getContractNo() + "，謹通知您盡速辦理保證人<a href=\"" + url + "\" rel=\"noopener\" target=\"_blank\">線上簽約</a></span></p>";

		BillhunterMailTypeEnum mailTypeEnum = null;
		if( ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() ) )
			mailTypeEnum = BillhunterMailTypeEnum.HOUSE_LOAN_SIGNING_WAITING_GUARANTOR;
		else
			mailTypeEnum = BillhunterMailTypeEnum.PERSONAL_LOAN_SIGNING_WAITING_GUARANTOR;

		notifyMail( guarantorInfo, mailTypeEnum, mailMsg );

	}

	private void notifyMail( SigningUserInfoBean userInfo, BillhunterMailTypeEnum mailTypeEnum, String message )
	{
		BillhunterSenderArgBean argBean = new BillhunterSenderArgBean();
		argBean.setSubject( mailTypeEnum.getSubject() );
		argBean.setCategory( mailTypeEnum.getCategory() );
		argBean.setMessage( message );

		ToMailInfoBean info = new ToMailInfoBean();
		info.setToMail( userInfo.getMlAddr() );
		info.setToUserId( userInfo.getIdNo() );
		info.setToUserName( userInfo.getName() );
		List<ToMailInfoBean> infoList = new ArrayList<>();
		infoList.add( info );

		argBean.setToMailInfoBeans( infoList );

		billhunterSenderService.send( argBean );
	}

	private void notifyMail( SigningUserInfoBean userInfo, BillhunterMailTypeEnum mailTypeEnum, String message, String fileName, byte[] fileContent )
	{

		ToMailInfoBean toMailInfoBean = new ToMailInfoBean();
		toMailInfoBean.setToMail( userInfo.getMlAddr() );
		toMailInfoBean.setToUserId( userInfo.getIdNo() );
		toMailInfoBean.setToUserName( userInfo.getName() );
		List<ToMailInfoBean> toMailInfoBeans = Arrays.asList( toMailInfoBean );

		AttachmentFileBean attachmentFileBean = new AttachmentFileBean();
		attachmentFileBean.setFileName( fileName );
		attachmentFileBean.setFileContent( CommonBase64Utils.base64Encoder( fileContent ) );
		List<AttachmentFileBean> attachmentFileBeans = Arrays.asList( attachmentFileBean );

		BillhunterSenderArgBean argBean = new BillhunterSenderArgBean();
		argBean.setSubject( mailTypeEnum.getSubject() );
		argBean.setCategory( mailTypeEnum.getCategory() );
		argBean.setMessage( message );
		argBean.setToMailInfoBeans( toMailInfoBeans );
		argBean.setAttachmentFileBeans( attachmentFileBeans );

		billhunterSenderService.send( argBean );
	}

	private void notifySms( String contractNo, String branchBankCode, String mobileNumber, String idNo, String message ) throws MyRuntimeException
	{
		smsSenderService.send( contractNo, branchBankCode, mobileNumber, idNo, message );
	}

	/**
	 * 對保完成通知
	 *
	 * @param userInfo
	 * @param contract
	 * @param mailTypeEnum
	 */
	private void notifyUserComplete( SigningUserInfoBean userInfo, ApplySigningContract contract, BillhunterMailTypeEnum mailTypeEnum )
		throws MyRuntimeException
	{
		String timeStamp = CommonDateStringUtils.transDate2String( new Date(), "yyyy年MM月dd月HH時mm分" );

		String mailMsg = "<p><span style=\"font-size:16px;\">親愛的兆豐銀行貴賓您好：<br />感謝您於" + timeStamp
			+ "完成兆豐銀行貸款線上簽約，本行將進行後續撥款相關作業，如需任何服務，敬請洽詢貸款服務專員，謝謝！</span></p>";

		String smsMsg = "感謝您於" + timeStamp + "完成兆豐銀行貸款線上簽約，本行將進行後續撥款相關作業，如需任何服務，敬請洽詢貸款服務專員，謝謝！";

		notifySms( contract.getContractNo(), contract.getCodeBranchBank().getBankCode(), userInfo.getMobileNumber(), userInfo.getIdNo(), smsMsg );

		notifyMail( userInfo, mailTypeEnum, mailMsg );
	}

	private String replaceContent( String content, Map<String, Object> map )
	{
		if( map == null )
			return content;

		return CommonHtml2PdfUtils.replaceHtml( content, "\\{(\\w+)\\}", map );
	}

	private void saveApplyCollateralContractAttachment( InetRptTemplateEnum inetRptTemplateEnum, Long signingContractId,
														IdentityInfoResultBean currentIdentityInfo )
	{
		PackagedInetResBean inetResBean = inetService.getPdf( inetRptTemplateEnum.getContext(), InetReturnTypeEnum.PDF.getContext(),
															  Collections.singletonList( signingContractId ) );

		ApplyCollateralContractAttachmentCreatedParamBean paramBean = new ApplyCollateralContractAttachmentCreatedParamBean();
		paramBean.setSigningContractId( signingContractId );
		paramBean.setValidatedIdentityId( currentIdentityInfo.getValidatedIdentityId() );
		paramBean.setTransmissionStatusCode( TransmissionStatusEnum.NO.getContext() );
		paramBean.setAttachmentType( AttachTypeEnum.OTHER.getContext() );
		paramBean.setFileName( getPdfFileNameByInetRptTemplate( inetRptTemplateEnum ) );
		paramBean.setInetRptName( inetRptTemplateEnum.getContext() );

		boolean isInetSuccess = InetResponseStatusEnum.SUCCESS.getCode().equals( inetResBean.getStat() );
		if( isInetSuccess )
		{
			paramBean.setFileContent( inetResBean.getPdfContent() );
			paramBean.setFileSize( ( long )inetResBean.getPdfContent().getLength() );
			paramBean.setInetResponseStatus( InetResponseStatusEnum.SUCCESS.getCode() );
		}
		else
			paramBean.setInetResponseStatus( InetResponseStatusEnum.FAIL.getCode() );

		applyCollateralContractAttachmentDAO.create( paramBean );
	}

	private EddaSenderResultBean sendEDDA( SigningContractFillInfoParamBean paramBean, ApplySigningContract contract,
										   SigningUserInfoBean borrowerInfo )
		throws Exception
	{
		EddaSenderResultBean result = new EddaSenderResultBean();

		try
		{
			// 如果有前一筆紀錄，先取消
			String contract_No = contract.getContractNo().substring( 0, 12 );

			ApplySigningEdda cancelApplySigningEDDA = signingEddaDAO.readCntrNoLastest( contract_No );
			if( cancelApplySigningEDDA != null )
			{
				EddaSenderArgBean cancelReqBean = new EddaSenderArgBean();
				cancelReqBean.setAdMark( "D" );
				cancelReqBean.setaID( cancelApplySigningEDDA.getAid() ); // 用戶身分證/統編
				cancelReqBean.setMegaMessageId( UUID.randomUUID().toString().replace( "-", "" ) );
				cancelReqBean.setpBank( "0170000" ); // 發動行代號
				cancelReqBean.setrBank( cancelApplySigningEDDA.getRbank() ); // 扣款行代號
				cancelReqBean.setRclNo( cancelApplySigningEDDA.getRclNo() ); // 授權扣款帳號 => 在銀行系統與訊息建置指引的26/170提到{採Socket 傳送若欄位不足位數時，右靠左補零。},
				cancelReqBean.setrID( cancelApplySigningEDDA.getRid() );
				cancelReqBean.setpBankNote( cancelApplySigningEDDA.getPbankNote() ); // 發動行專區 AN20
				cancelReqBean.setUserNo( cancelApplySigningEDDA.getUserNo() ); // 用戶號碼
				cancelReqBean.setSigningContractId( cancelApplySigningEDDA.getApplySigningContract().getSigningContractId() );
				cancelReqBean.setContractNo( cancelApplySigningEDDA.getApplySigningContract().getContractNo() );
				cancelReqBean.setIsNeedACH( paramBean.getIsNeedACH() );

				result = eDDASenderservice.send( cancelReqBean );
				if( !result.getRc().equals( "A0" ) && !result.getRc().equals( "A5" ) )
					throw new Exception( cancelReqBean.toString() + result.toString() );
				else
					signingEddaDAO.discard( cancelApplySigningEDDA.getEddaId(),
											cancelApplySigningEDDA.getApplySigningContract().getSigningContractId() );
			}

			// 預設帶空白欄位，mapper會變成Null，於EddaSenderClient.transArg2Req加工處理
			EddaSenderArgBean ReqBean = new EddaSenderArgBean();
			if( paramBean.getAdMark() == null || paramBean.getAdMark().equals( "A" ) )
				ReqBean.setAdMark( "A" ); // 交易類型 {A:新增, M:變更, D:取消(取消授權免傳卡片資料), I:確認}
			else
				ReqBean.setAdMark( paramBean.getAdMark() ); // 交易類型 {A:新增, M:變更, D:取消(取消授權免傳卡片資料), I:確認}
			ReqBean.setaID( borrowerInfo.getIdNo() ); // 用戶身分證/統編
			// ReqBean.setCertType( "K" ); // 憑證類別{K:連線單位驗證}
			// ReqBean.setExpiryDate( "********" ); // 授權扣款終止日
			ReqBean.setMegaMessageId( UUID.randomUUID().toString().replace( "-", "" ) );
			// ReqBean.setMegaSystemId( "PLOAN" );
			// ReqBean.setNote( " " );// 發動者專區 ,CHAR(40) 之前只有 CHAR(20)
			ReqBean.setpBank( "0170000" ); // 發動行代號
			ReqBean.setpBankNote( "CHANGE-BR:" + contract.getContractNo().substring( 0, 3 ) + "0TO0000" ); // 發動行專區 AN20
			ReqBean.setrBank( paramBean.getBankAcctCode() + "0000" ); // 扣款行代號
			ReqBean.setRclNo( paramBean.getBankAcctNo() ); // 授權扣款帳號 => 在銀行系統與訊息建置指引的26/170提到{採Socket 傳送若欄位不足位數時，右靠左補零。},
			// WebService版可以比照？
			ReqBean.setrID( borrowerInfo.getIdNo() );
			// ReqBean.setTaskId( "D409" );
			// ReqBean.setTix( "824" ); // {803:消費貸款 , 824:消費貸款}
			ReqBean.setUserNo( contract_No ); // 用戶號碼
			// ReqBean.setReservedField( "0000" );
			ReqBean.setSigningContractId( contract.getSigningContractId() );
			ReqBean.setContractNo( contract.getContractNo() );

			ReqBean.setIsNeedACH( paramBean.getIsNeedACH() );

			// 新增edda查詢紀錄
			Long eddaId = signingEddaDAO.create( signingEddaDAO.mapCreate( ReqBean ) );

			result = eDDASenderservice.send( ReqBean );

			// 回存edda結果
			SigningEddaUpdateParamBean updateParam = signingEddaDAO.mapUpdate( result );
			updateParam.setEddaId( eddaId );
			updateParam.setSigningContractId( ReqBean.getSigningContractId() );
			signingEddaDAO.update( updateParam );

			if( !result.getRc().equals( "A0" ) )
				throw new Exception( ReqBean.toString() + result.toString() );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "連結eDDA 系統錯誤" );
			throw new MyRuntimeException( ApplyErrorEnum.eDDA_Bank_Check_Error );
		}

		return result;
	}

	private void setGuaranteePlan( SigningContractBasicResBean resBean, ApplySigningContract contract, String userSubType )
	{
		if( UserSubTypeEnum.N_GUARANTOR.getContext().equals( userSubType ) )
		{
			// 一般保證人
			resBean.setGuaranteePlan( contract.getGeneralGuaranteePlan() );
			resBean.setGuaranteeDesc( contract.getGeneralGuaranteePlanInfo() );
		}
		else
		{
			// 連帶保證人
			resBean.setGuaranteePlan( contract.getJointGuaranteePlan() );
			resBean.setGuaranteeDesc( contract.getJointGuaranteePlanInfo() );
		}

	}

	private void setHouseSigningContractExpireInfo( ApplyHouseSigningContract houseSigningContract, Date appropriationDate, int loanPeriod,
													CodeProdKind prodKind )
	{
		SimpleDateFormat dateFormat = new SimpleDateFormat( "yyyy-MM-dd" );

		String[] start = dateFormat.format( appropriationDate ).split( "-" );
		Integer rocStartYear = Integer.valueOf( start[ 0 ] ) - 1911;
		Integer startMonth = Integer.valueOf( start[ 1 ] );
		Integer startDay = Integer.valueOf( start[ 2 ] );

		Date expireDate = CommonDateUtils.addMonth( appropriationDate, loanPeriod );
		// 中短期循環型及一年期（無論是否為循環型），貸款迄日須減一天
		if( "short-midterm".equals( prodKind.getCaseType() ) || loanPeriod == 12 )
			expireDate = DateUtils.addDays( expireDate, -1 );

		String[] expire = dateFormat.format( expireDate ).split( "-" );
		Integer rocExpireYear = Integer.valueOf( expire[ 0 ] ) - 1911;
		Integer expireMonth = Integer.valueOf( expire[ 1 ] );
		Integer expireDay = Integer.valueOf( expire[ 2 ] );

		switch( houseSigningContract.getExpireInfoType().toBigIntegerExact().intValue() )
		{
			case 1:
				houseSigningContract.setStartYear1( BigDecimal.valueOf( rocStartYear.intValue() ) );
				houseSigningContract.setStartMonth1( BigDecimal.valueOf( startMonth.intValue() ) );
				houseSigningContract.setStartDay1( BigDecimal.valueOf( startDay.intValue() ) );
				houseSigningContract.setExpireYear1( BigDecimal.valueOf( rocExpireYear.intValue() ) );
				houseSigningContract.setExpireMonth1( BigDecimal.valueOf( expireMonth.intValue() ) );
				houseSigningContract.setExpireDay1( BigDecimal.valueOf( expireDay.intValue() ) );
				break;
			case 2:
				houseSigningContract.setStartYear2( BigDecimal.valueOf( rocStartYear.intValue() ) );
				houseSigningContract.setStartMonth2( BigDecimal.valueOf( startMonth.intValue() ) );
				houseSigningContract.setStartDay2( BigDecimal.valueOf( startDay.intValue() ) );
				houseSigningContract.setExpireYear2( BigDecimal.valueOf( rocExpireYear.intValue() ) );
				houseSigningContract.setExpireMonth2( BigDecimal.valueOf( expireMonth.intValue() ) );
				houseSigningContract.setExpireDay2( BigDecimal.valueOf( expireDay.intValue() ) );
				break;
			case 3:
				houseSigningContract.setStartYear3( BigDecimal.valueOf( rocStartYear.intValue() ) );
				houseSigningContract.setStartMonth3( BigDecimal.valueOf( startMonth.intValue() ) );
				houseSigningContract.setStartDay3( BigDecimal.valueOf( startDay.intValue() ) );
				houseSigningContract.setExpireYear3( BigDecimal.valueOf( rocExpireYear.intValue() ) );
				houseSigningContract.setExpireMonth3( BigDecimal.valueOf( expireMonth.intValue() ) );
				houseSigningContract.setExpireDay3( BigDecimal.valueOf( expireDay.intValue() ) );
				break;
			case 4:
				houseSigningContract.setExpireYear4( BigDecimal.valueOf( rocExpireYear.intValue() ) );
				houseSigningContract.setExpireMonth4( BigDecimal.valueOf( expireMonth.intValue() ) );
				houseSigningContract.setExpireDay4( BigDecimal.valueOf( expireDay.intValue() ) );
				break;
		}
	}

	private Map<String, Object> uncheckedBoxSetter( Map<String, Object> placeHolderMap, String termsCheckBoxName, int amount )
	{
		for( int i = 1; i <= amount; i++ )
			placeHolderMap.put( termsCheckBoxName + i, UNCHECKED_BOX_STRING );
		return placeHolderMap;
	}

	private void updateApplyCollateralContract( ApplySigningContract contract, SigningContractFillInfoParamBean paramBean )
	{
		boolean isHouseloan = isHouseloan( contract );
		boolean isApplyCollateralContractExist = Optional.ofNullable( contract.getApplyCollateralContract() ).isPresent();
		if( isHouseloan && isApplyCollateralContractExist )
			applyCollateralContractDAO.updateMortgageSettingDesc( contract.getSigningContractId(), paramBean.getMortgageSettingDesc() );
	}

	private void updateSigningUser( Long contractId, Long contractIdentityId, Long updatedIdentityId, String signingUserType,
									SigningContractFillInfoParamBean paramBean )
	{
		SigningUserUpdateParamBean updatedParamBean = new SigningUserUpdateParamBean();
		updatedParamBean.setContractId( contractId );
		updatedParamBean.setContractIdentityId( contractIdentityId );
		updatedParamBean.setUpdatedIdentityId( updatedIdentityId );
		updatedParamBean.setHasAgreedCrossSelling( paramBean.getHasAgreedCrossSelling() );
		updatedParamBean.setBorrowerOverdueInformMethod( paramBean.getBorrowerOverdueInformMethod() );

		if( UserTypeEnum.BORROWER.getContext().equals( signingUserType ) )
		{
			updatedParamBean.setContractNotificationCode( paramBean.getBorrowerContractSendingMethodCode() );

			if( StringUtils.isNotBlank( paramBean.getBorrowerAddressTownCode() ) )
			{
				updatedParamBean.setTownCode( paramBean.getBorrowerAddressTownCode() );
				updatedParamBean.setStreet( paramBean.getBorrowerAddressStreet() );
			}
		}
		else
		{
			updatedParamBean.setContractNotificationCode( paramBean.getGuarantorContractSendingMethodCode() );

			if( StringUtils.isNotBlank( paramBean.getGuarantorAddressTownCode() ) )
			{
				updatedParamBean.setTownCode( paramBean.getGuarantorAddressTownCode() );
				updatedParamBean.setStreet( paramBean.getGuarantorAddressStreet() );
			}
		}

		signingUserDAO.update( updatedParamBean );
	}
}
