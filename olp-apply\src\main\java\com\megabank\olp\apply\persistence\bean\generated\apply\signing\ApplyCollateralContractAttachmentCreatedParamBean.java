package com.megabank.olp.apply.persistence.bean.generated.apply.signing;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

public class ApplyCollateralContractAttachmentCreatedParamBean extends BaseBean
{
	private Long signingContractId;

	private Long validatedIdentityId;

	private String transmissionStatusCode;

	private String attachmentType;

	private String fileName;

	private Long fileSize;

	private transient ImmutableByteArray fileContent;

	private transient ImmutableByteArray compressFileContent;

	private Integer inetResponseStatus;

	private String inetRptName;

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public ImmutableByteArray getCompressFileContent()
	{
		return compressFileContent;
	}

	public ImmutableByteArray getFileContent()
	{
		return fileContent;
	}

	public String getFileName()
	{
		return fileName;
	}

	public Long getFileSize()
	{
		return fileSize;
	}

	public Integer getInetResponseStatus()
	{
		return inetResponseStatus;
	}

	public String getInetRptName()
	{
		return inetRptName;
	}

	public Long getSigningContractId()
	{
		return signingContractId;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setCompressFileContent( ImmutableByteArray compressFileContent )
	{
		this.compressFileContent = compressFileContent;
	}

	public void setFileContent( ImmutableByteArray fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setFileSize( Long fileSize )
	{
		this.fileSize = fileSize;
	}

	public void setInetResponseStatus( Integer inetResponseStatus )
	{
		this.inetResponseStatus = inetResponseStatus;
	}

	public void setInetRptName( String inetRptName )
	{
		this.inetRptName = inetRptName;
	}

	public void setSigningContractId( Long signingContractId )
	{
		this.signingContractId = signingContractId;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}
}
