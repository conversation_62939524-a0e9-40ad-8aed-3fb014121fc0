/**
 *
 */
package com.megabank.olp.api.service.btt;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.management.apply.ApplyCustInfoClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyCustInfoArgBean;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyCustInfoResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
public class ApplyService extends BaseApiService
{
	@Autowired
	private ApplyCustInfoClient applyCustInfoClient;

	public ApplyCustInfoResultBean getApplyCustInfo( String caseNo )
	{
		ApplyCustInfoArgBean argBean = new ApplyCustInfoArgBean();
		argBean.setCaseNo( caseNo );

		return applyCustInfoClient.send( argBean, new JwtArgBean() );
	}

}
