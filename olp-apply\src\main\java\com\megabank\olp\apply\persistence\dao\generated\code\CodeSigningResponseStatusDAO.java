package com.megabank.olp.apply.persistence.dao.generated.code;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeSigningResponseStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeSigningResponseStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeSigningResponseStatusDAO extends BasePojoDAO<CodeSigningResponseStatus, String>
{
	@Override
	protected Class<CodeSigningResponseStatus> getPojoClass()
	{
		return CodeSigningResponseStatus.class;
	}
}
