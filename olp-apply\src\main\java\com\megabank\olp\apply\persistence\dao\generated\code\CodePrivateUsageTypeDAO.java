package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodePrivateUsageType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodePrivateUsageTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodePrivateUsageTypeDAO extends BasePojoDAO<CodePrivateUsageType, String>
{
	public CodePrivateUsageType read( String privateUsageType )
	{
		Validate.notBlank( privateUsageType );

		return getPojoByPK( privateUsageType, CodePrivateUsageType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodePrivateUsageType> getPojoClass()
	{
		return CodePrivateUsageType.class;
	}
}
