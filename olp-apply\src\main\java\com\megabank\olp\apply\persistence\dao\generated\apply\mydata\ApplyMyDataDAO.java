package com.megabank.olp.apply.persistence.dao.generated.apply.mydata;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.mydata.ApplyMyDataCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeMyDataStatusDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.pojo.apply.mydata.ApplyMyData;
import com.megabank.olp.apply.utility.enums.MyDataStatusEnum;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyMyDataDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyMyDataDAO extends BasePojoDAO<ApplyMyData, Long>
{

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private CodeMyDataStatusDAO codeMyDataStatusDAO;

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	public Long create( ApplyMyDataCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getLoanId() );
		Validate.notBlank( paramBean.getServiceId() );
		Validate.notBlank( paramBean.getTxId() );
		Validate.notBlank( paramBean.getMyDataStatusCode() );
		Validate.notBlank( paramBean.getTransmissionStatusCode() );

		ApplyMyData pojo = new ApplyMyData();
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setServiceId( paramBean.getServiceId() );
		pojo.setTxid( paramBean.getTxId() );
		pojo.setApplyLoan( applyLoanDAO.read( paramBean.getLoanId() ) );
		pojo.setCodeMyDataStatus( codeMyDataStatusDAO.read( paramBean.getMyDataStatusCode() ) );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( paramBean.getTransmissionStatusCode() ) );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public ApplyMyData getPojoByIds( Long loanId, String txId )
	{
		Validate.notNull( loanId );
		Validate.notBlank( txId );

		NameValueBean txid = new NameValueBean( ApplyMyData.TXID_CONSTANT, txId );
		NameValueBean applyLoan = new NameValueBean( ApplyMyData.APPLY_LOAN_CONSTANT, applyLoanDAO.read( loanId ) );
		NameValueBean[] conditions = new NameValueBean[]{ txid, applyLoan };

		return getUniquePojoByProperties( conditions, ApplyMyData.TABLENAME_CONSTANT );
	}

	public ApplyMyData getPojoByTxId( String txId )
	{
		Validate.notBlank( txId );

		NameValueBean condition = new NameValueBean( ApplyMyData.TXID_CONSTANT, txId );

		return getUniquePojoByProperty( condition, ApplyMyData.TABLENAME_CONSTANT );
	}

	public List<ApplyMyData> getPojosByMyDataStatus( Long loanId, List<String> myDataStatusCodes )
	{
		return getPojosByStatus( loanId, null, myDataStatusCodes );
	}

	public List<ApplyMyData> getPojosByMyDataStatus( String myDataStatusCodes )
	{
		Validate.notBlank( myDataStatusCodes );

		NameValueBean condition = new NameValueBean( ApplyMyData.CODE_MY_DATA_STATUS_CONSTANT, codeMyDataStatusDAO.read( myDataStatusCodes ) );

		return getPojosByProperty( condition );
	}

	public List<ApplyMyData> getPojosByStatus( Long loanId, String transmissionStatusCode, List<String> myDataStatusCodes )
	{
		Validate.notEmpty( myDataStatusCodes );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "mydata.getPojosByStatus" );
		nativeQuery.setParameter( "loanId", loanId, Long.class );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );
		nativeQuery.setParameterList( "myDataStatusCode", myDataStatusCodes, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyMyData.class );

		return nativeQuery.getResultList();
	}
	
	public boolean isMydataToday( String idNo )
	{
		Validate.notNull( idNo );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "mydata.isMydataToday" );
		nativeQuery.setParameter( "idNo", idNo, String.class );
		nativeQuery.setParameter( "myDataStatusCode", MyDataStatusEnum.WAITING_NOTIFICATION.getContext(), String.class );
		nativeQuery.setParameter( "currentTime", new Date() );

		return !nativeQuery.getResultList().isEmpty();
	}

	public List<ApplyMyData> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NameValueBean condition = new NameValueBean( ApplyMyData.CODE_TRANSMISSION_STATUS_CONSTANT,
													 codeTransmissionStatusDAO.read( transmissionStatusCode ) );

		return getPojosByProperty( condition );
	}

	public ApplyMyData read( Long myDataId )
	{
		Validate.notNull( myDataId );

		return getPojoByPK( myDataId, ApplyMyData.TABLENAME_CONSTANT );
	}

	public ApplyMyData updateErrorCode( Long myDataId, String errorCode )
	{
		Validate.notNull( myDataId );

		ApplyMyData pojo = read( myDataId );
		pojo.setErrorCode( errorCode );
		pojo.setUpdatedDate( new Date() );

		return pojo;
	}

	public Long updateMyDataStatus( Long myDataId, String myDataStatusCode )
	{
		Validate.notNull( myDataId );
		Validate.notBlank( myDataStatusCode );

		ApplyMyData pojo = read( myDataId );
		pojo.setCodeMyDataStatus( codeMyDataStatusDAO.read( myDataStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getMyDataId();
	}

	public Long updateNotified( Long myDataId )
	{
		Validate.notNull( myDataId );

		ApplyMyData pojo = read( myDataId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getMyDataId();
	}

	public Long updateTransmissionStatus( Long myDataId, String transmissionStatusCode )
	{
		Validate.notNull( myDataId );
		Validate.notBlank( transmissionStatusCode );

		ApplyMyData pojo = read( myDataId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getMyDataId();
	}

	@Override
	protected Class<ApplyMyData> getPojoClass()
	{
		return ApplyMyData.class;
	}
}
