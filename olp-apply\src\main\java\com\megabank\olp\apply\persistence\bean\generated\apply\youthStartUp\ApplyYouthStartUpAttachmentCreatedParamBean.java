package com.megabank.olp.apply.persistence.bean.generated.apply.youthStartUp;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

public class ApplyYouthStartUpAttachmentCreatedParamBean extends BaseBean
{
	private Long youthStartUpId;

	private Long validatedIdentityId;

	private String attachmentType;

	private String fileName;

	private Long fileSize;

	private transient ImmutableByteArray fileContent;

	private transient ImmutableByteArray compressFileContent;

	private String transmissionStatusCode;

	public ApplyYouthStartUpAttachmentCreatedParamBean()
	{}

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public ImmutableByteArray getCompressFileContent()
	{
		return compressFileContent;
	}

	public ImmutableByteArray getFileContent()
	{
		return fileContent;
	}

	public String getFileName()
	{
		return fileName;
	}

	public Long getFileSize()
	{
		return fileSize;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public Long getYouthStartUpId()
	{
		return youthStartUpId;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setCompressFileContent( ImmutableByteArray compressFileContent )
	{
		this.compressFileContent = compressFileContent;
	}

	public void setFileContent( ImmutableByteArray fileContent )
	{
		this.fileContent = fileContent;
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setFileSize( Long fileSize )
	{
		this.fileSize = fileSize;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

	public void setYouthStartUpId( Long youthStartUpId )
	{
		this.youthStartUpId = youthStartUpId;
	}
}