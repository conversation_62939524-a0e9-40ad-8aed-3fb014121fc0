package com.megabank.olp.apply.controller.management.bean.signing;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class ContractCtrTypeCMgmtArgBean extends BaseBean
{
	@NotBlank
	private String loanType;

	@NotBlank
	private String contractNo;

	private String bankAcctCode;

	private String bankAcctNo;

	@NotBlank
	private String borrowerIPAddr;

	@NotBlank
	private String borrowerIdentityType;

	@NotNull
	private Date loanBeginDate;

	@NotNull
	private Date loanEndDate;

	@NotBlank
	private String rateAdjustInformMethod;  // {1:簡訊, 2:書面, 3:電子郵件, 9:不通知} 在 L517 需另增加{4:網路銀行登入, 5:存摺登錄}

	@NotNull
	private Long borrowerSingingDate;

	@NotNull
	private Date contractCheckDate;

	private String borrowerAgreeCrossSelling;

	@NotBlank
	private String branchCode;

	@NotBlank
	private String borrowerId;

	@NotNull
	private Date borrowerBirthDate;

	@NotBlank
	private String borrowerName;

	@NotBlank
	private String borrowerMobileNumber;

	@NotBlank
	private String borrowerEmail;

	private String productCode;

	private String contractVersion;

	private BigDecimal loanAmt;

	@NotNull
	private Integer loanPeriod;

	private String drawDownType;

	private Integer oneTimeFee;

	private Integer preliminaryFee;

	private Integer creditCheckFee;

	private String repaymentMethod;

	private String advancedRateDesc;

	private BigDecimal advancedAPR;

	private String limitedRateDesc;

	private BigDecimal limitedAPR;

	private String showOption; // 呈現給客戶看的方案{1: 無限制清償, 2:限制清償}

	private String courtName;

	@NotBlank
	private String file;

	public ContractCtrTypeCMgmtArgBean()
	{}

	public BigDecimal getAdvancedAPR()
	{
		return advancedAPR;
	}

	public String getAdvancedRateDesc()
	{
		return advancedRateDesc;
	}

	public String getBankAcctCode()
	{
		return bankAcctCode;
	}

	public String getBankAcctNo()
	{
		return bankAcctNo;
	}

	public String getBorrowerAgreeCrossSelling()
	{
		return borrowerAgreeCrossSelling;
	}

	public Date getBorrowerBirthDate()
	{
		return borrowerBirthDate;
	}

	public String getBorrowerEmail()
	{
		return borrowerEmail;
	}

	public String getBorrowerId()
	{
		return borrowerId;
	}

	public String getBorrowerIdentityType()
	{
		return borrowerIdentityType;
	}

	public String getBorrowerIPAddr()
	{
		return borrowerIPAddr;
	}

	public String getBorrowerMobileNumber()
	{
		return borrowerMobileNumber;
	}

	public String getBorrowerName()
	{
		return borrowerName;
	}

	public Long getBorrowerSingingDate()
	{
		return borrowerSingingDate;
	}

	public String getBranchCode()
	{
		return branchCode;
	}

	public Date getContractCheckDate()
	{
		return contractCheckDate;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getContractVersion()
	{
		return contractVersion;
	}

	public String getCourtName()
	{
		return courtName;
	}

	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	public String getDrawDownType()
	{
		return drawDownType;
	}

	public String getFile()
	{
		return file;
	}

	public BigDecimal getLimitedAPR()
	{
		return limitedAPR;
	}

	public String getLimitedRateDesc()
	{
		return limitedRateDesc;
	}

	public BigDecimal getLoanAmt()
	{
		return loanAmt;
	}

	public Date getLoanBeginDate()
	{
		return loanBeginDate;
	}

	public Date getLoanEndDate()
	{
		return loanEndDate;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public String getProductCode()
	{
		return productCode;
	}

	public String getRateAdjustInformMethod()
	{
		return rateAdjustInformMethod;
	}

	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	public String getShowOption()
	{
		return showOption;
	}

	public void setAdvancedAPR( BigDecimal advancedAPR )
	{
		this.advancedAPR = advancedAPR;
	}

	public void setAdvancedRateDesc( String advancedRateDesc )
	{
		this.advancedRateDesc = advancedRateDesc;
	}

	public void setBankAcctCode( String bankAcctCode )
	{
		this.bankAcctCode = bankAcctCode;
	}

	public void setBankAcctNo( String bankAcctNo )
	{
		this.bankAcctNo = bankAcctNo;
	}

	public void setBorrowerAgreeCrossSelling( String borrowerAgreeCrossSelling )
	{
		this.borrowerAgreeCrossSelling = borrowerAgreeCrossSelling;
	}

	public void setBorrowerBirthDate( Date borrowerBirthDate )
	{
		this.borrowerBirthDate = borrowerBirthDate;
	}

	public void setBorrowerEmail( String borrowerEmail )
	{
		this.borrowerEmail = borrowerEmail;
	}

	public void setBorrowerId( String borrowerId )
	{
		this.borrowerId = borrowerId;
	}

	public void setBorrowerIdentityType( String borrowerIdentityType )
	{
		this.borrowerIdentityType = borrowerIdentityType;
	}

	public void setBorrowerIPAddr( String borrowerIPAddr )
	{
		this.borrowerIPAddr = borrowerIPAddr;
	}

	public void setBorrowerMobileNumber( String borrowerMobileNumber )
	{
		this.borrowerMobileNumber = borrowerMobileNumber;
	}

	public void setBorrowerName( String borrowerName )
	{
		this.borrowerName = borrowerName;
	}

	public void setBorrowerSingingDate( Long borrowerSingingDate )
	{
		this.borrowerSingingDate = borrowerSingingDate;
	}

	public void setBranchCode( String branchCode )
	{
		this.branchCode = branchCode;
	}

	public void setContractCheckDate( Date contractCheckDate )
	{
		this.contractCheckDate = contractCheckDate;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setContractVersion( String contractVersion )
	{
		this.contractVersion = contractVersion;
	}

	public void setCourtName( String courtName )
	{
		this.courtName = courtName;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public void setFile( String file )
	{
		this.file = file;
	}

	public void setLimitedAPR( BigDecimal limitedAPR )
	{
		this.limitedAPR = limitedAPR;
	}

	public void setLimitedRateDesc( String limitedRateDesc )
	{
		this.limitedRateDesc = limitedRateDesc;
	}

	public void setLoanAmt( BigDecimal loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanBeginDate( Date loanBeginDate )
	{
		this.loanBeginDate = loanBeginDate;
	}

	public void setLoanEndDate( Date loanEndDate )
	{
		this.loanEndDate = loanEndDate;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public void setProductCode( String productCode )
	{
		this.productCode = productCode;
	}

	public void setRateAdjustInformMethod( String rateAdjustInformMethod )
	{
		this.rateAdjustInformMethod = rateAdjustInformMethod;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}

	public void setShowOption( String showOption )
	{
		this.showOption = showOption;
	}

}
