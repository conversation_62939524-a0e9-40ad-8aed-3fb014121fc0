package com.megabank.olp.apply.service.mydata;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.mydata.bean.MyDataUrlResBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.enums.RecipientSystemEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class MyDataServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;
	
	@Autowired
	private MyDataService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void checkMyDataAuthSucceeded()
	{
		Long loanId = 1L;
		String txId = "7b8ce48d-390c-41d6-84e9-5b48e64abe4a";

		boolean result = service.checkMyDataAuthSucceeded( loanId, txId );

		logger.info( "result:{}", result );
	}

	@Test
	public void checkMyDataStatus()
	{
		service.checkMyDataStatus();

	}

	@Test
	public void getRedirectUrl()
	{
		Long loanId = 1L;

		MyDataUrlResBean resBean = service.getRedirectUrl( loanId );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getUserData()
	{
		String txId = "69fa89d3-13c0-4007-9d19-cfe578b08d45";

		service.getUserData( txId );
	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	@Test
	public void notifyServlet()
	{
		String txId = "72b9963f-1e5f-4ce2-afd9-f8ea7406226c";
		String idNo = "A123456789";
		int waitSec = 30;

		service.notifyServlet( txId, idNo, waitSec );
	}

	@Test
	public void retrySubmitMyData()
	{
		service.retryDeliverMyData( RecipientSystemEnum.ELOAN.getSystemId() );
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A123456789";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.SKIP.getContext(), IdentityTypeEnum.OTHER_BANK.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}

}
