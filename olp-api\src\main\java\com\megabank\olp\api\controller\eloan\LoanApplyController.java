/**
 *
 */
package com.megabank.olp.api.controller.eloan;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.controller.eloan.bean.apply.BranchUpdatedArgBean;
import com.megabank.olp.api.controller.eloan.bean.apply.LoanDiscardedArgBean;
import com.megabank.olp.api.service.eloan.LoanApplyService;
import com.megabank.olp.api.utility.BaseELoanAPIController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "eloan/apply" )
public class LoanApplyController extends BaseELoanAPIController
{
	@Autowired
	private LoanApplyService service;

	@PostMapping( "discardLoan" )
	public Map<String, Object> discardLoan( @RequestHeader( value = "AccessToken" ) String accessToken,
											@RequestBody @Validated LoanDiscardedArgBean argBean )
	{
		validAuth( accessToken );

		service.discardLoan( argBean.getCaseNo() );

		return getResponseMap();
	}

	@PostMapping( "updateBranch" )
	public Map<String, Object> updateBranch( @RequestHeader( value = "AccessToken" ) String accessToken,
											 @RequestBody @Validated BranchUpdatedArgBean argBean )
	{
		validAuth( accessToken );

		service.updateBranch( argBean.getCaseNo(), argBean.getBranchCode() );

		return getResponseMap();
	}

}
