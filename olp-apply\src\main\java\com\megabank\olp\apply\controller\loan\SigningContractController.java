/**
 *
 */
package com.megabank.olp.apply.controller.loan;

import java.io.IOException;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.loan.bean.apply.SigningContractProductCodeBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractAgreementArgBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractArgBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractFillInfoArgBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractProdKindArgBean;
import com.megabank.olp.apply.controller.loan.bean.signing.SigningContractUpdateInfoArgBean;
import com.megabank.olp.apply.service.loan.DownloadService;
import com.megabank.olp.apply.service.loan.SigningContractService;
import com.megabank.olp.apply.service.loan.bean.apply.SigningContractFillInfoParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/signingcontract" )
public class SigningContractController extends BaseController
{
	@Autowired
	private SigningContractService signingContractService;

	@Autowired
	private DownloadService downloadService;

	/**
	 * 取得對保契約PDF
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "downloadSigningContractPdf" )
	public Map<String, Object> downloadSigningContractPdf( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();

		return getResponseMap( downloadService.downloadSigningContractPdf( contractNo ) );
	}

	/**
	 * 取得同意事項資料
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getAgreement" )
	public Map<String, Object> getAgreement( @RequestBody @Validated SigningContractAgreementArgBean argBean )
	{
		String contractNo = argBean.getContractNo();
		String serviceType = argBean.getServiceType();

		return getResponseMap( signingContractService.getAgreement( contractNo, serviceType ) );
	}

	/**
	 * 取得撥款日清單
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getAppropirationDateListByContractNo" )
	public Map<String, Object> getAppropirationDateListByContractNo( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();

		return getResponseMap( signingContractService.getAppropirationDateListByContractNo( contractNo ) );
	}

	/**
	 * 取得銀行帳戶
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getBankAccount" )
	public Map<String, Object> getBankAccount( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();

		return getResponseMap( signingContractService.getBankAccount( contractNo ) );
	}

	/**
	 * 取得銀行帳戶
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getOtherBankAccount" )
	public Map<String, Object> getOtherBankAccount( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();
		return getResponseMap( signingContractService.getOtherBankAccount( contractNo ) );
	}

	/**
	 * 取得代償同意事項資料
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getRepaymentAgreement" )
	public Map<String, Object> getRepaymentAgreement( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();
		return getResponseMap( signingContractService.getRepaymentAgreement( contractNo ) );
	}

	/**
	 * 取得代償資料
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getRepaymentInfo" )
	public Map<String, Object> getRepaymentInfo( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();
		return getResponseMap( signingContractService.getRepaymentInfo( contractNo ) );
	}

	/**
	 * 取得對保案件基本貸款資料
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getSigningContractBasicInfo" )
	public Map<String, Object> getSigningContractBasicInfo( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();

		return getResponseMap( signingContractService.getSigningContractBasicInfo( contractNo ) );
	}

	/**
	 * 取得契約清單
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getSigningContracts" )
	public Map<String, Object> getSigningContracts( @RequestBody SigningContractProductCodeBean signingContractProductCodeBean )
	{
		return getResponseMap( signingContractService.getSigningContracts( signingContractProductCodeBean.getProductCode() ) );
	}

	/**
	 * 取得對保完成感謝頁內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getThankyouMessage" )
	public Map<String, Object> getThankyouMessage( @RequestBody @Validated SigningContractArgBean argBean )
	{
		String contractNo = argBean.getContractNo();

		return getResponseMap( signingContractService.getThankyouMessage( contractNo ) );
	}

	/**
	 * 儲存驗證成功之email
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "saveVerifiedEmail" )
	public Map<String, Object> saveVerifiedEmail( @RequestBody @Validated SigningContractUpdateInfoArgBean argBean )
	{
		return getResponseMap( signingContractService.saveVerifiedEmail( argBean ) );
	}

	/**
	 * 提交對保契約
	 *
	 * @param argBean
	 * @return
	 * @throws IOException
	 */
	@PostMapping( "submitContract" )
	public Map<String, Object> submitContract( @RequestBody @Validated SigningContractFillInfoArgBean argBean ) throws Exception
	{
		String result = signingContractService.saveContract( mapSigningContractFillInfoParamBean( argBean ) );

		signingContractService.createApplyCollateralContractAttachments( argBean );
		signingContractService.saveSigningContractPdf( argBean );

		return getResponseMap( result );
	}

	private SigningContractFillInfoParamBean mapSigningContractFillInfoParamBean( SigningContractFillInfoArgBean argBean )
	{
		SigningContractFillInfoParamBean paramBean = new SigningContractFillInfoParamBean();
		paramBean.setAppropriationDate( argBean.getAppropriationDate() );
		paramBean.setBankAcctCode( argBean.getBankAcctCode() );
		paramBean.setBankAcctNo( argBean.getBankAcctNo() );
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setBorrowerAddressTownCode( argBean.getBorrowerAddressTownCode() );
		paramBean.setBorrowerAddressStreet( argBean.getBorrowerAddressStreet() );
		paramBean.setGuarantorAddressTownCode( argBean.getGuarantorAddressTownCode() );
		paramBean.setGuarantorAddressStreet( argBean.getGuarantorAddressStreet() );
		paramBean.setRepayment( argBean.getRepayment() );
		paramBean.setFirstPaymentDate( argBean.getFirstPaymentDate() );
		paramBean.setBorrowerContractSendingMethodCode( argBean.getBorrowerContractSendingMethodCode() );
		paramBean.setGuarantorContractSendingMethodCode( argBean.getGuarantorContractSendingMethodCode() );
		paramBean.setRateAdjustInformMethodCode( argBean.getRateAdjustInformMethodCode() );
		paramBean.setContractCheckDate( argBean.getContractCheckDate() );
		paramBean.setHasAgreedCrossSelling( argBean.getHasAgreedCrossSelling() );
		paramBean.setIsNeedACH( argBean.getIsNeedACH() );
		paramBean.setBorrowerOverdueInformMethod( argBean.getBorrowerOverdueInformMethod() );
		paramBean.setMortgageSettingDesc( argBean.getMortgageSettingDesc() );
		if( argBean.getIsNeedACH() != null && argBean.getIsNeedACH() )
		{
			paramBean.setBankACHAcctCode( argBean.getBankAcctCode() );
			paramBean.setBankACHAcctNo( argBean.getBankAcctNo() );
		}

		return paramBean;
	}

	/**
	 * 取得產品種類
	 *
	 * @return
	 */
	@PostMapping( "getProdKindByCaseType" )
	public Map<String, Object> getProdKindByCaseType( @RequestBody SigningContractProdKindArgBean argBean )
	{
		return getResponseMap( signingContractService.getProdKindByCaseType( argBean.getCaseType() ) );
	}
}
