package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanBasicUpdatedEngNameParamBean extends BaseBean
{
	private Long loanId;

	private String engNameFg;

	private String engName;

	public ApplyLoanBasicUpdatedEngNameParamBean()
	{
		// default constructor
	}

	public String getEngName()
	{
		return engName;
	}

	public String getEngNameFg()
	{
		return engNameFg;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public void setEngName( String engName )
	{
		this.engName = engName;
	}

	public void setEngNameFg( String engNameFg )
	{
		this.engNameFg = engNameFg;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

}
