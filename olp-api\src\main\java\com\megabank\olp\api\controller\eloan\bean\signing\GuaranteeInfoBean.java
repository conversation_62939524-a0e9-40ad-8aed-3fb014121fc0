/**
 *
 */
package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class GuaranteeInfoBean extends BaseBean
{
	@NotBlank
	private String generalGuaranteePlan;

	@NotNull
	private List<String> generalGuaranteePlanInfo = new ArrayList<>();

	@NotBlank
	private String jointGuaranteePlan;

	@NotNull
	private List<String> jointGuaranteePlanInfo = new ArrayList<>();

	private BigDecimal guaranteeAmt;

	public GuaranteeInfoBean()
	{}

	public String getGeneralGuaranteePlan()
	{
		return generalGuaranteePlan;
	}

	public List<String> getGeneralGuaranteePlanInfo()
	{
		return generalGuaranteePlanInfo;
	}

	public BigDecimal getGuaranteeAmt()
	{
		return guaranteeAmt;
	}

	public String getJointGuaranteePlan()
	{
		return jointGuaranteePlan;
	}

	public List<String> getJointGuaranteePlanInfo()
	{
		return jointGuaranteePlanInfo;
	}

	public void setGeneralGuaranteePlan( String generalGuaranteePlan )
	{
		this.generalGuaranteePlan = generalGuaranteePlan;
	}

	public void setGeneralGuaranteePlanInfo( List<String> generalGuaranteePlanInfo )
	{
		this.generalGuaranteePlanInfo = generalGuaranteePlanInfo;
	}

	public void setGuaranteeAmt( BigDecimal guaranteeAmt )
	{
		this.guaranteeAmt = guaranteeAmt;
	}

	public void setJointGuaranteePlan( String jointGuaranteePlan )
	{
		this.jointGuaranteePlan = jointGuaranteePlan;
	}

	public void setJointGuaranteePlanInfo( List<String> jointGuaranteePlanInfo )
	{
		this.jointGuaranteePlanInfo = jointGuaranteePlanInfo;
	}

}
