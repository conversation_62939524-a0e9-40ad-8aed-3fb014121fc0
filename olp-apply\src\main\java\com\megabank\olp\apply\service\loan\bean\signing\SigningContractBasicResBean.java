/**
 *
 */
package com.megabank.olp.apply.service.loan.bean.signing;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractBasicResBean extends BaseBean
{
	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 身分證字號
	 */
	private String id;

	/**
	 * 契約類別
	 */
	private String contractType;

	/**
	 * 借款金額(萬)
	 */
	private Integer loanAmt;

	/**
	 * 借款期間(月)
	 */
	private Integer loanPeriod;

	/**
	 * 貸款用途
	 */
	private List<String> loanPurpose;

	/**
	 * 一次性費用
	 */
	private Integer oneTimeFee;

	/**
	 * 信用查詢費(元)
	 */
	private Integer creditCheckFee;

	/**
	 * 開辦手續費
	 */
	private Integer preliminaryFee;

	/**
	 * 動用方式
	 */
	private String drawDownType;

	/**
	 * 還款方式
	 */
	private String repaymentMethod;

	/**
	 * 借款方案
	 */
	private String lendingPlan;

	/**
	 * 方案描述清單
	 */
	@JsonProperty( "projects" )
	private List<ProjectDescBean> projectDescBeans;

	/**
	 * 保證人姓名
	 */
	private String guaranteeName;

	/**
	 * 保證人身分證字號
	 */
	private String guaranteeId;

	/**
	 * 保證人類別
	 */
	private String guaranteeType;

	/**
	 * 保證方式
	 */
	private String guaranteePlan;

	/**
	 * 保證方式內容
	 */
	private String guaranteeDesc;

	/**
	 * 保證主債務金額(萬元)
	 */
	private BigDecimal guaranteeAmt;

	/**
	 * 年費用百分率(%)
	 */
	private BigDecimal annualPercentageRate;

	/**
	 * 撥款資訊
	 */
	@JsonProperty( "appropriationInfo" )
	private AppropriationBean appropriationBean;

	/**
	 * 契約審閱日
	 */
	private String contractCheckDate;

	private String contractUrl;

	/**
	 * 行銷方案
	 */
	private String plan;

	private String payeeBankCode;

	private String payeeBankAccountNo;

	private String payeeBankAccountName;

	private Integer payeeTotalAmt;

	private Integer payeeRemittance;

	private Integer payeeSelfProvide;

	private String lnDate;

	private String prodKind;

	private boolean isSignatoryYouth;

	public SigningContractBasicResBean()
	{}

	public BigDecimal getAnnualPercentageRate()
	{
		return annualPercentageRate;
	}

	public AppropriationBean getAppropriationBean()
	{
		return appropriationBean;
	}

	public String getContractCheckDate()
	{
		return contractCheckDate;
	}

	public String getContractType()
	{
		return contractType;
	}

	public String getContractUrl()
	{
		return contractUrl;
	}

	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	public String getDrawDownType()
	{
		return drawDownType;
	}

	public BigDecimal getGuaranteeAmt()
	{
		return guaranteeAmt;
	}

	public String getGuaranteeDesc()
	{
		return guaranteeDesc;
	}

	public String getGuaranteeId()
	{
		return guaranteeId;
	}

	public String getGuaranteeName()
	{
		return guaranteeName;
	}

	public String getGuaranteePlan()
	{
		return guaranteePlan;
	}

	public String getGuaranteeType()
	{
		return guaranteeType;
	}

	public String getId()
	{
		return id;
	}

	public boolean getIsSignatoryYouth()
	{
		return isSignatoryYouth;
	}

	public String getLendingPlan()
	{
		return lendingPlan;
	}

	public String getLnDate()
	{
		return lnDate;
	}

	public Integer getLoanAmt()
	{
		return loanAmt;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public List<String> getLoanPurpose()
	{
		return loanPurpose;
	}

	public String getName()
	{
		return name;
	}

	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	public String getPayeeBankAccountName()
	{
		return payeeBankAccountName;
	}

	public String getPayeeBankAccountNo()
	{
		return payeeBankAccountNo;
	}

	public String getPayeeBankCode()
	{
		return payeeBankCode;
	}

	public Integer getPayeeRemittance()
	{
		return payeeRemittance;
	}

	public Integer getPayeeSelfProvide()
	{
		return payeeSelfProvide;
	}

	public Integer getPayeeTotalAmt()
	{
		return payeeTotalAmt;
	}

	public String getPlan()
	{
		return plan;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public String getProdKind()
	{
		return prodKind;
	}

	public List<ProjectDescBean> getProjectDescBeans()
	{
		return projectDescBeans;
	}

	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	public void setAnnualPercentageRate( BigDecimal annualPercentageRate )
	{
		this.annualPercentageRate = annualPercentageRate;
	}

	public void setAppropriationBean( AppropriationBean appropriationBean )
	{
		this.appropriationBean = appropriationBean;
	}

	public void setContractCheckDate( String contractCheckDate )
	{
		this.contractCheckDate = contractCheckDate;
	}

	public void setContractType( String contractType )
	{
		this.contractType = contractType;
	}

	public void setContractUrl( String contractUrl )
	{
		this.contractUrl = contractUrl;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public void setGuaranteeAmt( BigDecimal guaranteeAmt )
	{
		this.guaranteeAmt = guaranteeAmt;
	}

	public void setGuaranteeDesc( String guaranteeDesc )
	{
		this.guaranteeDesc = guaranteeDesc;
	}

	public void setGuaranteeId( String guaranteeId )
	{
		this.guaranteeId = guaranteeId;
	}

	public void setGuaranteeName( String guaranteeName )
	{
		this.guaranteeName = guaranteeName;
	}

	public void setGuaranteePlan( String guaranteePlan )
	{
		this.guaranteePlan = guaranteePlan;
	}

	public void setGuaranteeType( String guaranteeType )
	{
		this.guaranteeType = guaranteeType;
	}

	public void setId( String id )
	{
		this.id = id;
	}

	public void setIsSignatoryYouth( boolean isSignatoryYouth )
	{
		this.isSignatoryYouth = isSignatoryYouth;
	}

	public void setLendingPlan( String lendingPlan )
	{
		this.lendingPlan = lendingPlan;
	}

	public void setLnDate( String lnDate )
	{
		this.lnDate = lnDate;
	}

	public void setLoanAmt( Integer loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanPurpose( List<String> loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public void setPayeeBankAccountName( String payeeBankAccountName )
	{
		this.payeeBankAccountName = payeeBankAccountName;
	}

	public void setPayeeBankAccountNo( String payeeBankAccountNo )
	{
		this.payeeBankAccountNo = payeeBankAccountNo;
	}

	public void setPayeeBankCode( String payeeBankCode )
	{
		this.payeeBankCode = payeeBankCode;
	}

	public void setPayeeRemittance( Integer payeeRemittance )
	{
		this.payeeRemittance = payeeRemittance;
	}

	public void setPayeeSelfProvide( Integer payeeSelfProvide )
	{
		this.payeeSelfProvide = payeeSelfProvide;
	}

	public void setPayeeTotalAmt( Integer payeeTotalAmt )
	{
		this.payeeTotalAmt = payeeTotalAmt;
	}

	public void setPlan( String plan )
	{
		this.plan = plan;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public void setProdKind( String prodKind )
	{
		this.prodKind = prodKind;
	}

	public void setProjectDescBeans( List<ProjectDescBean> projectDescBeans )
	{
		this.projectDescBeans = projectDescBeans;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}
}
