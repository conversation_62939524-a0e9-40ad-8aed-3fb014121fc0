package com.megabank.olp.apply.service.loan.bean.signing;

import com.megabank.olp.apply.service.loan.bean.apply.AgreedBean;

public class ContractAgreementBean extends AgreedBean
{
	private boolean isGroup;

	private boolean isReadRequired;
	
	private boolean hasBlankToFillIn;

	public ContractAgreementBean()
	{
		// default constructor
	}

	public boolean getIsGroup()
	{
		return isGroup;
	}

	public void setIsGroup( boolean isGroup )
	{
		this.isGroup = isGroup;
	}

	public boolean getIsReadRequired()
	{
		return isReadRequired;
	}

	public void setIsReadRequired( boolean isReadRequired )
	{
		this.isReadRequired = isReadRequired;
	}

	public boolean getHasBlankToFillIn()
	{
		return hasBlankToFillIn;
	}

	public void setHasBlankToFillIn( boolean hasBlankToFillIn )
	{
		this.hasBlankToFillIn = hasBlankToFillIn;
	}
}
