package com.megabank.olp.apply.controller.management.bean.signing;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean;

public class PaymentInfoCreateArgBean extends BaseBean
{
	@NotBlank
	private String contractNo;

	@NotNull
	private Integer preliminaryFee;

	@NotNull
	@JsonProperty( "creditCheckFee" )
	private Integer crChkFee;

	@NotNull
	private List<PaymentInfoBean> paymentInfoList;

	public PaymentInfoCreateArgBean()
	{}

	public String getContractNo()
	{
		return contractNo;
	}

	public Integer getCrChkFee()
	{
		return crChkFee;
	}

	public List<PaymentInfoBean> getPaymentInfoList()
	{
		return paymentInfoList;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setCrChkFee( Integer crChkFee )
	{
		this.crChkFee = crChkFee;
	}

	public void setPaymentInfoList( List<PaymentInfoBean> paymentInfoList )
	{
		this.paymentInfoList = paymentInfoList;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}
}
