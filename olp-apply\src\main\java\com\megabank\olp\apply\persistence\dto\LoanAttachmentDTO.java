package com.megabank.olp.apply.persistence.dto;

import com.megabank.olp.base.bean.BaseBean;

public class LoanAttachmentDTO extends BaseBean
{
	private String attachmentType;

	private String fileName;

	public LoanAttachmentDTO()
	{
		// default constructor
	}

	/**
	 *
	 * @return attachmentType
	 */
	public String getAttachmentType()
	{
		return attachmentType;
	}

	/**
	 *
	 * @return fileName
	 */
	public String getFileName()
	{
		return fileName;
	}

	/**
	 *
	 * @param attachmentType
	 */
	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	/**
	 *
	 * @param fileName
	 */
	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

}
