package com.megabank.olp.apply.controller.loan.bean.apply;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

import javax.validation.constraints.NotBlank;

public class LoanApplyCheckedGuarantorArgBean extends BaseBean
{
	@NotBlank
	private String caseNo;

	public LoanApplyCheckedGuarantorArgBean()
	{
		// default constructor
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}


}
