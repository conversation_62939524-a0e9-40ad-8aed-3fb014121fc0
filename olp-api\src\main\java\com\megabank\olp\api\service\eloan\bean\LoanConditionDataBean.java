/**
 *
 */
package com.megabank.olp.api.service.eloan.bean;

import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class LoanConditionDataBean extends BaseBean
{
	private Integer loanAmt;

	private Integer loanPeriod;

	private List<LoanPurposeDataBean> loanPurposeDataBeans;

	private String drawDownType;

	private Integer oneTimeFee;

	private Integer preliminaryFee;

	private Integer creditCheckFee;

	private Integer renewFee;

	private Integer changeFee;

	private Integer certFee;

	private Integer reissueFee;

	private String repaymentMethod;

	private String lendingPlan;

	private LendingPlanDataBean lendingPlanDataBean;

	public LoanConditionDataBean()
	{
	}

	public Integer getCreditCheckFee()
	{
		return creditCheckFee;
	}

	public Integer getRenewFee()
	{
		return renewFee;
	}

	public void setRenewFee( Integer renewFee )
	{
		this.renewFee = renewFee;
	}

	public Integer getChangeFee()
	{
		return changeFee;
	}

	public void setChangeFee( Integer changeFee )
	{
		this.changeFee = changeFee;
	}

	public String getDrawDownType()
	{
		return drawDownType;
	}

	public String getLendingPlan()
	{
		return lendingPlan;
	}

	public LendingPlanDataBean getLendingPlanDataBean()
	{
		return lendingPlanDataBean;
	}

	public Integer getLoanAmt()
	{
		return loanAmt;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public List<LoanPurposeDataBean> getLoanPurposeDataBeans()
	{
		return loanPurposeDataBeans;
	}

	public Integer getOneTimeFee()
	{
		return oneTimeFee;
	}

	public Integer getPreliminaryFee()
	{
		return preliminaryFee;
	}

	public String getRepaymentMethod()
	{
		return repaymentMethod;
	}

	public void setCreditCheckFee( Integer creditCheckFee )
	{
		this.creditCheckFee = creditCheckFee;
	}

	public Integer getCertFee()
	{
		return certFee;
	}

	public void setCertFee( Integer certFee )
	{
		this.certFee = certFee;
	}

	public Integer getReissueFee()
	{
		return reissueFee;
	}

	public void setReissueFee( Integer reissueFee )
	{
		this.reissueFee = reissueFee;
	}

	public void setDrawDownType( String drawDownType )
	{
		this.drawDownType = drawDownType;
	}

	public void setLendingPlan( String lendingPlan )
	{
		this.lendingPlan = lendingPlan;
	}

	public void setLendingPlanDataBean( LendingPlanDataBean lendingPlanDataBean )
	{
		this.lendingPlanDataBean = lendingPlanDataBean;
	}

	public void setLoanAmt( Integer loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanPurposeDataBeans( List<LoanPurposeDataBean> loanPurposeDataBeans )
	{
		this.loanPurposeDataBeans = loanPurposeDataBeans;
	}

	public void setOneTimeFee( Integer oneTimeFee )
	{
		this.oneTimeFee = oneTimeFee;
	}

	public void setPreliminaryFee( Integer preliminaryFee )
	{
		this.preliminaryFee = preliminaryFee;
	}

	public void setRepaymentMethod( String repaymentMethod )
	{
		this.repaymentMethod = repaymentMethod;
	}

}
