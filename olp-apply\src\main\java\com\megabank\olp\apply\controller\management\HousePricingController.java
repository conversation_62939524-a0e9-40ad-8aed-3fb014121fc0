/**
 *
 */
package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.housepricing.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.housepricing.HouseEstimateInfoBean;
import com.megabank.olp.apply.controller.management.bean.housepricing.HouseInfoBean;
import com.megabank.olp.apply.controller.management.bean.housepricing.HousePricingDetailGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.housepricing.HousePricingExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.housepricing.HousePricingListedArgBean;
import com.megabank.olp.apply.controller.management.bean.housepricing.HousePricingSendCaseArgBean;
import com.megabank.olp.apply.controller.management.bean.housepricing.ProcessStatusUpdatedArgBean;
import com.megabank.olp.apply.service.management.HousePricingService;
import com.megabank.olp.apply.service.management.bean.housepricing.HouseDataBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HouseEstimateDataBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingExportedParamBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingListedParamBean;
import com.megabank.olp.apply.service.management.bean.housepricing.HousePricingSendCaseParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

@RestController
@RequestMapping( "management/housepricing" )
public class HousePricingController extends BaseController
{
	@Autowired
	private HousePricingService service;

	/**
	 * 房屋估價案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportList" )
	public Map<String, Object> exportList( @RequestBody @Validated HousePricingExportedArgBean argBean )
	{
		HousePricingExportedParamBean paramBean = new HousePricingExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( service.exportList( paramBean ) );
	}

	/**
	 * 取得房屋估價案件詳細內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getDetail" )
	public Map<String, Object> getDetail( @RequestBody @Validated HousePricingDetailGetterArgBean argBean )
	{
		Long housePricingId = argBean.getHousePricingId();

		return getResponseMap( service.getDetail( housePricingId ) );
	}

	/**
	 * 取得改派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getReassignBranchBank" )
	public Map<String, Object> getReassignBranchBank( @RequestBody @Validated HousePricingDetailGetterArgBean argBean )
	{
		Long housePricingId = argBean.getHousePricingId();

		return getResponseMap( service.getReassignBranchBank( housePricingId ) );
	}

	/**
	 * 取得房屋估價案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "list" )
	public Map<String, Object> listHousePricing( @RequestBody @Validated HousePricingListedArgBean argBean )
	{
		HousePricingListedParamBean paramBean = new HousePricingListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( service.listHousePricing( paramBean ) );
	}

	/**
	 * 房屋估價案件進件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "sendCase" )
	public Map<String, Object> sendCase( @RequestBody @Validated HousePricingSendCaseArgBean argBean )
	{
		return getResponseMap( service.create( mapPricingParamBean( argBean ) ) );
	}

	/**
	 * 更改案件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		Long housePricingId = argBean.getHousePricingId();
		Long branchBankId = argBean.getBranchBankId();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( service.updateBranchBank( housePricingId, branchBankId, employeeId, employeeName ) );
	}

	/**
	 * 更新案件處理狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateProcessStatus" )
	public Map<String, Object> updateProcessStatus( @RequestBody @Validated ProcessStatusUpdatedArgBean argBean )
	{
		Long housePricingId = argBean.getHousePricingId();
		String processStatus = argBean.getProcessStatus();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( service.updateProcessStatus( housePricingId, processStatus, employeeId, employeeName ) );
	}

	private HouseEstimateDataBean mapEvaluateEstimateData( HouseEstimateInfoBean argBean )
	{
		HouseEstimateDataBean data = new HouseEstimateDataBean();
		data.setAv750( argBean.getAv750() );

		return data;
	}

	private HouseDataBean mapEvaluateHouseData( HouseInfoBean argBean )
	{
		HouseDataBean data = new HouseDataBean();
		data.setAddr( argBean.getAddr() );
		data.setbAge( argBean.getbAge() );
		data.setbArea( argBean.getbArea() );
		data.setHouseType( argBean.getHouseType() );
		data.setCounty( argBean.getCounty() );
		data.setDistrict( argBean.getDistrict() );
		data.setFloors( argBean.getFloors() );
		data.setLevel( argBean.getLevel() );
		data.setLevelSelect( argBean.getLevelSelect() );
		data.setParkingP( argBean.getParkingP() );
		data.setParkingGty( argBean.getParkingGty() );
		data.setHouseParkingCode( argBean.getHouseParkingCode() );

		return data;
	}

	private HousePricingSendCaseParamBean mapPricingParamBean( HousePricingSendCaseArgBean argBean )
	{
		HousePricingSendCaseParamBean paramBean = new HousePricingSendCaseParamBean();

		paramBean.setCaseNo( argBean.getCaseNo() );
		paramBean.setCreatedDate( argBean.getCreatedDate() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setHouseInfo( mapEvaluateHouseData( argBean.getHouseInfo() ) );
		paramBean.setEstimateInfo( mapEvaluateEstimateData( argBean.getEstimateInfo() ) );

		return paramBean;
	}

}