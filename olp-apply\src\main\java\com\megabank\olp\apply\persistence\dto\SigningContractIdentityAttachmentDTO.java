package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

public class SigningContractIdentityAttachmentDTO extends BaseBean
{
	private Long signingUserId;

	private String userSubType;

	private Long otherBankId;

	private Long attachmentId;

	private String fileName;

	private String attachmentType;

	private Date createdDate;

	private transient ImmutableByteArray file_content;

	public SigningContractIdentityAttachmentDTO()
	{}

	public Long getAttachmentId()
	{
		return attachmentId;
	}

	public String getAttachmentType()
	{
		return attachmentType;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public ImmutableByteArray getFile_content()
	{
		return file_content;
	}

	public String getFileName()
	{
		return fileName;
	}

	public Long getOtherBankId()
	{
		return otherBankId;
	}

	public Long getSigningUserId()
	{
		return signingUserId;
	}

	public String getUserSubType()
	{
		return userSubType;
	}

	public void setAttachmentId( Long attachmentId )
	{
		this.attachmentId = attachmentId;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setFile_content( byte[] file_content )
	{
		this.file_content = file_content == null ? null : new ImmutableByteArray( file_content );
	}

	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	public void setOtherBankId( Long otherBankId )
	{
		this.otherBankId = otherBankId;
	}

	public void setSigningUserId( Long signingUserId )
	{
		this.signingUserId = signingUserId;
	}

	public void setUserSubType( String userSubType )
	{
		this.userSubType = userSubType;
	}

}
