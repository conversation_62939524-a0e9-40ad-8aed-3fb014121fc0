package com.megabank.olp.apply.controller.loan.bean.survey;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class SurveySubmittedArgBean extends BaseBean
{
	@JsonProperty( "job" )
	private SurveyJobBean surveyJobBean;

	@JsonProperty( "credit" )
	private SurveyCreditBean surveyCreditBean;

	private String loanPlanCode;

	public SurveySubmittedArgBean()
	{
		// default constructor
	}

	public SurveyCreditBean getSurveyCreditBean()
	{
		return surveyCreditBean;
	}

	public SurveyJobBean getSurveyJobBean()
	{
		return surveyJobBean;
	}

	public String getLoanPlanCode() {
		return loanPlanCode;
	}

	public void setSurveyCreditBean(SurveyCreditBean surveyCreditBean )
	{
		this.surveyCreditBean = surveyCreditBean;
	}

	public void setSurveyJobBean( SurveyJobBean surveyJobBean )
	{
		this.surveyJobBean = surveyJobBean;
	}

	public void setLoanPlanCode(String loanPlanCode) {
		this.loanPlanCode = loanPlanCode;
	}
}
