/**
 *
 */
package com.megabank.olp.api.service.estimation;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.service.estimation.bean.CalculateBasicDataBean;
import com.megabank.olp.api.service.estimation.bean.CalculateLoanDataBean;
import com.megabank.olp.api.service.estimation.bean.ContactMeBasicDataBean;
import com.megabank.olp.api.service.estimation.bean.ContactMeLoanDataBean;
import com.megabank.olp.api.service.estimation.bean.EvaluateEstimateDataBean;
import com.megabank.olp.api.service.estimation.bean.EvaluateHouseDataBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanCalculateParamBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanContactMeParamBean;
import com.megabank.olp.api.service.estimation.bean.HouseLoanEvaluateParamBean;
import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.api.utility.enums.APIErrorEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.HouseContactSendCaseClient;
import com.megabank.olp.client.sender.micro.apply.management.house.HouseLoanTrialSendCaseClient;
import com.megabank.olp.client.sender.micro.apply.management.house.HousePricingSendCaseClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactBasicInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactLoanInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactSendCaseArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.housepricing.HouseEstimateInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.housepricing.HouseInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.housepricing.HousePricingSendCaseArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialBasicInfoBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialSendCaseArgBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
public class HouseLoanService extends BaseApiService
{
	@Autowired
	private HousePricingSendCaseClient housePricingClient;

	@Autowired
	private HouseLoanTrialSendCaseClient houseTrialClient;

	@Autowired
	private HouseContactSendCaseClient houseContactClient;

	public Long calculate( HouseLoanCalculateParamBean paramBean )
	{
		validKeyData( paramBean.getEmail(), paramBean.getMobileNumber() );

		return houseTrialClient.send( mapLoanTrialSendCaseArgBean( paramBean ), new JwtArgBean() );
	}

	public Long contactMe( HouseLoanContactMeParamBean paramBean )
	{
		return houseContactClient.send( mapContactMeParamBean( paramBean ), new JwtArgBean() );
	}

	public Long evaluate( HouseLoanEvaluateParamBean paramBean )
	{
		validKeyData( paramBean.getEmail(), paramBean.getMobileNumber() );

		return housePricingClient.send( mapPricingParamBean( paramBean ), new JwtArgBean() );
	}

	private HouseContactBasicInfoBean mapContactMeBasicData( ContactMeBasicDataBean paramBean )
	{
		HouseContactBasicInfoBean argBean = new HouseContactBasicInfoBean();
		argBean.setCallBackTime( paramBean.getCallBackTime() );
		argBean.setcName( paramBean.getcName() );

		return argBean;
	}

	private HouseContactLoanInfoBean mapContactMeLoanData( ContactMeLoanDataBean paramBean )
	{
		HouseContactLoanInfoBean argBean = new HouseContactLoanInfoBean();
		argBean.setAddress( paramBean.getAddress() );
		argBean.setCounty( paramBean.getCounty() );
		argBean.setDistrict( paramBean.getDistrict() );
		argBean.setLoanBalance( paramBean.getLoanBalance() );
		argBean.setLoanPurpose( paramBean.getLoanPurpose() );

		return argBean;
	}

	private HouseContactSendCaseArgBean mapContactMeParamBean( HouseLoanContactMeParamBean paramBean )
	{
		HouseContactSendCaseArgBean argBean = new HouseContactSendCaseArgBean();
		argBean.setCaseNo( paramBean.getCaseID() );
		argBean.setCreatedDate( paramBean.getCreatedDate() );
		argBean.setMobileNumber( paramBean.getMobileNumber() );
		argBean.setBranchBankCode( paramBean.getBranchBankCode() );
		argBean.setOtherMsg( paramBean.getOtherMsg() );

		if( paramBean.getBasicInfo() != null )
			argBean.setBasicInfo( mapContactMeBasicData( paramBean.getBasicInfo() ) );
		if( paramBean.getLoanInfo() != null )
			argBean.setLoanInfo( mapContactMeLoanData( paramBean.getLoanInfo() ) );

		return argBean;
	}

	private HouseEstimateInfoBean mapEstimateData( EvaluateEstimateDataBean paramBean )
	{
		HouseEstimateInfoBean data = new HouseEstimateInfoBean();
		data.setAv750( paramBean.getAv750() );

		return data;
	}

	private HouseInfoBean mapHouseInfoBean( EvaluateHouseDataBean paramBean )
	{
		HouseInfoBean data = new HouseInfoBean();
		data.setAddr( paramBean.getAddr() );
		data.setbAge( paramBean.getbAge() );
		data.setbArea( paramBean.getbAreaP() );
		data.setHouseType( paramBean.getbTypeInt() );
		data.setCounty( paramBean.getCounty() );
		data.setDistrict( paramBean.getDistrict() );
		data.setFloors( paramBean.getFloors() );
		data.setLevel( paramBean.getLevel() );
		data.setLevelSelect( StringUtils.isNotBlank( paramBean.getLevelSelect() ) ? Integer.valueOf( paramBean.getLevelSelect() ) : null );
		data.setParkingP( paramBean.getParkingP() );
		data.setParkingGty( paramBean.getParkingGTY() );
		data.setHouseParkingCode( paramBean.getParking() );

		return data;
	}

	private HouseLoanInfoBean mapLoanInfoData( CalculateLoanDataBean paramBean )
	{
		HouseLoanInfoBean argBean = new HouseLoanInfoBean();
		argBean.setRate( paramBean.getRate() );
		argBean.setTopLoanCredit( paramBean.getTopLoanCredit() );

		return argBean;
	}

	private HouseLoanTrialBasicInfoBean mapLoanTrialBasicData( CalculateBasicDataBean argBean )
	{
		HouseLoanTrialBasicInfoBean paramBean = new HouseLoanTrialBasicInfoBean();
		paramBean.setBalance( StringUtils.isNotBlank( argBean.getBalance() ) && argBean.getBalance().equals( "1" ) );
		paramBean.setBorrow( StringUtils.isNotBlank( argBean.getBorrow() ) && argBean.getBorrow().equals( "1" ) );
		paramBean.setBuyHouseFrom( StringUtils.isNotBlank( argBean.getBuyHouseFrom() ) ? Integer.valueOf( argBean.getBuyHouseFrom() ) : null );
		paramBean.setCashCard( StringUtils.isNotBlank( argBean.getCashCard() ) && argBean.getCashCard().equals( "1" ) );
		paramBean.setCashCardBalance( argBean.getCashCardBalance() != null && argBean.getCashCardBalance().equals( "1" ) );
		paramBean.setCr3ditCard( StringUtils.isNotBlank( argBean.getCr3ditCard() ) && argBean.getCr3ditCard().equals( "1" ) );
		paramBean.setLoanPurpose( StringUtils.isNotBlank( argBean.getLoanPurpose() ) ? Integer.valueOf( argBean.getLoanPurpose() ) : null );
		paramBean.setPay( StringUtils.isNotBlank( argBean.getPay() ) && argBean.getPay().equals( "1" ) );
		paramBean.setTotalPrice( argBean.getTotalPrice() );
		paramBean.setUserAge( argBean.getUserAge() );
		paramBean.setUserChildren( argBean.getUserChildren() );
		paramBean.setUserIncome( argBean.getUserIncome() );
		paramBean.setJobType( argBean.getUserJob() );
		paramBean.setTitleType( argBean.getUserTitle() );

		return paramBean;
	}

	private HouseLoanTrialSendCaseArgBean mapLoanTrialSendCaseArgBean( HouseLoanCalculateParamBean paramBean )
	{
		HouseLoanTrialSendCaseArgBean argBean = new HouseLoanTrialSendCaseArgBean();
		argBean.setCaseNo( paramBean.getCaseID() );
		argBean.setCreatedDate( paramBean.getCreatedDate() );
		argBean.setEmail( paramBean.getEmail() );
		argBean.setMobileNumber( paramBean.getMobileNumber() );
		argBean.setBranchBankCode( paramBean.getBranchBankCode() );
		if( paramBean.getBasicInfo() != null )
			argBean.setBasicInfo( mapLoanTrialBasicData( paramBean.getBasicInfo() ) );
		if( paramBean.getLoanInfo() != null )
			argBean.setLoanInfo( mapLoanInfoData( paramBean.getLoanInfo() ) );

		return argBean;
	}

	private HousePricingSendCaseArgBean mapPricingParamBean( HouseLoanEvaluateParamBean paramBean )
	{
		HousePricingSendCaseArgBean argBean = new HousePricingSendCaseArgBean();

		argBean.setCaseNo( paramBean.getCaseID() );
		argBean.setCreatedDate( paramBean.getCreatedDate() );
		argBean.setEmail( paramBean.getEmail() );
		argBean.setMobileNumber( paramBean.getMobileNumber() );
		argBean.setBranchBankCode( paramBean.getBranchBankCode() );

		if( paramBean.getHouseInfo() != null )
			argBean.setHouseInfo( mapHouseInfoBean( paramBean.getHouseInfo() ) );
		if( paramBean.getEstimateInfo() != null )
			argBean.setEstimateInfo( mapEstimateData( paramBean.getEstimateInfo() ) );

		return argBean;
	}

	/**
	 * 檢查作為認證用的手機或電子郵件是否皆為空值
	 *
	 * @param email
	 * @param mobileNumber
	 */
	private void validKeyData( String email, String mobileNumber )
	{
		if( StringUtils.isBlank( email ) && StringUtils.isBlank( mobileNumber ) )
			throw new MyRuntimeException( APIErrorEnum.NO_VALID_KEY );
	}

}
