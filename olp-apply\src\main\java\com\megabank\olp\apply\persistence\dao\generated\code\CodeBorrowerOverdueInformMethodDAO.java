package com.megabank.olp.apply.persistence.dao.generated.code;

import com.megabank.olp.apply.persistence.pojo.code.CodeBorrowerOverdueInformMethod;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public class CodeBorrowerOverdueInformMethodDAO extends BasePojoDAO<CodeBorrowerOverdueInformMethod, BigDecimal>
{
	public CodeBorrowerOverdueInformMethod read( BigDecimal codeBorrowerOverdueInformMethod )
	{
		Validate.notNull( codeBorrowerOverdueInformMethod );

		return getPojoByPK( codeBorrowerOverdueInformMethod, CodeBorrowerOverdueInformMethod.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeBorrowerOverdueInformMethod> getPojoClass()
	{
		return CodeBorrowerOverdueInformMethod.class;
	}
}
