package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.List;

import org.hibernate.query.NativeQuery;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class AgreedDAO extends BaseDAO
{

	@SuppressWarnings( "unchecked" )
	public List<Long> getNeedToCheckItemIds( String userType, String identityType, String loanType )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "agreed.getNeedToCheckItemIds" );
		nativeQuery.setParameter( "userType", userType, String.class );
		nativeQuery.setParameter( "identityType", identityType, String.class );
		nativeQuery.setParameter( "serviceType", loanType, String.class );

		return nativeQuery.getResultList();
	}

}
