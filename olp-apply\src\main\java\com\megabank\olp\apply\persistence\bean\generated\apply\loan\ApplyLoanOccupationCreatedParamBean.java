package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanOccupationCreatedParamBean extends BaseBean
{
	private Long loanId;

	private Long jobSubTypeId;

	private String companyName;

	private Integer annualIncome;

	private Integer seniorityYear;

	private Integer seniorityMonth;

	private String titleType;

	private String companyTaxNo;

	private String companyPhoneCode;

	private String companyPhoneNumber;

	private String companyPhoneExt;

	private Long companyAddressId;

	private String amountPerMonthCode;

	private String empNo;

	private String jobPosition;

	public ApplyLoanOccupationCreatedParamBean()
	{
		// default constructor
	}

	public String getAmountPerMonthCode()
	{
		return amountPerMonthCode;
	}

	public Integer getAnnualIncome()
	{
		return annualIncome;
	}

	public Long getCompanyAddressId()
	{
		return companyAddressId;
	}

	public String getCompanyName()
	{
		return companyName;
	}

	public String getCompanyPhoneCode()
	{
		return companyPhoneCode;
	}

	public String getCompanyPhoneExt()
	{
		return companyPhoneExt;
	}

	public String getCompanyPhoneNumber()
	{
		return companyPhoneNumber;
	}

	public String getCompanyTaxNo()
	{
		return companyTaxNo;
	}

	public String getEmpNo()
	{
		return empNo;
	}

	public String getJobPosition()
	{
		return jobPosition;
	}

	public Long getJobSubTypeId()
	{
		return jobSubTypeId;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public Integer getSeniorityMonth()
	{
		return seniorityMonth;
	}

	public Integer getSeniorityYear()
	{
		return seniorityYear;
	}

	public String getTitleType()
	{
		return titleType;
	}

	public void setAmountPerMonthCode( String amountPerMonthCode )
	{
		this.amountPerMonthCode = amountPerMonthCode;
	}

	public void setAnnualIncome( Integer annualIncome )
	{
		this.annualIncome = annualIncome;
	}

	public void setCompanyAddressId( Long companyAddressId )
	{
		this.companyAddressId = companyAddressId;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public void setCompanyPhoneCode( String companyPhoneCode )
	{
		this.companyPhoneCode = companyPhoneCode;
	}

	public void setCompanyPhoneExt( String companyPhoneExt )
	{
		this.companyPhoneExt = companyPhoneExt;
	}

	public void setCompanyPhoneNumber( String companyPhoneNumber )
	{
		this.companyPhoneNumber = companyPhoneNumber;
	}

	public void setCompanyTaxNo( String companyTaxNo )
	{
		this.companyTaxNo = companyTaxNo;
	}

	public void setEmpNo( String empNo )
	{
		this.empNo = empNo;
	}

	public void setJobPosition( String jobPosition )
	{
		this.jobPosition = jobPosition;
	}

	public void setJobSubTypeId( Long jobSubTypeId )
	{
		this.jobSubTypeId = jobSubTypeId;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setSeniorityMonth( Integer seniorityMonth )
	{
		this.seniorityMonth = seniorityMonth;
	}

	public void setSeniorityYear( Integer seniorityYear )
	{
		this.seniorityYear = seniorityYear;
	}

	public void setTitleType( String titleType )
	{
		this.titleType = titleType;
	}
}
