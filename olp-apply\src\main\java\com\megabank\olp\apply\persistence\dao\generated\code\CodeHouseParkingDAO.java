package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeHouseParking;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeHouseParkingDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeHouseParkingDAO extends BasePojoDAO<CodeHouseParking, String>
{
	public CodeHouseParking read( String code )
	{
		Validate.notNull( code );

		return getPojoByPK( code, CodeHouseParking.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeHouseParking> getPojoClass()
	{
		return CodeHouseParking.class;
	}
}
