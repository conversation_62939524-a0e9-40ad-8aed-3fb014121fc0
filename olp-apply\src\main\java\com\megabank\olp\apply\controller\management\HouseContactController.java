/**
 *
 */
package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.housecontact.BranchBankUpdatedArgBean;
import com.megabank.olp.apply.controller.management.bean.housecontact.HouseContactBasicInfoBean;
import com.megabank.olp.apply.controller.management.bean.housecontact.HouseContactDetailGetterArgBean;
import com.megabank.olp.apply.controller.management.bean.housecontact.HouseContactExportedArgBean;
import com.megabank.olp.apply.controller.management.bean.housecontact.HouseContactListedArgBean;
import com.megabank.olp.apply.controller.management.bean.housecontact.HouseContactLoanInfoBean;
import com.megabank.olp.apply.controller.management.bean.housecontact.HouseContactSendCaseArgBean;
import com.megabank.olp.apply.controller.management.bean.housecontact.ProcessStatusUpdatedArgBean;
import com.megabank.olp.apply.service.management.HouseContactService;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactBasicDataBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactExportedParamBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactListedParamBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactLoanDataBean;
import com.megabank.olp.apply.service.management.bean.housecontact.HouseContactSendCaseParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/housecontact" )
public class HouseContactController extends BaseController
{
	@Autowired
	private HouseContactService service;

	/**
	 * 房貸專人聯絡案件列表 輸出檔
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "exportList" )
	public Map<String, Object> exportList( @RequestBody @Validated HouseContactExportedArgBean argBean )
	{
		HouseContactExportedParamBean paramBean = new HouseContactExportedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setCallBackTime( argBean.getCallBackTime() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( service.exportList( paramBean ) );
	}

	/**
	 * 取得房貸專人聯絡案件詳細內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getDetail" )
	public Map<String, Object> getDetail( @RequestBody @Validated HouseContactDetailGetterArgBean argBean )
	{
		Long houseContactId = argBean.getHouseContactId();

		return getResponseMap( service.getDetail( houseContactId ) );
	}

	/**
	 * 取得改派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getReassignBranchBank" )
	public Map<String, Object> getReassignBranchBank( @RequestBody @Validated HouseContactDetailGetterArgBean argBean )
	{
		Long houseContactId = argBean.getHouseContactId();

		return getResponseMap( service.getReassignBranchBank( houseContactId ) );
	}

	/**
	 * 取得房貸專人聯絡案件列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "list" )
	public Map<String, Object> listContactMe( @RequestBody @Validated HouseContactListedArgBean argBean )
	{
		HouseContactListedParamBean paramBean = new HouseContactListedParamBean();
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setNotificationStatusCode( argBean.getNotificationStatusCode() );
		paramBean.setProcessStatusCode( argBean.getProcessStatusCode() );
		paramBean.setCallBackTime( argBean.getCallBackTime() );
		paramBean.setName( argBean.getName() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setDateStart( argBean.getDateStart() );
		paramBean.setDateEnd( argBean.getDateEnd() );
		paramBean.setPage( argBean.getPage() );
		paramBean.setLength( argBean.getLength() );
		paramBean.setSortColumn( argBean.getSortColumn() );
		paramBean.setSortDirection( argBean.getSortDirection() );

		return getResponseMap( service.listContactMe( paramBean ) );
	}

	/**
	 * 房貸專人聯絡案件進件
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "sendCase" )
	public Map<String, Object> sendCase( @RequestBody @Validated HouseContactSendCaseArgBean argBean )
	{
		return getResponseMap( service.create( mapContactMeParamBean( argBean ) ) );
	}

	/**
	 * 更改案件派案分行
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateBranchBank" )
	public Map<String, Object> updateBranchBank( @RequestBody @Validated BranchBankUpdatedArgBean argBean )
	{
		Long houseContactId = argBean.getHouseContactId();
		Long branchBankId = argBean.getBranchBankId();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( service.updateBranchBank( houseContactId, branchBankId, employeeId, employeeName ) );
	}

	/**
	 * 更新案件處理狀態
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "updateProcessStatus" )
	public Map<String, Object> updateProcessStatus( @RequestBody @Validated ProcessStatusUpdatedArgBean argBean )
	{
		Long houseContactId = argBean.getHouseContactId();
		String processStatus = argBean.getProcessStatus();
		String employeeId = argBean.getEmployeeId();
		String employeeName = argBean.getEmployeeName();

		return getResponseMap( service.updateProcessStatus( houseContactId, processStatus, employeeId, employeeName ) );
	}

	private HouseContactBasicDataBean mapContactMeBasicData( HouseContactBasicInfoBean argBean )
	{
		HouseContactBasicDataBean paramBean = new HouseContactBasicDataBean();
		paramBean.setCallBackTime( argBean.getCallBackTime() );
		paramBean.setcName( argBean.getcName() );
		return paramBean;
	}

	private HouseContactLoanDataBean mapContactMeLoanData( HouseContactLoanInfoBean argBean )
	{
		HouseContactLoanDataBean paramBean = new HouseContactLoanDataBean();
		paramBean.setAddress( argBean.getAddress() );
		paramBean.setCounty( argBean.getCounty() );
		paramBean.setDistrict( argBean.getDistrict() );
		paramBean.setLoanBalance( argBean.getLoanBalance() );
		paramBean.setLoanPurpose( argBean.getLoanPurpose() );

		return paramBean;
	}

	private HouseContactSendCaseParamBean mapContactMeParamBean( HouseContactSendCaseArgBean argBean )
	{
		HouseContactSendCaseParamBean paramBean = new HouseContactSendCaseParamBean();
		paramBean.setCaseNo( argBean.getCaseNo() );
		paramBean.setCreatedDate( argBean.getCreatedDate() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setOtherMsg( argBean.getOtherMsg() );
		paramBean.setBasicInfo( mapContactMeBasicData( argBean.getBasicInfo() ) );
		paramBean.setLoanInfo( mapContactMeLoanData( argBean.getLoanInfo() ) );

		return paramBean;
	}
}
