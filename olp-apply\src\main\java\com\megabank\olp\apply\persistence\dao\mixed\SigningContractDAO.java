/**
 *
 */
package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.dto.SigningContractCountDTO;
import com.megabank.olp.apply.persistence.dto.SigningContractListDTO;
import com.megabank.olp.apply.utility.enums.SigningContractTypeEnum;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BaseDAO;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Repository
public class SigningContractDAO extends BaseDAO
{
	private static final String CURRENT_TIME_CONSTANT = "currentTime";

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String SIGNING_CONTRACT_TYPE_CONSTANT = "signingContractType";

	private static final String PRODUCT_CODE_CONSTANT = "productCode";

	private static final String VALIDATED_IDENTITY_IDS_CONSTANT = "validatedIdentityIds";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	public List<Long> checkUserOwnContracts( List<Long> validatedIdentityIds, String productCode )
	{
		Validate.notEmpty( validatedIdentityIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.checkUserOwnContracts" );
		nativeQuery.setParameterList( VALIDATED_IDENTITY_IDS_CONSTANT, validatedIdentityIds, Long.class );
		nativeQuery.setParameter( PRODUCT_CODE_CONSTANT, productCode, String.class );
		nativeQuery.setParameter( SIGNING_CONTRACT_TYPE_CONSTANT, SigningContractTypeEnum.COMPLETED.getContext(), String.class );

		return nativeQuery.getResultList();
	}

	public List<SigningContractCountDTO> getContractCountByBranch( Long branchBankId, String loanType )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.getSigningContractCount" );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( SIGNING_CONTRACT_TYPE_CONSTANT, SigningContractTypeEnum.COMPLETED.getContext(), String.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( SigningContractCountDTO.class ) );

		return nativeQuery.getResultList();
	}

	public List<SigningContractListDTO> getContractList( List<Long> validatedIdentityIds, List<String> productCodes )
	{
		Validate.notEmpty( validatedIdentityIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.getContractListByIdentityIds" );
		nativeQuery.setParameterList( VALIDATED_IDENTITY_IDS_CONSTANT, validatedIdentityIds, Long.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( PRODUCT_CODE_CONSTANT, productCodes, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( SigningContractListDTO.class ) );

		return nativeQuery.getResultList();
	}

	/**取得未作廢之尚未簽約對保的案件
	 * 
	 * @param validatedIdentityIds
	 * @return
	 */
	public List<SigningContractListDTO> getContractListByIdentityIdsAndType( List<Long> validatedIdentityIds, List<String> productCode )
	{
		Validate.notEmpty( validatedIdentityIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "signingcontract.getContractListByIdentityIdsAndType" );
		nativeQuery.setParameterList( VALIDATED_IDENTITY_IDS_CONSTANT, validatedIdentityIds, Long.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( PRODUCT_CODE_CONSTANT, productCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( SigningContractListDTO.class ) );

		return nativeQuery.getResultList();
	}

}
