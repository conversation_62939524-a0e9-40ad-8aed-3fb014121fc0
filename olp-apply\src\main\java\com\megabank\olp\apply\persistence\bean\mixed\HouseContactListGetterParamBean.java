package com.megabank.olp.apply.persistence.bean.mixed;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class HouseContactListGetterParamBean extends BaseBean
{
	private Long branchBankId;

	private Integer notified;

	private String processCode;

	private String callBackTime;

	private String name;

	private String mobileNumber;

	private Date createdDateStart;

	private Date createdDateEnd;

	public HouseContactListGetterParamBean()
	{
		// default constructor
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public String getCallBackTime()
	{
		return callBackTime;
	}

	public Date getCreatedDateEnd()
	{
		return createdDateEnd;
	}

	public Date getCreatedDateStart()
	{
		return createdDateStart;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public Integer getNotified()
	{
		return notified;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setCallBackTime( String callBackTime )
	{
		this.callBackTime = callBackTime;
	}

	public void setCreatedDateEnd( Date createdDateEnd )
	{
		this.createdDateEnd = createdDateEnd;
	}

	public void setCreatedDateStart( Date createdDateStart )
	{
		this.createdDateStart = createdDateStart;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotified( Integer notified )
	{
		this.notified = notified;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

}
