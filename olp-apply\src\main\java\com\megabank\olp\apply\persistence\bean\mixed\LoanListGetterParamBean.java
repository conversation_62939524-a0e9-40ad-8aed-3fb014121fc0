package com.megabank.olp.apply.persistence.bean.mixed;

import java.util.Date;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */
public class LoanListGetterParamBean extends BaseBean
{
	private List<String> applyStatusCodes;

	private Long finalBranchBankId;

	private Integer notified;

	private String processCode;

	private String transmissionStatusCode;

	private String idNo;

	private String name;

	private String mobileNumber;

	private Date dateStart;

	private Date dateEnd;

	private String loanType;

	private Integer secondDiff;

	private Boolean discard;

	private List<Long> loanRecipientId;

	private String introduceBrNo;
	
	private String introduceEmpId;

	public LoanListGetterParamBean()
	{
		// default constructor
	}

	public List<String> getApplyStatusCodes()
	{
		return applyStatusCodes;
	}

	public Date getDateEnd()
	{
		return dateEnd;
	}

	public Date getDateStart()
	{
		return dateStart;
	}

	public Boolean getDiscard()
	{
		return discard;
	}

	public Long getFinalBranchBankId()
	{
		return finalBranchBankId;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public Integer getNotified()
	{
		return notified;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public Integer getSecondDiff()
	{
		return secondDiff;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public List<Long> getLoanRecipientId() {return loanRecipientId;}

	public String getIntroduceBrNo() { return introduceBrNo; }

	public String getIntroduceEmpId()
	{
		return introduceEmpId;
	}

	public void setApplyStatusCodes(List<String> applyStatusCodes )
	{
		this.applyStatusCodes = applyStatusCodes;
	}

	public void setDateEnd( Date dateEnd )
	{
		this.dateEnd = dateEnd;
	}

	public void setDateStart( Date dateStart )
	{
		this.dateStart = dateStart;
	}

	public void setDiscard( Boolean discard )
	{
		this.discard = discard;
	}

	public void setFinalBranchBankId( Long finalBranchBankId )
	{
		this.finalBranchBankId = finalBranchBankId;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotified( Integer notified )
	{
		this.notified = notified;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

	public void setSecondDiff( Integer secondDiff )
	{
		this.secondDiff = secondDiff;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setLoanRecipientId(List<Long> loanRecipientId) { this.loanRecipientId = loanRecipientId; }

	public void setIntroduceBrNo(String introduceBrNo) { this.introduceBrNo = introduceBrNo; }

	public void setIntroduceEmpId( String introduceEmpId )
	{
		this.introduceEmpId = introduceEmpId;
	}
}
