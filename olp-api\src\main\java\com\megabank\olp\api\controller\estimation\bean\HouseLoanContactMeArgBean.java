/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanContactMeArgBean extends BaseBean
{
	@NotBlank
	private String caseID;

	@NotBlank
	private String mobileNumber;

	@NotNull
	private Date createdDate;

	@JsonProperty( "contactBranchCode" )
	@NotBlank
	private String branchBankCode;

	@NotNull
	@Valid
	private ContactMeBasicInfoBean basicInfo;

	@Valid
	private ContactMeLoanInfoBean loanInfo;

	private String otherMsg;

	public HouseLoanContactMeArgBean()
	{}

	public ContactMeBasicInfoBean getBasicInfo()
	{
		return basicInfo;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseID()
	{
		return caseID;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public ContactMeLoanInfoBean getLoanInfo()
	{
		return loanInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public void setBasicInfo(ContactMeBasicInfoBean basicInfo )
	{
		this.basicInfo = basicInfo;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseID( String caseID )
	{
		this.caseID = caseID;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setLoanInfo( ContactMeLoanInfoBean loanInfo )
	{
		this.loanInfo = loanInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}
}
