package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.dao.generated.apply.note.ApplyNoteDAO;
import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.base.config.BasePersistenceConfig;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyLoanNoteDAOIntegration
{
	@Autowired
	private ApplyNoteDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getApplyPersonalLoanNotes()
	{
		Long personalLoanId = 1L;
		List<ApplyNote> result = dao.getApplyLoanNotes( personalLoanId );
		logger.info( "result:{}", result );
	}
}
