package com.megabank.olp.apply.service.loan;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.persistence.dao.generated.apply.attachment.ApplyAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.collateral.ApplyCollateralDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplyCollateralContractAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningContractDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningEddaDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.task.ApplyTaskDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.youthStartUp.ApplyYouthStartUpAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.youthStartUp.ApplyYouthStartUpDAO;
import com.megabank.olp.apply.persistence.dao.generated.ixml.applyRecord.IxmlApplyRecordDAO;
import com.megabank.olp.apply.persistence.dao.generated.ixml.attachment.IxmlAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.ixml.signatures.IxmlSignaturesDAO;
import com.megabank.olp.apply.persistence.dao.mixed.AttachmentDAO;
import com.megabank.olp.apply.persistence.dao.mixed.LoanDAO;
import com.megabank.olp.apply.persistence.dao.mixed.TempDAO;
import com.megabank.olp.apply.persistence.dto.SigningContractIdentityAttachmentDTO;
import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddress;
import com.megabank.olp.apply.persistence.pojo.apply.attachment.ApplyAttachment;
import com.megabank.olp.apply.persistence.pojo.apply.collateral.ApplyCollateral;
import com.megabank.olp.apply.persistence.pojo.apply.collateral.ApplyCollateralAgreement;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanBasic;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContactInfo;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContent;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanGuaranteeInfo;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanOccupation;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanRelation;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanServed;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyCollateralContractAttachment;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningAppropriation;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningEdda;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningUser;
import com.megabank.olp.apply.persistence.pojo.apply.task.ApplyTask;
import com.megabank.olp.apply.persistence.pojo.apply.youthStartUp.ApplyYouthStartUpAttachment;
import com.megabank.olp.apply.persistence.pojo.code.CodeNationality;
import com.megabank.olp.apply.persistence.pojo.ixml.applyRecord.IxmlApplyRecord;
import com.megabank.olp.apply.persistence.pojo.ixml.attachment.IxmlAttachment;
import com.megabank.olp.apply.persistence.pojo.ixml.signatures.IxmlSignatures;
import com.megabank.olp.apply.utility.ApplyLoanUtils;
import com.megabank.olp.apply.utility.BaseApplyService;
import com.megabank.olp.apply.utility.ObjectConversionUtils;
import com.megabank.olp.apply.utility.enums.ApplyStatusEnum;
import com.megabank.olp.apply.utility.enums.ApplyTaskActionEnum;
import com.megabank.olp.apply.utility.enums.SigningContractSendStatusEnum;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.ProductCodeEnum;
import com.megabank.olp.base.enums.RecipientSystemEnum;
import com.megabank.olp.base.enums.ServiceTypeEnum;
import com.megabank.olp.base.enums.UserSubTypeEnum;
import com.megabank.olp.base.enums.UserTypeEnum;
import com.megabank.olp.base.utility.CommonStringUtils;
import com.megabank.olp.base.utility.CommonUtil;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.base.utility.date.CommonDateUtils;
import com.megabank.olp.base.utility.text.CommonBase64Utils;
import com.megabank.olp.client.sender.eloan.submitted.attachment.bean.AttachmentSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.collateral.bean.ProviderAgreementBean;
import com.megabank.olp.client.sender.eloan.submitted.collateral.bean.ProviderAgreementSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.bean.HouseLoanApplySubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.bean.HouseLoanBasicInfoBean;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.bean.HouseLoanInfoBean;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.bean.HouseLoanRelationDataBean;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.bean.HouseLoanServedDataBean;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.bean.PersonalLoanApplySubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.bean.PersonalLoanBasicInfoBean;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.bean.PersonalLoanInfoBean;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.bean.PersonalLoanRelationDataBean;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.bean.PersonalLoanServedDataBean;
import com.megabank.olp.client.sender.eloan.submitted.signing.bean.SigningContractCompletedArgBean;
import com.megabank.olp.client.sender.iloan.submitted.personalloan.bean.PersonalLoanApplySubmittedIloanArgBean;
import com.megabank.olp.client.sender.iloan.submitted.personalloan.bean.PersonalLoanBasicInfoIloanBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultCertParamsBean;
import com.megabank.olp.client.sender.ixml.savecert.bean.IxmlSaveCertArgBean;
import com.megabank.olp.client.sender.ixml.savecert.bean.IxmlSaveCertResBean;
import com.megabank.olp.client.sender.micro.identity.AuthBankInfoClient;
import com.megabank.olp.client.sender.micro.identity.IdentityAttachmentsClient;
import com.megabank.olp.client.sender.micro.identity.bean.AuthBankInfoArgBean;
import com.megabank.olp.client.sender.micro.identity.bean.AuthBankInfoResultBean;
import com.megabank.olp.client.sender.micro.identity.bean.IdentityAttachmentResultBean;
import com.megabank.olp.client.sender.micro.identity.bean.IdentityAttachmentsArgBean;
import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoResultBean;
import com.megabank.olp.client.service.common.UserClientService;
import com.megabank.olp.client.service.eloan.EloanSenderService;
import com.megabank.olp.client.service.iloan.SenderILoanService;
import com.megabank.olp.client.service.ixml.IxmlSenderService;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Transactional
public class DeliverService extends BaseApplyService
{
	private static final String AP_NAME = "olp-apply";

	private static final int DAY_DIFF_VALUE = 180;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private ApplyAttachmentDAO applyAttachmentDAO;

	@Autowired
	private ApplyCollateralDAO applyCollateralDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private ApplyTaskDAO applyTaskDAO;

	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	private SystemService systemService;

	@Autowired
	private UserClientService userClientService;

	@Autowired
	private IdentityAttachmentsClient identityAttachmentsClient;

	@Autowired
	private AuthBankInfoClient authBankInfoClient;

	@Autowired
	private EloanSenderService eloanSenderService;

	@Autowired
	private SenderILoanService iloanSenderService;

	@Autowired
	private GenerateService generateService;

	@Autowired
	private ApplySigningEddaDAO signingEddaDAO;

	@Autowired
	private LoanDAO loanDAO;

	@Autowired
	private TempDAO tempDAO;

	@Autowired
	private AttachmentDAO attachmentDAO;

	@Autowired
	private IxmlAttachmentDAO ixmlAttachmentDAO;

	@Autowired
	private IxmlSignaturesDAO ixmlSignaturesDAO;

	@Autowired
	private IxmlApplyRecordDAO ixmlApplyRecordDAO;

	@Autowired
	private ApplyYouthStartUpAttachmentDAO applyYouthStartUpAttachmentDAO;

	@Autowired
	private ApplyYouthStartUpDAO applyYouthStartUpDAO;

	@Autowired
	private IxmlSenderService ixmlSenderService;

	@Autowired
	private ApplyCollateralContractAttachmentDAO applyCollateralContractAttachmentDAO;

	/**
	 * 已進件申貸案件 PDF 重送 eloan
	 */
	public synchronized void retryApplyPdfToEloan()
	{
		List<ApplyTask> applyTask_list = applyTaskDAO.getPojosByTaskActionNullDoneTs( ApplyTaskActionEnum.APPLY_PDF_TO_ELOAN.getContext() );

		for( ApplyTask applyTask : applyTask_list )
		{
			Long taskId = applyTask.getTaskId();
			int upd_cnt = applyTaskDAO.updateProcTs( taskId );
			if( upd_cnt == 0 )
				continue;

			ApplyLoan applyLoan = applyLoanDAO.read( applyTask.getRefId() );

			boolean submitSuccess = submitApplyPdf( applyLoan );
			if( submitSuccess )
				applyTaskDAO.updateDoneTs( taskId );
		}
	}

	/**
	 * 重送 各項約據PDF&央行切結書PDF至 eloan
	 *
	 * @return
	 */
	public synchronized void retryCollateralContractAttachment()
	{
		List<ApplyCollateralContractAttachment> attachmentList = applyCollateralContractAttachmentDAO
					.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );

		for( ApplyCollateralContractAttachment attachment : attachmentList )
		{
			applyCollateralContractAttachmentDAO.updateResend( attachment.getAttachmentId() );

			boolean isSubmitSuccess = submitCollateralContractAttachment( attachment );
			String transmissionStatusCode = isSubmitSuccess ? TransmissionStatusEnum.COMPLETED.getContext()
															: TransmissionStatusEnum.EXCEPTION.getContext();
			applyCollateralContractAttachmentDAO.updateTransmissionStatus( attachment.getAttachmentId(), transmissionStatusCode );
		}
	}

	public synchronized void retryContractPdfToEloan()
	{
		/*
		 * Step-1 , insert into dbo.apply_task(ref_id, task_action) values(254,'contractPdfToEloan')
		 * Step-2 , delete from log_schedule_history where name='contractPdfToEloan'
		 * Step-3 , curl -X POST http://127.0.0.1:9195/open/deliver/retryContractPdfToEloan
		 */
		List<ApplyTask> applyTask_list = applyTaskDAO.getPojosByTaskActionNullDoneTs( ApplyTaskActionEnum.CONTRACT_PDF_TO_ELOAN.getContext() );

		for( ApplyTask applyTask : applyTask_list )
		{
			Long taskId = applyTask.getTaskId();
			int upd_cnt = applyTaskDAO.updateProcTs( taskId );
			if( upd_cnt == 0 )
				continue;

			ApplySigningContract applySigningContract = applySigningContractDAO.read( applyTask.getRefId() );

			boolean submitSuccess = submitContractPdf( applySigningContract );
			if( submitSuccess )
				applyTaskDAO.updateDoneTs( taskId );
		}

	}

	/**
	 * 批次傳輸 ixml 憑證申請紀錄
	 */
	public synchronized void retryIxmlCert()
	{
		List<IxmlApplyRecord> applyRecords = ixmlApplyRecordDAO.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );

		for( IxmlApplyRecord applyRecord : applyRecords )
		{
			ixmlApplyRecordDAO.updateResend( applyRecord.getApplyRecordId() );

			boolean submitSuccess = submitIxmlCert( applyRecord );

			if( submitSuccess )
				ixmlApplyRecordDAO.updateTransmissionStatus( applyRecord.getApplyRecordId(), TransmissionStatusEnum.COMPLETED.getContext() );
		}
	}

	/**
	 * 上傳補件重送 eloan / iloan
	 */
	public synchronized void retrySubmitAttachment( Integer systemId )
	{
		List<ApplyAttachment> attachments = applyAttachmentDAO.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );

		attachments = attachments.stream().filter( applyAttach -> {
			// 排程為 eloan 觸發
			if( systemId == RecipientSystemEnum.ELOAN.getSystemId() && Objects.isNull( applyAttach.getApplyLoan().getCodeRecipientSystem() ) )
				return true;
			// 排程為 iloan 觸發
			if( Objects.isNull( applyAttach.getApplyLoan().getCodeRecipientSystem() ) )
				return false;
			return systemId == applyAttach.getApplyLoan().getCodeRecipientSystem().getSystemId();
		} ).collect( Collectors.toList() );

		for( ApplyAttachment attachment : attachments )
		{
			applyAttachmentDAO.updateResend( attachment.getAttachmentId() );

			boolean submitSuccess = submitAttachment( attachment );

			if( submitSuccess )
				applyAttachmentDAO.updateTransmissionStatus( attachment.getAttachmentId(), TransmissionStatusEnum.COMPLETED.getContext() );
		}
	}

	/**
	 * 擔保品提供人同意案件重送eloan
	 */
	public synchronized void retrySubmitCollateral()
	{
		List<ApplyCollateral> applyCollaterals = applyCollateralDAO.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );

		for( ApplyCollateral applyCollateral : applyCollaterals )
		{
			applyCollateralDAO.updateResend( applyCollateral.getCollateralId() );

			boolean submitSuccess = submitLoanCollateral( applyCollateral );

			if( submitSuccess )
				applyCollateralDAO.updateTransmissionStatus( applyCollateral.getCollateralId(), TransmissionStatusEnum.COMPLETED.getContext() );
		}

	}

	/**
	 * ixml附件重送 eloan / iloan
	 */
	public synchronized void retrySubmitIxmlAttachment( Integer systemId )
	{
		List<IxmlAttachment> pdfList = ixmlAttachmentDAO.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );

		pdfList = pdfList.stream().filter( pdf -> {
			ApplyLoan applyLoan = applyLoanDAO.read( pdf.getLoanId() );
			if( systemId == RecipientSystemEnum.ELOAN.getSystemId() && Objects.isNull( applyLoan.getCodeRecipientSystem() ) )
				return true;
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() ) )
				return false;
			return systemId == applyLoan.getCodeRecipientSystem().getSystemId();
		} ).collect( Collectors.toList() );

		for( IxmlAttachment pdf : pdfList )
		{
			ixmlAttachmentDAO.updateResend( pdf.getLoanId() );

			boolean submitSuccess = submitIxmlPdf( pdf.getLoanId() );

			if( submitSuccess )
				ixmlAttachmentDAO.updateTransmissionStatus( pdf.getLoanId(), TransmissionStatusEnum.COMPLETED.getContext() );
		}

		List<IxmlSignatures> signatureList = ixmlSignaturesDAO.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );

		signatureList = signatureList.stream().filter( signature -> {
			ApplyLoan applyLoan = applyLoanDAO.read( signature.getLoanId() );
			if( systemId == RecipientSystemEnum.ELOAN.getSystemId() && Objects.isNull( applyLoan.getCodeRecipientSystem() ) )
				return true;
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() ) )
				return false;
			return systemId == applyLoan.getCodeRecipientSystem().getSystemId();
		} ).collect( Collectors.toList() );

		for( IxmlSignatures signature : signatureList )
		{
			ixmlSignaturesDAO.updateResend( signature.getSignatureId() );

			boolean submitSuccess = submitIxmlSignature( signature.getLoanId(), signature.getSignatureId() );

			if( submitSuccess )
				ixmlSignaturesDAO.updateTransmissionStatus( signature.getSignatureId(), TransmissionStatusEnum.COMPLETED.getContext() );
		}
	}

	/**
	 * 貸款申請案件重送 eloan / iloan
	 */
	public synchronized void retrySubmitLoan( Integer systemId )
	{
		List<ApplyLoan> applyLoans = applyLoanDAO.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );
		applyLoans = applyLoans.stream().filter( applyLoan -> {
			if( systemId == RecipientSystemEnum.ELOAN.getSystemId() && Objects.isNull( applyLoan.getCodeRecipientSystem() ) )
				return true;
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() ) )
				return false;
			return systemId == applyLoan.getCodeRecipientSystem().getSystemId();
		} ).collect( Collectors.toList() );

		for( ApplyLoan applyLoan : applyLoans )
		{
			applyLoanDAO.updateResendCount( applyLoan.getLoanId() );

			String loanType = applyLoan.getCodeLoanType().getLoanType();
			boolean submitSuccess = false;

			if( loanType.equals( LoanTypeEnum.PERSONAL_LOAN.getContext() ) )
				submitSuccess = submitPersonalLoanApply( applyLoan );
			else
				submitSuccess = submitHouseLoanApply( applyLoan );

			if( submitSuccess )
				applyLoanDAO.updateTransmissionStatus( applyLoan.getLoanId(), TransmissionStatusEnum.COMPLETED.getContext() );

		}
	}

	/**
	 * 對保案件重送 eloan / iloan
	 */
	public synchronized void retrySubmitSigningContract( Integer systemId )
	{
		List<ApplySigningContract> signingContracts = applySigningContractDAO
					.getPojosBySendStatus( SigningContractSendStatusEnum.EXCEPTION.getContext() );

		signingContracts = signingContracts.stream().filter( ( contract ) -> {
			if( systemId == RecipientSystemEnum.ELOAN.getSystemId() && Objects.isNull( contract.getCodeRecipientSystem() ) )
				return true;
			if( Objects.isNull( contract.getCodeRecipientSystem() ) )
				return false;
			return systemId == contract.getCodeRecipientSystem().getSystemId();
		} ).collect( Collectors.toList() );

		for( ApplySigningContract signingContract : signingContracts )
		{
			applySigningContractDAO.updateResendCount( signingContract.getSigningContractId() );

			boolean submitSuccess = submitSigningContract( signingContract, CommonUtil.getByteArray( signingContract.getPdfContent() ) );

			if( submitSuccess )
				submitSuccess = submitSigningContractAttachment( signingContract );

			if( submitSuccess )
				applySigningContractDAO.updateSendStatus( signingContract.getSigningContractId(),
														  SigningContractSendStatusEnum.COMPLETED.getContext() );

		}
	}

	/**
	 * 撥款後PDF重送 eloan / iloan
	 */
	public synchronized void retrySubmitSigningContractPdf( Integer systemId )
	{
		List<ApplySigningContract> signingContracts = applySigningContractDAO
					.getPojosBySendStatusAndIsAppropiration( SigningContractSendStatusEnum.EXCEPTION.getContext() );

		signingContracts = signingContracts.stream().filter( ( contract ) -> {
			if( systemId == RecipientSystemEnum.ELOAN.getSystemId() && Objects.isNull( contract.getCodeRecipientSystem() ) )
				return true;
			if( Objects.isNull( contract.getCodeRecipientSystem() ) )
				return false;
			return systemId == contract.getCodeRecipientSystem().getSystemId();
		} ).collect( Collectors.toList() );

		for( ApplySigningContract signingContract : signingContracts )
		{
			applySigningContractDAO.updateResendCount( signingContract.getSigningContractId() );

			boolean submitSuccess = submitSigningContractPdf( signingContract );

			if( submitSuccess )
				applySigningContractDAO.updateSendStatus( signingContract.getSigningContractId(),
														  SigningContractSendStatusEnum.COMPLETED.getContext() );

		}
	}

	/**
	 * 青創上傳補件重送eloan
	 */
	public synchronized void retrySubmitYouthAttachment()
	{
		List<ApplyYouthStartUpAttachment> youthStartUpAttachments = applyYouthStartUpAttachmentDAO
					.getPojosByTransmissionStatus( TransmissionStatusEnum.EXCEPTION.getContext() );

		for( ApplyYouthStartUpAttachment attachment : youthStartUpAttachments )
		{
			applyYouthStartUpAttachmentDAO.updateResend( attachment.getAttachmentId() );

			boolean submitSuccess = submitAttachment( attachment );

			if( submitSuccess )
			{
				applyYouthStartUpAttachmentDAO.updateTransmissionStatus( attachment.getAttachmentId(),
																		 TransmissionStatusEnum.COMPLETED.getContext() );
				applyYouthStartUpDAO.updateTransmissionStatus( attachment.getApplyYouthStartUp().getYouthStartUpId(),
															   TransmissionStatusEnum.COMPLETED.getContext() );
			}
		}
	}

	public boolean submitApplyPdf( ApplyLoan applyLoan )
	{
		try
		{
			AttachmentSubmittedArgBean argBean = new AttachmentSubmittedArgBean();
			argBean.setCaseNo( applyLoan.getCaseNo() );
			argBean.setServiceType( "3" );
			argBean.setAttachmentId( "0" );
			argBean.setAttachmentType( "apply" );
			argBean.setFileName( "申請書.pdf" );
			argBean.setFileContent( CommonBase64Utils.base64Encoder( generateService.generateLoanApplyPdf( applyLoan ) ) );
			argBean.setCreateDate( new Date() );

			boolean submitResult = false;
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitAttachment( argBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = iloanSenderService.submitAttachment( argBean ).isStat();
			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "申請書PDF重送eLoan-連結 eloan 系統錯誤" );

			return false;
		}

	}

	/**
	 * 上傳補件送 eloan / iloan
	 *
	 * @param attachment
	 * @return
	 */
	public boolean submitAttachment( ApplyAttachment attachment )
	{
		try
		{
			AttachmentSubmittedArgBean argBean = new AttachmentSubmittedArgBean();
			argBean.setCaseNo( attachment.getApplyLoan().getCaseNo() );
			argBean.setServiceType( getServiceCode( attachment.getApplyLoan().getCodeLoanType().getLoanType() ) );
			argBean.setAttachmentId( attachment.getAttachmentId().toString() );
			argBean.setAttachmentType( attachment.getCodeAttachmentType().getAttachmentType() );
			argBean.setFileName( attachment.getFileName() );
			argBean.setFileContent( CommonBase64Utils.base64Encoder( attachment.getFileContent() ) );
			argBean.setCreateDate( attachment.getCreatedDate() );

			boolean submitResult = false;
			ApplyLoan applyLoan = attachment.getApplyLoan();
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitAttachment( argBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = iloanSenderService.submitAttachment( argBean ).isStat();

			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "補件上傳-連結 eloan / iloan 系統錯誤" );

			return false;
		}

	}

	/**
	 * 青創上傳補件送 eloan
	 *
	 * @param attachment
	 * @return
	 */
	public boolean submitAttachment( ApplyYouthStartUpAttachment attachment )
	{
		try
		{
			AttachmentSubmittedArgBean argBean = new AttachmentSubmittedArgBean();
			argBean.setCaseNo( attachment.getApplyYouthStartUp().getCaseNo() );
			argBean.setServiceType( "1" );
			argBean.setAttachmentId( attachment.getAttachmentId().toString() );
			argBean.setAttachmentType( attachment.getCodeAttachmentType().getAttachmentType() );
			argBean.setFileName( attachment.getFileName() );
			argBean.setFileContent( CommonBase64Utils.base64Encoder( attachment.getFileContent() ) );
			argBean.setCreateDate( attachment.getCreatedDate() );
			argBean.setAgreedDate( attachment.getApplyYouthStartUp().getAgreedDate() );

			boolean submitResult = false;
			submitResult = eloanSenderService.submitAttachment( argBean ).isStat();

			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "青創補件上傳-連結 eloan 系統錯誤" );

			return false;
		}

	}

	/**
	 * 房貸增貸對保完成後發送各項約據PDF&央行切結書PDF至 eloan
	 *
	 * @param ApplyCollateralContractAttachment attachment
	 * @return
	 */
	public boolean submitCollateralContractAttachment( ApplyCollateralContractAttachment attachment )
	{
		try
		{
			AttachmentSubmittedArgBean submittedArgBean = new AttachmentSubmittedArgBean();
			submittedArgBean.setCaseNo( attachment.getApplySigningContract().getContractNo() );
			submittedArgBean.setServiceType( getServiceCode( ServiceTypeEnum.SIGNING_CONTRACT.getContext() ) );
			submittedArgBean.setAttachmentId( attachment.getAttachmentId().toString() );
			submittedArgBean.setAttachmentType( attachment.getCodeAttachmentType().getAttachmentType() );
			submittedArgBean.setFileName( attachment.getFileName() );
			submittedArgBean.setFileContent( CommonBase64Utils.base64Encoder( attachment.getFileContent() ) );
			submittedArgBean.setCreateDate( attachment.getCreatedDate() );

			return eloanSenderService.submitAttachment( submittedArgBean ).isStat();
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME,
											"房貸增貸對保完成後發送各項約據PDF&央行切結書PDF-連結 eloan 系統錯誤" );
			return false;
		}
	}

	public boolean submitContractPdf( ApplySigningContract applySigningContract )
	{
		try
		{
			AttachmentSubmittedArgBean argBean = new AttachmentSubmittedArgBean();
			argBean.setCaseNo( applySigningContract.getContractNo() );
			argBean.setServiceType( "4" );
			argBean.setAttachmentId( "0" );
			argBean.setAttachmentType( "ctr" );
			argBean.setFileName( "契約書.pdf" );
			argBean.setFileContent( CommonBase64Utils.base64Encoder( generateService.generateSigningContractPdf( applySigningContract ) ) );
			argBean.setCreateDate( new Date() );

			boolean submitResult = false;
			if( Objects.isNull( applySigningContract.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == applySigningContract.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitAttachment( argBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == applySigningContract.getCodeRecipientSystem().getSystemId() )
				submitResult = iloanSenderService.submitAttachment( argBean ).isStat();
			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME,
											"契約書PDF重送eLoan-連結 eloan / iloan 系統錯誤" );

			return false;
		}

	}

	/**
	 * 房貸申請案件送 eloan
	 *
	 * @param applyLoan
	 * @return
	 */
	public boolean submitHouseLoanApply( ApplyLoan applyLoan )
	{
		try
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( applyLoan.getValidatedIdentityId() );

			AuthBankInfoResultBean authBankInfo = getAuthBankInfoResult( identityInfo.getIdentityType(), identityInfo.getRefIdentityId() );

			HouseLoanApplySubmittedArgBean argBean = new HouseLoanApplySubmittedArgBean();
			argBean.setCaseNo( applyLoan.getCaseNo() );
			argBean.setUserType( getUserType( identityInfo.getUserSubType() ) );
			argBean.setIdentityType( identityInfo.getIdentityType() );
			argBean.setAgreementVersion( applyLoan.getLoanVersion() );
			argBean.setAgreedDate( applyLoan.getAgreedDate() );
			argBean.setApplyTime( applyLoan.getApplyCompletedDate() );
			argBean.setIpAddr( identityInfo.getClientAddress().split( ":" )[ 0 ] );
			argBean.setAuthBankCode( authBankInfo.getBankCode() );
			argBean.setAuthBankAcct( authBankInfo.getBankAccount() );
			argBean.setFile( CommonBase64Utils.base64Encoder( applyLoan.getPdfContent() ) );
			argBean.setHouseLoanBasicInfoBean( getHouseLoanLoanBasicInfo( applyLoan ) );

			if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
				argBean.setHouseLoanInfoBean( getHouseLoanLoanInfo( applyLoan ) );
			else
			{
				argBean.setHouseLoanInfoBean( getHouseLoanLoanInfo( applyLoan.getRefBorrowerApplyLoan() ) );
				argBean.setRelatedCaseNo( applyLoan.getRefBorrowerApplyLoan().getCaseNo() );
			}

			argBean.setLoanPlan( StringUtils.trimToEmpty( applyLoan.getLoanPlanCode() ) );
			argBean.setIntroduceBr1st( StringUtils.trimToEmpty( applyLoan.getIntroduceBr1st() ) );
			argBean.setIntroduceBrNo( StringUtils.trimToEmpty( applyLoan.getIntroduceBrNo() ) );
			return eloanSenderService.submitHouseLoanApply( argBean ).isStat();
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "房貸申請-連結 eloan 系統錯誤" );
			logger.error( CommonStringUtils.getStackTrace( ex ) );

			return false;
		}

	}

	/**
	 * 上傳 ixml 憑證申請紀錄
	 *
	 * @param applyRecord
	 * @return
	 */
	public boolean submitIxmlCert( IxmlApplyRecord applyRecord )
	{
		try
		{
			IxmlSaveCertArgBean bean = new IxmlSaveCertArgBean();
			bean.setPlatForm( "ploan" );
			bean.setUserId( applyRecord.getIdNo().trim() );
			bean.setUserIdDup( "0" );
			bean.setUserCode( "" );
			bean.setDeviceUuid( "ploan" );
			bean.setDeviceInfo( "ploan web" );
			bean.setCertNo( applyRecord.getCertSn().trim() );
			bean.setCertCn( applyRecord.getCertNo().trim() );
			bean.setCertNotBefore( applyRecord.getCertNoBefore().trim() );
			bean.setCertNotAfter( applyRecord.getCertNoAfter().trim() );
			bean.setClientAddress( applyRecord.getClientAddress() );

			IxmlSaveCertResBean resBean = ixmlSenderService.saveCert( bean );

			return "0000".equals( resBean.getRescode() ) && resBean.getIxmlSaveCertResbodyBean() != null
				&& "0000".equals( resBean.getIxmlSaveCertResbodyBean().getRsCode() );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "ixml 憑證紀錄批次傳輸失敗" );

			return false;
		}
	}

	/**
	 * 上傳 ixml 憑證申請紀錄
	 *
	 * @param loanId
	 * @return
	 */
	public boolean submitIxmlCert( String userId, IxmlQueryVerifyResultCertParamsBean certParamsBean, String clientAddress )
	{
		try
		{
			IxmlSaveCertArgBean bean = new IxmlSaveCertArgBean();
			bean.setPlatForm( "ploan" );
			bean.setUserId( userId.trim() );
			bean.setUserIdDup( "0" );
			bean.setUserCode( "" );
			bean.setDeviceUuid( "ploan" );
			bean.setDeviceInfo( "ploan web" );
			bean.setCertNo( certParamsBean.getCertSN().trim() );
			bean.setCertCn( certParamsBean.getCertCN().trim() );
			bean.setCertNotBefore( certParamsBean.getCertNotBefore().trim() );
			bean.setCertNotAfter( certParamsBean.getCertNotAfter().trim() );
			bean.setClientAddress( clientAddress );

			IxmlSaveCertResBean resBean = ixmlSenderService.saveCert( bean );

			return "0000".equals( resBean.getRescode() ) && resBean.getIxmlSaveCertResbodyBean() != null
				&& "0000".equals( resBean.getIxmlSaveCertResbodyBean().getRsCode() );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "ixml 憑證紀錄批次傳輸失敗" );

			return false;
		}
	}

	/**
	 * 上傳 ixml PDF附件
	 *
	 * @param loanId
	 * @return
	 */
	public boolean submitIxmlPdf( Long loanId )
	{
		try
		{
			IxmlAttachment pdf = ixmlAttachmentDAO.read( loanId );
			ApplyLoan applyLoan = applyLoanDAO.read( loanId );

			AttachmentSubmittedArgBean argBean = new AttachmentSubmittedArgBean();
			argBean.setCaseNo( applyLoan.getCaseNo() );
			argBean.setServiceType( "1" );
			// argBean.setAttachmentId( pdf.getLoanId() + pdf.getUpdatedDate().toString() );
			argBean.setAttachmentId( String.valueOf( pdf.getLoanId() ) );
			argBean.setFileName( "查調公務機關資料授權書.pdf" );
			argBean.setAttachmentType( "ixmlagreement" );
			argBean.setCreateDate( new Date() );
			argBean.setFileContent( CommonBase64Utils.base64Encoder( pdf.getPdfContent() ) );

			boolean submitResult = false;
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitAttachment( argBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = iloanSenderService.submitAttachment( argBean ).isStat();

			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "補件上傳-連結 eloan / iloan 系統錯誤" );

			return false;
		}
	}

	/**
	 * 上傳 ixml 簽章附件
	 *
	 * @param loanId
	 * @return
	 */
	public boolean submitIxmlSignature( Long loanId, Long signatureId )
	{
		try
		{
			IxmlSignatures signatures = ixmlSignaturesDAO.read( signatureId );
			ApplyLoan applyLoan = applyLoanDAO.read( loanId );

			AttachmentSubmittedArgBean argBean = new AttachmentSubmittedArgBean();
			argBean.setCaseNo( applyLoan.getCaseNo() );
			argBean.setServiceType( "1" );
			// argBean.setAttachmentId( String.valueOf( signatures.getSignatureId() ) );
			argBean.setAttachmentId( String.valueOf( signatures.getLoanId() ) );
			argBean.setFileName( "agreement.json" );
			argBean.setAttachmentType( "ixmljws" );
			argBean.setCreateDate( new Date() );
			// argBean.setFileContent( CommonBase64Utils.base64Encoder( signatures.getSendContent() ) );
			argBean.setFileContent( new String( CommonUtil.getByteArray( signatures.getSendContent() ), StandardCharsets.UTF_8 ) );

			boolean submitResult = false;
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitAttachment( argBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = iloanSenderService.submitAttachment( argBean ).isStat();

			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "補件上傳-連結 eloan / iloan 系統錯誤" );

			return false;
		}
	}

	/**
	 * 擔保品提供人同意案件送 eloan
	 *
	 * @param applyCollateral
	 * @return
	 */
	public boolean submitLoanCollateral( ApplyCollateral applyCollateral )
	{
		try
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( applyCollateral.getValidatedIdentityId() );

			ProviderAgreementSubmittedArgBean argBean = new ProviderAgreementSubmittedArgBean();

			argBean.setProviderContractNo( applyCollateral.getContractNo() );
			argBean.setProviderBirthDate( CommonDateStringUtils.transDate2String( identityInfo.getBirthDate() ) );
			argBean.setProviderSignDate( applyCollateral.getCompletedDate() );
			argBean.setProviderAgreementFile( CommonBase64Utils.base64Encoder( applyCollateral.getPdfContent() ) );

			ApplyCollateralAgreement applyCollateralAgreement = applyCollateral.getApplyCollateralAgreement();
			argBean.setProviderEmail( applyCollateralAgreement.getEmail() );
			argBean.setCollateralStatus( BooleanUtils.toString( applyCollateralAgreement.isFullPayment(), "0", "1" ) );
			argBean.setCollateralCity( applyCollateralAgreement.getCodeTown().getCodeCity().getName() );
			argBean.setCollateralTown( applyCollateralAgreement.getCodeTown().getName() );
			argBean.setCollateralStreet( applyCollateralAgreement.getCollateralStreet() );
			argBean.setBorrowerSignDate( CommonDateStringUtils.transDate2String( applyCollateralAgreement.getBorrowerSignDate() ) );
			argBean.setProviderAgreementBean( getProviderAgreementBean( applyCollateralAgreement ) );

			return eloanSenderService.submitProviderAgreement( argBean ).isStat();
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "房貸擔保品提供人同意事項-連結 eloan 系統錯誤" );

			return false;
		}
	}

	/**
	 * 擔保品提供人同意案件送 eloan
	 *
	 * @param applyCollateral
	 * @return
	 */
	public boolean submitLoanCollateral( ApplyCollateral applyCollateral, byte[] pdfConent )
	{
		try
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( applyCollateral.getValidatedIdentityId() );

			ProviderAgreementSubmittedArgBean argBean = new ProviderAgreementSubmittedArgBean();

			argBean.setProviderContractNo( applyCollateral.getContractNo() );
			argBean.setProviderBirthDate( CommonDateStringUtils.transDate2String( identityInfo.getBirthDate() ) );
			argBean.setProviderSignDate( applyCollateral.getCompletedDate() );
			argBean.setProviderAgreementFile( CommonBase64Utils.base64Encoder( pdfConent ) );

			ApplyCollateralAgreement applyCollateralAgreement = applyCollateral.getApplyCollateralAgreement();
			argBean.setProviderEmail( applyCollateralAgreement.getEmail() );
			argBean.setCollateralStatus( BooleanUtils.toString( applyCollateralAgreement.isFullPayment(), "0", "1" ) );
			argBean.setCollateralCity( applyCollateralAgreement.getCodeTown().getCodeCity().getName() );
			argBean.setCollateralTown( applyCollateralAgreement.getCodeTown().getName() );
			argBean.setCollateralStreet( applyCollateralAgreement.getCollateralStreet() );
			argBean.setBorrowerSignDate( CommonDateStringUtils.transDate2String( applyCollateralAgreement.getBorrowerSignDate() ) );
			argBean.setProviderAgreementBean( getProviderAgreementBean( applyCollateralAgreement ) );

			return eloanSenderService.submitProviderAgreement( argBean ).isStat();
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "房貸擔保品提供人同意事項-連結 eloan 系統錯誤" );

			return false;
		}
	}

	/**
	 * 信貸申請案件送 eloan / iloan
	 *
	 * @param applyLoan
	 * @return
	 */
	public boolean submitPersonalLoanApply( ApplyLoan applyLoan )
	{
		try
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( applyLoan.getValidatedIdentityId() );

			AuthBankInfoResultBean authBankInfo = getAuthBankInfoResult( identityInfo.getIdentityType(), identityInfo.getRefIdentityId() );

			PersonalLoanApplySubmittedArgBean argBean = new PersonalLoanApplySubmittedArgBean();
			argBean.setCaseNo( applyLoan.getCaseNo() );
			argBean.setUserType( getUserType( identityInfo.getUserSubType() ) );
			argBean.setIdentityType( identityInfo.getIdentityType() );
			argBean.setAgreementVersion( applyLoan.getLoanVersion() );
			argBean.setAgreedDate( applyLoan.getAgreedDate() );
			argBean.setApplyTime( applyLoan.getApplyCompletedDate() );
			argBean.setIpAddr( identityInfo.getClientAddress().split( ":" )[ 0 ] );
			argBean.setAuthBankCode( authBankInfo.getBankCode() );
			argBean.setAuthBankAcct( authBankInfo.getBankAccount() );
			argBean.setFile( CommonBase64Utils.base64Encoder( applyLoan.getPdfContent() ) );
			argBean.setPersonalLoanBasicInfoBean( getPersonalLoanBasicInfo( applyLoan ) );

			if( !Objects.isNull( applyLoan.getApplyLoanContent().getCodeCaseSource() ) )
				argBean.setCaseSource( applyLoan.getApplyLoanContent().getCodeCaseSource().getCaseSourceCode() );

			if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
				argBean.setPersonalLoanInfoBean( getPersonalLoanInfo( applyLoan ) );
			else
			{
				argBean.setPersonalLoanInfoBean( getPersonalLoanInfo( applyLoan.getRefBorrowerApplyLoan() ) );
				argBean.setRelatedCaseNo( applyLoan.getRefBorrowerApplyLoan().getCaseNo() );
			}

			argBean.setLoanPlan( StringUtils.trimToEmpty( applyLoan.getLoanPlanCode() ) );
			argBean.setIntroduceBr1st( StringUtils.trimToEmpty( applyLoan.getIntroduceBr1st() ) );
			argBean.setIntroduceBrNo( StringUtils.trimToEmpty( applyLoan.getIntroduceBrNo() ) );

			boolean submitResult = false;
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitPersonalLoanApply( argBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				submitResult = iloanSenderService.submitPersonalLoanApply( personalLoanCovert2IloanFormat( argBean ) ).isStat();
			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "信貸申請-連結 eloan / iloan 系統錯誤" );
			logger.error( CommonStringUtils.getStackTrace( ex ) );

			return false;
		}
	}

	/**
	 * iloan 信貸申請案件轉送 eloan
	 *
	 * @param applyLoan
	 * @return
	 */
	public boolean submitPersonalLoanApply2Eloan( ApplyLoan applyLoan )
	{
		try
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( applyLoan.getValidatedIdentityId() );

			AuthBankInfoResultBean authBankInfo = getAuthBankInfoResult( identityInfo.getIdentityType(), identityInfo.getRefIdentityId() );

			PersonalLoanApplySubmittedArgBean argBean = new PersonalLoanApplySubmittedArgBean();
			argBean.setCaseNo( applyLoan.getCaseNo() );
			argBean.setUserType( getUserType( identityInfo.getUserSubType() ) );
			argBean.setIdentityType( identityInfo.getIdentityType() );
			argBean.setAgreementVersion( applyLoan.getLoanVersion() );
			argBean.setAgreedDate( applyLoan.getAgreedDate() );
			argBean.setApplyTime( applyLoan.getApplyCompletedDate() );
			argBean.setIpAddr( identityInfo.getClientAddress().split( ":" )[ 0 ] );
			argBean.setAuthBankCode( authBankInfo.getBankCode() );
			argBean.setAuthBankAcct( authBankInfo.getBankAccount() );
			argBean.setFile( CommonBase64Utils.base64Encoder( applyLoan.getPdfContent() ) );
			argBean.setPersonalLoanBasicInfoBean( getPersonalLoanBasicInfo( applyLoan ) );

			if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
				argBean.setPersonalLoanInfoBean( getPersonalLoanInfo( applyLoan ) );
			else
			{
				argBean.setPersonalLoanInfoBean( getPersonalLoanInfo( applyLoan.getRefBorrowerApplyLoan() ) );
				argBean.setRelatedCaseNo( applyLoan.getRefBorrowerApplyLoan().getCaseNo() );
			}

			argBean.setLoanPlan( StringUtils.trimToEmpty( applyLoan.getLoanPlanCode() ) );
			argBean.setIntroduceBr1st( StringUtils.trimToEmpty( applyLoan.getIntroduceBr1st() ) );
			argBean.setIntroduceBrNo( StringUtils.trimToEmpty( applyLoan.getIntroduceBrNo() ) );
			return eloanSenderService.submitPersonalLoanApply( argBean ).isStat();
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "信貸申請-連結 eloan 系統錯誤" );
			logger.error( CommonStringUtils.getStackTrace( ex ) );

			return false;
		}
	}

	/**
	 * 對保案件送 eloan
	 *
	 * @param contract
	 * @param pdfContent
	 * @return
	 */
	public boolean submitSigningContract( ApplySigningContract contract, byte[] pdfContent )
	{
		try
		{
			ApplySigningAppropriation appropiration = contract.getApplySigningAppropriation();

			if( appropiration == null )
				return false;

			SigningContractCompletedArgBean argBean = new SigningContractCompletedArgBean();
			argBean.setContractNo( contract.getContractNo() );
			argBean.setBankAcctCode( contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankAccount() );
			argBean.setBankAcctNo( contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankCode() );
			argBean.setFile( CommonBase64Utils.base64Encoder( pdfContent ) );
			argBean.setLoanBeginDate( CommonDateStringUtils.transDate2String( appropiration.getAppropriationDate() ) );
			argBean.setLoanEndDate( calulateLoanEndDate( contract ) );
			argBean.setRepaymentschedule( String.valueOf( appropiration.getRepaymentDay() ) );
			argBean.setFirstPaymentDate( CommonDateStringUtils.transDate2String( appropiration.getFirstPaymentDate() ) );
			argBean.setRateAdjustInformMethod( appropiration.getCodeRateAdjustmentNotification().getRateAdjustmentNotificaitonCode() );
			argBean.setContractCheckDate( CommonDateStringUtils.transDate2String( appropiration.getContractCheckDate() ) );

			argBean.setBorrowerAgreementVersion( contract.getContractVersion() );
			argBean.setStakeholderAgreementVersion( contract.getContractVersion() );

			for( ApplySigningUser user : contract.getApplySigningUsers() )
			{
				IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );

				AuthBankInfoResultBean authBankInfo = getAuthBankInfoResult( identityInfo.getIdentityType(), identityInfo.getRefIdentityId() );

				if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
					setSigningBorrowerInfo( argBean, identityInfo, user, authBankInfo );
				else
					setSigningStakeholderInfo( argBean, identityInfo, user, authBankInfo );

			}

			// J-111-0272 eDDA驗證，需要上傳身分證正反面及eDDA相關資訊
			if( contract.getIsNeedAch() != null )
			{
				if( contract.getIsNeedAch() )
				{
					ApplySigningEdda applySigningEDDA = signingEddaDAO.readLastest( contract.getSigningContractId() );
					argBean.setRc( applySigningEDDA.getRc() );
					argBean.setRcDesc( applySigningEDDA.getRcDesc() );
					argBean.setRcADate( CommonDateStringUtils.transDate2String( applySigningEDDA.getRcAdate(), "yyyy-MM-dd HH:mm:ss" ) );
					argBean.setBankACHAcctCode( contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankCode() );
					argBean.setBankACHAcctNo( contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankAccount() );
					argBean.setBankACHAcctBranchCode( contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankCode()
						+ contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankBranchCode() );
				}
				argBean.setBankAcctBranchCode( contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankCode()
					+ contract.getApplySigningAppropriation().getApplySigningBankAccount().getBankBranchCode() );
				argBean.setIsNeedACH( contract.getIsNeedAch() );

				// for( ApplySigningUser user : contract.getApplySigningUsers() )
				// {
				// IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );
				//
				// if( IdentityTypeEnum.OTHER_BANK.getContext().equals( identityInfo.getIdentityType() )
				// && UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ))
				// {
				// IdentityAttachmentsArgBean identityInfoargBean = new IdentityAttachmentsArgBean();
				// identityInfoargBean.setOtherBankId( identityInfo.getRefIdentityId() );
				//
				// List<IdentityAttachmentResultBean> identityAttachments = identityAttachmentsClient.send( identityInfoargBean );
				//
				// for(IdentityAttachmentResultBean attach: identityAttachments) {
				// String fileExt = "jpg";
				// try {
				// fileExt = attach.getFileName().split( "." )[1];
				// }
				// catch(Exception ex) {
				//
				// }
				//
				// if(attach.getAttachmentType().equals( "front" )) {
				// argBean.setIdCardFile1( attach.getCompressFileContent() );
				// argBean.setIdCardFileExt1( fileExt );
				// }else if(attach.getAttachmentType().equals( "back" )) {
				// argBean.setIdCardFile2( attach.getCompressFileContent() );
				// argBean.setIdCardFileExt2( fileExt );
				// }
				// }
				// }
				// }
			}

			boolean submitResult = false;
			if( Objects.isNull( contract.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == contract.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitSigningContract( argBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == contract.getCodeRecipientSystem().getSystemId() )
			{
				SigningContractCompletedArgBean iloanFormatArgBean = completedSigningContractCovert2IloanFormat( argBean );
				submitResult = iloanSenderService.submitSigningContract( iloanFormatArgBean ).isStat();
			}
			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "貸款對保-連結 eloan / iloan 系統錯誤" );
			logger.error( CommonStringUtils.getStackTrace( ex ) );

			return false;
		}
	}

	/**
	 * 對保補件送 eloan
	 *
	 * @param contract
	 * @return
	 */
	public boolean submitSigningContractAttachment( ApplySigningContract contract )
	{
		try
		{
			// for( ApplySigningUser user : contract.getApplySigningUsers() )
			// {
			// IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );
			//
			// if( IdentityTypeEnum.OTHER_BANK.getContext().equals( identityInfo.getIdentityType() ) )
			// submitIdentityAttachments( identityInfo.getRefIdentityId(), contract.getContractNo(),
			// ServiceTypeEnum.SIGNING_CONTRACT.getContext() );
			//
			// }

			submitIdentityAttachmentDTOs( contract.getSigningContractId(), contract.getContractNo(), ServiceTypeEnum.SIGNING_CONTRACT.getContext() );

			return true;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME, "貸款對保-連結 eloan / iloan 系統錯誤" );

			return false;
		}
	}

	/**
	 * 撥款完成後發送契約書PDF至 eloan / iloan
	 *
	 * @param contract
	 * @return
	 */
	public boolean submitSigningContractPdf( ApplySigningContract contract )
	{
		try
		{
			AttachmentSubmittedArgBean submittedArgBean = new AttachmentSubmittedArgBean();
			submittedArgBean.setCaseNo( contract.getContractNo() );
			submittedArgBean.setServiceType( "2" );
			submittedArgBean.setAttachmentId( contract.getSigningContractId().toString() );
			submittedArgBean.setAttachmentType( "contractpdf" );
			submittedArgBean.setFileName( "撥款合約.pdf" );
			submittedArgBean.setFileContent( CommonBase64Utils.base64Encoder( contract.getPdfContent() ) );
			submittedArgBean.setCreateDate( new Date() );

			boolean submitResult = false;
			if( Objects.isNull( contract.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == contract.getCodeRecipientSystem().getSystemId() )
				submitResult = eloanSenderService.submitAttachment( submittedArgBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == contract.getCodeRecipientSystem().getSystemId() )
				submitResult = iloanSenderService.submitAttachment( submittedArgBean ).isStat();
			return submitResult;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, AP_NAME,
											"撥款完成後發送契約書PDF-連結 eloan / iloan 系統錯誤" );
			logger.error( CommonStringUtils.getStackTrace( ex ) );

			return false;
		}
	}

	private String calulateLoanEndDate( ApplySigningContract contract )
	{
		Date loanEndDate = getLoanEndDate( contract.getLoanPeriod(), contract.getApplySigningAppropriation().getAppropriationDate() );

		if( isHouseloan( contract ) && isShortMidTerm( contract ) )
			loanEndDate = DateUtils.addDays( loanEndDate, -1 );

		return CommonDateStringUtils.transDate2String( loanEndDate );
	}

	private SigningContractCompletedArgBean completedSigningContractCovert2IloanFormat( SigningContractCompletedArgBean argBean )
	{
		String temp = argBean.getBankAcctCode();
		argBean.setBankAcctCode( argBean.getBankAcctNo() );
		argBean.setBankAcctNo( temp );

		return argBean;
	}

	private String emptyIfNull( Object obj )
	{
		return obj == null ? "" : String.valueOf( obj );

	}

	private String getAddress( String city, String town, String street )
	{
		if( StringUtils.isAnyBlank( city, town, street ) )
			return null;

		return city + town + street;
	}

	private List<String> getApplyStatusCodes( String applyStatusGroup )
	{
		List<String> applyStatusList = new ArrayList<>();

		for( ApplyStatusEnum applyStatusEnum : ApplyStatusEnum.values() )
			if( applyStatusEnum.isEnabled() && ( applyStatusEnum.getGroup().equals( applyStatusGroup ) || StringUtils.isBlank( applyStatusGroup ) ) )
				applyStatusList.add( applyStatusEnum.getContext() );

		return applyStatusList;
	}

	private AuthBankInfoResultBean getAuthBankInfoResult( String identityType, Long refIdentityId )
	{
		if( !IdentityTypeEnum.OTHER_BANK.getContext().equals( identityType ) )
			return new AuthBankInfoResultBean();

		AuthBankInfoArgBean argBean = new AuthBankInfoArgBean();
		argBean.setOtherBankId( refIdentityId );

		return authBankInfoClient.send( argBean );

	}

	private HouseLoanBasicInfoBean getHouseLoanLoanBasicInfo( ApplyLoan applyLoan )
	{
		HouseLoanBasicInfoBean basicInfoBean = new HouseLoanBasicInfoBean();

		ApplyLoanBasic basic = applyLoan.getApplyLoanBasic();
		basicInfoBean.setName( basic.getName() );
		basicInfoBean.setEnName( ApplyLoanUtils.getEnName_decide_by_fg( basic ) );
		basicInfoBean.setBirthDate( CommonDateStringUtils.transDate2String( basic.getBirthDate() ) );
		basicInfoBean.setIdNo( basic.getIdNo() );
		basicInfoBean.setEducationLevel( basic.getCodeEducationLevel().getEducationLevelCode() );
		basicInfoBean.setMarriageStatus( basic.getCodeMarriageStatus().getMarriageStatusCode() );
		basicInfoBean.setChildrenCount( ApplyLoanUtils.get_ApplyLoanBasic_childrenCount( applyLoan.getLoanPlanCode(), basic ) );
		basicInfoBean.setNationality( getNationality( basic.getCodeNationality() ) );

		ApplyLoanContactInfo contactInfo = applyLoan.getApplyLoanContactInfo();
		basicInfoBean.setResidenceStatus( contactInfo.getCodeResidenceStatus().getResidenceStatusCode() );
		basicInfoBean.setRentAmt( contactInfo.getRent() );
		basicInfoBean.setMobileNumber( contactInfo.getMobileNumber() );
		basicInfoBean.setEmail( contactInfo.getEmail() );
		basicInfoBean.setHomePhone( getPhnNumber( contactInfo.getHomePhoneCode(), contactInfo.getHomePhoneNumber(), null ) );

		ApplyAddress homeAddress = contactInfo.getHomeAddress();
		basicInfoBean.setHomeAddressCity( homeAddress.getCodeTown().getCodeCity().getName() );
		basicInfoBean.setHomeAddressTown( homeAddress.getCodeTown().getName() );
		basicInfoBean.setHomeAddressVillage( homeAddress.getVillage() );
		basicInfoBean.setHomeAddressNeighborhood( homeAddress.getNeighborhood() );
		basicInfoBean.setHomeAddressStreet( homeAddress.getStreet() );
		basicInfoBean.setHomeAddressSection( emptyIfNull( homeAddress.getSection() ) );
		basicInfoBean.setHomeAddressLane( homeAddress.getLane() );
		basicInfoBean.setHomeAddressAlley( homeAddress.getAlley() );
		basicInfoBean.setHomeAddressNo( homeAddress.getNo() );
		basicInfoBean.setHomeAddressFloor( homeAddress.getFloor() );
		basicInfoBean.setHomeAddressRoom( emptyIfNull( homeAddress.getRoom() ) );

		ApplyAddress mailingAddress = contactInfo.getMailingAddress();
		basicInfoBean.setMailingAddressCity( mailingAddress.getCodeTown().getCodeCity().getName() );
		basicInfoBean.setMailingAddressTown( mailingAddress.getCodeTown().getName() );
		basicInfoBean.setMailingAddressVillage( mailingAddress.getVillage() );
		basicInfoBean.setMailingAddressNeighborhood( mailingAddress.getNeighborhood() );
		basicInfoBean.setMailingAddressStreet( mailingAddress.getStreet() );
		basicInfoBean.setMailingAddressSection( emptyIfNull( mailingAddress.getSection() ) );
		basicInfoBean.setMailingAddressLane( mailingAddress.getLane() );
		basicInfoBean.setMailingAddressAlley( mailingAddress.getAlley() );
		basicInfoBean.setMailingAddressNo( mailingAddress.getNo() );
		basicInfoBean.setMailingAddressFloor( mailingAddress.getFloor() );
		basicInfoBean.setMailingAddressRoom( emptyIfNull( mailingAddress.getRoom() ) );

		ApplyLoanOccupation occupation = applyLoan.getApplyLoanOccupation();
		basicInfoBean.setJobType( occupation.getCodeJobSubType().getCodeJobType().getJobType() );
		basicInfoBean.setJobSubType( occupation.getCodeJobSubType().getSubTypeCode() );
		basicInfoBean.setTitleType( occupation.getCodeTitleType().getTitleType() );
		basicInfoBean.setCompanyName( occupation.getCompanyName() );
		basicInfoBean.setCompanyPhn( getPhnNumber( occupation.getCompanyPhoneCode(), occupation.getCompanyPhoneNumber(),
												   occupation.getCompanyPhoneExt() ) );
		basicInfoBean.setCompanyTaxNo( occupation.getTaxNo() );

		ApplyAddress companyAddress = occupation.getApplyAddress();
		basicInfoBean.setCompanyAddressCity( companyAddress.getCodeTown().getCodeCity().getName() );
		basicInfoBean.setCompanyAddressTown( companyAddress.getCodeTown().getName() );
		basicInfoBean.setCompanyAddressVillage( companyAddress.getVillage() );
		basicInfoBean.setCompanyAddressNeighborhood( companyAddress.getNeighborhood() );
		basicInfoBean.setCompanyAddressStreet( companyAddress.getStreet() );
		basicInfoBean.setCompanyAddressSection( emptyIfNull( companyAddress.getSection() ) );
		basicInfoBean.setCompanyAddressLane( companyAddress.getLane() );
		basicInfoBean.setCompanyAddressAlley( companyAddress.getAlley() );
		basicInfoBean.setCompanyAddressNo( companyAddress.getNo() );
		basicInfoBean.setCompanyAddressFloor( companyAddress.getFloor() );
		basicInfoBean.setCompanyAddressRoom( emptyIfNull( companyAddress.getRoom() ) );
		basicInfoBean.setAnnualIncome( occupation.getAnnualIncome() );
		basicInfoBean.setSeniority( occupation.getSeniorityYear() );
		basicInfoBean.setSeniorityMonth( occupation.getSeniorityMonth() );
		basicInfoBean.setAvgTransactionAmt( occupation.getCodeAmountPerMonth().getAmountPerMonthCode() );

		if( contactInfo.getCodeServiceAssociateDept() != null )
		{
			basicInfoBean.setServiceAssociateDeptCode( contactInfo.getCodeServiceAssociateDept().getServiceAssociateDeptCode() );
			basicInfoBean.setServiceAssociateCode( contactInfo.getServiceAssociate() );

		}

		basicInfoBean.setAssignedBranchCode( contactInfo.getCodeBranchBank().getBankCode() );
		basicInfoBean.setResultBranchCode( applyLoan.getCodeBranchBank().getBankCode() );

		setHouseLoanGuaranteeInfo( basicInfoBean, applyLoan.getApplyLoanGuaranteeInfo() );

		return basicInfoBean;
	}

	private HouseLoanInfoBean getHouseLoanLoanInfo( ApplyLoan applyLoan )
	{
		HouseLoanInfoBean loanInfoBean = new HouseLoanInfoBean();

		ApplyLoanContent content = applyLoan.getApplyLoanContent();
		loanInfoBean.setLoanRequestAmt( content.getLoanRequestAmt() );
		loanInfoBean.setLoanPeriod( Integer.parseInt( content.getCodeLoanPeriod().getName() ) );
		loanInfoBean.setLoanPurpose( content.getCodeLoanPurpose().getLoanPurposeCode() );
		loanInfoBean.setOtherPurpose( content.getOtherPurpose() );
		loanInfoBean.setNotificationMethod( content.getCodeNotification().getNotificationCode() );
		loanInfoBean.setGracePeriod( content.getCodeGracePeriod() == null ? null : Integer.parseInt( content.getCodeGracePeriod().getName() ) * 12 );

		ApplyAddress collateralAddress = content.getApplyAddress();
		if( collateralAddress != null )
		{
			loanInfoBean.setCollateralAddressCity( collateralAddress.getCodeTown().getCodeCity().getName() );
			loanInfoBean.setCollateralAddressTown( collateralAddress.getCodeTown().getName() );
			loanInfoBean.setCollateralAddressVillage( collateralAddress.getVillage() );
			loanInfoBean.setCollateralAddressNeighborhood( collateralAddress.getNeighborhood() );
			loanInfoBean.setCollateralAddressStreet( collateralAddress.getStreet() );
			loanInfoBean.setCollateralAddressSection( emptyIfNull( collateralAddress.getSection() ) );
			loanInfoBean.setCollateralAddressLane( collateralAddress.getLane() );
			loanInfoBean.setCollateralAddressAlley( collateralAddress.getAlley() );
			loanInfoBean.setCollateralAddressNo( collateralAddress.getNo() );
			loanInfoBean.setCollateralAddressFloor( collateralAddress.getFloor() );
			loanInfoBean.setCollateralAddressRoom( emptyIfNull( collateralAddress.getRoom() ) );
		}

		loanInfoBean.setMortgageType( content.getCodeMortgageType() == null ? null : content.getCodeMortgageType().getMortgageType() );
		loanInfoBean.setNonPrivateUsageType( content.getCodeNonPrivateUsageType() == null ? null
																						  : content.getCodeNonPrivateUsageType()
																									  .getNonPrivateUsageType() );
		loanInfoBean.setNonPrivateUsageSubType( content.getNonPrivateUsageSubType() );
		loanInfoBean.setPrivateUsageType( content.getCodePrivateUsageType() == null ? null
																					: content.getCodePrivateUsageType().getPrivateUsageType() );
		loanInfoBean.setHouseLoanRelationDataBeans( getHouseLoanRelationDataBeans( content.getApplyLoanRelations() ) );
		loanInfoBean.setHouseLoanServedDataBeans( getHouseLoanServedDataBean( content.getApplyLoanServeds() ) );

		loanInfoBean.setPurposeType( BooleanUtils.toString( content.getIncreasingLoan(), "2", "1", null ) );

		return loanInfoBean;
	}

	private List<HouseLoanRelationDataBean> getHouseLoanRelationDataBeans( Set<ApplyLoanRelation> relations )
	{
		List<HouseLoanRelationDataBean> relationDataBeans = new ArrayList<>();

		for( ApplyLoanRelation relation : relations )
		{
			HouseLoanRelationDataBean relationDataBean = new HouseLoanRelationDataBean();
			relationDataBean.setRelationIdNo( relation.getRelationIdNo() );
			relationDataBean.setRelationName( relation.getRelationName() );
			relationDataBean.setRelationTypeName( relation.getCodeRelationType().getRelationType() );

			relationDataBeans.add( relationDataBean );
		}

		return relationDataBeans;
	}

	private List<HouseLoanServedDataBean> getHouseLoanServedDataBean( Set<ApplyLoanServed> serveds )
	{
		List<HouseLoanServedDataBean> servedDataBeans = new ArrayList<>();
		for( ApplyLoanServed served : serveds )
		{
			HouseLoanServedDataBean servedDataBean = new HouseLoanServedDataBean();
			servedDataBean.setComment( served.getComment() );
			servedDataBean.setCompanyName( served.getCompanyName() );
			servedDataBean.setServedTitle( served.getServedTitle() );
			servedDataBean.setTaxNo( served.getTaxNo() );
			servedDataBean.setCompanyRepresentativeType( served.getCodeRepresentativeType().getRepresentativeType() );

			servedDataBeans.add( servedDataBean );
		}

		return servedDataBeans;
	}

	private Date getLoanEndDate( Integer period, Date beginDate )
	{
		Calendar cal = Calendar.getInstance();
		cal.setTime( beginDate );
		cal.add( Calendar.MONTH, period );
		int year = cal.get( Calendar.YEAR );
		int month = cal.get( Calendar.MONTH ) + 1;
		int day = cal.get( Calendar.DAY_OF_MONTH );

		return CommonDateUtils.getDate( year, month, day );
	}

	private String getNationality( CodeNationality codeNationality )
	{
		return codeNationality == null ? "TW" : codeNationality.getNationalityCode();
	}

	private PersonalLoanBasicInfoBean getPersonalLoanBasicInfo( ApplyLoan applyLoan )
	{
		PersonalLoanBasicInfoBean basicInfoBean = new PersonalLoanBasicInfoBean();

		ApplyLoanBasic basic = applyLoan.getApplyLoanBasic();
		basicInfoBean.setName( basic.getName() );
		basicInfoBean.setEnName( ApplyLoanUtils.getEnName_decide_by_fg( basic ) );
		basicInfoBean.setBirthDate( CommonDateStringUtils.transDate2String( basic.getBirthDate() ) );
		basicInfoBean.setIdNo( basic.getIdNo() );
		basicInfoBean.setEducationLevel( basic.getCodeEducationLevel() == null ? null : basic.getCodeEducationLevel().getEducationLevelCode() );
		basicInfoBean.setMarriageStatus( basic.getCodeMarriageStatus() == null ? null : basic.getCodeMarriageStatus().getMarriageStatusCode() );
		basicInfoBean.setChildrenCount( ApplyLoanUtils.get_ApplyLoanBasic_childrenCount( applyLoan.getLoanPlanCode(), basic ) );
		basicInfoBean.setNationality( getNationality( basic.getCodeNationality() ) );

		ApplyLoanContactInfo contactInfo = applyLoan.getApplyLoanContactInfo();
		basicInfoBean.setResidenceStatus( contactInfo.getCodeResidenceStatus() == null ? null
																					   : contactInfo.getCodeResidenceStatus()
																								   .getResidenceStatusCode() );
		basicInfoBean.setRentAmt( contactInfo.getRent() );
		basicInfoBean.setMobileNumber( contactInfo.getMobileNumber() );
		basicInfoBean.setEmail( contactInfo.getEmail() );
		basicInfoBean.setHomePhone( getPhnNumber( contactInfo.getHomePhoneCode(), contactInfo.getHomePhoneNumber(), null ) );

		ApplyAddress homeAddress = contactInfo.getHomeAddress();
		if( homeAddress != null )
		{
			basicInfoBean.setHomeAddressCity( homeAddress.getCodeTown().getCodeCity().getName() );
			basicInfoBean.setHomeAddressTown( homeAddress.getCodeTown().getName() );
			basicInfoBean.setHomeAddressVillage( homeAddress.getVillage() );
			basicInfoBean.setHomeAddressNeighborhood( homeAddress.getNeighborhood() );
			basicInfoBean.setHomeAddressStreet( homeAddress.getStreet() );
			basicInfoBean.setHomeAddressSection( emptyIfNull( homeAddress.getSection() ) );
			basicInfoBean.setHomeAddressLane( homeAddress.getLane() );
			basicInfoBean.setHomeAddressAlley( homeAddress.getAlley() );
			basicInfoBean.setHomeAddressNo( homeAddress.getNo() );
			basicInfoBean.setHomeAddressFloor( homeAddress.getFloor() );
			basicInfoBean.setHomeAddressRoom( emptyIfNull( homeAddress.getRoom() ) );
		}

		ApplyAddress mailingAddress = contactInfo.getMailingAddress();
		if( mailingAddress != null )
		{
			basicInfoBean.setMailingAddressCity( mailingAddress.getCodeTown().getCodeCity().getName() );
			basicInfoBean.setMailingAddressTown( mailingAddress.getCodeTown().getName() );
			basicInfoBean.setMailingAddressVillage( mailingAddress.getVillage() );
			basicInfoBean.setMailingAddressNeighborhood( mailingAddress.getNeighborhood() );
			basicInfoBean.setMailingAddressStreet( mailingAddress.getStreet() );
			basicInfoBean.setMailingAddressSection( emptyIfNull( mailingAddress.getSection() ) );
			basicInfoBean.setMailingAddressLane( mailingAddress.getLane() );
			basicInfoBean.setMailingAddressAlley( mailingAddress.getAlley() );
			basicInfoBean.setMailingAddressNo( mailingAddress.getNo() );
			basicInfoBean.setMailingAddressFloor( mailingAddress.getFloor() );
			basicInfoBean.setMailingAddressRoom( emptyIfNull( mailingAddress.getRoom() ) );
		}
		ApplyLoanOccupation occupation = applyLoan.getApplyLoanOccupation();
		basicInfoBean.setJobType( occupation.getCodeJobSubType() == null ? null : occupation.getCodeJobSubType().getCodeJobType().getJobType() );
		basicInfoBean.setJobSubType( occupation.getCodeJobSubType() == null ? null : occupation.getCodeJobSubType().getSubTypeCode() );
		basicInfoBean.setTitleType( occupation.getCodeTitleType() == null ? null : occupation.getCodeTitleType().getTitleType() );
		basicInfoBean.setCompanyName( occupation.getCompanyName() );
		basicInfoBean.setCompanyPhone( getPhnNumber( occupation.getCompanyPhoneCode(), occupation.getCompanyPhoneNumber(),
													 occupation.getCompanyPhoneExt() ) );
		basicInfoBean.setCompanyTaxNo( occupation.getTaxNo() );

		ApplyAddress companyAddress = occupation.getApplyAddress();
		if( companyAddress != null )
		{
			basicInfoBean.setCompanyAddressCity( companyAddress.getCodeTown().getCodeCity().getName() );
			basicInfoBean.setCompanyAddressTown( companyAddress.getCodeTown().getName() );
			basicInfoBean.setCompanyAddressVillage( companyAddress.getVillage() );
			basicInfoBean.setCompanyAddressNeighborhood( companyAddress.getNeighborhood() );
			basicInfoBean.setCompanyAddressStreet( companyAddress.getStreet() );
			basicInfoBean.setCompanyAddressSection( emptyIfNull( companyAddress.getSection() ) );
			basicInfoBean.setCompanyAddressLane( companyAddress.getLane() );
			basicInfoBean.setCompanyAddressAlley( companyAddress.getAlley() );
			basicInfoBean.setCompanyAddressNo( companyAddress.getNo() );
			basicInfoBean.setCompanyAddressFloor( companyAddress.getFloor() );
			basicInfoBean.setCompanyAddressRoom( emptyIfNull( companyAddress.getRoom() ) );
		}
		basicInfoBean.setAnnualIncome( occupation.getAnnualIncome() );
		basicInfoBean.setSeniority( occupation.getSeniorityYear() );
		basicInfoBean.setSeniorityMonth( occupation.getSeniorityMonth() );
		basicInfoBean.setAvgTransactionAmt( occupation.getCodeAmountPerMonth() == null ? null
																					   : occupation.getCodeAmountPerMonth().getAmountPerMonthCode() );

		if( contactInfo.getCodeServiceAssociateDept() != null )
		{
			basicInfoBean.setServiceAssociateDeptCode( contactInfo.getCodeServiceAssociateDept().getServiceAssociateDeptCode() );
			basicInfoBean.setServiceAssociateCode( contactInfo.getServiceAssociate() );
		}

		basicInfoBean.setAssignedBranchCode( contactInfo.getCodeBranchBank().getBankCode() );
		basicInfoBean.setResultBranchCode( applyLoan.getCodeBranchBank().getBankCode() );
		if( basic.getNotUsTaxpayer() != null )
			basicInfoBean.setNeedW8BEN( basic.getNotUsTaxpayer() ? "N" : "Y" );
		else
			basicInfoBean.setNeedW8BEN( "" );
		// ========
		if( basic.getNotOuttwTaxpayer() != null )
			basicInfoBean.setNeedCRS( basic.getNotOuttwTaxpayer() ? "N" : "Y" );
		else
			basicInfoBean.setNeedCRS( "" );
		// ========
		basicInfoBean.setRateAdjNotify( basic.getRateAdjNotify() );
		// ========
		if( basic.getCrossMarketing() != null )
			basicInfoBean.setCrossMarket( basic.getCrossMarketing() ? "Y" : "N" );
		else
			basicInfoBean.setCrossMarket( "" );
		// ========
		basicInfoBean.setEmpNo( occupation.getEmpNo() );

		setPersonalLoanGuaranteeInfo( basicInfoBean, applyLoan.getApplyLoanGuaranteeInfo() );

		return basicInfoBean;
	}

	private PersonalLoanInfoBean getPersonalLoanInfo( ApplyLoan applyLoan )
	{
		ApplyLoanContent content = applyLoan.getApplyLoanContent();

		PersonalLoanInfoBean loanInfoBean = new PersonalLoanInfoBean();
		loanInfoBean.setLoanRequestAmt( content.getLoanRequestAmt() );
		loanInfoBean.setLoanPeriod( Integer.parseInt( content.getCodeLoanPeriod().getName() ) );
		loanInfoBean.setLoanPurpose( content.getCodeLoanPurpose().getLoanPurposeCode() );
		loanInfoBean.setOtherPurpose( content.getOtherPurpose() );
		loanInfoBean.setNotificationMethod( content.getCodeNotification().getNotificationCode() );
		loanInfoBean.setAppnBankCode( content.getAppnBankCode() );
		loanInfoBean.setAppnDpAcct( content.getAppnDpAcct() );
		loanInfoBean.setRestrictContr( BooleanUtils.toString( content.getRestrictContr(), "Y", "N", "" ) );
		loanInfoBean.setPersonalLoanRelationDataBeans( getPersonaLoanRelationDataBeans( content.getApplyLoanRelations() ) );
		loanInfoBean.setPersonalLoanServedDataBeans( getPersonaLoanServedDataBean( content.getApplyLoanServeds() ) );
		loanInfoBean.setUrlToIdentifyFraud( content.getUrlToIdentifyFraud() );
		return loanInfoBean;
	}

	private List<PersonalLoanRelationDataBean> getPersonaLoanRelationDataBeans( Set<ApplyLoanRelation> relations )
	{
		List<PersonalLoanRelationDataBean> relationDataBeans = new ArrayList<>();

		for( ApplyLoanRelation relation : relations )
		{
			PersonalLoanRelationDataBean relationDataBean = new PersonalLoanRelationDataBean();
			relationDataBean.setRelationIdNo( relation.getRelationIdNo() );
			relationDataBean.setRelationName( relation.getRelationName() );
			relationDataBean.setRelationTypeName( relation.getCodeRelationType().getRelationType() );

			relationDataBeans.add( relationDataBean );
		}

		return relationDataBeans;
	}

	private List<PersonalLoanServedDataBean> getPersonaLoanServedDataBean( Set<ApplyLoanServed> serveds )
	{
		List<PersonalLoanServedDataBean> servedDataBeans = new ArrayList<>();
		for( ApplyLoanServed served : serveds )
		{
			PersonalLoanServedDataBean servedDataBean = new PersonalLoanServedDataBean();
			servedDataBean.setComment( served.getComment() );
			servedDataBean.setCompanyName( served.getCompanyName() );
			servedDataBean.setServedTitle( served.getServedTitle() );
			servedDataBean.setTaxNo( served.getTaxNo() );
			servedDataBean.setCompanyRepresentativeType( served.getCodeRepresentativeType().getRepresentativeType() );

			servedDataBeans.add( servedDataBean );
		}

		return servedDataBeans;
	}

	/*
	 * getPhoneNumber
	 */
	private String getPhnNumber( String phoneCode, String phoneNumber, String phoneExt )
	{
		if( StringUtils.isAnyBlank( phoneCode, phoneNumber ) )
			return null;

		if( StringUtils.isBlank( phoneExt ) )
			return phoneCode + phoneNumber;

		return phoneCode + phoneNumber + "#" + phoneExt;
	}

	private ProviderAgreementBean getProviderAgreementBean( ApplyCollateralAgreement applyCollateralAgreement )
	{
		ProviderAgreementBean agreementBean = new ProviderAgreementBean();
		agreementBean.setCollateralAmt( applyCollateralAgreement.getCollateralAmt() );
		agreementBean.setLoanAmt( applyCollateralAgreement.getLoanAmt() );
		agreementBean.setGuranteeAmt( applyCollateralAgreement.getGuranteeAmt() );
		agreementBean.setSignatory( applyCollateralAgreement.getSignatory() );
		agreementBean.setTermNo( applyCollateralAgreement.getTermNo() );
		agreementBean.setFirstWarrantee( applyCollateralAgreement.getWarrantee1() );
		agreementBean.setSecondWarrantee( applyCollateralAgreement.getWarrantee2() );
		agreementBean.setThirdWarrantee( applyCollateralAgreement.getWarrantee3() );
		agreementBean.setFirstLoanProduct( applyCollateralAgreement.getLoanProduct1() );
		agreementBean.setSecondLoanProduct( applyCollateralAgreement.getLoanProduct2() );
		agreementBean.setThirdLoanProduct( applyCollateralAgreement.getLoanProduct3() );
		agreementBean.setFourthLoanProduct( applyCollateralAgreement.getLoanProduct4() );
		agreementBean.setFifthLoanProduct( applyCollateralAgreement.getLoanProduct5() );

		return agreementBean;
	}

	private String getServiceCode( String serviceType )
	{
		if( ServiceTypeEnum.SIGNING_CONTRACT.getContext().equals( serviceType ) )
			return "2";

		return "1";
	}

	private String getUserType( String userSubType )
	{
		if( UserSubTypeEnum.BORROWER.getContext().equals( userSubType ) )
			return null;

		return userSubType;
	}

	private String getValidEmailToUse( ApplySigningUser user )
	{
		return Optional.ofNullable( user.getVerifiedEmail() ).orElse( user.getEmail() );
	}

	private boolean isHouseloan( ApplySigningContract contract )
	{
		return ProductCodeEnum.HOUSE_LOAN.getContext().equals( contract.getProductCode() );
	}

	private boolean isShortMidTerm( ApplySigningContract contract )
	{
		return "short-midterm".equals( contract.getCodeProdKind().getCaseType() ) || contract.getLoanPeriod() == 12;
	}

	private PersonalLoanApplySubmittedIloanArgBean personalLoanCovert2IloanFormat( PersonalLoanApplySubmittedArgBean argBean )
		throws InstantiationException, IllegalAccessException
	{
		PersonalLoanApplySubmittedIloanArgBean iloanArgBean = new PersonalLoanApplySubmittedIloanArgBean();

		iloanArgBean.setCaseNo( argBean.getCaseNo() );
		iloanArgBean.setUserType( argBean.getUserType() );
		iloanArgBean.setIdentityType( argBean.getIdentityType() );
		iloanArgBean.setApplyTime( argBean.getApplyTime() );
		iloanArgBean.setIpAddr( argBean.getIpAddr() );
		iloanArgBean.setAgreementVersion( argBean.getAgreementVersion() );
		iloanArgBean.setAgreedDate( argBean.getAgreedDate() );
		iloanArgBean.setAuthBankCode( argBean.getAuthBankCode() );
		iloanArgBean.setAuthBankAcct( argBean.getAuthBankAcct() );
		iloanArgBean.setRelatedCaseNo( argBean.getRelatedCaseNo() );
		iloanArgBean.setPersonalLoanInfoBean( argBean.getPersonalLoanInfoBean() );
		iloanArgBean.setFile( argBean.getFile() );
		iloanArgBean.setLoanPlan( argBean.getLoanPlan() );
		iloanArgBean.setIntroduceBr1st( argBean.getIntroduceBr1st() );
		iloanArgBean.setIntroduceBrNo( argBean.getIntroduceBrNo() );
		iloanArgBean.setCaseSource( argBean.getCaseSource() );

		try
		{
			iloanArgBean.setPersonalLoanBasicInfoIloanBean( ObjectConversionUtils.convert( argBean.getPersonalLoanBasicInfoBean(),
																						   PersonalLoanBasicInfoIloanBean.class ) );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.INTERNAL.getCode(), ex, null, AP_NAME, "信貸申請- apply 系統錯誤" );
		}

		return iloanArgBean;
	}

	private void setHouseLoanGuaranteeInfo( HouseLoanBasicInfoBean basicInfoBean, ApplyLoanGuaranteeInfo guaranteeInfo )
	{
		if( guaranteeInfo == null )
			return;

		basicInfoBean.setRelationWithBorrower( guaranteeInfo.getCodeRelationBorrowerType().getRelationBorrowerType() );
		basicInfoBean.setLiveWithBorrower( BooleanUtils.toString( guaranteeInfo.isCohabitationFlag(), "Y", "N" ) );

		if( guaranteeInfo.getCodeGuarantyReason() != null )
		{
			basicInfoBean.setGuarantyReason( guaranteeInfo.getCodeGuarantyReason().getGuarantyReasonCode() );
			basicInfoBean.setOtherGuarantyReason( guaranteeInfo.getOtherGuarantyReason() );
		}

	}

	private void setPersonalLoanGuaranteeInfo( PersonalLoanBasicInfoBean basicInfoBean, ApplyLoanGuaranteeInfo guaranteeInfo )
	{
		if( guaranteeInfo == null )
			return;

		basicInfoBean.setRelationWithBorrower( guaranteeInfo.getCodeRelationBorrowerType().getRelationBorrowerType() );
		basicInfoBean.setLiveWithBorrower( BooleanUtils.toString( guaranteeInfo.isCohabitationFlag(), "Y", "N" ) );

		if( guaranteeInfo.getCodeGuarantyReason() != null )
		{
			basicInfoBean.setGuarantyReason( guaranteeInfo.getCodeGuarantyReason().getGuarantyReasonCode() );
			basicInfoBean.setOtherGuarantyReason( guaranteeInfo.getOtherGuarantyReason() );
		}

	}

	private void setSigningBorrowerInfo( SigningContractCompletedArgBean argBean, IdentityInfoResultBean identityInfo, ApplySigningUser user,
										 AuthBankInfoResultBean authBankInfo )
	{
		argBean.setBorrowerIPAddr( identityInfo.getClientAddress() );
		argBean.setBorrowerIdentityType( identityInfo.getIdentityType() );
		argBean.setBorrowerAuthBankCode( authBankInfo.getBankCode() );
		argBean.setBorrowerAuthBankAcct( authBankInfo.getBankAccount() );
		argBean.setBorrowerSingingDate( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( user ) ); // C340M01A.ploanCtrSignTimeM
		argBean.setBorrowerEmail( getValidEmailToUse( user ) );

		if( user.getAgreeCrossSelling() != null )
			argBean.setBorrowerAgreeCrossSelling( BooleanUtils.isTrue( user.getAgreeCrossSelling() ) ? "1" : "0" );

		if( user.getCodeContractNotification() != null )
			argBean.setBorrowerContractSendingMethod( user.getCodeContractNotification().getContractNotificationCode() );

		if( user.getCodeTown() != null )
			argBean.setBorrowerAddress( getAddress( user.getCodeTown().getCodeCity().getName(), user.getCodeTown().getName(),
													user.getNotificationAddress() ) );

	}

	private void setSigningStakeholderInfo( SigningContractCompletedArgBean argBean, IdentityInfoResultBean identityInfo, ApplySigningUser user,
											AuthBankInfoResultBean authBankInfo )
	{
		argBean.setStakeholderIPAddr( identityInfo.getClientAddress() );
		argBean.setStakeholderIdentityType( identityInfo.getIdentityType() );
		argBean.setStakeholderAuthBankCode( authBankInfo.getBankCode() );
		argBean.setStakeholderAuthBankAcct( authBankInfo.getBankAccount() );
		argBean.setRelatedPersonSingingDate( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( user ) ); // C340M01A.ploanCtrSignTime1
		argBean.setStakeholderEmail( getValidEmailToUse( user ) );

		if( user.getAgreeCrossSelling() != null )
			argBean.setStakeholderAgreeCrossSelling( BooleanUtils.isTrue( user.getAgreeCrossSelling() ) ? "1" : "0" );

		if( user.getCodeContractNotification() != null )
			argBean.setStakeholderContractSendingMethod( user.getCodeContractNotification().getContractNotificationCode() );

		if( user.getCodeTown() != null )
			argBean.setStakeholderAddress( getAddress( user.getCodeTown().getCodeCity().getName(), user.getCodeTown().getName(),
													   user.getNotificationAddress() ) );

		if( user.getCodeBorrowerOverdueInformMethod() != null )
			argBean.setBorrowerOverdueInformMethod( user.getCodeBorrowerOverdueInformMethod().getBorrowerOverdueInformCode() );

	}

	private void submitIdentityAttachmentDTOs( Long signing_contract_id, String caseNo, String serviceType )
	{

		for( SigningContractIdentityAttachmentDTO dto : attachmentDAO.getSigningContractOtherBankIdentityAttachment( signing_contract_id ) )
		{
			AttachmentSubmittedArgBean submittedArgBean = new AttachmentSubmittedArgBean();
			submittedArgBean.setCaseNo( caseNo );
			submittedArgBean.setServiceType( getServiceCode( serviceType ) );
			submittedArgBean.setAttachmentId( dto.getAttachmentId().toString() );
			submittedArgBean.setAttachmentType( dto.getAttachmentType() );
			submittedArgBean.setFileName( dto.getFileName() );
			submittedArgBean.setFileContent( CommonBase64Utils.base64Encoder( dto.getFile_content() ) );
			submittedArgBean.setCreateDate( dto.getCreatedDate() );

			ApplySigningContract contract = applySigningContractDAO.read( signing_contract_id );
			if( Objects.isNull( contract.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == contract.getCodeRecipientSystem().getSystemId() )
				eloanSenderService.submitAttachment( submittedArgBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == contract.getCodeRecipientSystem().getSystemId() )
				iloanSenderService.submitAttachment( submittedArgBean ).isStat();

		}
	}

	private void submitIdentityAttachments( Long otherBankId, String caseNo, String serviceType )
	{
		IdentityAttachmentsArgBean argBean = new IdentityAttachmentsArgBean();
		argBean.setOtherBankId( otherBankId );

		List<IdentityAttachmentResultBean> identityAttachments = identityAttachmentsClient.send( argBean );

		for( IdentityAttachmentResultBean identityAttachment : identityAttachments )
		{
			AttachmentSubmittedArgBean submittedArgBean = new AttachmentSubmittedArgBean();
			submittedArgBean.setCaseNo( caseNo );
			submittedArgBean.setServiceType( getServiceCode( serviceType ) );
			submittedArgBean.setAttachmentId( identityAttachment.getAttachmentId().toString() );
			submittedArgBean.setAttachmentType( identityAttachment.getAttachmentType() );
			submittedArgBean.setFileName( identityAttachment.getFileName() );
			submittedArgBean.setFileContent( identityAttachment.getFileContent() );
			submittedArgBean.setCreateDate( identityAttachment.getCreatedDate() );

			ApplyLoan applyLoan = applyLoanDAO.getPojoByCaseNo( caseNo );
			if( Objects.isNull( applyLoan.getCodeRecipientSystem() )
				|| RecipientSystemEnum.ELOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				eloanSenderService.submitAttachment( submittedArgBean ).isStat();
			else if( RecipientSystemEnum.ILOAN.getSystemId() == applyLoan.getCodeRecipientSystem().getSystemId() )
				iloanSenderService.submitAttachment( submittedArgBean ).isStat();
		}
	}
}
