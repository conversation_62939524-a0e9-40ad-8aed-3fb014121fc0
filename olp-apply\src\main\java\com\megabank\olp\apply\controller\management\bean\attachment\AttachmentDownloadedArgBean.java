package com.megabank.olp.apply.controller.management.bean.attachment;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class AttachmentDownloadedArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long attachmentId;

	public AttachmentDownloadedArgBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return attachmentId
	 */
	public Long getAttachmentId()
	{
		return attachmentId;
	}

	/**
	 *
	 * @param attachmentId
	 */
	public void setAttachmentId( Long attachmentId )
	{
		this.attachmentId = attachmentId;
	}
}
