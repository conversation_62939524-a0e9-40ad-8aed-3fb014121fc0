package com.megabank.olp.apply.service.loan;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.service.loan.bean.apply.AgreementResBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanApplyResBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanApplySubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanContentParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanRelationParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanServedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalBasicParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalBasicResBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalContactParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalContactResBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalInfoSubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalJobParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalJobResBean;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.base.utility.date.CommonDateUtils;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class ApplyServiceIntegration
{
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;
	
	private final String loanType = "personalloan";

	@Autowired
	private ApplyService service;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void backToPersonalInfo()
	{
		Long id = service.backToPersonalInfo( loanType );

		logger.info( "id:{}", id );
	}

	@Test
	public void checkApplyCount()
	{
		boolean result = service.checkApplyCount( loanType );

		logger.info( "result:{}", result );
	}

	@Test
	public void checkLoanApplyExisted()
	{
		String caseNo = "PA000001";
		String idNo = "A1********";
		String name = "王大明";
		String mobileNumber = "**********";
		String loanType = "personalloan";

		boolean result = service.checkLoanApplyExisted( caseNo, name, idNo, mobileNumber, loanType );

		logger.info( "result:{}", result );
	}

	@Test
	public void confirmAgreement()
	{
		List<Long> itemIds = Arrays.asList( 1L );

		Long resBean = service.confirmAgreement( loanType, itemIds );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void confirmFilledInfo() throws IOException
	{
		service.confirmFilledInfo( loanType );

		logger.info( "id:{}" );
	}

	@Test
	public void getAgreement()
	{
		AgreementResBean resBean = service.getAgreement( loanType );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getLoanApplyInfo()
	{
		LoanApplyResBean resBean = service.getLoanApplyInfo( loanType );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getPersonalBasicInfo()
	{
		PersonalBasicResBean resBean = service.getPersonalBasicInfo( loanType );

		logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getPersonalContactInfo()
	{
		PersonalContactResBean resBean = service.getPersonalContactInfo( loanType );

		// logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getPersonalJobInfo()
	{
		PersonalJobResBean resBean = service.getPersonalJobInfo( loanType );

		// logger.info( "resBean:{}", resBean );
	}

	@Test
	public void getThankyouMessage()
	{
		MessageResBean resBean = service.getThankyouMessage( loanType );

		logger.info( "resBean:{}", resBean );
	}

	@BeforeEach
	public void init()
	{
		setSessionInfoThreadLocal();
	}

	@Test
	public void submitLoanApplyInfo()
	{
		LoanApplySubmittedParamBean paramBean = new LoanApplySubmittedParamBean();
		paramBean.setLoanType( loanType );
		paramBean.setLoanContentParamBean( getLoanApplyContentParamBean() );
		paramBean.setLoanRelationParamBeans( getLoanApplyRelationParamBeans() );
		paramBean.setLoanServedParamBeans( getLoanApplyServedParamBeans() );

		Long id = service.submitLoanApplyInfo( paramBean );

		logger.info( "id:{}", id );
	}

	@Test
	public void submitPersonalInfo()
	{
		PersonalInfoSubmittedParamBean paramBean = new PersonalInfoSubmittedParamBean();
		paramBean.setLoanType( loanType );
		paramBean.setPersonalBasicParamBean( getPersonalBasicParamBean() );
		paramBean.setPersonalContactParamBean( getPersonalContactParamBean() );
		paramBean.setPersonalJobParamBean( getPersonalJobParamBean() );

		Long id = service.submitPersonalInfo( paramBean );

		logger.info( "id:{}", id );
	}

	private LoanContentParamBean getLoanApplyContentParamBean()
	{
		int loanRequestAmt = 100;
		String loanPurpose = "3";
		String otherPurpose = "其他用途說明";
		String loanPeriod = "01";
		String gracePeriodCode = "01";
		String collateralAddressTownCode = "110";
		String collateralAddressStreet = "松江";
		String collateralAddressNo = "7";
		String notificationCode = "1";

		LoanContentParamBean paramBean = new LoanContentParamBean();
		paramBean.setLoanRequestAmt( loanRequestAmt );
		paramBean.setLoanPurpose( loanPurpose );
		paramBean.setOtherPurpose( otherPurpose );
		paramBean.setLoanPeriod( loanPeriod );
		paramBean.setCollateralAddressTownCode( collateralAddressTownCode );
		paramBean.setCollateralAddressStreet( collateralAddressStreet );
		paramBean.setCollateralAddressNo( collateralAddressNo );
		paramBean.setGracePeriodCode( gracePeriodCode );
		paramBean.setNotificationCode( notificationCode );

		return paramBean;
	}

	private List<LoanRelationParamBean> getLoanApplyRelationParamBeans()
	{
		String relationIdNo = "B1********";
		String relationName = "關係人";
		String relationType = "1";

		List<LoanRelationParamBean> paramBeans = new ArrayList<>();
		LoanRelationParamBean paramBean = new LoanRelationParamBean();
		paramBean.setRelationIdNo( relationIdNo );
		paramBean.setRelationName( relationName );
		paramBean.setRelationType( relationType );
		paramBeans.add( paramBean );

		return paramBeans;
	}

	private List<LoanServedParamBean> getLoanApplyServedParamBeans()
	{
		String companyName = "公司名稱";
		String servedTitle = "負責人";
		String taxNo = "123456";
		String comment = "無";

		List<LoanServedParamBean> paramBeans = new ArrayList<>();
		LoanServedParamBean paramBean = new LoanServedParamBean();
		paramBean.setCompanyName( companyName );
		paramBean.setServedTitle( servedTitle );
		paramBean.setTaxNo( taxNo );
		paramBean.setComment( comment );
		paramBeans.add( paramBean );

		return paramBeans;
	}

	private PersonalBasicParamBean getPersonalBasicParamBean()
	{
		String idNo = "A1********";
		String name = "王大明";
		String engFirstName = "DA-MING";
		String engLastName = "WANG";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		String marriageStatus = "1";
		String educationLevel = "01";
		int childrenCount = 1;
		String nationality = "US";

		PersonalBasicParamBean paramBean = new PersonalBasicParamBean();
		paramBean.setIdNo( idNo );
		paramBean.setName( name );
		paramBean.setEngFirstName( engFirstName );
		paramBean.setEngLastName( engLastName );
		paramBean.setBirthDate( birthDate );
		paramBean.setMarriageStatus( marriageStatus );
		paramBean.setEducationLevel( educationLevel );
		paramBean.setChildrenCount( childrenCount );
		paramBean.setNationality( nationality );

		return paramBean;
	}

	private PersonalContactParamBean getPersonalContactParamBean()
	{
		String homeAddressTownCode = "110";
		String homeAddressStreet = "松江";
		String homeAddressNo = "7";
		String mailingAddressTownCode = "110";
		String mailingAddressStreet = "松江";
		String mailingAddressNo = "7";
		String residenceStatus = "1";
		String houseStatus = "1";
		String homePhoneCode = "02";
		String homePhoneNumber = "2345678";
		String email = "<EMAIL>";
		String mobileNumber = "**********";
		String branchBankCode = "2";
		String serviceAssociateDeptCode = "90000";
		String serviceAssociate = "emp-001";
		int rent = 10000;

		PersonalContactParamBean paramBean = new PersonalContactParamBean();
		paramBean.setHomeAddressTownCode( homeAddressTownCode );
		paramBean.setHomeAddressStreet( homeAddressStreet );
		paramBean.setHomeAddressNo( homeAddressNo );
		paramBean.setMailingAddressTownCode( mailingAddressTownCode );
		paramBean.setMailingAddressStreet( mailingAddressStreet );
		paramBean.setMailingAddressNo( mailingAddressNo );
		paramBean.setResidenceStatus( residenceStatus );
		paramBean.setHouseStatus( houseStatus );
		paramBean.setHomePhoneCode( homePhoneCode );
		paramBean.setHomePhoneNumber( homePhoneNumber );
		paramBean.setEmail( email );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setBranchBankCode( branchBankCode );
		paramBean.setServiceAssociateDeptCode( serviceAssociateDeptCode );
		paramBean.setServiceAssociate( serviceAssociate );
		paramBean.setRent( rent );

		return paramBean;
	}

	private PersonalJobParamBean getPersonalJobParamBean()
	{
		String jobSubType = "1";
		String companyName = "公司名稱";
		int annualIncome = 40;
		int seniorityYear = 1;
		int seniorityMonth = 1;
		String titleType = "01";
		String companyTaxNo = "123456";
		String companyPhoneCode = "02";
		String companyPhoneNumber = "********";
		String companyPhoneExt = "11";
		String companyAddressTownCode = "110";
		String companyAddressStreet = "松江";
		String companyAddressNo = "7";
		String amountPerMonthCode = "01";

		PersonalJobParamBean paramBean = new PersonalJobParamBean();
		paramBean.setJobSubType( jobSubType );
		paramBean.setCompanyName( companyName );
		paramBean.setAnnualIncome( annualIncome );
		paramBean.setSeniorityYear( seniorityYear );
		paramBean.setSeniorityMonth( seniorityMonth );
		paramBean.setTitleType( titleType );
		paramBean.setCompanyTaxNo( companyTaxNo );
		paramBean.setCompanyPhoneCode( companyPhoneCode );
		paramBean.setCompanyPhoneNumber( companyPhoneNumber );
		paramBean.setCompanyPhoneExt( companyPhoneExt );
		paramBean.setCompanyAddressTownCode( companyAddressTownCode );
		paramBean.setCompanyAddressStreet( companyAddressStreet );
		paramBean.setCompanyAddressNo( companyAddressNo );
		paramBean.setAmountPerMonthCode( amountPerMonthCode );

		return paramBean;
	}

	private void setSessionInfoThreadLocal()
	{
		String idNo = "A1********";
		Date birthDate = CommonDateUtils.getDate( 1990, 1, 1 );
		List<String> identityTypes = Arrays.asList( IdentityTypeEnum.SKIP.getContext(), IdentityTypeEnum.OTHER_BANK.getContext() );
		String jwt =
				   "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************.uF-1EovFY4kX6LFklVuDDuB4JCs94aAz64DJ5UbZJ64kWbL4r4Juj6XnZP70jS6IIHDlnrfGhabSq857pKqE1w";

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( jwt );
		localBean.setIdNo( idNo );
		localBean.setBirthDate( birthDate );
		localBean.setIdentityTypes( identityTypes );

		sessionInfoThreadLocal.set( localBean );
	}

}