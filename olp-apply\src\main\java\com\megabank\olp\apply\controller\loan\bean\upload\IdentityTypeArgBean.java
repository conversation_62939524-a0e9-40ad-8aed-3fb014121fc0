package com.megabank.olp.apply.controller.loan.bean.upload;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class IdentityTypeArgBean extends BaseBean
{
	@NotBlank
	private String identityType;

	public IdentityTypeArgBean()
	{
		// default constructor
	}

	public String getIdentityType()
	{
		return identityType;
	}

	public void setIdentityType( String identityType )
	{
		this.identityType = identityType;
	}
}
