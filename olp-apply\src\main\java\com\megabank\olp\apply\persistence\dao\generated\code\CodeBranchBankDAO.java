package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeBranchBankDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeBranchBankDAO extends BasePojoDAO<CodeBranchBank, Long>
{
	public CodeBranchBank getPojoByBankCode( String branchBankCode )
	{
		Validate.notNull( branchBankCode );

		NameValueBean condition = new NameValueBean( CodeBranchBank.BANK_CODE_CONSTANT, branchBankCode );

		return getUniquePojoByProperty( condition, CodeBranchBank.TABLENAME_CONSTANT );
	}
	
	public CodeBranchBank getPojoByBankCode( String branchBankCode, boolean noRollback )
	{
		if( noRollback )
		{
			try
			{
				Validate.notNull( branchBankCode );
				
				NameValueBean condition = new NameValueBean( CodeBranchBank.BANK_CODE_CONSTANT, branchBankCode );
				
				return getUniquePojoByProperty( condition, CodeBranchBank.TABLENAME_CONSTANT );			
			}
			catch (Exception exception) 
			{
				return null;
			}			
		}
		else 
		{
			return getPojoByBankCode( branchBankCode );
		}
	}

	public CodeBranchBank getPojoByHeadOffice( boolean headOffice )
	{
		NameValueBean condition = new NameValueBean( CodeBranchBank.HEAD_OFFICE_CONSTANT, headOffice );

		return getUniquePojoByProperty( condition, CodeBranchBank.TABLENAME_CONSTANT );
	}

	public CodeBranchBank read( Long branchBankId )
	{
		Validate.notNull( branchBankId );

		return getPojoByPK( branchBankId, CodeBranchBank.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeBranchBank> getPojoClass()
	{
		return CodeBranchBank.class;
	}
}
