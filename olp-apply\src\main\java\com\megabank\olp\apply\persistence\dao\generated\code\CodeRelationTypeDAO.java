package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeRelationType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeRelationTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeRelationTypeDAO extends BasePojoDAO<CodeRelationType, String>
{
	public List<CodeRelationType> getList()
	{
		return getAllPojos();
	}

	public CodeRelationType read( String relationType )
	{
		Validate.notBlank( relationType );

		return getPojoByPK( relationType, CodeRelationType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeRelationType> getPojoClass()
	{
		return CodeRelationType.class;
	}
}
