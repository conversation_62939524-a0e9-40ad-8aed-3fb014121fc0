package com.megabank.olp.apply.controller.loan.bean.signing;

import com.megabank.olp.base.bean.BaseBean;

import javax.validation.constraints.NotBlank;

public class SigningContractAgreementArgBean  extends BaseBean
{
	@NotBlank
	private String contractNo;

	@NotBlank
	private String serviceType;

	public SigningContractAgreementArgBean()
	{
		// default constructor
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public String getServiceType()
	{
		return serviceType;
	}

	public void setServiceType(String serviceType)
	{
		this.serviceType = serviceType;
	}
}
