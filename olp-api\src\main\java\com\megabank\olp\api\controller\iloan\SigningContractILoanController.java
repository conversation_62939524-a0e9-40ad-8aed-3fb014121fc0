package com.megabank.olp.api.controller.iloan;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.megabank.olp.api.controller.eloan.bean.signing.*;
import com.megabank.olp.api.controller.iloan.bean.signing.LoanConditionInfoIloanBean;
import com.megabank.olp.api.controller.iloan.bean.signing.PaymentInfoCreateArgILoanBean;
import com.megabank.olp.api.controller.iloan.bean.signing.SigningContractCreateArgILoanBean;
import com.megabank.olp.api.service.eloan.bean.*;
import com.megabank.olp.base.enums.RecipientSystemEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.api.service.iloan.SigningContractILoanService;
import com.megabank.olp.api.utility.BaseILoanAPIController;

@RestController
@RequestMapping( "iloan/signingcontract" )
public class SigningContractILoanController extends BaseILoanAPIController
{
	@Autowired
	private SigningContractILoanService service;

	@PostMapping( "discardContract" )
	public Map<String, Object> discardContract( @RequestHeader( value = "AccessToken" ) String accessToken,
												@RequestBody @Validated SigningContractDiscardArgBean argBean )
	{
		validAuth( accessToken );

		return getResponseMap( service.discardContract( argBean.getContractNo() ) );
	}

	@PostMapping( "sendSigningContract" )
	public Map<String, Object> sendContract( @RequestHeader( value = "AccessToken" ) String accessToken,
											 @RequestBody @Validated SigningContractCreateArgILoanBean argBean )
	{
		validAuth( accessToken );

		checkSigningContractCreateArgBean( argBean );

		return getResponseMap( service.createContract( mapCreatedParamBean( argBean ) ) );
	}

	@PostMapping( "sendPaymentInfo" )
	public Map<String, Object> sendPaymentInfo( @RequestHeader( value = "AccessToken" ) String accessToken,
												@RequestBody @Validated PaymentInfoCreateArgILoanBean argBean )
	{
		validAuth( accessToken );

		return getResponseMap( service.createPaymentInfo( mapPaymentInfoBean( argBean ) ) );
	}

	private void checkSigningContractCreateArgBean( SigningContractCreateArgILoanBean argBean )
	{
		if( StringUtils.isNotBlank( argBean.getRelatedPersonType() )
				&& ( StringUtils.isAnyBlank( argBean.getRelatedPersonId(), argBean.getRelatedPersonName(), argBean.getRelatedPersonMobileNumber(),
				                             argBean.getRelatedPersonEmail() )
				|| argBean.getRelatedPersonBirthDate() == null ) )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
			                              new String[]{"relatedPersonId, relatedPersonName, relatedPersonMobileNumber, relatedPersonBirthDate, relatedPersonEmail",
					                              "must not be null"} );

		if( argBean.getLoanConditionInfoIloanBean().getLendingPlanInfoBean().getAdvancedApr() == null
				&& argBean.getLoanConditionInfoIloanBean().getLendingPlanInfoBean().getLimitedApr() == null )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
			                              new String[]{"advancedAPR, limitedAPR", "at least one field not null"} );

		if( "Y".equals( argBean.getIsRepayment() ) && argBean.getRepaymentList().size() == 0 )
			throw new MyRuntimeException( SystemErrorEnum.REQUEST_BODY_PROPERTY,
			                              new String[]{"when isRepayment is 'Y' repaymentList", "must not be null"} );
	}

	private SigningContractCreatedParamBean mapCreatedParamBean(SigningContractCreateArgILoanBean argBean )
	{
		SigningContractCreatedParamBean paramBean = new SigningContractCreatedParamBean();
		paramBean.setBranchCode( argBean.getBranchCode() );
		paramBean.setBorrowerId( argBean.getBorrowerId() );
		paramBean.setBorrowerBirthDate( argBean.getBorrowerBirthDate() );
		paramBean.setBorrowerMobileNumber( argBean.getBorrowerMobileNumber() );
		paramBean.setBorrowerEmail( argBean.getBorrowerEmail() );
		paramBean.setBorrowerName( argBean.getBorrowerName() );
		paramBean.setIsBorrowerYouth( argBean.getIsBorrowerYouth() );
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setContractVersion( argBean.getContractVersion() );
		paramBean.setCourtName( argBean.getCourtName() );
		paramBean.setExpiredDate( argBean.getExpiredDate() );
		paramBean.setProductCode( argBean.getProductCode() );
		paramBean.setRelatedPersonBirthDate( argBean.getRelatedPersonBirthDate() );
		paramBean.setRelatedPersonId( argBean.getRelatedPersonId() );
		paramBean.setRelatedPersonMobileNumber( argBean.getRelatedPersonMobileNumber() );
		paramBean.setRelatedPersonName( argBean.getRelatedPersonName() );
		paramBean.setRelatedPersonEmail( argBean.getRelatedPersonEmail() );
		paramBean.setRelatedPersonType( argBean.getRelatedPersonType() );
		paramBean.setLoanConditionDataBean( mapLoanConditionInfoBean( argBean.getLoanConditionInfoIloanBean() ) );
		paramBean.setBankAccountDataBeans( mapBankAccountDataBeans( argBean.getBankAccountInfoBeans() ) );
		paramBean.setGuaranteeDataBean( mapGuaranteeDataBean( argBean.getGuaranteeInfoBean() ) );
		paramBean.setLoanPlan( argBean.getLoanPlan() );
		paramBean.setGrpCntrNo( argBean.getGrpCntrNo() );
		paramBean.setGivenApprBegDate( argBean.getGivenApprBegDate() );
		paramBean.setGivenApprEndDate( argBean.getGivenApprEndDate() );
		paramBean.setPayeeBankCode( argBean.getPayeeBankCode() );
		paramBean.setPayeeBankAccountNo( argBean.getPayeeBankAccountNo() );
		paramBean.setPayeeBankAccountName( argBean.getPayeeBankAccountName() );
		paramBean.setPayeeTotalAmt( argBean.getPayeeTotalAmt() );
		paramBean.setPayeeRemittance( argBean.getPayeeRemittance() );
		paramBean.setPayeeSelfProvide( argBean.getPayeeSelfProvide() );
		paramBean.setBaseRate( argBean.getBaseRate() );
		paramBean.setRateList( argBean.getRateList() );
		paramBean.setIsRepayment( argBean.getIsRepayment() );
		paramBean.setRepaymentList( argBean.getRepaymentList() );
		paramBean.setRefSystemId( RecipientSystemEnum.ILOAN.getSystemId() );

		return paramBean;
	}

	private LoanConditionDataBean mapLoanConditionInfoBean( LoanConditionInfoIloanBean infoBean )
	{
		LoanConditionDataBean conditionDataBean = new LoanConditionDataBean();
		conditionDataBean.setLoanAmt( infoBean.getLoanAmt() );
		conditionDataBean.setLoanPeriod( infoBean.getLoanPeriod() );
		conditionDataBean.setLoanPurposeDataBeans( mapLoanPurposeData( infoBean.getLoanPurposeInfoBeans() ) );
		conditionDataBean.setOneTimeFee( infoBean.getOneTimeFee() );
		conditionDataBean.setCreditCheckFee( infoBean.getCreditCheckFee() );
		conditionDataBean.setPreliminaryFee( infoBean.getPreliminaryFee() );
		conditionDataBean.setRepaymentMethod( infoBean.getRepaymentMethod() );
		conditionDataBean.setDrawDownType( infoBean.getDrawDownType() );
		conditionDataBean.setLendingPlan( infoBean.getLendingPlan() );
		conditionDataBean.setLendingPlanDataBean( mapLendingPlanData( infoBean.getLendingPlanInfoBean() ) );

		return conditionDataBean;
	}

	private List<BankAccountDataBean> mapBankAccountDataBeans(List<BankAccountInfoBean> bankAccountInfoBeans )
	{
		List<BankAccountDataBean> dataBeans = new ArrayList<>();
		for( BankAccountInfoBean bankAccountInfoBean : bankAccountInfoBeans )
		{
			BankAccountDataBean dataBean = new BankAccountDataBean();
			dataBean.setBankCode( bankAccountInfoBean.getBankCode() );
			dataBean.setAccount( bankAccountInfoBean.getAccount() );

			dataBeans.add( dataBean );
		}

		return dataBeans;
	}

	private GuaranteeDataBean mapGuaranteeDataBean(GuaranteeInfoBean infoBean )
	{
		GuaranteeDataBean dataBean = new GuaranteeDataBean();
		dataBean.setGeneralGuaranteePlan( infoBean.getGeneralGuaranteePlan() );
		dataBean.setGeneralGuaranteePlanInfo( infoBean.getGeneralGuaranteePlanInfo() );
		dataBean.setGuaranteeAmt( infoBean.getGuaranteeAmt() );
		dataBean.setJointGuaranteePlan( infoBean.getJointGuaranteePlan() );
		dataBean.setJointGuaranteePlanInfo( infoBean.getJointGuaranteePlanInfo() );

		return dataBean;
	}

	private List<LoanPurposeDataBean> mapLoanPurposeData(List<LoanPurposeInfoBean> infoBeans )
	{
		List<LoanPurposeDataBean> dataBeans = new ArrayList<>();

		for( LoanPurposeInfoBean infoBean : infoBeans )
		{
			LoanPurposeDataBean dataBean = new LoanPurposeDataBean();
			dataBean.setLoanPurposeName( infoBean.getLoanPurposeName() );
			dataBean.setIsChecked( infoBean.getIsChecked() );

			dataBeans.add( dataBean );
		}

		return dataBeans;
	}

	private LendingPlanDataBean mapLendingPlanData( LendingPlanInfoBean infoBean )
	{
		LendingPlanDataBean dataBean = new LendingPlanDataBean();
		dataBean.setAdvancedRedemptionTitle( infoBean.getAdvancedRedemptionTitle() );
		dataBean.setAdvancedRedemptionDesc( infoBean.getAdvancedRedemptionDesc() );
		dataBean.setAdvancedRateTitle( infoBean.getAdvancedRateTitle() );
		dataBean.setAdvancedRateDesc( infoBean.getAdvancedRateDesc() );
		dataBean.setAdvancedApr( infoBean.getAdvancedApr() );
		dataBean.setLimitedRedemptionTitle( infoBean.getLimitedRedemptionTitle() );
		dataBean.setLimitedRedemptionDesc( infoBean.getLimitedRedemptionDesc() );
		dataBean.setLimitedRateTitle( infoBean.getLimitedRateTitle() );
		dataBean.setLimitedRateDesc( infoBean.getLimitedRateDesc() );
		dataBean.setLimitedApr( infoBean.getLimitedApr() );
		dataBean.setOtherInfoTitle( infoBean.getOtherInfoTitle() );
		dataBean.setOtherInfoDesc( infoBean.getOtherInfoDesc() );
		dataBean.setShowOption( infoBean.getShowOption() );

		return dataBean;
	}

	private PaymentInfoCreateParamBean mapPaymentInfoBean(PaymentInfoCreateArgILoanBean argBean )
	{
		PaymentInfoCreateParamBean paramBean = new PaymentInfoCreateParamBean();
		paramBean.setContractNo( argBean.getContractNo() );
		paramBean.setPreliminaryFee( argBean.getPreliminaryFee() );
		paramBean.setCrChkFee( argBean.getCrChkFee() );
		paramBean.setPaymentInfoList( argBean.getPaymentInfoList() );

		return paramBean;
	}
}
