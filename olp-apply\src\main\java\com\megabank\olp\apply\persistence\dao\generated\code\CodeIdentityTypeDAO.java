package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeIdentityType;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeIdentityTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeIdentityTypeDAO extends BasePojoDAO<CodeIdentityType, String>
{
	public CodeIdentityType read( String code )
	{
		Validate.notNull( code );

		return getPojoByPK( code, CodeIdentityType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeIdentityType> getPojoClass()
	{
		return CodeIdentityType.class;
	}
}
