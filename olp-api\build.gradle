buildscript {  
  dependencies {
    classpath fileTree(dir: 'C:\\Project\\Ploan\\tool\\gradle-build\\plugin-jars', include: '*.jar')
  }
}

plugins {
	id 'eclipse'
	id 'java'
}

apply plugin: "org.springframework.boot"
apply plugin: "io.spring.dependency-management"

ext{
	myProjectName = 'olp-api'
}

apply from: '../olp-base/common.gradle'

group = 'com.megabank.olp'

bootJar{
	archiveFileName = 'app.jar'
	mainClass = 'com.megabank.olp.api.ApiMain'
}

dependencies {
    implementation project( ':olp-base' )
    implementation project( ':olp-client' )
    implementation project( ':olp-system' )
}