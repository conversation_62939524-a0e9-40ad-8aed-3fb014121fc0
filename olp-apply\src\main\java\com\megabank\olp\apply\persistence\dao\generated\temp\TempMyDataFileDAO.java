package com.megabank.olp.apply.persistence.dao.generated.temp;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.persistence.bean.generated.temp.TempMyDataFileCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.mydata.ApplyMyDataDAO;
import com.megabank.olp.apply.persistence.pojo.temp.TempMyDataFile;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The TempMyDataFileDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class TempMyDataFileDAO extends BasePojoDAO<TempMyDataFile, Long>
{

	@Autowired
	private ApplyMyDataDAO applyMyDataDAO;

	@Transactional( propagation = Propagation.REQUIRES_NEW )
	public Long create( TempMyDataFileCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getMyDataId() );
		Validate.notNull( paramBean.getFileType() );
		Validate.notNull( paramBean.getFileContent() );

		TempMyDataFile pojo = new TempMyDataFile();
		pojo.setApplyMyData( applyMyDataDAO.read( paramBean.getMyDataId() ) );
		pojo.setFileType( paramBean.getFileType() );
		pojo.setFileContent( paramBean.getFileContent() );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public void deletePojosByMyDataId( Long myDataId )
	{
		List<TempMyDataFile> pojos = getPojosByMyDataId( myDataId );

		super.deletePojos( pojos );
	}

	public List<TempMyDataFile> getPojosByMyDataId( Long myDataId )
	{
		NameValueBean condition = new NameValueBean( TempMyDataFile.APPLY_MY_DATA_CONSTANT, applyMyDataDAO.read( myDataId ) );

		return getPojosByProperty( condition );
	}

	@Override
	protected Class<TempMyDataFile> getPojoClass()
	{
		return TempMyDataFile.class;
	}
}
