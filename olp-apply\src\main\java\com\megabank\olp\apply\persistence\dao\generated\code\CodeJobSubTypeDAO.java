package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeJobSubType;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeJobSubTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeJobSubTypeDAO extends BasePojoDAO<CodeJobSubType, Long>
{
	@Autowired
	private CodeJobTypeDAO codeJobTypeDAO;

	public CodeJobSubType getPojoByTypes( String jobType, String jobSubType )
	{
		Validate.notBlank( jobType );
		Validate.notBlank( jobSubType );

		NameValueBean codeJobType = new NameValueBean( CodeJobSubType.CODE_JOB_TYPE_CONSTANT, codeJobTypeDAO.read( jobType ) );
		NameValueBean subType = new NameValueBean( CodeJobSubType.SUB_TYPE_CODE_CONSTANT, jobSubType );
		NameValueBean[] conditions = new NameValueBean[]{ codeJobType, subType };

		return this.getUniquePojoByProperties( conditions );
	}

	public List<CodeJobSubType> getPojosByJobType( String jobType )
	{
		Validate.notBlank( jobType );

		NameValueBean condition = new NameValueBean( CodeJobSubType.CODE_JOB_TYPE_CONSTANT, codeJobTypeDAO.read( jobType ) );

		return getPojosByProperty( condition );
	}

	public CodeJobSubType read( Long jobSubTypeId )
	{
		Validate.notNull( jobSubTypeId );

		return getPojoByPK( jobSubTypeId, CodeJobSubType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeJobSubType> getPojoClass()
	{
		return CodeJobSubType.class;
	}
}
