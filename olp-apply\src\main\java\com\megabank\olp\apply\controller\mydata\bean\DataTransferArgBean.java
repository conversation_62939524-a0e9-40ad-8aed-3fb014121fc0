/**
 *
 */
package com.megabank.olp.apply.controller.mydata.bean;

import javax.validation.constraints.NotBlank;
import com.megabank.olp.base.bean.BaseBean;

public class DataTransferArgBean extends BaseBean
{
	@NotBlank
	private String transactionId;

	@NotBlank
	private String tx_id;

	@NotBlank
	private String file;

	@NotBlank
	private String status;

	public DataTransferArgBean()
	{
		// default constructor
	}

	public String getTransactionId()
	{
		return transactionId;
	}

	public void setTransactionId(String transactionId)
	{
		this.transactionId = transactionId;
	}

	public String getTx_id()
	{
		return tx_id;
	}

	public void setTx_id(String tx_id)
	{
		this.tx_id = tx_id;
	}

	public String getFile()
	{
		return file;
	}

	public void setFile(String file)
	{
		this.file = file;
	}

	public String getStatus()
	{
		return status;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}
	
}
