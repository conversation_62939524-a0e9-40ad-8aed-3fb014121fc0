package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeApplyStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeApplyStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeApplyStatusDAO extends BasePojoDAO<CodeApplyStatus, String>
{
	public List<CodeApplyStatus> getList()
	{
		return getAllPojos();
	}

	public CodeApplyStatus read( String applyStatusCode )
	{
		Validate.notBlank( applyStatusCode );

		return getPojoByPK( applyStatusCode, CodeApplyStatus.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeApplyStatus> getPojoClass()
	{
		return CodeApplyStatus.class;
	}
}
