/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.house;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanInfoCreatedParamBean extends BaseBean
{
	private Long loanTrialId;

	private BigDecimal topLoanCredit;

	private BigDecimal rate;

	public HouseLoanInfoCreatedParamBean()
	{}

	public Long getLoanTrialId()
	{
		return loanTrialId;
	}

	public BigDecimal getRate()
	{
		return rate;
	}

	public BigDecimal getTopLoanCredit()
	{
		return topLoanCredit;
	}

	public void setLoanTrialId( Long loanTrialId )
	{
		this.loanTrialId = loanTrialId;
	}

	public void setRate( BigDecimal rate )
	{
		this.rate = rate;
	}

	public void setTopLoanCredit( BigDecimal topLoanCredit )
	{
		this.topLoanCredit = topLoanCredit;
	}

}
