package com.megabank.olp.apply.service.loan.bean.survey;

import com.megabank.olp.base.bean.BaseBean;

public class ContactInfoResBean extends BaseBean
{

	private String name;

	private String cityCode;

	private String cityName;

	private String townCode;

	private String townName;

	private String branchBankCode;

	private String branchBankName;

	private String branchBankAddress;

	private String phoneCode;

	private String phoneNumber;

	private String phoneExt;

	private String email;

	private String mobileNumber;

	private String contactTimeCode;

	private String contactTimeName;

	private String otherMsg;

	private String sexCode;

	public ContactInfoResBean()
	{
		// default constructor
	}

	public String getBranchBankAddress()
	{
		return branchBankAddress;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getBranchBankName()
	{
		return branchBankName;
	}

	public String getCityCode()
	{
		return cityCode;
	}

	public String getCityName()
	{
		return cityName;
	}

	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	public String getContactTimeName()
	{
		return contactTimeName;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getPhoneCode()
	{
		return phoneCode;
	}

	public String getPhoneExt()
	{
		return phoneExt;
	}

	public String getPhoneNumber()
	{
		return phoneNumber;
	}

	public String getSexCode()
	{
		return sexCode;
	}

	public String getTownCode()
	{
		return townCode;
	}

	public String getTownName()
	{
		return townName;
	}

	public void setBranchBankAddress( String branchBankAddress )
	{
		this.branchBankAddress = branchBankAddress;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setBranchBankName( String branchBankName )
	{
		this.branchBankName = branchBankName;
	}

	public void setCityCode( String cityCode )
	{
		this.cityCode = cityCode;
	}

	public void setCityName( String cityName )
	{
		this.cityName = cityName;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setContactTimeName( String contactTimeName )
	{
		this.contactTimeName = contactTimeName;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setPhoneCode( String phoneCode )
	{
		this.phoneCode = phoneCode;
	}

	public void setPhoneExt( String phoneExt )
	{
		this.phoneExt = phoneExt;
	}

	public void setPhoneNumber( String phoneNumber )
	{
		this.phoneNumber = phoneNumber;
	}

	public void setSexCode( String sexCode )
	{
		this.sexCode = sexCode;
	}

	public void setTownCode( String townCode )
	{
		this.townCode = townCode;
	}

	public void setTownName( String townName )
	{
		this.townName = townName;
	}

}
