/**
 *
 */
package com.megabank.olp.apply.controller.management;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.management.bean.notification.AbortContractNotificationArgBean;
import com.megabank.olp.apply.service.management.NotificationService;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "management/notification" )
public class NotificationController extends BaseController
{
	@Autowired
	private NotificationService notificationService;

	/**
	 * 通知行員有房貸上傳補件
	 *
	 * @return
	 */
	@PostMapping( "houseloan/sendAttachment" )
	public Map<String, Object> sendHouseLoanAttachment()
	{
		notificationService.sendHouseLoanAttachment();

		return getResponseMap();
	}

	/**
	 * 通知行員有房貸申請完成案件
	 *
	 * @return
	 */
	@PostMapping( "houseloan/sendCompleted" )
	public Map<String, Object> sendHouseLoanCompleted()
	{
		notificationService.sendHouseLoanCompleted();

		return getResponseMap();
	}

	/**
	 * 通知行員有房貸行銷案件
	 *
	 * @return
	 */
	@PostMapping( "houseloan/sendPromotion" )
	public Map<String, Object> sendHouseLoanPromotion()
	{
		notificationService.sendHouseLoanPromotion();

		return getResponseMap();
	}

	/**
	 * 通知行員有擔保物提供人同意案件
	 *
	 * @return
	 */
	@PostMapping( "collateral/send" )
	public Map<String, Object> sendLoanCollateral()
	{
		notificationService.sendLoanCollateral();

		return getResponseMap();
	}

	/**
	 * 通知行員有信貸上傳補件
	 *
	 * @return
	 */
	@PostMapping( "personalloan/sendAttachment" )
	public Map<String, Object> sendPersonalLoanAttachment()
	{
		notificationService.sendPersonalLoanAttachment();

		return getResponseMap();
	}

	/**
	 * 通知行員有信貸申請完成案件
	 *
	 * @return
	 */
	@PostMapping( "personalloan/sendCompleted" )
	public Map<String, Object> sendPersonalLoanCompleted()
	{
		notificationService.sendPersonalLoanCompleted();

		return getResponseMap();
	}

	/**
	 * 通知行員有信貸行銷案件
	 *
	 * @return
	 */
	@PostMapping( "personalloan/sendPromotion" )
	public Map<String, Object> sendPersonalLoanPromotion()
	{
		notificationService.sendPersonalLoanPromotion();

		return getResponseMap();
	}

	/**
	 * 通知行員有信貸對保案件
	 * 
	 * @return
	 */
	@PostMapping( "signingcontract/send" )
	public Map<String, Object> sendSigningContract()
	{
		notificationService.sendSigningContract();

		return getResponseMap();
	}

	/**
	 * 通知行員有信貸對保案件
	 * 
	 * @return
	 */
	@PostMapping( "housesigningcontract/abort" )
	public Map<String, Object> sendHouseLoanSigningContractAborted( @RequestBody @Validated AbortContractNotificationArgBean argBean )
	{
		notificationService.sendHouseLoanSigningContractAborted( argBean.getContractNo() );

		return getResponseMap();
	}

}
