package com.megabank.olp.apply.controller.loan.bean.apply;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class PersonalJobBean extends BaseBean
{
	@NotBlank
	private String jobSubType;

	@NotBlank
	private String companyName;

	@NotNull
	@Min( 0 )
	private Integer annualIncome;

	@NotNull
	@Min( 0 )
	private Integer seniorityYear;

	@NotNull
	@Min( 0 )
	private Integer seniorityMonth;

	@NotBlank
	private String titleType;

	private String companyTaxNo;

	@NotBlank
	private String companyPhoneCode;

	@NotBlank
	private String companyPhoneNumber;

	private String companyPhoneExt;

	@NotBlank
	private String companyAddressTownCode;

	private String companyAddressVillage;

	private String companyAddressNeighborhood;

	@NotBlank
	private String companyAddressStreet;

	private Integer companyAddressSection;

	private String companyAddressLane;

	private String companyAddressAlley;

	@NotBlank
	private String companyAddressNo;

	private String companyAddressFloor;

	private Integer companyAddressRoom;

	@NotBlank
	private String amountPerMonthCode;

	private String empNo;

	public PersonalJobBean()
	{
		// default constructor
	}

	public String getAmountPerMonthCode()
	{
		return amountPerMonthCode;
	}

	public Integer getAnnualIncome()
	{
		return annualIncome;
	}

	public String getCompanyAddressAlley()
	{
		return companyAddressAlley;
	}

	public String getCompanyAddressFloor()
	{
		return companyAddressFloor;
	}

	public String getCompanyAddressLane()
	{
		return companyAddressLane;
	}

	public String getCompanyAddressNeighborhood()
	{
		return companyAddressNeighborhood;
	}

	public String getCompanyAddressNo()
	{
		return companyAddressNo;
	}

	public Integer getCompanyAddressRoom()
	{
		return companyAddressRoom;
	}

	public Integer getCompanyAddressSection()
	{
		return companyAddressSection;
	}

	public String getCompanyAddressStreet()
	{
		return companyAddressStreet;
	}

	public String getCompanyAddressTownCode()
	{
		return companyAddressTownCode;
	}

	public String getCompanyAddressVillage()
	{
		return companyAddressVillage;
	}

	public String getCompanyName()
	{
		return companyName;
	}

	public String getCompanyPhoneCode()
	{
		return companyPhoneCode;
	}

	public String getCompanyPhoneExt()
	{
		return companyPhoneExt;
	}

	public String getCompanyPhoneNumber()
	{
		return companyPhoneNumber;
	}

	public String getCompanyTaxNo()
	{
		return companyTaxNo;
	}

	public String getEmpNo()
	{
		return empNo;
	}

	public String getJobSubType()
	{
		return jobSubType;
	}

	public Integer getSeniorityMonth()
	{
		return seniorityMonth;
	}

	public Integer getSeniorityYear()
	{
		return seniorityYear;
	}

	public String getTitleType()
	{
		return titleType;
	}

	public void setAmountPerMonthCode( String amountPerMonthCode )
	{
		this.amountPerMonthCode = amountPerMonthCode;
	}

	public void setAnnualIncome( Integer annualIncome )
	{
		this.annualIncome = annualIncome;
	}

	public void setCompanyAddressAlley( String companyAddressAlley )
	{
		this.companyAddressAlley = companyAddressAlley;
	}

	public void setCompanyAddressFloor( String companyAddressFloor )
	{
		this.companyAddressFloor = companyAddressFloor;
	}

	public void setCompanyAddressLane( String companyAddressLane )
	{
		this.companyAddressLane = companyAddressLane;
	}

	public void setCompanyAddressNeighborhood( String companyAddressNeighborhood )
	{
		this.companyAddressNeighborhood = companyAddressNeighborhood;
	}

	public void setCompanyAddressNo( String companyAddressNo )
	{
		this.companyAddressNo = companyAddressNo;
	}

	public void setCompanyAddressRoom( Integer companyAddressRoom )
	{
		this.companyAddressRoom = companyAddressRoom;
	}

	public void setCompanyAddressSection( Integer companyAddressSection )
	{
		this.companyAddressSection = companyAddressSection;
	}

	public void setCompanyAddressStreet( String companyAddressStreet )
	{
		this.companyAddressStreet = companyAddressStreet;
	}

	public void setCompanyAddressTownCode( String companyAddressTownCode )
	{
		this.companyAddressTownCode = companyAddressTownCode;
	}

	public void setCompanyAddressVillage( String companyAddressVillage )
	{
		this.companyAddressVillage = companyAddressVillage;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public void setCompanyPhoneCode( String companyPhoneCode )
	{
		this.companyPhoneCode = companyPhoneCode;
	}

	public void setCompanyPhoneExt( String companyPhoneExt )
	{
		this.companyPhoneExt = companyPhoneExt;
	}

	public void setCompanyPhoneNumber( String companyPhoneNumber )
	{
		this.companyPhoneNumber = companyPhoneNumber;
	}

	public void setCompanyTaxNo( String companyTaxNo )
	{
		this.companyTaxNo = companyTaxNo;
	}

	public void setEmpNo( String empNo )
	{
		this.empNo = empNo;
	}

	public void setJobSubType( String jobSubType )
	{
		this.jobSubType = jobSubType;
	}

	public void setSeniorityMonth( Integer seniorityMonth )
	{
		this.seniorityMonth = seniorityMonth;
	}

	public void setSeniorityYear( Integer seniorityYear )
	{
		this.seniorityYear = seniorityYear;
	}

	public void setTitleType( String titleType )
	{
		this.titleType = titleType;
	}

}
