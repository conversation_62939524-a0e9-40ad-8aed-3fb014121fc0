package com.megabank.olp.apply.persistence.pojo.apply.signing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import com.megabank.olp.apply.persistence.pojo.code.CodeBorrowerOverdueInformMethod;
import com.megabank.olp.apply.persistence.pojo.code.CodeContractNotification;
import com.megabank.olp.apply.persistence.pojo.code.CodeTown;
import com.megabank.olp.base.bean.BaseBean;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The ApplySigningUser is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_signing_user" )
public class ApplySigningUser extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_signing_user";

	public static final String SIGNING_USER_ID_CONSTANT = "signingUserId";

	public static final String APPLY_SIGNING_CONTRACT_CONSTANT = "applySigningContract";

	public static final String CODE_CONTRACT_NOTIFICATION_CONSTANT = "codeContractNotification";

	public static final String CODE_TOWN_CONSTANT = "codeTown";

	public static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	public static final String NAME_CONSTANT = "name";

	public static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	public static final String EMAIL_CONSTANT = "email";

	public static final String SINGING_DATE_CONSTANT = "singingDate";

	public static final String NOTIFICATION_ADDRESS_CONSTANT = "notificationAddress";

	public static final String AGREE_CROSS_SELLING_CONSTANT = "agreeCrossSelling";

	public static final String SINGING_TIME_CONSTANT = "singingTime";

	private Long signingUserId;

	private transient ApplySigningContract applySigningContract;

	private transient CodeContractNotification codeContractNotification;

	private transient CodeTown codeTown;

	private long validatedIdentityId;

	private String name;

	private String mobileNumber;

	private String email;

	private Date singingDate;

	private String notificationAddress;

	private Boolean agreeCrossSelling;

	private Date singingTime;

	private String verifiedEmail;

	private transient CodeBorrowerOverdueInformMethod codeBorrowerOverdueInformMethod;

	private Boolean isYouth;

	public ApplySigningUser()
	{}

	public ApplySigningUser( ApplySigningContract applySigningContract, long validatedIdentityId, String name, String mobileNumber, String email )
	{
		this.applySigningContract = applySigningContract;
		this.validatedIdentityId = validatedIdentityId;
		this.name = name;
		this.mobileNumber = mobileNumber;
		this.email = email;
	}

	public ApplySigningUser( Long signingUserId )
	{
		this.signingUserId = signingUserId;
	}

	@Column( name = "agree_cross_selling", precision = 1, scale = 0 )
	public Boolean getAgreeCrossSelling()
	{
		return agreeCrossSelling;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "signing_contract_id", nullable = false )
	public ApplySigningContract getApplySigningContract()
	{
		return applySigningContract;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "borrower_overdue_inform_method" )
	public CodeBorrowerOverdueInformMethod getCodeBorrowerOverdueInformMethod()
	{
		return codeBorrowerOverdueInformMethod;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "contract_notification_code" )
	public CodeContractNotification getCodeContractNotification()
	{
		return codeContractNotification;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "notification_town_code" )
	public CodeTown getCodeTown()
	{
		return codeTown;
	}

	@Column( name = "email", nullable = false, length = 100 )
	public String getEmail()
	{
		return email;
	}

	@Column( name = "is_youth", nullable = true, precision = 1, scale = 0 )
	public Boolean getIsYouth()
	{
		return isYouth;
	}

	@Column( name = "mobile_number", nullable = false, length = 10 )
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	@Column( name = "name", nullable = false, length = 100 )
	public String getName()
	{
		return name;
	}

	@Column( name = "notification_address" )
	public String getNotificationAddress()
	{
		return notificationAddress;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "signing_user_id", unique = true, nullable = false )
	public Long getSigningUserId()
	{
		return signingUserId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "singing_date", length = 10 )
	public Date getSingingDate()
	{
		return singingDate;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "singing_time", length = 23 )
	public Date getSingingTime()
	{
		return singingTime;
	}

	@Column( name = "validated_identity_id", nullable = false )
	public long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	@Column( name = "verified_email", nullable = true, length = 100 )
	public String getVerifiedEmail()
	{
		return verifiedEmail;
	}

	public void setAgreeCrossSelling( Boolean agreeCrossSelling )
	{
		this.agreeCrossSelling = agreeCrossSelling;
	}

	public void setApplySigningContract( ApplySigningContract applySigningContract )
	{
		this.applySigningContract = applySigningContract;
	}

	public void setCodeBorrowerOverdueInformMethod( CodeBorrowerOverdueInformMethod codeBorrowerOverdueInformMethod )
	{
		this.codeBorrowerOverdueInformMethod = codeBorrowerOverdueInformMethod;
	}

	public void setCodeContractNotification( CodeContractNotification codeContractNotification )
	{
		this.codeContractNotification = codeContractNotification;
	}

	public void setCodeTown( CodeTown codeTown )
	{
		this.codeTown = codeTown;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setIsYouth( Boolean isYouth )
	{
		this.isYouth = isYouth;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationAddress( String notificationAddress )
	{
		this.notificationAddress = notificationAddress;
	}

	public void setSigningUserId( Long signingUserId )
	{
		this.signingUserId = signingUserId;
	}

	public void setSingingDate( Date singingDate )
	{
		this.singingDate = singingDate;
	}

	public void setSingingTime( Date singingTime )
	{
		this.singingTime = singingTime;
	}

	public void setValidatedIdentityId( long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

	public void setVerifiedEmail( String verifiedEmail )
	{
		this.verifiedEmail = verifiedEmail;
	}
}