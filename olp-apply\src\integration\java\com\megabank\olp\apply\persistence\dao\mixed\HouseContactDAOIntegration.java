package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.mixed.HouseContactListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.HouseContactListDTO;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.bean.threadlocal.PagingThreadLocalBean;
import com.megabank.olp.base.threadlocal.PagingThreadLocal;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class HouseContactDAOIntegration
{
	@Autowired
	private PagingThreadLocal pagingThreadLocal;
	
	@Autowired
	private HouseContactDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getList()
	{
		HouseContactListGetterParamBean paramBean = new HouseContactListGetterParamBean();

		List<HouseContactListDTO> dtos = dao.getList( paramBean );

		logger.info( "dtos:{}", dtos );
	}

	@Test
	public void getPaging()
	{
		PagingThreadLocalBean localBean = new PagingThreadLocalBean();
		localBean.setStart( 0 );
		localBean.setLength( 10 );
		localBean.setSortColumn( "createdDate" );
		localBean.setSortDirection( "asc" );
		pagingThreadLocal.set( localBean );

		HouseContactListGetterParamBean paramBean = new HouseContactListGetterParamBean();

		PagingBean<HouseContactListDTO> result = dao.getPaging( paramBean );

		logger.info( "data:{}", result.getData() );
		logger.info( "recordsFiltered:{}", result.getRecordsFiltered() );
		logger.info( "recordsTotal:{}", result.getRecordsTotal() );

	}

}
