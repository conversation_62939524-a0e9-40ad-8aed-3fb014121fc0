package com.megabank.olp.apply.persistence.dto;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class AttachmentListDTO extends BaseBean
{
	private Long attachmentId;

	private String attachmentType;

	private String fileName;

	private String caseNo;

	private String idNo;

	private Date birthDate;

	private String mobileNumber;

	private Date createdDate;

	private String transmissionStatusCode;

	private String transmissionStatusName;

	public AttachmentListDTO()
	{
		// default constructor
	}

	/**
	 *
	 * @return attachmentId
	 */
	public Long getAttachmentId()
	{
		return attachmentId;
	}

	/**
	 *
	 * @return attachmentType
	 */
	public String getAttachmentType()
	{
		return attachmentType;
	}

	/**
	 *
	 * @return birthDate
	 */
	public Date getBirthDate()
	{
		return birthDate;
	}

	/**
	 *
	 * @return caseNo
	 */
	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	/**
	 *
	 * @return fileName
	 */
	public String getFileName()
	{
		return fileName;
	}

	/**
	 *
	 * @return idNo
	 */
	public String getIdNo()
	{
		return idNo;
	}

	/**
	 *
	 * @return mobileNumber
	 */
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	/**
	 *
	 * @return transmissionStatusCode
	 */
	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	/**
	 *
	 * @return transmissionStatusName
	 */
	public String getTransmissionStatusName()
	{
		return transmissionStatusName;
	}

	/**
	 *
	 * @param attachmentId
	 */
	public void setAttachmentId( Long attachmentId )
	{
		this.attachmentId = attachmentId;
	}

	/**
	 *
	 * @param attachmentType
	 */
	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	/**
	 *
	 * @param birthDate
	 */
	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	/**
	 *
	 * @param caseNo
	 */
	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	/**
	 *
	 * @param fileName
	 */
	public void setFileName( String fileName )
	{
		this.fileName = fileName;
	}

	/**
	 *
	 * @param idNo
	 */
	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	/**
	 *
	 * @param mobileNumber
	 */
	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	/**
	 *
	 * @param transmissionStatusCode
	 */
	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	/**
	 *
	 * @param transmissionStatusName
	 */
	public void setTransmissionStatusName( String transmissionStatusName )
	{
		this.transmissionStatusName = transmissionStatusName;
	}
}
