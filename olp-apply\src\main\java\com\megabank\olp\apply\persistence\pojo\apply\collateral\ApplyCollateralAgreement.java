package com.megabank.olp.apply.persistence.pojo.apply.collateral;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.apply.persistence.pojo.code.CodeTown;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyCollateralAgreement is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_collateral_agreement" )
public class ApplyCollateralAgreement extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_collateral_agreement";

	public static final String COLLATERAL_ID_CONSTANT = "collateralId";

	public static final String APPLY_COLLATERAL_CONSTANT = "applyCollateral";

	public static final String CODE_TOWN_CONSTANT = "codeTown";

	public static final String EMAIL_CONSTANT = "email";

	public static final String FULL_PAYMENT_CONSTANT = "fullPayment";

	public static final String COLLATERAL_STREET_CONSTANT = "collateralStreet";

	public static final String COLLATERAL_AMT_CONSTANT = "collateralAmt";

	public static final String LOAN_AMT_CONSTANT = "loanAmt";

	public static final String GURANTEE_AMT_CONSTANT = "guranteeAmt";

	public static final String SIGNATORY_CONSTANT = "signatory";

	public static final String TERM_NO_CONSTANT = "termNo";

	public static final String BORROWER_SIGN_DATE_CONSTANT = "borrowerSignDate";

	public static final String WARRANTEE1_CONSTANT = "warrantee1";

	public static final String WARRANTEE2_CONSTANT = "warrantee2";

	public static final String WARRANTEE3_CONSTANT = "warrantee3";

	public static final String LOAN_PRODUCT1_CONSTANT = "loanProduct1";

	public static final String LOAN_PRODUCT2_CONSTANT = "loanProduct2";

	public static final String LOAN_PRODUCT3_CONSTANT = "loanProduct3";

	public static final String LOAN_PRODUCT4_CONSTANT = "loanProduct4";

	public static final String LOAN_PRODUCT5_CONSTANT = "loanProduct5";

	private long collateralId;

	private transient ApplyCollateral applyCollateral;

	private transient CodeTown codeTown;

	private String email;

	private boolean fullPayment;

	private String collateralStreet;

	private long collateralAmt;

	private long loanAmt;

	private Long guranteeAmt;

	private String signatory;

	private String termNo;

	private Date borrowerSignDate;

	private String warrantee1;

	private String warrantee2;

	private String warrantee3;

	private String loanProduct1;

	private String loanProduct2;

	private String loanProduct3;

	private String loanProduct4;

	private String loanProduct5;

	public ApplyCollateralAgreement()
	{}

	public ApplyCollateralAgreement( ApplyCollateral applyCollateral, CodeTown codeTown, String email, boolean fullPayment, String collateralStreet,
									 long collateralAmt, long loanAmt, Date borrowerSignDate, String warrantee1, String warrantee2, String warrantee3,
									 String loanProduct1 )
	{
		this.applyCollateral = applyCollateral;
		this.codeTown = codeTown;
		this.email = email;
		this.fullPayment = fullPayment;
		this.collateralStreet = collateralStreet;
		this.collateralAmt = collateralAmt;
		this.loanAmt = loanAmt;
		this.borrowerSignDate = borrowerSignDate;
		this.warrantee1 = warrantee1;
		this.warrantee2 = warrantee2;
		this.warrantee3 = warrantee3;
		this.loanProduct1 = loanProduct1;
	}

	public ApplyCollateralAgreement( Long collateralId )
	{
		this.collateralId = collateralId;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public ApplyCollateral getApplyCollateral()
	{
		return applyCollateral;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "borrower_sign_date", nullable = false, length = 23 )
	public Date getBorrowerSignDate()
	{
		return borrowerSignDate;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "collateral_town_code", nullable = false )
	public CodeTown getCodeTown()
	{
		return codeTown;
	}

	@Column( name = "collateral_amt", nullable = false, precision = 10, scale = 0 )
	public long getCollateralAmt()
	{
		return collateralAmt;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "applyCollateral" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "collateral_id", unique = true, nullable = false )
	public long getCollateralId()
	{
		return collateralId;
	}

	@Column( name = "collateral_street", nullable = false )
	public String getCollateralStreet()
	{
		return collateralStreet;
	}

	@Column( name = "email", nullable = false, length = 100 )
	public String getEmail()
	{
		return email;
	}

	@Column( name = "gurantee_amt", precision = 10, scale = 0 )
	public Long getGuranteeAmt()
	{
		return guranteeAmt;
	}

	@Column( name = "loan_amt", nullable = false, precision = 10, scale = 0 )
	public long getLoanAmt()
	{
		return loanAmt;
	}

	@Column( name = "loan_product_1", nullable = false )
	public String getLoanProduct1()
	{
		return loanProduct1;
	}

	@Column( name = "loan_product_2" )
	public String getLoanProduct2()
	{
		return loanProduct2;
	}

	@Column( name = "loan_product_3" )
	public String getLoanProduct3()
	{
		return loanProduct3;
	}

	@Column( name = "loan_product_4" )
	public String getLoanProduct4()
	{
		return loanProduct4;
	}

	@Column( name = "loan_product_5" )
	public String getLoanProduct5()
	{
		return loanProduct5;
	}

	@Column( name = "signatory" )
	public String getSignatory()
	{
		return signatory;
	}

	@Column( name = "term_no", length = 10 )
	public String getTermNo()
	{
		return termNo;
	}

	@Column( name = "warrantee_1", nullable = false )
	public String getWarrantee1()
	{
		return warrantee1;
	}

	@Column( name = "warrantee_2", nullable = false )
	public String getWarrantee2()
	{
		return warrantee2;
	}

	@Column( name = "warrantee_3", nullable = false )
	public String getWarrantee3()
	{
		return warrantee3;
	}

	@Column( name = "full_payment", nullable = false, precision = 1, scale = 0 )
	public boolean isFullPayment()
	{
		return fullPayment;
	}

	public void setApplyCollateral( ApplyCollateral applyCollateral )
	{
		this.applyCollateral = applyCollateral;
	}

	public void setBorrowerSignDate( Date borrowerSignDate )
	{
		this.borrowerSignDate = borrowerSignDate;
	}

	public void setCodeTown( CodeTown codeTown )
	{
		this.codeTown = codeTown;
	}

	public void setCollateralAmt( long collateralAmt )
	{
		this.collateralAmt = collateralAmt;
	}

	public void setCollateralId( long collateralId )
	{
		this.collateralId = collateralId;
	}

	public void setCollateralStreet( String collateralStreet )
	{
		this.collateralStreet = collateralStreet;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setFullPayment( boolean fullPayment )
	{
		this.fullPayment = fullPayment;
	}

	public void setGuranteeAmt( Long guranteeAmt )
	{
		this.guranteeAmt = guranteeAmt;
	}

	public void setLoanAmt( long loanAmt )
	{
		this.loanAmt = loanAmt;
	}

	public void setLoanProduct1( String loanProduct1 )
	{
		this.loanProduct1 = loanProduct1;
	}

	public void setLoanProduct2( String loanProduct2 )
	{
		this.loanProduct2 = loanProduct2;
	}

	public void setLoanProduct3( String loanProduct3 )
	{
		this.loanProduct3 = loanProduct3;
	}

	public void setLoanProduct4( String loanProduct4 )
	{
		this.loanProduct4 = loanProduct4;
	}

	public void setLoanProduct5( String loanProduct5 )
	{
		this.loanProduct5 = loanProduct5;
	}

	public void setSignatory( String signatory )
	{
		this.signatory = signatory;
	}

	public void setTermNo( String termNo )
	{
		this.termNo = termNo;
	}

	public void setWarrantee1( String warrantee1 )
	{
		this.warrantee1 = warrantee1;
	}

	public void setWarrantee2( String warrantee2 )
	{
		this.warrantee2 = warrantee2;
	}

	public void setWarrantee3( String warrantee3 )
	{
		this.warrantee3 = warrantee3;
	}
}