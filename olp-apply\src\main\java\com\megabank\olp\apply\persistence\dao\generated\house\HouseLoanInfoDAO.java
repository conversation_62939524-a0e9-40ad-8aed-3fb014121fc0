package com.megabank.olp.apply.persistence.dao.generated.house;

import java.math.BigDecimal;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.house.HouseLoanInfo;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The HouseLoanInfoDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class HouseLoanInfoDAO extends BasePojoDAO<HouseLoanInfo, Long>
{
	@Autowired
	private HouseLoanTrialInfoDAO loanTrialInfoDAO;

	public Long create( Long houseTrialId, BigDecimal topLoanCredit, BigDecimal rate )
	{
		Validate.notNull( houseTrialId );

		HouseLoanInfo pojo = new HouseLoanInfo();
		pojo.setHouseLoanTrialInfo( loanTrialInfoDAO.read( houseTrialId ) );
		pojo.setTopLoanCredit( topLoanCredit );
		pojo.setRate( rate );

		return super.createPojo( pojo );
	}

	@Override
	protected Class<HouseLoanInfo> getPojoClass()
	{
		return HouseLoanInfo.class;
	}
}
