package com.megabank.olp.apply.persistence.pojo.apply.loan;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyLoanCounter is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_counter" )
public class ApplyLoanCounter extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_counter";

	public static final String LOAN_COUNTER_CONSTANT = "loanCounter";

	private Long loanCounter;

	public ApplyLoanCounter()
	{}

	public ApplyLoanCounter( Long loanCounter )
	{
		this.loanCounter = loanCounter;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "loan_counter", unique = true, nullable = false )
	public Long getLoanCounter()
	{
		return loanCounter;
	}

	public void setLoanCounter( Long loanCounter )
	{
		this.loanCounter = loanCounter;
	}
}