package com.megabank.olp.apply.service.loan;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.persistence.dao.generated.apply.collateral.ApplyCollateralDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplyPayeeInfoDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningAppropriationPaymentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningContractDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningEddaDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningRateDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningRepaymentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.TempApplySigningRateDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeIdentityTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeListDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeOtherBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeRateAdjustmentNotificationDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeUserSubTypeDAO;
import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddress;
import com.megabank.olp.apply.persistence.pojo.apply.collateral.ApplyCollateral;
import com.megabank.olp.apply.persistence.pojo.apply.collateral.ApplyCollateralAgreement;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanBasic;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContactInfo;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContent;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanGuaranteeInfo;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanOccupation;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanRelation;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanServed;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyPayeeInfo;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningAppropriation;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningAppropriationPayment;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningBankAccount;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningEdda;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningRate;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningUser;
import com.megabank.olp.apply.persistence.pojo.apply.signing.TempApplySigningRate;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeList;
import com.megabank.olp.apply.persistence.pojo.code.CodeOtherBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeRateAdjustmentNotification;
import com.megabank.olp.apply.persistence.pojo.code.CodeTown;
import com.megabank.olp.apply.service.inet.InetService;
import com.megabank.olp.apply.service.inet.bean.PackagedInetResBean;
import com.megabank.olp.apply.service.loan.bean.signing.AmortiseTableParamBean;
import com.megabank.olp.apply.utility.ApplyLoanUtils;
import com.megabank.olp.apply.utility.BaseApplyService;
import com.megabank.olp.apply.utility.enums.InetResponseStatusEnum;
import com.megabank.olp.apply.utility.enums.InetReturnTypeEnum;
import com.megabank.olp.apply.utility.enums.InetRptTemplateEnum;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.enums.LoanPlanEnum;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.UserSubTypeEnum;
import com.megabank.olp.base.enums.UserTypeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.utility.CommonStringUtils;
import com.megabank.olp.base.utility.CommonUtil;
import com.megabank.olp.base.utility.date.CommonDateFormatUtils;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.base.utility.date.CommonDateUtils;
import com.megabank.olp.base.utility.date.CommonTimeUtils;
import com.megabank.olp.base.utility.text.CommonHtml2PdfUtils;
import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoResultBean;
import com.megabank.olp.client.service.common.UserClientService;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

@Service
@Transactional
public class GenerateService extends BaseApplyService
{
	private static final String BLANK_TEXT = "&nbsp;&nbsp;&nbsp;&nbsp;";

	private static final String REGEX = "\\{(\\w+)\\}";

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String CHECKED_CONSTANT = "checked";

	private static final String CONTRACT_VERSION_CONSTANT = "contractVersion";

	private static final String BOLD_FONT_PATH = "classpath:fonts/NotoSansCJKtc-Bold.otf";

	private static final String REGULAR_FONT_PATH = "classpath:fonts/NotoSansCJKtc-Regular.otf";

	private static final String AP_NAME = "olp-apply";

	private static final int REPAYMENT_ROW_COUNT = 10;

	private byte[] regularFontProgram;

	private byte[] boldFontProgram;

	@Autowired
	private ResourceLoader resourceLoader;

	@Autowired
	private ApplyCollateralDAO applyCollateralDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private ApplyPayeeInfoDAO applyPayeeInfoDAO;

	@Autowired
	private ApplySigningContractDAO signingContractDAO;

	@Autowired
	private CodeUserSubTypeDAO codeUserSubTypeDAO;

	@Autowired
	private UserClientService userClientService;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeOtherBankDAO codeOtherBankDAO;

	@Autowired
	private CodeIdentityTypeDAO codeIdentityTypeDAO;

	@Autowired
	private CodeRateAdjustmentNotificationDAO codeRateAdjustmentNotificationDAO;

	@Autowired
	private ApplySigningEddaDAO signingEddaDAO;

	@Autowired
	private CodeListDAO codeListDAO;

	private Map<String, String> htmlMap = new HashMap<>();

	@Autowired
	private SystemService systemService;

	@Autowired
	private InetService inetService;

	@Autowired
	private ApplySigningRateDAO signingRateDAO;

	@Autowired
	private ApplySigningRepaymentDAO applySigningRepaymentDAO;

	@Autowired
	private ApplySigningAppropriationPaymentDAO applySigningAppropriationPaymentDAO;

	@Autowired
	private TempApplySigningRateDAO tempApplySigningRateDAO;

	public AmortiseTableParamBean calculateRate( Integer loan, Integer limitMonth, Integer bufferMonth, Integer cal_Type, List<Double> multipleRate,
												 List<Integer> multipleRate_TimeStart, List<Integer> multipleRate_TimeEnd, List<Integer> costList,
												 ApplySigningAppropriation appropriation )
		throws Exception
	{
		String checkMessage = "";
		BigDecimal rate = BigDecimal.ONE;
		BigDecimal num1 = BigDecimal.ONE;
		BigDecimal num2 = BigDecimal.ONE;
		AmortiseTableParamBean rateBean = new AmortiseTableParamBean();

		List<String> errorMessage = checkCalculateRate( loan, limitMonth, bufferMonth, cal_Type, multipleRate, multipleRate_TimeStart,
														multipleRate_TimeEnd, costList );

		if( errorMessage.size() > 0 )
			throw new Exception( CommonStringUtils.join( errorMessage, "。" ) );

		Integer totalCost = 0;
		for( Integer cost : costList )
			totalCost += cost;

		if( cal_Type == 0 || cal_Type > 3 )
			cal_Type = 1;
		Integer totalPeriod = limitMonth;
		double loan_money = CommonUtil.parseDouble( loan );
		Integer loan_time_true = limitMonth - bufferMonth;
		double loan_money_now = CommonUtil.parseDouble( loan );

		double ii = 0;
		double pp = CommonUtil.parseDouble( loan_money );  // 剩餘本金
		double pb = 0; // 償還本金
		double pm = 0; // 本利和
		double rr = 0; // 月利率
		double rt = 0; // 本期目前月利率
		double rt_old = 0; // 驗證利率異動
		double loan_money_old = CommonUtil.parseDouble( loan_money ); // 初期貸款金額，利率沒動的情況下，不需異動
		double tm = 1; // 目前應在期數
		double nm = 1; // 目前所在期數
		double loanType_2_pp = 0; // 累積總回款本金
		double yp = 0;
		double rt_pow = 1;// 計算平均攤還利率
		double add_ii = 0;

		double loan_money_int = loan_money; // 初期貸款金額
		BigDecimal total_fee = CommonUtil.parseBigDecimal( totalCost ); // 其他成本
		Double tmirr = 0.0; // 內部投資報酬率(月)
		Double tirr = 0.0;  // 內部投資報酬率(年)
		List<Double> moneyList1 = new ArrayList<>( Arrays.asList( CommonUtil.parseDouble( loan_money_int ) ) )
		{};  // 正現金流量
		List<Double> moneyList2 = new ArrayList<>();  // 負現金流量

		List<Double> moneyList3 = new ArrayList<>();  // 本金
		List<Double> moneyList4 = new ArrayList<>();  // 利息

		List<String> moneyList5 = new ArrayList<>();  // 利息

		int ratePointNumberCount = 15;  // 利率計算小數位數
		int moneyPointNumberCount = 2;  // 費用計算小數位數

		String totalCostYearRate = "";
		// 各期試算
		try
		{
			for( int nn = 1; nn <= totalPeriod; nn++ )
			{
				// 決定利率
				int index_now = 0;
				for( int i = 0; i < multipleRate_TimeEnd.size(); i++ )
					if( nn <= multipleRate_TimeEnd.get( i ) )
					{
						index_now = i;
						tm = i + 1;
						rr = multipleRate.get( index_now );
						break;
					}
				rt = rr / 1200;

				if( tm > nm )
				{
					loan_money = loan_money_now;
					loan_time_true = totalPeriod - nn + 1;
				}

				// 寬限期內
				if( nn <= bufferMonth )
				{
					ii = getAwayFromZero( loan_money_now * rt, moneyPointNumberCount );
					pp = getAwayFromZero( loan_money_now, moneyPointNumberCount );
					pm = getAwayFromZero( ii, moneyPointNumberCount );
					pb = getAwayFromZero( pm - ii, moneyPointNumberCount );
				}

				// 寬限期外
				if( nn > bufferMonth )
				{
					// 依還款方式進行計算
					if( cal_Type == 1 )  // 本息平均攤還
					{
						if( rt != rt_old )
						{
							rt_old = rt;// 記錄利率，利率有異動才須重算平均攤還利率
							rt_pow = 1;
							for( int i = 0; i < loan_time_true; i++ )
								rt_pow *= ( 1 + rt );
							loan_money_old = loan_money_now;
						}

						yp = getAwayFromZero( ( rt * ( rt_pow ) ) / ( ( rt_pow ) - 1 ), ratePointNumberCount );  // 利率
						pm = Math.ceil( loan_money_old * yp );               // 月繳金額
						ii = getAwayFromZero( loan_money_now * rt, 0 );               // 利息
						pb = getAwayFromZero( pm - ii, 0 );                           // 償還本金

						// J-111-0378 增加判斷前後利息差
						if( nn == 1 )
						{
							// 首次還款日
							Date first_payment_date = appropriation.getFirstPaymentDate();
							// 首次還款日計算起日
							Date first_payment_startDate = DateUtils.addMonths( appropriation.getFirstPaymentDate(), -1 );
							// 撥款日
							Date appropriation_date = appropriation.getAppropriationDate();

							int diffDate = CommonUtil.parseInt( CommonDateFormatUtils.sub( first_payment_startDate, appropriation_date ) );

							if( diffDate != 0 )
							{
								add_ii = getAwayFromZero( loan_money_old * ( rr / 100 ) * diffDate / 365, 0 );
								ii += add_ii;
								pm += add_ii;
							}
						}
						if( nn == totalPeriod )
						{
							pm = getAwayFromZero( ii + loan_money_now, 0 );                   // 月繳金額
							pb = getAwayFromZero( loan_money_now, 0 );                        // 償還本金
						}
					}
					if( cal_Type == 2 )  // 本金平均攤還
					{
						pb = getToEven( loan / ( totalPeriod - bufferMonth ), moneyPointNumberCount );
						if( nn == totalPeriod )
							pb = loan - loanType_2_pp;

						loanType_2_pp = loanType_2_pp + pb;
						ii = getToEven( loan_money_now * rt, moneyPointNumberCount );
						pm = pb + ii;
					}
					if( cal_Type == 3 )  // 到期一次還本
					{
						ii = getAwayFromZero( loan_money_now * rt, moneyPointNumberCount );
						pb = 0;
						if( nn == totalPeriod )
							pb = loan_money_now;
						pm =
						   getAwayFromZero( getToEven( ii, moneyPointNumberCount ) + getToEven( pb, moneyPointNumberCount ), moneyPointNumberCount );
					}

					loan_money_now = loan_money_now - pb;
					pp = getAwayFromZero( loan_money_now, moneyPointNumberCount );   // 剩餘本金
					if( pp < 1 )
						pp = 0;
				}

				moneyList1.add( CommonUtil.parseDouble( pp ) );// 貸款餘額
				moneyList2.add( CommonUtil.parseDouble( pm ) );// 本利和

				moneyList3.add( CommonUtil.parseDouble( pb ) );// 本金
				moneyList4.add( CommonUtil.parseDouble( ii ) );// 利息

				moneyList5.add( CommonDateFormatUtils.shiftDaysString( appropriation.getFirstPaymentDate(), "yyyyMM", nn - 1 ) );// 期數
			}

			// 計算總費用年百分率
			double maxprate = 0.5;
			double minparate = -0.5;

			Double[] ml1 = moneyList1.toArray( new Double[ moneyList1.size() ] );
			Double[] ml2 = moneyList2.toArray( new Double[ moneyList2.size() ] );

			tmirr = IRRtry( ml1, ml2, maxprate, 0 );  // 本次月IRR
			if( tmirr == null )
				tmirr = IRRtry( ml1, ml2, 0, minparate );  // 本次月IRR
			if( tmirr != null )
			{
				tirr = tmirr * 1200d;
				DecimalFormat df = new DecimalFormat( "###.##" );
				totalCostYearRate = df.format( tirr ).toString();
			}
			else
			{
				tirr = maxprate * 1200;  // 年利率超過正(負) 0.5*1200
				totalCostYearRate = "";
			}
		}
		catch( Exception ex )
		{

		}

		rateBean.setLoanAmt( moneyList1 );
		rateBean.setCompoundAmt( moneyList2 );
		rateBean.setPrincipalAmt( moneyList3 );
		rateBean.setInterestAmt( moneyList4 );
		rateBean.setPeriod( moneyList5 );
		rateBean.setTotalCostYearRate( totalCostYearRate );

		return rateBean;
	}

	public List<String> checkCalculateRate( Integer loan, Integer limitMonth, Integer bufferMonth, Integer cal_Type, List<Double> multipleRate,
											List<Integer> multipleRate_TimeStart, List<Integer> multipleRate_TimeEnd, List<Integer> costList )
	{
		List<String> errorMessage = new ArrayList<>();
		BigDecimal loan_ErrCount = BigDecimal.ZERO;
		BigDecimal limitMonth_ErrCount = BigDecimal.ZERO;
		BigDecimal bufferMonth_ErrCount = BigDecimal.ZERO;
		BigDecimal mutipleRate_ErrCount = BigDecimal.ZERO;
		BigDecimal mutipleRate_TimeStart_ErrCount = BigDecimal.ZERO;
		BigDecimal mutipleRate_TimeEnd_ErCount = BigDecimal.ZERO;
		BigDecimal costList_ErrCount = BigDecimal.ZERO;
		BigDecimal changeNumber = BigDecimal.ONE;

		if( CommonUtil.isEmpty( loan ) )
		{
			errorMessage.add( "貸款總金額數字不正確，只能用阿拉伯數字組成且不含小數點" );
			loan_ErrCount.add( BigDecimal.ONE );
		}
		// checkMessage = CalTool.CheckPositiveInt(loan, "貸款總金額");
		// if (!Util.isEmpty(checkMessage))
		// {
		// errorMessage.add(checkMessage);
		// loan_ErrCount.add(BigDecimal.ONE);
		// }
		if( loan > 999990000 )
		{
			errorMessage.add( "貸款總金額不能大於 99,999 萬元" );
			loan_ErrCount.add( BigDecimal.ONE );
		}
		if( CommonUtil.isEmpty( limitMonth ) )
		{
			errorMessage.add( "貸款期間數字不正確，只能用阿拉伯數字組成且不含小數點" );
			loan_ErrCount.add( BigDecimal.ONE );
		}
		// checkMessage = CalTool.CheckPositiveInt(limitMonth, "貸款期間");
		// if (!Util.isEmpty(checkMessage))
		// {
		// errorMessage.add(checkMessage);
		// loan_ErrCount.add(BigDecimal.ONE);
		// }
		if( limitMonth > 40 * 12 )
		{
			errorMessage.add( "貸款期間不能大於40年" );
			loan_ErrCount.add( BigDecimal.ONE );
		}
		if( CommonUtil.isEmpty( bufferMonth ) )
		{
			errorMessage.add( "寬限期數字不正確，只能用阿拉伯數字組成且不含小數點" );
			bufferMonth_ErrCount.add( BigDecimal.ONE );
		}
		if( bufferMonth < 0 )
		{
			errorMessage.add( "寬限期不能小於零" );
			bufferMonth_ErrCount.add( BigDecimal.ONE );
		}
		if( bufferMonth > 60 )
		{
			errorMessage.add( "寬限期須小於等於60個月" );
			bufferMonth_ErrCount.add( BigDecimal.ONE );
		}
		if( multipleRate == null || multipleRate.size() <= 0 )
		{
			errorMessage.add( "請填入貸款利率" );
			mutipleRate_ErrCount.add( BigDecimal.ONE );
		}
		if( multipleRate_TimeStart == null || multipleRate_TimeStart.size() <= 0 )
		{
			errorMessage.add( "請填入貸款利率開始月份" );
			mutipleRate_TimeStart_ErrCount.add( BigDecimal.ONE );
		}
		if( multipleRate_TimeEnd == null || multipleRate_TimeEnd.size() <= 0 )
		{
			errorMessage.add( "請填入貸款利率結束月份" );
			mutipleRate_TimeEnd_ErCount.add( BigDecimal.ONE );
		}
		if( mutipleRate_ErrCount.compareTo( BigDecimal.ZERO ) == 0 && mutipleRate_TimeStart_ErrCount.compareTo( BigDecimal.ZERO ) == 0
			&& mutipleRate_TimeEnd_ErCount.compareTo( BigDecimal.ZERO ) == 0 && multipleRate.size() == 1 )
		{
			if( CommonUtil.isEmpty( multipleRate.get( 0 ) ) )
			{
				errorMessage.add( "貸款利率數字不正確" );
				mutipleRate_ErrCount.add( BigDecimal.ONE );
			}
			if( CommonUtil.equals( multipleRate.get( 0 ), BigDecimal.ZERO ) )
			{
				errorMessage.add( "貸款利率數字不正確" );
				mutipleRate_ErrCount.add( BigDecimal.ONE );
			}
			if( CommonUtil.equals( multipleRate.get( 0 ), 0.0 ) )
			{
				errorMessage.add( "貸款利率數字不正確" );
				mutipleRate_ErrCount.add( BigDecimal.ONE );
			}
			// checkMessage = CalTool.CheckYearRate(rate, "貸款利率");
			// if (!Util.isEmpty(checkMessage))
			// {
			// errorMessage.add(checkMessage);
			// mutipleRate_ErrCount.add(BigDecimal.ONE);
			// }
			if( CommonUtil.isEmpty( multipleRate_TimeStart.get( 0 ) ) )
			{
				errorMessage.add( "貸款利率開始月份數字不正確，只能用阿拉伯數字組成且不含小數點" );
				mutipleRate_TimeStart_ErrCount.add( BigDecimal.ONE );
			}
			// checkMessage = CalTool.CheckPositiveInt(changeNumber, "貸款利率開始月份");
			// if (!CommonUtil.isEmpty(checkMessage))
			// {
			// errorMessage.add(checkMessage);
			// mutipleRate_TimeStart_ErrCount.add(BigDecimal.ONE);
			// }
			if( CommonUtil.isEmpty( multipleRate_TimeEnd.get( 0 ) ) )
			{
				errorMessage.add( "貸款利率結束月份數字不正確，只能用阿拉伯數字組成且不含小數點" );
				mutipleRate_TimeEnd_ErCount.add( BigDecimal.ONE );
			}
			// checkMessage = CalTool.CheckPositiveInt(changeNumber, "貸款利率結束月份");
			// if (!CommonUtil.isEmpty(checkMessage))
			// {
			// errorMessage.add(checkMessage);
			// mutipleRate_TimeEnd_ErCount.add(BigDecimal.ONE);
			// }

			if( multipleRate_TimeStart.get( 0 ).compareTo( multipleRate_TimeEnd.get( 0 ) ) > 0 )
			{
				errorMessage.add( "貸款利率的開始月份須小於結束月份" );
				mutipleRate_TimeStart_ErrCount.add( BigDecimal.ONE );
			}
		}
		if( mutipleRate_ErrCount.compareTo( BigDecimal.ZERO ) == 0 && mutipleRate_TimeStart_ErrCount.compareTo( BigDecimal.ZERO ) == 0
			&& mutipleRate_TimeEnd_ErCount.compareTo( BigDecimal.ZERO ) == 0 && multipleRate.size() > 1 )
			for( int i = 0; i < multipleRate.size(); i++ )
		{
			if( CommonUtil.isEmpty( multipleRate.get( i ) ) )
			{
				errorMessage.add( "第" + ( i + 1 ) + "段" + "貸款利率數字不正確" );
				mutipleRate_ErrCount.add( BigDecimal.ONE );
			}
			if( CommonUtil.equals( multipleRate.get( i ), BigDecimal.ZERO ) )
			{
				errorMessage.add( "第" + ( i + 1 ) + "段" + "貸款利率數字不正確" );
				mutipleRate_ErrCount.add( BigDecimal.ONE );
			}
			if( CommonUtil.equals( multipleRate.get( i ), 0.0 ) )
			{
				errorMessage.add( "第" + ( i + 1 ) + "段" + "貸款利率數字不正確" );
				mutipleRate_ErrCount.add( BigDecimal.ONE );
			}
			// checkMessage = CalTool.CheckYearRate(rate, "第" + (i + 1) + "段" + "貸款利率");
			// if (!Util.isEmpty(checkMessage))
			// {
			// errorMessage.add(checkMessage);
			// mutipleRate_ErrCount.add(BigDecimal.ONE);
			// }
			if( CommonUtil.isEmpty( multipleRate_TimeStart.get( i ) ) || !CommonUtil.isInteger( multipleRate_TimeStart.get( i ).toString() ) )
			{
				errorMessage.add( "第" + ( i + 1 ) + "段" + "貸款利率開始月份數字不正確，只能用阿拉伯數字組成且不含小數點" );
				mutipleRate_TimeStart_ErrCount.add( BigDecimal.ONE );
			}
			// checkMessage = CalTool.CheckPositiveInt(changeNumber, "第" + (i + 1) + "段" + "貸款利率開始月份");
			// if (!CommonUtil.isEmpty(checkMessage))
			// {
			// errorMessage.add(checkMessage);
			// mutipleRate_TimeStart_ErrCount.add(BigDecimal.ONE);
			// }
			if( CommonUtil.isEmpty( multipleRate_TimeEnd.get( i ) ) || !CommonUtil.isInteger( multipleRate_TimeEnd.get( i ).toString() ) )
			{
				errorMessage.add( "第" + ( i + 1 ) + "段" + "貸款利率結束月份數字不正確，只能用阿拉伯數字組成且不含小數點" );
				mutipleRate_TimeEnd_ErCount.add( BigDecimal.ONE );
			}
			// checkMessage = CalTool.CheckPositiveInt(changeNumber, "第" + (i + 1) + "段" + "貸款利率結束月份");
			// if (!CommonUtil.isEmpty(checkMessage))
			// {
			// errorMessage.add(checkMessage);
			// mutipleRate_TimeEnd_ErCount.add(BigDecimal.ONE);
			// }

			if( CommonUtil.isInteger( multipleRate_TimeStart.get( i ).toString() )
				&& CommonUtil.isInteger( multipleRate_TimeEnd.get( i ).toString() ) )
				if( multipleRate_TimeStart.get( i ).compareTo( multipleRate_TimeEnd.get( i ) ) > 0 )
			{
				errorMessage.add( "第" + ( i + 1 ) + "段" + "貸款利率的開始月份須小於結束月份" );
				mutipleRate_TimeStart_ErrCount.add( BigDecimal.ONE );
			}
		}
		// if (costList == null || costList.size() <= 0)
		// {
		// errorMessage.add("請填入貸款費用");
		// costList_ErrCount.add(BigDecimal.ONE);
		// }
		if( costList_ErrCount.compareTo( BigDecimal.ZERO ) == 0 )
			if( costList.size() == 1 )
		{
			if( CommonUtil.isEmpty( costList.get( 0 ) ) || !CommonUtil.isInteger( costList.get( 0 ).toString() ) )
			{
				errorMessage.add( "貸款費用數字不正確，只能用阿拉伯數字組成且不含小數點" );
				costList_ErrCount.add( BigDecimal.ONE );
			}
			if( changeNumber.compareTo( BigDecimal.ZERO ) < 0 )
			{
				errorMessage.add( "貸款費用不能小於零" );
				costList_ErrCount.add( BigDecimal.ONE );
			}
		}
			else
			for( int i = 0; i < costList.size(); i++ )
			{
				if( CommonUtil.isEmpty( costList.get( 0 ) ) )
				{
					errorMessage.add( "第" + ( i + 1 ) + "個" + "貸款費用數字不正確，只能用阿拉伯數字組成且不含小數點" );
					costList_ErrCount.add( BigDecimal.ONE );
				}
				if( costList.get( 0 ) < 0 )
				{
					errorMessage.add( "第" + ( i + 1 ) + "個" + "貸款費用不能小於零" );
					costList_ErrCount.add( BigDecimal.ONE );
				}
			}
		if( limitMonth_ErrCount.compareTo( BigDecimal.ZERO ) == 0 && bufferMonth_ErrCount.compareTo( BigDecimal.ZERO ) == 0 )
			if( bufferMonth > ( limitMonth - 12 ) )
		{
			int tmonth = limitMonth - 12;
			if( tmonth < 0 )
				errorMessage.add( "貸款期間不能短於一年" );
			else
				errorMessage.add( "貸款期間需大於寬限期12個月以上" );
		}
		if( limitMonth_ErrCount.compareTo( BigDecimal.ZERO ) == 0 && mutipleRate_ErrCount.compareTo( BigDecimal.ZERO ) == 0
			&& mutipleRate_TimeStart_ErrCount.compareTo( BigDecimal.ZERO ) == 0 && mutipleRate_TimeEnd_ErCount.compareTo( BigDecimal.ZERO ) == 0 )
		{
			Integer sumInterval = 0;
			for( int i = 0; i < multipleRate.size(); i++ )
				sumInterval += multipleRate_TimeEnd.get( i ) - multipleRate_TimeStart.get( i ) + 1;
			if( !CommonUtil.equals( sumInterval, limitMonth ) )
				errorMessage.add( "貸款利率的時間間隔總和須等於貸款期間" );

			// Integer[] sortDecimalArray;
			// sortDecimalArray = multipleRate_TimeStart.OrderBy(x => x).ToArray();
			// if (!multipleRate_TimeStart.SequenceEqual(sortDecimalArray))
			// {
			// errorMessage.add("貸款利率開始月份的後段值需大於前段值");
			// }
			// sortDecimalArray = multipleRate_TimeEnd.OrderBy(x => x).ToArray();
			// if (!multipleRate_TimeEnd.SequenceEqual(sortDecimalArray))
			// {
			// errorMessage.add("貸款利率結束月份的後段值需大於前段值");
			// }
		}
		if( CommonUtil.isEmpty( cal_Type ) || cal_Type <= 0 || cal_Type > 3 )
			errorMessage.add( "還款方式選項不正確" );

		return errorMessage;
	}

	public Map<String, Object> createApplyDescAndDeclarationMap( ApplyLoanContent content )
	{
		Map<String, Object> map = new HashMap<>();
		String headerSpace = "&ensp;&ensp;";
		if( ( content.getCodeMortgageType() != null ) )
		{
			if( content.getCodeMortgageType().getMortgageType().equals( "3" ) )
			{
				map.put( "houseLoanDesc", null );
				map.put( "loanDeclaration", "" );
			}
			else
			{
				// 第三段都要顯示
				map.put( "loanDeclaration",
						 "申貸人聲明並切結本次為辦理房屋購置擔保放款提供予　貴行作為擔保品之上開不動產，願依照上開勾選之用途使用，非供投資使用，並認知　貴行係以上開聲明切結作為本貸款核貸成數及計息之基礎。上述聲明切結若有不實，申貸人同意　貴行得重新核定本貸款之利率，並溯自本貸款撥貸日起，以重新核定之利率計息，並補收差額息，絕無異議。" );
				switch( content.getCodeMortgageType().getMortgageType() )
				{
					case "1":
						switch( content.getCodePrivateUsageType().getPrivateUsageType() )
						{
							case "1":
								map.put( "houseLoanDesc", headerSpace + "本次申貸之不動產屬：自用住宅(首購)，填寫委任書予  貴行查詢之用。" );
								break;
							case "2":
								map.put( "houseLoanDesc", headerSpace + "本次申貸之不動產屬：自用住宅(首購)，提供申貸人財產清單予  貴行。" );
								break;
							case "3":
								map.put( "houseLoanDesc", headerSpace + "本次申貸之不動產屬：自用住宅(首購)，提供擔保物提供人財產清單予  貴行。" );
								break;
						}
						break;
					case "2":
						switch( content.getCodeNonPrivateUsageType().getNonPrivateUsageType() )
						{
							case "1":
								map.put( "houseLoanDesc", headerSpace + "本次申貸提供之不動產係申貸人(含配偶)購買第2戶(含)以上房屋，自住(供本人或其他親人)使用。" );
								break;
							case "2":
								map.put( "houseLoanDesc", headerSpace + "本次申貸提供之不動產係申貸人(含配偶)購買第2戶(含)以上房屋，換屋使用。" );
								break;
						}
						break;
					default:
						map.put( "houseLoanDesc", "" );
				}
			}
		}
		else
		{
			map.put( "houseLoanDesc", "" );
			map.put( "loanDeclaration", "" );
		}
		return map;
	}

	// use this to test
	public String createLoanHtml( ApplyLoan applyLoan, Date passed_applyTs ) throws IOException
	{
		return getLoanApplyHtml( applyLoan, passed_applyTs );
	}

	/**
	 * 產生聯徵中心同意書pdf (IXML)
	 *
	 * @return
	 * @throws IOException
	 */
	public byte[] generateIxmlAuthorizePdf( Long loanId ) throws IOException
	{
		// TODO
		// String html = getIxmlAuthorizeHtml( applyLoanDAO.read( loanId ) );
		//
		// ByteArrayOutputStream baos = CommonHtml2PdfUtils.convertString2OutputStream( html, getRegularFontProgram(), getBoldFontProgram()
		// );
		//
		// return baos.toByteArray();

		PackagedInetResBean resBean = inetService.getPdf( InetRptTemplateEnum.IXML_AUTHORIZE.getContext(), InetReturnTypeEnum.PDF.getContext(),
														  Collections.singletonList( loanId ) );

		return resBean.getPdfContent().getData();
	}

	/**
	 * 產生申請案件pdf
	 *
	 * @param applyLoan
	 * @return
	 * @throws IOException
	 */
	public byte[] generateLoanApplyPdf( ApplyLoan applyLoan ) throws IOException
	{
		boolean isPersonalLoan = LoanTypeEnum.PERSONAL_LOAN.getContext().equals( applyLoan.getCodeLoanType().getLoanType() );

		String rptTemplate =
						   isPersonalLoan ? InetRptTemplateEnum.PERSONAL_LOAN_APPLY.getContext() : InetRptTemplateEnum.HOUSE_LOAN_APPLY.getContext();

		PackagedInetResBean resBean = inetService.getPdf( rptTemplate, InetReturnTypeEnum.PDF.getContext(),
														  Collections.singletonList( applyLoan.getLoanId() ) );
		if( InetResponseStatusEnum.FAIL.getCode() == resBean.getStat() )
			throw new MyRuntimeException( SystemErrorEnum.CONNECTED_OTHER_SYSTEM, "", resBean.getException() );

		return resBean.getPdfContent().getData();
	}

	// public byte[] generateLoanApplyPdf( ApplyLoan applyLoan, Date passed_applyTs ) throws IOException
	// {
	// String loanType = applyLoan.getCodeLoanType().getLoanType();
	//
	// boolean isInet = ( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) && propertyBean.getPersonalloanHidden() )
	// || ( LoanTypeEnum.HOUSE_LOAN.getContext().equals( loanType ) && propertyBean.getHouseloanHidden() );
	//
	// if( isInet )
	// return generateLoanApplyPdf( applyLoan );
	//
	// String html = getLoanApplyHtml( applyLoan, passed_applyTs );
	// ByteArrayOutputStream baos = CommonHtml2PdfUtils.convertString2OutputStream( html, getRegularFontProgram(), getBoldFontProgram() );
	// return baos.toByteArray();
	// }

	// public byte[] generateLoanApplyPdf( Long loanId ) throws IOException
	// {
	// return generateLoanApplyPdf( applyLoanDAO.read( loanId ), new Date() );
	// }

	// public byte[] generateLoanApplyPdf( Long loanId, Date passed_applyTs ) throws IOException
	// {
	// return generateLoanApplyPdf( applyLoanDAO.read( loanId ), passed_applyTs );
	// }

	/**
	 * 產生擔保品提供人同意案件pdf
	 *
	 * @param applyCollateral
	 * @return
	 * @throws IOException
	 */
	public byte[] generateLoanCollateralPdf( ApplyCollateral applyCollateral ) throws IOException
	{
		PackagedInetResBean resBean = inetService.getPdf( InetRptTemplateEnum.COLLATERAL.getContext(), InetReturnTypeEnum.PDF.getContext(),
														  Collections.singletonList( applyCollateral.getValidatedIdentityId() ) );
		if( InetResponseStatusEnum.FAIL.getCode() == resBean.getStat() )
			throw new MyRuntimeException( SystemErrorEnum.CONNECTED_OTHER_SYSTEM, "", resBean.getException() );

		return resBean.getPdfContent().getData();
	}

	public byte[] generateLoanCollateralPdf( Long collateralId ) throws IOException
	{
		return generateLoanCollateralPdf( applyCollateralDAO.read( collateralId ) );
	}

	/**
	 * 產生對保案件pdf
	 *
	 * @param signingContract
	 * @return
	 * @throws IOException
	 */
	public byte[] generateSigningContractPdf( ApplySigningContract signingContract ) throws IOException
	{
		PackagedInetResBean resBean =
									inetService.getPdf( InetRptTemplateEnum.PERSONAL_LOAN_CONTRACT.getContext(), InetReturnTypeEnum.PDF.getContext(),
														Collections.singletonList( signingContract.getSigningContractId() ) );

		return resBean.getPdfContent().getData();
	}

	// /**
	// * 對保案件pdf套浮水印(temp)
	// *
	// * @param data
	// * @param signingContract
	// * @return
	// * @throws IOException
	// */
	// public byte[] generateSigningContractPdf( byte[] data, ApplySigningContract signingContract ) throws IOException
	// {
	//
	// ByteArrayOutputStream baos =
	// CommonHtml2PdfUtils.convertString2OutputStream( data, getRegularFontProgram(), getBoldFontProgram(),
	// new WatermarkingEventHandler( getRegularFontProgram(),
	// getWatermarkText( signingContract ),
	// 20f, 45f ) );
	//
	// return baos.toByteArray();
	// }

	public byte[] generateSigningContractPdf( Long signingContractId ) throws IOException
	{
		return generateSigningContractPdf( signingContractDAO.read( signingContractId ) );
	}

	public String getEddaBankName( String bankCode )
	{
		if( bankCode == null )
			return "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";

		CodeList codeList = codeListDAO.getPojosByCodeTypeAndcodeValue( CodeList.EDDA_BANK_CODE, bankCode );

		String bankName = codeList != null ? codeList.getCodeDesc() : "";

		if( !StringUtils.isEmpty( bankName ) )
		{
			int index = bankName.indexOf( "銀行" );
			if( index > -1 )
				bankName = bankName.substring( 0, index );
			else
			{
				index = bankName.indexOf( "合作社" );
				if( index > -1 )
					bankName = bankName.substring( 0, index );
			}
		}

		return bankName;
	}

	public String getEddaBranchName( String bankCode, String bankAccNo, String bankBranchCode )
	{
		String branchName = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
		if( bankBranchCode != null )
		{
			CodeList codeList = codeListDAO.getPojosByCodeTypeAndcodeValue( CodeList.EDDA_BANK_BRANCH_CODE, bankCode + bankBranchCode );
			branchName = codeList != null ? codeList.getCodeDesc() : "";
		}
		else if( bankCode.equals( "017" ) )
		{
			String branchCode = bankAccNo.substring( 0, 3 );
			CodeBranchBank codeBranchBank = codeBranchBankDAO.getPojoByBankCode( branchCode );
			branchName = codeBranchBank.getName();
		}
		if( !StringUtils.isEmpty( branchName ) )
		{
			int index = branchName.indexOf( "分行" );
			if( index > -1 )
				branchName = branchName.substring( 0, index );
			index = branchName.indexOf( "分社" );
			if( index > -1 )
				branchName = branchName.substring( 0, index );
			index = branchName.indexOf( "分會" );
			if( index > -1 )
				branchName = branchName.substring( 0, index );
		}
		return branchName;
	}

	/**
	 * 取得房貸感謝頁內容
	 *
	 * @param identityType
	 * @param applyLoan
	 * @return
	 */
	public String getHouseLoanMessage( String identityType )
	{
		return getLoanApplyMessage( identityType );
	}

	/**
	 * 取得信貸感謝頁內容
	 *
	 * @param identityType
	 * @param applyLoan
	 * @return
	 */
	public String getPersonalLoanMessage( String identityType, String loanPlanCode )
	{
		if( StringUtils.isNotBlank( loanPlanCode ) )
			if( StringUtils.equals( loanPlanCode, LoanPlanEnum.M001.getContext() ) )
			return getLoanApplyMessage_PersonalLoan_M001( identityType );
			else if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
				return getLoanApplyMessage_PersonalLoan_ChinaSteelGroup_BatchPersonalLoan( identityType );
		// =============
		return getLoanApplyMessage( identityType );
	}

	public void saveTempSigningRates( ApplySigningContract signingContract ) throws Exception
	{
		// 判斷有資料則刪除。因若已在對保環節製表，為保證表格是最新的資料，撥款環節需刪除舊資料，重新產製
		tempApplySigningRateDAO.deleteAllBySigningContractId( signingContract.getSigningContractId() );

		List<TempApplySigningRate> tempApplySigningRates = new ArrayList<>();
		List<Double> multipleRate = new ArrayList<>();
		List<Integer> multipleRate_TimeStart = new ArrayList<>();
		List<Integer> multipleRate_TimeEnd = new ArrayList<>();
		List<Integer> costList = new ArrayList<>();

		List<ApplySigningRate> applySigningRates = signingRateDAO.getContractRates( signingContract.getSigningContractId() );

		for( ApplySigningRate rate : applySigningRates )
		{
			multipleRate.add( rate.getRate() );
			multipleRate_TimeStart.add( rate.getRateBgn() );
			multipleRate_TimeEnd.add( rate.getRateEnd() );
		}

		ApplySigningAppropriation appropriation = signingContract.getApplySigningAppropriation();
		Integer loan = signingContract.getLoanAmt() * 10000;
		Integer bufferMonth = 0; // 信貸目前沒有寬限期
		Integer cal_Type = 1; // 信貸目前只有本息平均攤還，之後有調整這邊要改
		Integer limitMonth = signingContract.getLoanPeriod();

		AmortiseTableParamBean amortiseTableParamBean = calculateRate( loan, limitMonth, bufferMonth, cal_Type, multipleRate, multipleRate_TimeStart,
																	   multipleRate_TimeEnd, costList, appropriation );
		for( int i = 0; i < amortiseTableParamBean.getPeriod().size(); i++ )
		{
			String Period = amortiseTableParamBean.getPeriod().get( i );
			String LoanAmt = String.format( "%,.0f", amortiseTableParamBean.getLoanAmt().get( i ) );
			String CompoundAmt = String.format( "%,.0f", amortiseTableParamBean.getCompoundAmt().get( i ) );
			String PrincipalAmt = String.format( "%,.0f", amortiseTableParamBean.getPrincipalAmt().get( i ) );
			String InterestAmt = String.format( "%,.0f", amortiseTableParamBean.getInterestAmt().get( i ) );
			String nowLoanAmt = String.format( "%,.0f", amortiseTableParamBean.getLoanAmt().get( i + 1 ) );

			TempApplySigningRate tempApplySigningRate = new TempApplySigningRate();
			tempApplySigningRate.setApplySigningContract( signingContract );
			tempApplySigningRate.setPeriod( Period );
			tempApplySigningRate.setLoanAmt( LoanAmt );
			tempApplySigningRate.setCompoundAmt( CompoundAmt );
			tempApplySigningRate.setPrincipalAmt( PrincipalAmt );
			tempApplySigningRate.setInterestAmt( InterestAmt );
			tempApplySigningRate.setNowLoanAmt( nowLoanAmt );

			tempApplySigningRates.add( tempApplySigningRate );
		}
		tempApplySigningRateDAO.create( tempApplySigningRates );
	}

	private String decidePersonalLoanBorrowerPdfTemplate( String loanPlanCode )
	{
		if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode ) )
			return propertyBean.getPersonalLoanBorrowerCo70647919_c101_PdfTemplate();

		return propertyBean.getPersonalLoanBorrowerPdfTemplate();
	}

	private String decidePersonalLoanSigningContractPage8Template( String loanPlanCode )
	{
		/*
		 * // J-112-0206 配合團貸案之個別商議條款調整為可由分行於簽報書自行選擇(非團貸案件一律不顯示個別商議條款)，
		 * // 精銳科技也比照辦理，故Page8不須特殊處理，註解掉精銳科技SigningPdfTemplatePage8相關判斷邏輯
		 * if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode ) )
		 * return propertyBean.getSigningPdfTemplatePage8_co70647919_c101();
		 */

		return propertyBean.getSigningPdfTemplatePage8();
	}

	private String getAddress( ApplyAddress address )
	{
		if( address == null )
			return null;

		StringBuilder builder = new StringBuilder();

		builder.append( address.getCodeTown().getCodeCity().getName() );
		builder.append( address.getCodeTown().getName() );

		if( !StringUtils.isBlank( address.getVillage() ) )
			builder.append( address.getVillage() );

		if( !StringUtils.isBlank( address.getNeighborhood() ) )
			builder.append( address.getNeighborhood() + "鄰" );

		if( !StringUtils.isBlank( address.getStreet() ) )
			builder.append( address.getStreet() );

		if( !StringUtils.isBlank( address.getStreet() ) && address.getSection() != null )
			builder.append( address.getSection() + "段" );

		if( !StringUtils.isBlank( address.getLane() ) )
			builder.append( address.getLane() + "巷" );

		if( !StringUtils.isBlank( address.getAlley() ) )
			builder.append( address.getAlley() + "弄" );

		if( !StringUtils.isBlank( address.getNo() ) )
			builder.append( address.getNo() + "號" );

		if( address.getFloor() != null )
			builder.append( address.getFloor() + "樓" );

		if( address.getRoom() != null )
			builder.append( "之" + address.getRoom() );

		return builder.toString();
	}

	private String getAddress( CodeTown codeTown, String address )
	{
		if( codeTown == null )
			return null;

		StringBuilder builder = new StringBuilder();

		builder.append( codeTown.getCodeCity().getName() );
		builder.append( codeTown.getName() );
		builder.append( address );

		return builder.toString();
	}

	private Date getAppropriationEndDate( Integer period, Date beginDate )
	{
		Calendar cal = Calendar.getInstance();
		cal.setTime( beginDate );
		cal.add( Calendar.MONTH, period );
		int year = cal.get( Calendar.YEAR );
		int month = cal.get( Calendar.MONTH ) + 1;
		int day = cal.get( Calendar.DAY_OF_MONTH );

		return CommonDateUtils.getDate( year, month, day );
	}

	private double getAwayFromZero( double toBeTruncated, int moneyPointNumberCount )
	{
		Double truncatedDouble = BigDecimal.valueOf( toBeTruncated ).setScale( moneyPointNumberCount, RoundingMode.HALF_UP ).doubleValue();
		return truncatedDouble;
	}

	private synchronized byte[] getBoldFontProgram() throws IOException
	{
		String fontPath = BOLD_FONT_PATH;

		if( boldFontProgram != null && boldFontProgram.length > 0 )
			return boldFontProgram;

		try (InputStream inputStream = resourceLoader.getResource( fontPath ).getInputStream();)
		{
			boldFontProgram = IOUtils.toByteArray( inputStream );

			return boldFontProgram;
		}
	}

	private Map<String, Map<String, Object>> getHouseLoanPathMap( ApplyLoan applyLoan, Date passed_applyTs )
	{
		Map<String, Map<String, Object>> maps = new LinkedHashMap<>();

		if( applyLoan.getRefBorrowerApplyLoan() == null )
			maps.put( propertyBean.getHouseLoanBorrowerPdfTemplate(), getLoanApplyMap( applyLoan, passed_applyTs ) );
		else
		{
			maps.put( propertyBean.getHouseLoanBorrowerPdfTemplate(), getLoanApplyMap( applyLoan.getRefBorrowerApplyLoan(), passed_applyTs ) );
			maps.put( propertyBean.getHouseLoanGuarantorPdfTemplate(), getLoanApplyMap( applyLoan, passed_applyTs ) );
		}

		// H-110-0082 無論有無填寫同一關係人或同一關係企業，在申請書PDF檔皆顯示該頁面並帶入本人姓名及ID資料
		/*
		 * 原本只有當填寫 同一關係人 或 同一關係企業 才顯示，改為一定顯示(因為顯示了借款人本人的資料)
		 *
		 * if( applyLoan.getApplyLoanContent() != null && (
		 * !applyLoan.getApplyLoanContent().getApplyLoanRelations().isEmpty() ||
		 * !applyLoan.getApplyLoanContent().getApplyLoanServeds().isEmpty() ) )
		 * maps.put( propertyBean.getHouseLoanRelationPdfTemplate(), getLoanRelationMap(
		 * applyLoan.getApplyLoanContent(), applyLoan.getCodeLoanType().getLoanType() )
		 * );
		 */
		procRelationPdfTemplate( maps, propertyBean.getHouseLoanRelationPdfTemplate(), applyLoan );

		Map<String, Object> loanAgreementMap = getLoanAgreementMap( applyLoan );
		// for( String agreementTemplate : propertyBean.getHouseLoanAgreementPdfTemplates() )
		// maps.put( agreementTemplate, loanAgreementMap );

		return maps;
	}

	private synchronized String getHtml( String key ) throws IOException
	{
		String html = htmlMap.get( key );

		if( html != null )
			return html;

		InputStream inputStream = null;
		try
		{
			inputStream = resourceLoader.getResource( "classpath:" + key ).getInputStream();

			String tempHtml = IOUtils.toString( inputStream, StandardCharsets.UTF_8 );

			htmlMap.put( key, tempHtml );

			return tempHtml;
		}
		finally
		{
			if( inputStream != null )
				inputStream.close();
		}
	}

	private String getIxmlAuthorizeHtml( ApplyLoan applyLoan ) throws IOException
	{
		Map<String, Map<String, Object>> map = getIxmlAuthorizeHtmlPathMap( applyLoan );
		return getReplacedHtml( map );
	}

	private Map<String, Map<String, Object>> getIxmlAuthorizeHtmlPathMap( ApplyLoan applyLoan )
	{
		return getIxmlAuthorizePathMap( applyLoan );
	}

	private Map<String, Object> getIxmlAuthorizeMap( ApplyLoan applyLoan )
	{
		ApplyLoanBasic applyLoanBasic = applyLoan.getApplyLoanBasic();

		Map<String, Object> map = new HashMap<>();
		map.put( "userName", applyLoanBasic.getName() );
		map.put( "userId", applyLoanBasic.getIdNo() );

		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime( date );

		map.put( "todayYear", calendar.get( Calendar.YEAR ) - 1911 );
		map.put( "todayMonth", calendar.get( Calendar.MONTH ) + 1 ); // calendar 月份為 0 - 11
		map.put( "todayDay", calendar.get( Calendar.DAY_OF_MONTH ) );

		calendar.add( Calendar.MONTH, 3 );

		map.put( "authYear", calendar.get( Calendar.YEAR ) - 1911 );
		map.put( "authMonth", calendar.get( Calendar.MONTH ) + 1 ); // calendar 月份為 0 - 11
		map.put( "authDay", calendar.get( Calendar.DAY_OF_MONTH ) );

		return map;
	}

	private Map<String, Map<String, Object>> getIxmlAuthorizePathMap( ApplyLoan applyLoan )
	{
		Map<String, Map<String, Object>> maps = new LinkedHashMap<>();

		maps.put( propertyBean.getSigningPdfTemplateIxml1(), getIxmlAuthorizeMap( applyLoan ) );
		maps.put( propertyBean.getSigningPdfTemplateIxml2(), new LinkedHashMap<>() );

		return maps;
	}

	private Map<String, Object> getLoanAgreementMap( ApplyLoan applyLoan )
	{
		Map<String, Object> map = new HashMap<>();
		map.put( LOAN_TYPE_CONSTANT, getLoanTitle( applyLoan.getCodeLoanType().getLoanType() ) );
		map.put( "agreementVersion", applyLoan.getLoanVersion() );

		return map;
	}

	private String getLoanApplyHtml( ApplyLoan applyLoan, Date passed_applyTs ) throws IOException
	{
		Map<String, Map<String, Object>> map = getLoanApplyHtmlPathMap( applyLoan, passed_applyTs );

		return getReplacedHtml( map );
	}

	private Map<String, Map<String, Object>> getLoanApplyHtmlPathMap( ApplyLoan applyLoan, Date passed_applyTs )
	{
		if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( applyLoan.getCodeLoanType().getLoanType() ) )
			return getPersonalLoanPathMap( applyLoan, passed_applyTs );

		return getHouseLoanPathMap( applyLoan, passed_applyTs );
	}

	private Map<String, Object> getLoanApplyMap( ApplyLoan applyLoan, Date passed_applyTs )
	{
		Map<String, Object> map = new HashMap<>();

		setApplyIdentityInfoMap( map, applyLoan.getValidatedIdentityId() );

		map.put( "caseNo", applyLoan.getCaseNo() );

		ApplyLoanBasic basic = applyLoan.getApplyLoanBasic();
		map.put( "name", basic.getName() );
		map.put( "educationLevelName", basic.getCodeEducationLevel() == null ? "" : basic.getCodeEducationLevel().getName() );
		map.put( "engFirstName", basic.getEngFirstName() );
		map.put( "engLastName", basic.getEngLastName() );
		map.put( "engName", ApplyLoanUtils.getEnName_decide_by_fg( basic ) );
		map.put( "marriageStatusName", basic.getCodeMarriageStatus() == null ? "" : basic.getCodeMarriageStatus().getName() );
		map.put( "birthDate", CommonDateStringUtils.transDate2String( basic.getBirthDate() ) );
		map.put( "childrenCount", ApplyLoanUtils.get_ApplyLoanBasic_childrenCount( applyLoan.getLoanPlanCode(), basic ) );
		map.put( "idNo", basic.getIdNo() );
		map.put( "otherNationality", basic.getCodeNationality() == null ? null : basic.getCodeNationality().getName() );
		CodeRateAdjustmentNotification rateAdjNotify = StringUtils
					.isBlank( basic.getRateAdjNotify() ) ? null : codeRateAdjustmentNotificationDAO.readToNull( basic.getRateAdjNotify() );
		map.put( "rateAdjNotify", rateAdjNotify == null ? null : rateAdjNotify.getName() );

		ApplyLoanContactInfo contactInfo = applyLoan.getApplyLoanContactInfo();
		map.put( "mobileNumber", contactInfo.getMobileNumber() );
		map.put( "branchBankName", contactInfo.getCodeBranchBank().getName() );
		map.put( "homePhoneNumber", getPhoneNumberWithExtensionSign( contactInfo.getHomePhoneCode(), contactInfo.getHomePhoneNumber(), null ) );
		map.put( "residenceStatus", contactInfo.getCodeResidenceStatus() == null ? "" : contactInfo.getCodeResidenceStatus().getName() );
		map.put( "rent", contactInfo.getRent() == null ? null : contactInfo.getRent() + "元" );
		map.put( "homeAddress", getAddress( contactInfo.getHomeAddress() ) );
		map.put( "mailingAddress", getAddress( contactInfo.getMailingAddress() ) );
		map.put( "email", contactInfo.getEmail() );

		ApplyLoanOccupation occupation = applyLoan.getApplyLoanOccupation();
		map.put( "jobType", occupation.getCodeJobSubType() == null ? "" : occupation.getCodeJobSubType().getCodeJobType().getName() );
		map.put( "jobSubType", occupation.getCodeJobSubType() == null ? "" : occupation.getCodeJobSubType().getName() );
		map.put( "companyName", occupation.getCompanyName() );
		map.put( "titleType", occupation.getCodeTitleType() == null ? "" : occupation.getCodeTitleType().getName() );
		map.put( "companyTaxNo", occupation.getTaxNo() );
		map.put( "annualIncome", occupation.getAnnualIncome() );
		map.put( "companyPhoneNumber", getPhoneNumberWithExtensionSign( occupation.getCompanyPhoneCode(), occupation.getCompanyPhoneNumber(),
																		occupation.getCompanyPhoneExt() ) );
		map.put( "amountPerMonth", occupation.getCodeAmountPerMonth() == null ? null : occupation.getCodeAmountPerMonth().getName() );
		map.put( "seniorityYear", occupation.getSeniorityYear() );
		map.put( "seniorityMonth", occupation.getSeniorityMonth() );
		map.put( "companyAddress", occupation.getApplyAddress() == null ? null : getAddress( occupation.getApplyAddress() ) );
		map.put( "empNo", occupation.getEmpNo() );

		setApplyContentMap( map, applyLoan.getLoanPlanCode(), applyLoan.getApplyLoanContent() );

		map.put( "applyDate",
				 CommonDateStringUtils
							 .transDate2String( applyLoan.getApplyCompletedDate() != null ? applyLoan.getApplyCompletedDate() : passed_applyTs,
												"yyyy/MM/dd HH:mm" ) );

		map.put( LOAN_TYPE_CONSTANT, getLoanTitle( applyLoan.getCodeLoanType().getLoanType() ) );

		setApplyGuaranteeInfoMap( map, applyLoan.getApplyLoanGuaranteeInfo() );

		return map;
	}

	private Map<String, Object> getLoanApplyMapForChinaSteelCompany( ApplyLoan applyLoan, String loanPlanCode, Date passed_applyTs )
	{
		Map<String, Object> map = new HashMap<>();
		map.putAll( getLoanApplyMap( applyLoan, passed_applyTs ) );

		ApplyLoanOccupation occupation = applyLoan.getApplyLoanOccupation();
		ApplyLoanContent content = applyLoan.getApplyLoanContent();
		ApplyLoanBasic basic = applyLoan.getApplyLoanBasic();
		ApplyLoanContactInfo contractInfo = applyLoan.getApplyLoanContactInfo();
		boolean isNotUsTaxpayerIdentity = basic.getNotUsTaxpayer();
		boolean isNotTaxpayerIdentityOutSideRoc = basic.getNotOuttwTaxpayer();
		String homeAddress = contractInfo.getHomeAddrText() != null ? contractInfo.getHomeAddrText().getAddr() : "";
		String mailingAddress = contractInfo.getMailingAddrText() != null ? contractInfo.getMailingAddrText().getAddr() : "";
		String bankCode = StringUtils.isNotEmpty( content.getAppnBankCode() ) ? "(" + content.getAppnBankCode() + ")" : "";
		String account = StringUtils.isNotEmpty( content.getAppnDpAcct() ) ? content.getAppnDpAcct() : "";

		if( LoanPlanEnum.C001.getContext().equals( loanPlanCode ) )
		{
			map.put( "titleType", occupation.getJobPosition() == null ? "" : occupation.getJobPosition() );
			map.put( "companyPhoneNumber",
					 getPhoneNumber( occupation.getCompanyPhoneCode(), occupation.getCompanyPhoneNumber(), occupation.getCompanyPhoneExt() ) );
		}

		map.put( "employeeNo", occupation.getEmpNo() );
		map.put( "entryAccount", bankCode + " " + account );
		map.put( "yes_UsTaxpayerIdentity", isNotUsTaxpayerIdentity ? "□" : "■" );
		map.put( "no_UsTaxpayerIdentity", isNotUsTaxpayerIdentity ? "■" : "□" );
		map.put( "yes_TaxpayerIdentityOutSideRoc", isNotTaxpayerIdentityOutSideRoc ? "□" : "■" );
		map.put( "no_TaxpayerIdentityOutSideRoc", isNotTaxpayerIdentityOutSideRoc ? "■" : "□" );

		// 子公司戶籍及通訊地址儲存在不同欄位
		if( map.get( "homeAddress" ) == null )
			map.put( "homeAddress", homeAddress );

		if( map.get( "mailingAddress" ) == null )
			map.put( "mailingAddress", mailingAddress );

		map.put( "seniorityYear", occupation.getSeniorityYear() != null ? occupation.getSeniorityYear() + " 年" : "" );
		map.put( "seniorityMonth", occupation.getSeniorityMonth() != null ? occupation.getSeniorityMonth() + " 個月" : "" );

		return map;
	}

	private String getLoanApplyMessage( String identityType )
	{
		String message = "如您尚未上傳<br>" + "1.身分證<br>" + "2.財力文件<br>" + "(如薪轉存摺封面與近半年內頁、薪資扣繳憑單或報稅所得等)<br>" + "等相關文件，可於本網站點選線上補件功能補行上傳，<br>"
			+ "我們將於收到完整資料後盡速為您審核!";

		String skipMessage = "";

		if( IdentityTypeEnum.SKIP.getContext().equals( identityType ) )
			return StringUtils.isBlank( skipMessage ) ? message : skipMessage;

		return message;

	}

	private String getLoanApplyMessage_PersonalLoan_ChinaSteelGroup_BatchPersonalLoan( String identityType )
	{
		List<String> msg_list = new ArrayList<>();
		msg_list.add( "如您尚未上傳" );
		msg_list.addAll( ApplyLoanUtils.getAttchItem_ChinaSteelGroup_BatchPersonalLoan() );
		// =========================
		msg_list.add( "可於本網站點選線上補件功能補行上傳，" );
		msg_list.add( "我們將於收到完整資料後盡速為您審核!" );
		String message = StringUtils.join( msg_list, "<br>" );

		String skipMessage = "";

		if( IdentityTypeEnum.SKIP.getContext().equals( identityType ) )
			return StringUtils.isBlank( skipMessage ) ? message : skipMessage;

		return message;
	}

	private String getLoanApplyMessage_PersonalLoan_M001( String identityType )
	{
		List<String> msg_list = new ArrayList<>();
		msg_list.add( "如您尚未上傳" );
		msg_list.addAll( ApplyLoanUtils.getAttchItem_PersonalLoan_M001() );
		// =========================
		msg_list.add( "可於本網站點選線上補件功能補行上傳，" );
		msg_list.add( "我們將於收到完整資料後盡速為您審核!" );
		String message = StringUtils.join( msg_list, "<br>" );

		String skipMessage = "";

		if( IdentityTypeEnum.SKIP.getContext().equals( identityType ) )
			return StringUtils.isBlank( skipMessage ) ? message : skipMessage;

		return message;
	}

	private String getLoanCollateralHtml( ApplyCollateral applyCollateral ) throws IOException
	{
		Map<String, Map<String, Object>> map = getLoanCollateralHtmlPathMap( applyCollateral );

		return getReplacedHtml( map );
	}

	private Map<String, Map<String, Object>> getLoanCollateralHtmlPathMap( ApplyCollateral applyCollateral )
	{
		Map<String, Map<String, Object>> maps = new LinkedHashMap<>();

		String pdfTemplate = applyCollateral.getApplyCollateralAgreement().isFullPayment() ? propertyBean.getCollateralFullPaymentPdfTemplate()
																						   : propertyBean.getCollateralNonFullPaymentPdfTemplate();
		maps.put( pdfTemplate, getLoanCollateralMap( applyCollateral ) );

		return maps;
	}

	private Map<String, Object> getLoanCollateralMap( ApplyCollateral applyCollateral )
	{
		Map<String, Object> map = new HashMap<>();

		IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( applyCollateral.getValidatedIdentityId() );

		ApplyCollateralAgreement applyCollateralAgreement = applyCollateral.getApplyCollateralAgreement();
		map.put( "collateralAddress", getAddress( applyCollateralAgreement.getCodeTown(), applyCollateralAgreement.getCollateralStreet() ) );
		map.put( "collateralAmt", applyCollateralAgreement.getCollateralAmt() );
		map.put( "loanAmt", applyCollateralAgreement.getLoanAmt() );
		map.put( "guranteeAmt", applyCollateralAgreement.getGuranteeAmt() );
		map.put( "borrowerSignDate", CommonDateStringUtils.transDate2String( applyCollateralAgreement.getBorrowerSignDate() ) );
		map.put( "warrantee1", applyCollateralAgreement.getWarrantee1() );
		map.put( "warrantee2", applyCollateralAgreement.getWarrantee2() );
		map.put( "warrantee3", applyCollateralAgreement.getWarrantee3() );
		map.put( "loanProduct1", applyCollateralAgreement.getLoanProduct1() );
		map.put( "loanProduct2", getLoanProduct( applyCollateralAgreement.getLoanProduct2() ) );
		map.put( "loanProduct3", getLoanProduct( applyCollateralAgreement.getLoanProduct3() ) );
		map.put( "loanProduct4", getLoanProduct( applyCollateralAgreement.getLoanProduct4() ) );
		map.put( "loanProduct5", getLoanProduct( applyCollateralAgreement.getLoanProduct5() ) );
		map.put( "signatory", applyCollateralAgreement.getSignatory() );
		map.put( "termNo", applyCollateralAgreement.getTermNo() );

		map.put( "providerName", applyCollateral.getProviderName() );
		map.put( "providerSignDate", CommonDateStringUtils.transDate2String( new Date() ) );

		map.put( "identityType", codeIdentityTypeDAO.read( identityInfo.getIdentityType() ).getName() );

		return map;
	}

	private String getLoanProduct( String value )
	{
		return StringUtils.isBlank( value ) ? BLANK_TEXT : value;
	}

	private String getLoanPurpose( String loanPurpose )
	{
		if( StringUtils.isBlank( loanPurpose ) )
			return "";

		String[] strs = StringUtils.split( loanPurpose, "\\|" );

		if( strs == null )
			return "";

		StringBuilder builder = new StringBuilder();
		for( String str : strs )
		{
			String[] substr = StringUtils.split( str, "," );

			if( substr == null || substr.length == 0 )
				continue;

			if( ( substr.length == 1 ) || ( substr.length > 1 && "1".equals( substr[ 1 ] ) ) )
				builder.append( "<input type=\"checkbox\" name=\"\" value=\"\" checked>" );
			else
				builder.append( "<input type=\"checkbox\" name=\"\" value=\"\">" );

			builder.append( substr[ 0 ] );
			builder.append( "&nbsp;" );
		}

		return builder.toString();
	}

	private Map<String, Object> getLoanRelationMap( ApplyLoanBasic basic, ApplyLoanContent contact, String loanType )
	{
		Map<String, Object> map = new HashMap<>();

		map.put( LOAN_TYPE_CONSTANT, getLoanTitle( loanType ) );
		// 在 ApplyServicePropertyBean.java 裡
		/*
		 * @Value( "${service.pdf.template.personalloan.relation}" ) private String
		 * personalLoanRelationPdfTemplate;
		 *
		 * 會抓到 application.yml 內的設定 將把 以下的變數，填入到 template 檔案 ===> pdf/apply/page-3.html
		 *
		 */
		map.put( "name", basic.getName() );
		map.put( "idNo", basic.getIdNo() );
		int size = 5;

		setApplyRelationMap( map, contact.getApplyLoanRelations(), size );

		setApplyServedMap( map, contact.getApplyLoanServeds(), size );

		return map;
	}

	private String getLoanTitle( String loanType )
	{
		return LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) ? "個人信用" : "房屋";
	}

	private Map<String, Map<String, Object>> getPersonalLoanPathMap( ApplyLoan applyLoan, Date passed_applyTs )
	{
		Map<String, Map<String, Object>> maps = new LinkedHashMap<>();

		ApplyLoan borrowerApplyLoan = applyLoan.getRefBorrowerApplyLoan();
		String loanPlanCode = borrowerApplyLoan != null ? borrowerApplyLoan.getLoanPlanCode() : applyLoan.getLoanPlanCode();

		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )

			maps.putAll( processPersonalLoanBorrowerPdfForChinaSteelCompany( applyLoan, loanPlanCode, passed_applyTs ) );

		else if( borrowerApplyLoan == null )
			maps.put( decidePersonalLoanBorrowerPdfTemplate( loanPlanCode ), getLoanApplyMap( applyLoan, passed_applyTs ) );
		else
		{
			maps.put( decidePersonalLoanBorrowerPdfTemplate( loanPlanCode ), getLoanApplyMap( borrowerApplyLoan, passed_applyTs ) );
			maps.put( propertyBean.getPersonalLoanGuarantorPdfTemplate(), getLoanApplyMap( applyLoan, passed_applyTs ) );
		}

		// H-110-0082 無論有無填寫同一關係人或同一關係企業，在申請書PDF檔皆顯示該頁面並帶入本人姓名ID等資料
		/*
		 * 原本只有當填寫 同一關係人 或 同一關係企業 才顯示，改為一定顯示
		 *
		 * if( applyLoan.getApplyLoanContent() != null && (
		 * !applyLoan.getApplyLoanContent().getApplyLoanRelations().isEmpty() ||
		 * !applyLoan.getApplyLoanContent().getApplyLoanServeds().isEmpty() ) )
		 * maps.put( propertyBean.getPersonalLoanRelationPdfTemplate(),
		 * getLoanRelationMap( applyLoan.getApplyLoanContent(),
		 * applyLoan.getCodeLoanType().getLoanType() ) );
		 */
		procRelationPdfTemplate( maps, propertyBean.getPersonalLoanRelationPdfTemplate(), applyLoan );

		maps.put( propertyBean.getPersonalLoanAgreementPdfTemplate(), getLoanAgreementMap( applyLoan ) );

		return maps;
	}

	private String getPhoneNumber( String phoneCode, String phoneNumber, String phoneExt )
	{
		if( StringUtils.isAnyBlank( phoneCode, phoneNumber ) )
			return null;

		if( StringUtils.isBlank( phoneExt ) )
			return phoneCode + "-" + phoneNumber;

		return phoneCode + "-" + phoneNumber + phoneExt;
	}

	private String getPhoneNumberWithExtensionSign( String phoneCode, String phoneNumber, String phoneExt )
	{
		if( StringUtils.isAnyBlank( phoneCode, phoneNumber ) )
			return null;

		if( StringUtils.isBlank( phoneExt ) )
			return phoneCode + "-" + phoneNumber;

		return phoneCode + "-" + phoneNumber + "#" + phoneExt;
	}

	private String getPlanInfo( String title, String info )
	{
		if( StringUtils.isBlank( title ) )
			return null;

		return title + "：" + info;

	}

	private synchronized byte[] getRegularFontProgram() throws IOException
	{
		String fontPath = REGULAR_FONT_PATH;

		if( regularFontProgram != null && regularFontProgram.length > 0 )
			return regularFontProgram;

		try (InputStream inputStream = resourceLoader.getResource( fontPath ).getInputStream();)
		{
			regularFontProgram = IOUtils.toByteArray( inputStream );

			return regularFontProgram;
		}
	}

	private String getReplacedHtml( Map<String, Map<String, Object>> map ) throws IOException
	{

		StringBuilder builder = new StringBuilder();

		for( Map.Entry<String, Map<String, Object>> entry : map.entrySet() )
		{
			String html = getHtml( entry.getKey() );

			if( entry.getValue() == null )
				builder.append( html );
			else
				builder.append( CommonHtml2PdfUtils.replaceHtml( html, REGEX, entry.getValue() ) );
		}

		return builder.toString();
	}

	private String getSigningContractHtml( ApplySigningContract signingContract ) throws IOException
	{
		Map<String, Map<String, Object>> map = getSigningContractHtmlPathMap( signingContract );

		return getReplacedHtml( map );
	}

	private Map<String, Map<String, Object>> getSigningContractHtmlPathMap( ApplySigningContract signingContract )
	{
		Map<String, Map<String, Object>> maps = new LinkedHashMap<>();

		IdentityInfoResultBean borrowerIdentityInfo = null;
		IdentityInfoResultBean guarantorIdentityInfo = null;
		ApplySigningUser signingBorrower = null;
		ApplySigningUser signingGuarantor = null;
		for( ApplySigningUser user : signingContract.getApplySigningUsers() )
		{
			IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( user.getValidatedIdentityId() );

			if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			{
				borrowerIdentityInfo = identityInfo;
				signingBorrower = user;
			}
			else
			{
				guarantorIdentityInfo = identityInfo;
				signingGuarantor = user;
			}

		}

		Map<String, Object> versionMap = getSigningVersionMap( signingContract );

		maps.put( propertyBean.getSigningPdfTemplatePage1(), getSigningMapPage1( signingContract, signingBorrower, guarantorIdentityInfo ) );
		maps.put( propertyBean.getSigningPdfTemplatePage2(), getSigningMapPage2( signingContract ) );
		maps.put( propertyBean.getSigningPdfTemplatePage2_1(), getSigningMapPage2_1( signingContract ) );
		maps.put( propertyBean.getSigningPdfTemplatePage3(),
				  getSigningMapPage3( signingContract, signingBorrower, signingGuarantor, borrowerIdentityInfo, guarantorIdentityInfo ) );
		maps.put( propertyBean.getSigningPdfTemplatePage4(), getSigningMapPage4( signingContract, signingGuarantor, guarantorIdentityInfo ) );
		maps.put( propertyBean.getSigningPdfTemplatePage5(), versionMap );
		maps.put( propertyBean.getSigningPdfTemplatePage6(), versionMap );
		maps.put( propertyBean.getSigningPdfTemplatePage7(), getSigningMapPage7( signingContract ) );
		maps.put( decidePersonalLoanSigningContractPage8Template( signingContract.getLoanPlanCode() ),
				  getSigningMapPage8( signingContract, signingBorrower, signingGuarantor, borrowerIdentityInfo, guarantorIdentityInfo ) );

		if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( signingContract.getLoanPlanCode() ) )
			maps.put( propertyBean.getSigningPdfTemplateDebit_auth_co70647919_c101(),
					  getSigningMapDebit_auth( signingContract, signingBorrower, signingGuarantor, borrowerIdentityInfo, guarantorIdentityInfo ) );

		if( signingContract.getIsNeedAch() != null && signingContract.getIsNeedAch() )
		{
			maps.put( propertyBean.getSigningPdfTemplatePageAch1(), getSigningMapEddaPage( signingContract, signingBorrower, borrowerIdentityInfo ) );
			maps.put( propertyBean.getSigningPdfTemplatePageAch2(), getSigningMapEddaPage( signingContract, signingBorrower, borrowerIdentityInfo ) );
		}

		if( signingContract.getApplySigningRates().size() > 0 )
			try
		{
			maps.put( propertyBean.getSigningPdfTemplatePageRate(), getSigningMapRatePage( signingContract ) );
		}
			catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.INTERNAL.getCode(), ex, null, AP_NAME, "產生對保契約書攤還表失敗" );
			// throw new MyRuntimeException( ApplyErrorEnum.REPEATED_CASE_SEND, new String[]{ "攤還表", contractNo } );
		}

		if( signingContract.getIsAppropiration() != null && signingContract.getIsAppropiration() )
			maps.put( propertyBean.getSigningPdfTemplatePageReceipt(), getSigningMapPageReceipt( signingContract ) );

		return maps;
	}

	private Map<String, Object> getSigningMapDebit_auth( ApplySigningContract signingContract, ApplySigningUser signingBorrower,
														 ApplySigningUser signingGuarantor, IdentityInfoResultBean borrowerIdentityInfo,
														 IdentityInfoResultBean guarantorIdentityInfo )
	{
		Map<String, Object> map = new HashMap<>();

		String companyName = "";
		String creditLoanAmt = "";
		String singingDateRocYear = "";
		String singingDateMonth = "";
		String singingDateDay = "";

		String payeeDateRocYear = "";
		String payeeDateMonth = "";
		String payeeDateDay = "";
		String payeeBankCodeDesc = "";
		String payeeAccountNo = "";
		String payeeAccountName = "";
		String amt_at9 = "";
		String amt_at8 = "";
		String amt_at7 = "";
		String amt_at6 = "";
		String amt_at5 = "";
		String amt_at4 = "";
		String amt_at3 = "";
		String amt_at2 = "";
		String amt_at1 = "";
		String exeBrName = "";
		ApplySigningAppropriation appropriation = signingContract.getApplySigningAppropriation();
		ApplySigningBankAccount bankAccount = appropriation != null ? appropriation.getApplySigningBankAccount() : null;
		// =======================
		if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( signingContract.getLoanPlanCode() ) )
			companyName = "台灣精銳科技股份有限公司";
		else
			companyName = signingContract.getLoanPlanCode();
		// =======================
		creditLoanAmt = CommonStringUtils.addComma( String.valueOf( signingContract.getLoanAmt() * 10000 ) );
		// =======================
		if( StringUtils.isNoneBlank( signingContract.getContractNo() ) )
		{
			String brNo_from_contractNo = StringUtils.substring( signingContract.getContractNo(), 0, 3 );
			CodeBranchBank codeBranchBank = codeBranchBankDAO.getPojoByBankCode( brNo_from_contractNo );
			exeBrName = ( codeBranchBank != null ) ? codeBranchBank.getName() : "";
		}
		// =======================
		if( appropriation != null && appropriation.getAppropriationDate() != null )
		{
			String[] dateInfoArr = CommonDateStringUtils.transDate2StringArray( appropriation.getAppropriationDate() );
			if( dateInfoArr != null )
			{
				payeeDateRocYear = CommonDateStringUtils.convert_to_RocYear( dateInfoArr[ 0 ] );
				payeeDateMonth = dateInfoArr[ 1 ];
				payeeDateDay = dateInfoArr[ 2 ];
			}
		}
		// =======================
		if( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( signingBorrower ) != null )
		{
			String[] dateInfoArr = CommonDateStringUtils
						.transDate2StringArray( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( signingBorrower ) );
			if( dateInfoArr != null )
			{
				singingDateRocYear = CommonDateStringUtils.convert_to_RocYear( dateInfoArr[ 0 ] );
				singingDateMonth = dateInfoArr[ 1 ];
				singingDateDay = dateInfoArr[ 2 ];
			}
		}
		// =======================
		if( signingContract.getPayeeInfoId() != null )
		{
			ApplyPayeeInfo applyPayeeInfo = applyPayeeInfoDAO.read( signingContract.getPayeeInfoId() );
			String payeebankCode = applyPayeeInfo.getBankCode();
			CodeOtherBank codeOtherBank = codeOtherBankDAO.getPojoByOtherBankCode( applyPayeeInfo.getBankCode() );
			payeeBankCodeDesc = payeebankCode + " " + ( codeOtherBank != null ? codeOtherBank.getName() : "" );
			payeeAccountNo = applyPayeeInfo.getAccountNo();
			payeeAccountName = StringUtils.trimToEmpty( applyPayeeInfo.getAccountName() );

			if( applyPayeeInfo.getTotalAmt() != null )
			{
				String[] num_digit_arr = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" };
				// String[] roc_digit_arr = { "零", "壹", "貳", "參", "肆", "伍", "陸", "柒", "捌", "玖" };
				List<String> convert_list = new ArrayList<>();
				String roc_totAmt = String.valueOf( applyPayeeInfo.getTotalAmt() );
				int str_length = roc_totAmt.length();
				for( int i = 0; i < str_length; i++ )
				{
					int currentIdx = str_length - i - 1;
					int single_value = NumberUtils.toInt( StringUtils.substring( roc_totAmt, currentIdx, currentIdx + 1 ) );
					String ch_value = "";
					/*
					 * if( single_value == 0 )
					 * ch_value = "";
					 * else
					 * ch_value = roc_digit_arr[ single_value ];
					 */
					ch_value = num_digit_arr[ single_value ];
					// ======
					convert_list.add( ch_value );
				}
				int padding_cnt = 9 - convert_list.size();
				for( int i = 0; i < padding_cnt; i++ )
					convert_list.add( "" );
				// =========
				amt_at9 = convert_list.get( 8 );
				amt_at8 = convert_list.get( 7 );
				amt_at7 = convert_list.get( 6 );
				amt_at6 = convert_list.get( 5 );
				amt_at5 = convert_list.get( 4 );
				amt_at4 = convert_list.get( 3 );
				amt_at3 = convert_list.get( 2 );
				amt_at2 = convert_list.get( 1 );
				amt_at1 = convert_list.get( 0 );
			}
		}

		map.put( "companyName", companyName );
		map.put( "creditLoanAmt", creditLoanAmt );
		map.put( "singingDateRocYear", singingDateRocYear );
		map.put( "singingDateMonth", singingDateMonth );
		map.put( "singingDateDay", singingDateDay );
		map.put( "contractNo", signingContract.getContractNo() );
		map.put( "bankAcctNo", bankAccount != null ? bankAccount.getBankAccount() : "" );
		map.put( "payeeDateRocYear", payeeDateRocYear );
		map.put( "payeeDateMonth", payeeDateMonth );
		map.put( "payeeDateDay", payeeDateDay );
		map.put( "payeeBankCodeDesc", payeeBankCodeDesc );
		map.put( "payeeAccountNo", payeeAccountNo );
		map.put( "payeeAccountName", payeeAccountName );
		map.put( "amt_at9", amt_at9 );
		map.put( "amt_at8", amt_at8 );
		map.put( "amt_at7", amt_at7 );
		map.put( "amt_at6", amt_at6 );
		map.put( "amt_at5", amt_at5 );
		map.put( "amt_at4", amt_at4 );
		map.put( "amt_at3", amt_at3 );
		map.put( "amt_at2", amt_at2 );
		map.put( "amt_at1", amt_at1 );
		map.put( "exeBrName", exeBrName );
		map.put( "borrowerName", signingBorrower != null ? signingBorrower.getName() : "" );
		map.put( "borrowerIdentityType", codeIdentityTypeDAO.read( borrowerIdentityInfo.getIdentityType() ).getName() );
		map.put( "borrowerSigningDate",
				 CommonDateStringUtils.transDate2String( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( signingBorrower ),
														 CommonTimeUtils.TIME_PATTERN_01 ) );
		map.put( "borrowerIPAddr", borrowerIdentityInfo.getClientAddress() );
		return map;
	}

	private Map<String, Object> getSigningMapEddaPage( ApplySigningContract signingContract, ApplySigningUser signingBorrower,
													   IdentityInfoResultBean borrowerIdentityInfo )
	{
		Map<String, Object> map = new HashMap<>();

		ApplySigningEdda applySigningEDDA = signingEddaDAO.readLastest( signingContract.getSigningContractId() );
		String eDDARocYear = "";
		String eDDADateMonth = "";
		String eDDADateDay = "";
		String[] dateInfoArr = CommonDateStringUtils.transDate2StringArray( applySigningEDDA.getRcAdate() );
		if( dateInfoArr != null )
		{
			eDDARocYear = CommonDateStringUtils.convert_to_RocYear( dateInfoArr[ 0 ] );
			eDDADateMonth = dateInfoArr[ 1 ];
			eDDADateDay = dateInfoArr[ 2 ];
		}

		ApplySigningAppropriation appropriation = signingContract.getApplySigningAppropriation();
		// ApplySigningEdda applySigningEDDA =signingEddaDAO.readLastest(signingContract.getSigningContractId());
		ApplySigningBankAccount bankAccount = appropriation != null ? appropriation.getApplySigningBankAccount() : null;

		map.put( "year", eDDARocYear );
		map.put( "month", eDDADateMonth );
		map.put( "date", eDDADateDay );

		map.put( "mobileNumber", signingBorrower.getMobileNumber() );
		map.put( "email", signingBorrower.getEmail() );
		map.put( "contractNoFlag", "checked" );
		map.put( "cntrNo", StringUtils.trimToEmpty( signingContract.getContractNo() ).split( "-" )[ 0 ] );

		map.put( "contractNo", StringUtils.trimToEmpty( signingContract.getContractNo() ) );
		map.put( "checkDate", CommonDateStringUtils.transDate2String( appropriation.getContractCheckDate() ) );
		map.put( "borrowerEddaChecked", "checked" );
		map.put( "addEddaFlag", "checked" );

		map.put( "borrowerName", signingBorrower != null ? signingBorrower.getName() : "" );
		map.put( "borrowerId", borrowerIdentityInfo.getIdNo() );
		map.put( "borrowerIpAddress", borrowerIdentityInfo.getClientAddress() );
		map.put( "borrowerIdentityType", codeIdentityTypeDAO.read( borrowerIdentityInfo.getIdentityType() ).getName() );
		map.put( "borrowerSigningDate",
				 CommonDateStringUtils.transDate2String( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( signingBorrower ),
														 CommonTimeUtils.TIME_PATTERN_01 ) );

		setSigningBankAccountMap( map, bankAccount );

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		return map;
	}

	private Map<String, Object> getSigningMapPage1( ApplySigningContract signingContract, ApplySigningUser signingBorrower,
													IdentityInfoResultBean guarantorIdentityInfo )
	{
		if( signingBorrower == null )
			return null;

		ApplySigningAppropriation appropriation = signingContract.getApplySigningAppropriation();

		if( appropriation == null )
			return null;

		Map<String, Object> map = new HashMap<>();

		setGuarantorChecked( map, guarantorIdentityInfo );

		Integer loanPeriodYear = signingContract.getLoanPeriod() / 12;
		Integer loanPeriodMonth = signingContract.getLoanPeriod() - ( loanPeriodYear * 12 );

		map.put( "borrowerName", signingBorrower.getName() );
		map.put( "contractNo", signingContract.getContractNo() );
		map.put( "checkDate", CommonDateStringUtils.transDate2String( appropriation.getContractCheckDate() ) );
		map.put( "loanAmt", signingContract.getLoanAmt() );
		map.put( "appropriationDate", CommonDateStringUtils.transDate2String( appropriation.getAppropriationDate() ) );
		map.put( "appropriationEndDate", CommonDateStringUtils
					.transDate2String( getAppropriationEndDate( signingContract.getLoanPeriod(), appropriation.getAppropriationDate() ) ) );
		map.put( "loanPeriod", signingContract.getLoanPeriod() );
		map.put( "loanPurpose", getLoanPurpose( signingContract.getLoanPurpose() ) );

		map.put( "preliminaryFee", signingContract.getPreliminaryFee() );
		map.put( "creditCheckFee", signingContract.getCreditCheckFee() );

		String advancedRedemptionChecked = "";
		String limitedRedemptionChecked = "";
		if( signingContract.getShowOption() == 1 )
			advancedRedemptionChecked = CHECKED_CONSTANT;
		else if( signingContract.getShowOption() == 2 )
			limitedRedemptionChecked = CHECKED_CONSTANT;

		map.put( "advancedRedemptionChecked", advancedRedemptionChecked );
		map.put( "limitedRedemptionChecked", limitedRedemptionChecked );

		map.put( "advancedTitle", signingContract.getAdvancedRedemptionTitle() );
		map.put( "advancedAPR", signingContract.getAdvancedApr() );
		map.put( "advancedDesc", signingContract.getAdvancedRedemptionDesc() );
		map.put( "advancedRateDesc", signingContract.getAdvancedRateDesc() );

		map.put( "limitedTitle", signingContract.getLimitedRedemptionTitle() );
		map.put( "limitedAPR", signingContract.getLimitedApr() );
		map.put( "limitedDesc", signingContract.getLimitedRedemptionDesc() );
		map.put( "limitedRateDesc", signingContract.getLimitedRateDesc() );

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		// J-111-0227 eDDA新增相關參數
		map.put( "loanPeriodYear", loanPeriodYear );
		map.put( "loanPeriodMonth", loanPeriodMonth );
		map.put( "baseRate", signingContract.getBaseRate() );
		setSigningBankAccountMap( map, appropriation.getApplySigningBankAccount() );

		return map;
	}

	private Map<String, Object> getSigningMapPage2( ApplySigningContract signingContract )
	{
		ApplySigningAppropriation appropriation = signingContract.getApplySigningAppropriation();

		if( appropriation == null )
			return null;

		Map<String, Object> map = new HashMap<>();

		String limitedRedemptionChecked = "";
		if( signingContract.getShowOption() == 2 )
			limitedRedemptionChecked = CHECKED_CONSTANT;

		map.put( "limitedRedemptionChecked", limitedRedemptionChecked );

		map.put( "limitedTitle", signingContract.getLimitedRedemptionTitle() );
		map.put( "limitedAPR", signingContract.getLimitedApr() );
		map.put( "limitedDesc", signingContract.getLimitedRedemptionDesc() );
		map.put( "limitedRateDesc", signingContract.getLimitedRateDesc() );

		map.put( "baseRate", signingContract.getBaseRate() );

		map.put( "repayment", appropriation.getRepaymentDay() );
		map.put( "firstPaymentDate", CommonDateStringUtils.transDate2String( appropriation.getFirstPaymentDate() ) );

		if( signingContract.getIsNeedAch() == null )
			setSigningBankAccountMap( map, appropriation.getApplySigningBankAccount() );
		else
			map.put( "bankAcctNo",
					 "<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>" );

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		return map;
	}

	private Map<String, Object> getSigningMapPage2_1( ApplySigningContract signingContract )
	{
		Map<String, Object> map = new HashMap<>();
		DecimalFormat dollarFormat = new DecimalFormat( "#,###" );

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		if( ( signingContract.getIsAppropiration() != null && signingContract.getIsRepayment() != null )
			&& ( signingContract.getIsAppropiration() && signingContract.getIsRepayment() ) )
		{
			List<ApplySigningAppropriationPayment> applySigningAppropriationPayments = applySigningAppropriationPaymentDAO
						.getPojoByContractId( signingContract.getSigningContractId(), false );

			for( int i = 0; i < REPAYMENT_ROW_COUNT; i++ )
				if( i < applySigningAppropriationPayments.size() )
				{
					map.put( "bankName" + i, applySigningAppropriationPayments.get( i ).getBankName() );
					map.put( "type" + i, applySigningAppropriationPayments.get( i ).getRepaymentProductType() );
					map.put( "product" + i, applySigningAppropriationPayments.get( i ).getRepaymentProduct() );
					map.put( "bankAccount" + i, applySigningAppropriationPayments.get( i ).getBankAcctNo() );
					map.put( "amount" + i, dollarFormat.format( applySigningAppropriationPayments.get( i ).getRepaymentAmt() ) );
					map.put( "accountName" + i, applySigningAppropriationPayments.get( i ).getAccountName() );
				}
				else
				{
					map.put( "bankName" + i, BLANK_TEXT );
					map.put( "type" + i, BLANK_TEXT );
					map.put( "product" + i, BLANK_TEXT );
					map.put( "bankAccount" + i, BLANK_TEXT );
					map.put( "amount" + i, BLANK_TEXT );
					map.put( "accountName" + i, BLANK_TEXT );
				}
		}
		else
			for( int i = 0; i < REPAYMENT_ROW_COUNT; i++ )
			{
				map.put( "bankName" + i, BLANK_TEXT );
				map.put( "type" + i, BLANK_TEXT );
				map.put( "product" + i, BLANK_TEXT );
				map.put( "bankAccount" + i, BLANK_TEXT );
				map.put( "amount" + i, BLANK_TEXT );
				map.put( "accountName" + i, BLANK_TEXT );
			}

		return map;
	}

	private Map<String, Object> getSigningMapPage3( ApplySigningContract signingContract, ApplySigningUser signingBorrower,
													ApplySigningUser signingGuarantor, IdentityInfoResultBean borrowerIdentityInfo,
													IdentityInfoResultBean guarantorIdentityInfo )
	{
		if( signingBorrower == null )
			return null;

		Map<String, Object> map = new HashMap<>();

		map.put( "borrowerChecked", BooleanUtils.toString( signingBorrower.getAgreeCrossSelling(), CHECKED_CONSTANT, "", "" ) );
		map.put( "borrowerUnchecked", BooleanUtils.toString( signingBorrower.getAgreeCrossSelling(), "", CHECKED_CONSTANT, "" ) );

		Boolean gurantorAgreeCrossSelling = null;
		if( signingGuarantor != null )
			gurantorAgreeCrossSelling = signingGuarantor.getAgreeCrossSelling();

		map.put( "guaranteeChecked", BooleanUtils.toString( gurantorAgreeCrossSelling, CHECKED_CONSTANT, "", "" ) );
		map.put( "guaranteeUnchecked", BooleanUtils.toString( gurantorAgreeCrossSelling, "", CHECKED_CONSTANT, "" ) );

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		String PersonIdentityType = "";
		String relatedPersonIdentityType = "";
		if( signingBorrower.getAgreeCrossSelling() != null && signingBorrower.getAgreeCrossSelling() )
			PersonIdentityType = ":" + codeIdentityTypeDAO.read( borrowerIdentityInfo.getIdentityType() ).getName();

		if( signingGuarantor != null && guarantorIdentityInfo != null )
			if( signingBorrower.getAgreeCrossSelling() != null && signingBorrower.getAgreeCrossSelling() )
				relatedPersonIdentityType = ":" + codeIdentityTypeDAO.read( guarantorIdentityInfo.getIdentityType() ).getName();
		map.put( "borrowerIdentityType", PersonIdentityType );
		map.put( "guaranteeIdentityType", relatedPersonIdentityType );
		return map;
	}

	private Map<String, Object> getSigningMapPage4( ApplySigningContract signingContract, ApplySigningUser signingGuarantor,
													IdentityInfoResultBean guarantorIdentityInfo )
	{
		Map<String, Object> map = new HashMap<>();

		setSigningRateAdjustmentMap( map, signingContract.getApplySigningAppropriation() );

		String generalPlanChecked = "";
		String jointPlanChecked = "";
		if( guarantorIdentityInfo != null && UserTypeEnum.GUARANTOR.getContext().equals( guarantorIdentityInfo.getUserType() ) )
			generalPlanChecked = CHECKED_CONSTANT;
		else if( guarantorIdentityInfo != null )
			jointPlanChecked = CHECKED_CONSTANT;

		map.put( "generalPlanChecked", generalPlanChecked );
		map.put( "generalPlanInfo", getPlanInfo( signingContract.getGeneralGuaranteePlan(), signingContract.getGeneralGuaranteePlanInfo() ) );
		map.put( "jointPlanChecked", jointPlanChecked );
		map.put( "jointPlanInfo", getPlanInfo( signingContract.getJointGuaranteePlan(), signingContract.getJointGuaranteePlanInfo() ) );

		String guaranteeAmt = signingContract.getGuaranteeAmt() == null ? BLANK_TEXT
																		: signingContract.getGuaranteeAmt().setScale( 0, BigDecimal.ROUND_DOWN )
																					.toString();
		map.put( "guaranteeAmt", guaranteeAmt );

		String relatedPersonName = "";
		String relatedPersonIdentityType = "";
		String relatedPersonSigningDate = "";
		if( signingGuarantor != null && guarantorIdentityInfo != null )
		{
			relatedPersonName = signingGuarantor.getName();
			relatedPersonIdentityType = codeIdentityTypeDAO.read( guarantorIdentityInfo.getIdentityType() ).getName();
			relatedPersonSigningDate = CommonDateStringUtils
						.transDate2String( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( signingGuarantor ) );
		}

		map.put( "guaranteeName", relatedPersonName );
		map.put( "guaranteeIdentityType", relatedPersonIdentityType );
		map.put( "guaranteeSigningDate", relatedPersonSigningDate );

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		return map;

	}

	private Map<String, Object> getSigningMapPage7( ApplySigningContract signingContract )
	{
		Map<String, Object> map = new HashMap<>();

		map.put( "courtName", signingContract.getCourtName() );
		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		return map;
	}

	private Map<String, Object> getSigningMapPage8( ApplySigningContract signingContract, ApplySigningUser signingBorrower,
													ApplySigningUser signingGuarantor, IdentityInfoResultBean borrowerIdentityInfo,
													IdentityInfoResultBean guarantorIdentityInfo )
	{

		if( signingBorrower == null || borrowerIdentityInfo == null )
			return null;

		Map<String, Object> map = new HashMap<>();

		map.put( "borrowerId", borrowerIdentityInfo.getIdNo() );
		map.put( "borrowerIpAddress", borrowerIdentityInfo.getClientAddress() );
		map.put( "borrowerName", signingBorrower.getName() );
		map.put( "borrowerIdentityType", codeIdentityTypeDAO.read( borrowerIdentityInfo.getIdentityType() ).getName() );
		map.put( "borrowerSigningDate",
				 CommonDateStringUtils.transDate2String( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( signingBorrower ),
														 CommonTimeUtils.TIME_PATTERN_01 ) );
		String relatedGuaranteeId = "";
		String relatedGuaranteeIpAddress = "";
		String relatedPersonName = "";
		String relatedPersonIdentityType = "";
		String relatedPersonSigningDate = "";
		if( signingGuarantor != null && guarantorIdentityInfo != null )
		{

			relatedGuaranteeId = guarantorIdentityInfo.getIdNo();
			relatedGuaranteeIpAddress = guarantorIdentityInfo.getClientAddress();
			relatedPersonName = signingGuarantor.getName();
			relatedPersonIdentityType = codeIdentityTypeDAO.read( guarantorIdentityInfo.getIdentityType() ).getName();
			relatedPersonSigningDate = CommonDateStringUtils
						.transDate2String( ApplyLoanUtils.get_singingTimeOrSingingData_from_applySigningUser( signingGuarantor ),
										   CommonTimeUtils.TIME_PATTERN_01 );
		}

		map.put( "guaranteeId", relatedGuaranteeId );
		map.put( "guaranteeIpAddress", relatedGuaranteeIpAddress );
		map.put( "guaranteeName", relatedPersonName );
		map.put( "guaranteeIdentityType", relatedPersonIdentityType );
		map.put( "guaranteeSigningDate", relatedPersonSigningDate );

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		String staffRuleStyle = "none";
		if( signingContract.getStaffRule() != null && signingContract.getStaffRule() )
			staffRuleStyle = "display";
		map.put( "staffRuleStyle", staffRuleStyle );

		// J-112-0206 配合團貸案之個別商議條款調整為可由分行於簽報書自行選擇(非團貸案件一律不顯示個別商議條款)，
		// 精銳科技也比照辦理，故Page8不須特殊處理，這邊就算有新增精銳科技專屬的key-value也不會有影響
		if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( signingContract.getLoanPlanCode() ) )
			map.put( ApplyLoanUtils.get_signing_contract_term_7_4th_co70647919_C101_key(),
					 ApplyLoanUtils.get_signing_contract_term_7_4th_co70647919_C101_value() );
		// ================
		return map;
	}

	private Map<String, Object> getSigningMapPageReceipt( ApplySigningContract signingContract )
	{
		Map<String, Object> map = new HashMap<>();

		LocalDate currentDate = LocalDate.now();
		DecimalFormat dollarFormat = new DecimalFormat( "#,###" );

		map.put( "handlingFee", dollarFormat.format( signingContract.getPreliminaryFee() ) );
		map.put( "enquiryFee", dollarFormat.format( signingContract.getCreditCheckFee() ) );
		map.put( "sum", dollarFormat.format( signingContract.getPreliminaryFee() + signingContract.getCreditCheckFee() ) );
		map.put( "year", currentDate.getYear() );
		map.put( "month", currentDate.getMonthValue() );
		map.put( "date", currentDate.getDayOfMonth() );

		return map;
	}

	private Map<String, Object> getSigningMapRatePage( ApplySigningContract signingContract ) throws Exception
	{
		Map<String, Object> map = new HashMap<>();
		List<Double> multipleRate = new ArrayList<>();
		List<Integer> multipleRate_TimeStart = new ArrayList<>();
		List<Integer> multipleRate_TimeEnd = new ArrayList<>();
		List<Integer> costList = new ArrayList<>();
		String rowRate = "";
		Integer row = 1;

		List<ApplySigningRate> applySigningRates = signingRateDAO.getContractRates( signingContract.getSigningContractId() );

		for( ApplySigningRate rate : applySigningRates )
		{
			multipleRate.add( rate.getRate() );
			multipleRate_TimeStart.add( rate.getRateBgn() );
			multipleRate_TimeEnd.add( rate.getRateEnd() );
		}

		ApplySigningAppropriation appropriation = signingContract.getApplySigningAppropriation();
		Integer loan = signingContract.getLoanAmt() * 10000;
		Integer bufferMonth = 0; // 信貸目前沒有寬限期
		Integer cal_Type = 1; // 信貸目前只有本息平均攤還，之後有調整這邊要改
		Integer limitMonth = signingContract.getLoanPeriod();

		AmortiseTableParamBean amortiseTableParamBean = calculateRate( loan, limitMonth, bufferMonth, cal_Type, multipleRate, multipleRate_TimeStart,
																	   multipleRate_TimeEnd, costList, appropriation );
		for( int i = 0; i < amortiseTableParamBean.getPeriod().size(); i++ )
		{
			String Period = amortiseTableParamBean.getPeriod().get( i );
			String LoanAmt = String.format( "%,.0f", amortiseTableParamBean.getLoanAmt().get( i ) );
			String CompoundAmt = String.format( "%,.0f", amortiseTableParamBean.getCompoundAmt().get( i ) );
			String PrincipalAmt = String.format( "%,.0f", amortiseTableParamBean.getPrincipalAmt().get( i ) );
			String InterestAmt = String.format( "%,.0f", amortiseTableParamBean.getInterestAmt().get( i ) );
			String nowLoanAmt = String.format( "%,.0f", amortiseTableParamBean.getLoanAmt().get( i + 1 ) );

			map.put( "Period" + row, Period );
			map.put( "LoanAmt" + row, LoanAmt );
			map.put( "CompoundAmt" + row, CompoundAmt );
			map.put( "PrincipalAmt" + row, PrincipalAmt );
			map.put( "InterestAmt" + row, InterestAmt );
			map.put( "nowLoanAmt" + row, nowLoanAmt );

			row++;
		}

		return map;
	}

	private Map<String, Object> getSigningVersionMap( ApplySigningContract signingContract )
	{
		Map<String, Object> map = new HashMap<>();

		map.put( CONTRACT_VERSION_CONSTANT, signingContract.getContractVersion() );

		return map;
	}

	private double getToEven( double toBeTruncated, int moneyPointNumberCount )
	{
		Double truncatedDouble = BigDecimal.valueOf( toBeTruncated ).setScale( moneyPointNumberCount, RoundingMode.HALF_EVEN ).doubleValue();
		return truncatedDouble;
	}

	private String getWatermarkText( ApplySigningContract signingContract )
	{
		List<Date> signingDates = signingContract.getApplySigningUsers().stream().map( ApplySigningUser::getSingingDate ).filter( Objects::nonNull )
					.sorted( Comparator.comparing( Date::getTime ).reversed() ).collect( Collectors.toList() );

		int user_cnt = 0;
		int user_singing_time_cnt = 0;
		for( ApplySigningUser applySigningUser : signingContract.getApplySigningUsers() )
		{
			++user_cnt;
			if( applySigningUser.getSingingTime() != null )
				++user_singing_time_cnt;
		}

		boolean has_singing_time = user_cnt > 0 && user_cnt == user_singing_time_cnt;
		if( has_singing_time )
			signingDates = signingContract.getApplySigningUsers().stream().map( ApplySigningUser::getSingingTime ).filter( Objects::nonNull )
						.sorted( Comparator.comparing( Date::getTime ).reversed() ).collect( Collectors.toList() );

		return "契約編號:" + signingContract.getContractNo() + " 簽約時間:"
			+ CommonDateStringUtils.transDate2String( signingDates.get( 0 ), "yyyy年MM月dd日HH時mm分ss秒" );
	}

	private Double IRRtry( Double[] addArray, Double[] cutArray, double ir1, double ir2 )
	{
		double ir0 = 0;
		double ir3 = 0;
		int degree_limited = 30;   // 精確度(10~30)
		double aa = 0;
		double bb = 0;
		double curvetype = 0;

		for( int t = 1; t <= degree_limited; t++ )
		{
			// 前一個狀況
			if( aa > bb )
				curvetype = 1;
			if( aa < bb )
				curvetype = 2;

			// 本次結果
			aa = SumArray( addArray, ir1 ) - SumArray( cutArray, ir1 );
			bb = SumArray( addArray, ir2 ) - SumArray( cutArray, ir2 );

			if( aa > 0 && bb > 0 )  // 同一邊,表示除2時跳過正確的值了
			{
				if( curvetype == 1 ) // 向右遞減(表示ir2太靠左)
				{
					ir2 = ir2 - ( ir1 - ir2 );       // 先恢復ir2
					ir1 = ir1 - ( ir1 - ir2 ) / 2;  // 改ir1靠過去
					ir0 = ir1;
				}
				else if( curvetype == 2 )
				{
					ir1 = ir1 + ( ir1 - ir2 );       // 先恢復ir1
					ir2 = ir2 + ( ir1 - ir2 ) / 2;  // 改ir2靠過去
					ir0 = ir2;
				}
			}
			else if( aa < 0 && bb < 0 )
			{
				if( curvetype == 1 )   // 向右遞減(表示ir1太靠右)
				{
					ir1 = ir1 + ( ir1 - ir2 );       // 先恢復ir1
					ir2 = ir2 + ( ir1 - ir2 ) / 2;  // 改ir2靠過去
					ir0 = ir2;
				}
				else if( curvetype == 2 )
				{
					ir2 = ir2 - ( ir1 - ir2 );       // 先恢復ir2
					ir1 = ir1 - ( ir1 - ir2 ) / 2;  // 改ir1靠過去
					ir0 = ir1;
				}
			}
			else if( ir1 < ir2 )  // 假如利率兩邊大小有問題
			{
				ir3 = ir1;
				ir1 = ir2;
				ir2 = ir3;
			}

			// 2分逼近法
			double aa_2 = getAwayFromZero( aa, 2 );
			double bb_2 = getAwayFromZero( bb, 2 );
			if( aa_2 == bb_2 || aa == 0 || bb == 0 )
				break;
			else if( Math.abs( aa ) < Math.abs( bb ) )
			{
				ir0 = ir1;
				// ir1 = ir1;
				ir2 = ir2 + ( ir1 - ir2 ) / 2;
			}
			else
			{
				ir0 = ir2;
				ir1 = ir1 - ( ir1 - ir2 ) / 2;
				// ir2 = ir2;
			}
		}

		if( ir0 >= 0 )
			return ir0;
		else
			return null;
	}

	private Map<String, Map<String, Object>> processPersonalLoanBorrowerPdfForChinaSteelCompany( ApplyLoan applyLoan, String loanPlanCode,
																								 Date passed_applyTs )
	{

		Map<String, Map<String, Object>> maps = new LinkedHashMap<>();

		// parent
		if( LoanPlanEnum.C001.getContext().equals( loanPlanCode ) )
			if( applyLoan.getRefBorrowerApplyLoan() == null )
			maps.put( propertyBean.getPersonalLoanChinaSteelParentBorrowerPdfTemplate(),
					  getLoanApplyMapForChinaSteelCompany( applyLoan, loanPlanCode, passed_applyTs ) );
			else
		{
			maps.put( propertyBean.getPersonalLoanChinaSteelParentBorrowerPdfTemplate(),
					  getLoanApplyMapForChinaSteelCompany( applyLoan.getRefBorrowerApplyLoan(), loanPlanCode, passed_applyTs ) );
			maps.put( propertyBean.getPersonalLoanGuarantorPdfTemplate(), getLoanApplyMap( applyLoan, passed_applyTs ) );
		}

		// Subsidiary
		if( !LoanPlanEnum.C001.getContext().equals( loanPlanCode ) )
			if( applyLoan.getRefBorrowerApplyLoan() == null )
			maps.put( propertyBean.getPersonalLoanChinaSteelSubsidiaryBorrowerPdfTemplate(),
					  getLoanApplyMapForChinaSteelCompany( applyLoan, loanPlanCode, passed_applyTs ) );
			else
		{
			maps.put( propertyBean.getPersonalLoanChinaSteelSubsidiaryBorrowerPdfTemplate(),
					  getLoanApplyMapForChinaSteelCompany( applyLoan.getRefBorrowerApplyLoan(), loanPlanCode, passed_applyTs ) );
			maps.put( propertyBean.getPersonalLoanGuarantorPdfTemplate(), getLoanApplyMap( applyLoan, passed_applyTs ) );
		}

		return maps;
	}

	private void procRelationPdfTemplate( Map<String, Map<String, Object>> maps, String pdfTemplate, ApplyLoan applyLoan )
	{
		// 借款人本人( applyLoan.getApplyLoanContent() != null ) 要印出同一關係人
		// 從債務人進件時( applyLoan.getApplyLoanContent() 為 null)，此時要印出主借人的同一關係人

		ApplyLoan mainBorrowser = ( applyLoan.getRefBorrowerApplyLoan() != null ) ? applyLoan.getRefBorrowerApplyLoan() : applyLoan;
		maps.put( pdfTemplate, getLoanRelationMap( mainBorrowser.getApplyLoanBasic(), mainBorrowser.getApplyLoanContent(),
												   mainBorrowser.getCodeLoanType().getLoanType() ) );
	}

	private void setApplyContentMap( Map<String, Object> map, String loanPlanCode, ApplyLoanContent content )
	{
		if( content == null )
			return;

		// 在此填房屋申貸資訊
		map.put( "loanRequestAmt", content.getLoanRequestAmt() );
		map.put( "loanPeriod", content.getCodeLoanPeriod().getName() + "年" );
		map.put( "loanPurpose", content.getCodeLoanPurpose().getName() );
		map.put( "otherPurpose", content.getOtherPurpose() );
		map.put( "notification", content.getCodeNotification().getName() );
		map.put( "gracePeriod", content.getCodeGracePeriod() == null ? null : content.getCodeGracePeriod().getName() + "年" );
		map.put( "mortgageTypeName", content.getCodeMortgageType() == null ? null : content.getCodeMortgageType().getName() );
		map.put( "nonPrivateUsageTypeName", content.getCodeNonPrivateUsageType() == null ? null : content.getCodeNonPrivateUsageType().getName() );
		map.put( "privateUsageTypeName", content.getCodePrivateUsageType() == null ? null : content.getCodePrivateUsageType().getName() );
		map.put( "collateralAddress", content.getApplyAddress() == null ? null : getAddress( content.getApplyAddress() ) );
		map.put( "isIncreasingLoan", BooleanUtils.toString( content.getIncreasingLoan(), "是", "否", null ) );
		map.put( "appnBankCode", content.getAppnBankCode() );
		map.put( "appnDpAcct", content.getAppnDpAcct() );
		map.put( "restrictContr", BooleanUtils.toString( content.getRestrictContr(), "是", "否", null ) );

		map.putAll( createApplyDescAndDeclarationMap( content ) );

	}

	private void setApplyGuaranteeInfoMap( Map<String, Object> map, ApplyLoanGuaranteeInfo guaranteeInfo )
	{
		if( guaranteeInfo == null )
			return;

		map.put( "relationBorrowerTypeName", guaranteeInfo.getCodeRelationBorrowerType().getName() );
		map.put( "isCohabiting", BooleanUtils.toString( guaranteeInfo.isCohabitationFlag(), "是", "否" ) );

		String guarantyReasonName = null;
		String otherGurantyReason = null;
		if( guaranteeInfo.getCodeGuarantyReason() != null )
		{
			guarantyReasonName = guaranteeInfo.getCodeGuarantyReason().getName();
			otherGurantyReason = guaranteeInfo.getOtherGuarantyReason();
		}

		map.put( "guarantyReasonName", guarantyReasonName );
		map.put( "otherGurantyReason", otherGurantyReason );

	}

	private void setApplyIdentityInfoMap( Map<String, Object> map, Long validatedIdentityId )
	{
		IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( validatedIdentityId );

		String skipMention = null;
		String signature = null;
		if( IdentityTypeEnum.SKIP.getContext().equals( identityInfo.getIdentityType() ) )
		{
			skipMention = "(如認證方式為略過認證，本手機將作為申請書調閱約定手機使用)";
			signature = "申請人親簽";
		}

		map.put( "skipMention", skipMention );
		map.put( "signature", signature );

		map.put( "identityType", codeIdentityTypeDAO.read( identityInfo.getIdentityType() ).getName() );
		map.put( "userSubTypeName", codeUserSubTypeDAO.read( identityInfo.getUserSubType() ).getName() );
	}

	private void setApplyRelationMap( Map<String, Object> map, Set<ApplyLoanRelation> applyLoanRelations, int size )
	{
		List<ApplyLoanRelation> relations = applyLoanRelations.stream().collect( Collectors.toList() );

		for( int index = 0; index < size; index++ )
		{
			int groupNo = index + 1;

			if( relations.size() > index )
			{
				map.put( "relationName" + groupNo, relations.get( index ).getRelationName() );
				map.put( "relationIdNo" + groupNo, relations.get( index ).getRelationIdNo() );
				map.put( "relationType" + groupNo, relations.get( index ).getCodeRelationType().getName() );
			}
			else
			{
				map.put( "relationName" + groupNo, "-" );
				map.put( "relationIdNo" + groupNo, "-" );
				map.put( "relationType" + groupNo, "-" );
			}
		}
	}

	private void setApplyServedMap( Map<String, Object> map, Set<ApplyLoanServed> applyLoanServeds, int size )
	{
		List<ApplyLoanServed> serveds = applyLoanServeds.stream().collect( Collectors.toList() );
		for( int index = 0; index < size; index++ )
		{
			int groupNo = index + 1;

			if( serveds.size() > index )
			{
				map.put( "representativeType" + groupNo, serveds.get( index ).getCodeRepresentativeType().getName() );
				map.put( "servedTitle" + groupNo, serveds.get( index ).getServedTitle() );
				map.put( "taxNo" + groupNo, serveds.get( index ).getTaxNo() );
				map.put( "servedCompanyName" + groupNo, serveds.get( index ).getCompanyName() );
				map.put( "comment" + groupNo, serveds.get( index ).getComment() );
			}
			else
			{
				map.put( "representativeType" + groupNo, "-" );
				map.put( "servedTitle" + groupNo, "-" );
				map.put( "taxNo" + groupNo, "-" );
				map.put( "servedCompanyName" + groupNo, "-" );
				map.put( "comment" + groupNo, "-" );
			}
		}

	}

	private void setGuarantorChecked( Map<String, Object> map, IdentityInfoResultBean guarantorIdentityInfo )
	{
		String generalChecked = "";
		String jointChecked = "";
		if( guarantorIdentityInfo != null )
		{
			String userSubType = guarantorIdentityInfo.getUserSubType();

			if( UserSubTypeEnum.N_GUARANTOR.getContext().equals( userSubType ) )
				generalChecked = CHECKED_CONSTANT;
			else if( UserSubTypeEnum.G_GUARANTOR.getContext().equals( userSubType ) )
				jointChecked = CHECKED_CONSTANT;
		}

		map.put( "jointChecked", jointChecked );
		map.put( "generalChecked", generalChecked );
	}

	private void setSigningBankAccountMap( Map<String, Object> map, ApplySigningBankAccount bankAccount )
	{
		if( bankAccount == null )
			return;
		String bankName = getEddaBankName( StringUtils.trimToEmpty( bankAccount.getBankCode() ) );
		String branchName = getEddaBranchName( bankAccount.getBankCode(), bankAccount.getBankAccount(), bankAccount.getBankBranchCode() );
		String branchCode = bankAccount.getBankBranchCode();

		map.put( "bankName", bankName );
		map.put( "bankAcctNo", bankAccount.getBankAccount() );
		map.put( "bankCode", StringUtils.trimToEmpty( bankAccount.getBankCode() ) );

		map.put( "branchName", branchName );
		map.put( "branchCode", branchCode );
	}

	private void setSigningRateAdjustmentMap( Map<String, Object> map, ApplySigningAppropriation appropriation )
	{
		if( appropriation == null )
			return;

		String rateAdjustmentMailChecked = "";
		String rateAdjustmentSMSChecked = "";
		String rateAdjustmentPaperChecked = "";
		if( appropriation.getCodeRateAdjustmentNotification() != null )
		{
			String notificationCode = appropriation.getCodeRateAdjustmentNotification().getRateAdjustmentNotificaitonCode();

			if( "1".equals( notificationCode ) )
				rateAdjustmentSMSChecked = CHECKED_CONSTANT;
			else if( "2".equals( notificationCode ) )
				rateAdjustmentPaperChecked = CHECKED_CONSTANT;
			else if( "3".equals( notificationCode ) )
				rateAdjustmentMailChecked = CHECKED_CONSTANT;
		}
		map.put( "rateAdjustmentMailChecked", rateAdjustmentMailChecked );
		map.put( "rateAdjustmentSMSChecked", rateAdjustmentSMSChecked );
		map.put( "rateAdjustmentPaperChecked", rateAdjustmentPaperChecked );

	}

	private double SumArray( Double[] inputArray, double nr )
	{
		double tmpSum = 0;
		double nr_pow = 1;
		for( int i = 0; i < inputArray.length; i++ )
		{
			nr_pow = 1;
			for( int j = 0; j < inputArray.length - i; j++ )
				nr_pow *= ( 1 + nr );
			tmpSum += inputArray[ i ] * nr_pow;
		}
		return tmpSum;
	}
}
