package com.megabank.olp.apply.controller.loan;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.apply.controller.loan.bean.survey.ContactInfoSubmittedArgBean;
import com.megabank.olp.apply.controller.loan.bean.survey.SurveySubmittedArgBean;
import com.megabank.olp.apply.service.loan.SurveyService;
import com.megabank.olp.apply.service.loan.bean.survey.ContactInfoSubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.survey.SurveySubmittedParamBean;
import com.megabank.olp.base.layer.BaseController;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "loan/survey" )
public class SurveyController extends BaseController
{
	@Autowired
	private SurveyService surveyService;

	/**
	 * 取得 聯絡資訊
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getContactInfo" )
	public Map<String, Object> getContactInfo()
	{
		return getResponseMap( surveyService.getContactInfo() );
	}

	/**
	 * 取得試算結果
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getSurveyResult" )
	public Map<String, Object> getSurveyResult()
	{
		return getResponseMap( surveyService.getSurveyResult() );
	}

	/**
	 * 取得感謝頁內容
	 *
	 * @return
	 */
	@PostMapping( "getThankyouMessage" )
	public Map<String, Object> getThankyouMessage()
	{

		return getResponseMap( surveyService.getThankyouMessage() );
	}

	/**
	 * 送出聯絡資訊
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "submitContactInfo" )
	public Map<String, Object> submitContactInfo( @RequestBody @Validated ContactInfoSubmittedArgBean argBean )
	{
		ContactInfoSubmittedParamBean paramBean = getContactInfoSubmittedParamBean( argBean );

		Long applySurveyId = surveyService.submitContactInfo( paramBean );

		return getResponseMap( applySurveyId );
	}

	/**
	 * 送出試算問卷內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "submitSurvey" )
	public Map<String, Object> submitSurvey( @RequestBody @Validated SurveySubmittedArgBean argBean )
	{
		SurveySubmittedParamBean paramBean = getSurveySubmittedParamBean( argBean );

		Long applySurveyId = surveyService.submitSurvey( paramBean );

		return getResponseMap( applySurveyId );

	}

	private ContactInfoSubmittedParamBean getContactInfoSubmittedParamBean( ContactInfoSubmittedArgBean argBean )
	{
		ContactInfoSubmittedParamBean paramBean = new ContactInfoSubmittedParamBean();
		paramBean.setName( argBean.getName() );
		paramBean.setBranchBankCode( argBean.getBranchBankCode() );
		paramBean.setPhoneCode( argBean.getPhoneCode() );
		paramBean.setPhoneNumber( argBean.getPhoneNumber() );
		paramBean.setPhoneExt( argBean.getPhoneExt() );
		paramBean.setMobileNumber( argBean.getMobileNumber() );
		paramBean.setEmail( argBean.getEmail() );
		paramBean.setContactTimeCode( argBean.getContactTimeCode() );
		paramBean.setOtherMsg( argBean.getOtherMsg() );
		paramBean.setSexCode( argBean.getSexCode() );

		return paramBean;
	}

	private SurveySubmittedParamBean getSurveySubmittedParamBean( SurveySubmittedArgBean argBean )
	{
		SurveySubmittedParamBean paramBean = new SurveySubmittedParamBean();
		paramBean.setAnnualIncome( argBean.getSurveyJobBean().getAnnualIncome() );
		paramBean.setJobSubType( argBean.getSurveyJobBean().getJobSubType() );
		paramBean.setCashAdvance( argBean.getSurveyCreditBean().getCashAdvance() );
		paramBean.setCreditCardTotalAmt( argBean.getSurveyCreditBean().getCreditCardTotalAmt() );
		paramBean.setDebitCardTotalAmt( argBean.getSurveyCreditBean().getDebitCardTotalAmt() );
		paramBean.setHoldingCr3ditCard( argBean.getSurveyCreditBean().getHoldingCreditCard() );
		paramBean.setHoldingDebitCard( argBean.getSurveyCreditBean().getHoldingDebitCard() );
		paramBean.setHoldingPersonalLoan( argBean.getSurveyCreditBean().getHoldingPersonalLoan() );
		paramBean.setRevolvingCr3dit( argBean.getSurveyCreditBean().getRevovingCredit() );
		paramBean.setLoanPlanCode( argBean.getLoanPlanCode() );

		return paramBean;
	}
}
