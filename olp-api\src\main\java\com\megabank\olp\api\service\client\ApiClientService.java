/**
 *
 */
package com.megabank.olp.api.service.client;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.persistence.dao.mixed.ApiRequestDAO;
import com.megabank.olp.api.utility.BaseApiService;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
public class ApiClientService extends BaseApiService
{
	@Autowired
	private ApiRequestDAO apiRequestDAO;

	public boolean checkLegalAddress( String requestUrl, String clientAddress )
	{
		Validate.notBlank( requestUrl );
		Validate.notBlank( clientAddress );

		if( StringUtils.startsWith( requestUrl, "/open" ) )
			return true;

		String clientIP = StringUtils.split( clientAddress, ":" )[ 0 ];

		List<String> allowedUrls = apiRequestDAO.getRequestUrl( clientIP );

		for( String allowedUrl : allowedUrls )
			if( StringUtils.startsWith( requestUrl, allowedUrl ) )
				return true;

		return false;
	}

}
