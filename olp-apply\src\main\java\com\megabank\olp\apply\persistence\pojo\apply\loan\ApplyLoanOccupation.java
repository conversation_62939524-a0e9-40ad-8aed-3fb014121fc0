package com.megabank.olp.apply.persistence.pojo.apply.loan;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddress;
import com.megabank.olp.apply.persistence.pojo.code.CodeAmountPerMonth;
import com.megabank.olp.apply.persistence.pojo.code.CodeJobSubType;
import com.megabank.olp.apply.persistence.pojo.code.CodeTitleType;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyLoanOccupation is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_occupation" )
public class ApplyLoanOccupation extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_occupation";

	public static final String LOAN_ID_CONSTANT = "loanId";

	public static final String APPLY_ADDRESS_CONSTANT = "applyAddress";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String CODE_AMOUNT_PER_MONTH_CONSTANT = "codeAmountPerMonth";

	public static final String CODE_JOB_SUB_TYPE_CONSTANT = "codeJobSubType"; // select * from dbo.code_job_sub_type 對應到 e-Loan 的職業大小類

	public static final String CODE_TITLE_TYPE_CONSTANT = "codeTitleType"; // select * from dbo.code_title_type

	public static final String COMPANY_NAME_CONSTANT = "companyName";

	public static final String ANNUAL_INCOME_CONSTANT = "annualIncome";

	public static final String SENIORITY_YEAR_CONSTANT = "seniorityYear";

	public static final String SENIORITY_MONTH_CONSTANT = "seniorityMonth";

	public static final String TAX_NO_CONSTANT = "taxNo";

	public static final String COMPANY_PHONE_CODE_CONSTANT = "companyPhoneCode";

	public static final String COMPANY_PHONE_NUMBER_CONSTANT = "companyPhoneNumber";

	public static final String COMPANY_PHONE_EXT_CONSTANT = "companyPhoneExt";

	public static final String EMP_NO_CONSTANT = "empNo"; // J-110-0373 中鋼消貸線上申請暨對保作業

	public static final String JOB_POSITION_CONSTANT = "jobPosition";

	private long loanId;

	private transient ApplyAddress applyAddress;

	private transient ApplyLoan applyLoan;

	private transient CodeAmountPerMonth codeAmountPerMonth;

	private transient CodeJobSubType codeJobSubType;

	private transient CodeTitleType codeTitleType;

	private String companyName;

	private Integer annualIncome;

	private Integer seniorityYear;

	private Integer seniorityMonth;

	private String taxNo;

	private String companyPhoneCode;

	private String companyPhoneNumber;

	private String companyPhoneExt;

	private String empNo;

	private String jobPosition;

	public ApplyLoanOccupation()
	{}

	public ApplyLoanOccupation( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public ApplyLoanOccupation( Long loanId )
	{
		this.loanId = loanId;
	}

	@Column( name = "annual_income", precision = 5, scale = 0 )
	public Integer getAnnualIncome()
	{
		return annualIncome;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "company_address_id" )
	public ApplyAddress getApplyAddress()
	{
		return applyAddress;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "amount_per_month_code" )
	public CodeAmountPerMonth getCodeAmountPerMonth()
	{
		return codeAmountPerMonth;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "job_sub_type_id" )
	public CodeJobSubType getCodeJobSubType()
	{
		return codeJobSubType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "title_type" )
	public CodeTitleType getCodeTitleType()
	{
		return codeTitleType;
	}

	@Column( name = "company_name" )
	public String getCompanyName()
	{
		return companyName;
	}

	@Column( name = "company_phone_code", length = 4 )
	public String getCompanyPhoneCode()
	{
		return companyPhoneCode;
	}

	@Column( name = "company_phone_ext", length = 6 )
	public String getCompanyPhoneExt()
	{
		return companyPhoneExt;
	}

	@Column( name = "company_phone_number", length = 10 )
	public String getCompanyPhoneNumber()
	{
		return companyPhoneNumber;
	}

	@Column( name = "emp_no", length = 6 )
	public String getEmpNo()
	{
		return empNo;
	}

	@Column( name = "job_position" )
	public String getJobPosition()
	{
		return jobPosition;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "applyLoan" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "loan_id", unique = true, nullable = false )
	public long getLoanId()
	{
		return loanId;
	}

	@Column( name = "seniority_month", precision = 5, scale = 0 )
	public Integer getSeniorityMonth()
	{
		return seniorityMonth;
	}

	@Column( name = "seniority_year", precision = 5, scale = 0 )
	public Integer getSeniorityYear()
	{
		return seniorityYear;
	}

	@Column( name = "tax_no", length = 8 )
	public String getTaxNo()
	{
		return taxNo;
	}

	public void setAnnualIncome( Integer annualIncome )
	{
		this.annualIncome = annualIncome;
	}

	public void setApplyAddress( ApplyAddress applyAddress )
	{
		this.applyAddress = applyAddress;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setCodeAmountPerMonth( CodeAmountPerMonth codeAmountPerMonth )
	{
		this.codeAmountPerMonth = codeAmountPerMonth;
	}

	public void setCodeJobSubType( CodeJobSubType codeJobSubType )
	{
		this.codeJobSubType = codeJobSubType;
	}

	public void setCodeTitleType( CodeTitleType codeTitleType )
	{
		this.codeTitleType = codeTitleType;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public void setCompanyPhoneCode( String companyPhoneCode )
	{
		this.companyPhoneCode = companyPhoneCode;
	}

	public void setCompanyPhoneExt( String companyPhoneExt )
	{
		this.companyPhoneExt = companyPhoneExt;
	}

	public void setCompanyPhoneNumber( String companyPhoneNumber )
	{
		this.companyPhoneNumber = companyPhoneNumber;
	}

	public void setEmpNo( String empNo )
	{
		this.empNo = empNo;
	}

	public void setJobPosition( String jobPosition )
	{
		this.jobPosition = jobPosition;
	}

	public void setLoanId( long loanId )
	{
		this.loanId = loanId;
	}

	public void setSeniorityMonth( Integer seniorityMonth )
	{
		this.seniorityMonth = seniorityMonth;
	}

	public void setSeniorityYear( Integer seniorityYear )
	{
		this.seniorityYear = seniorityYear;
	}

	public void setTaxNo( String taxNo )
	{
		this.taxNo = taxNo;
	}
}