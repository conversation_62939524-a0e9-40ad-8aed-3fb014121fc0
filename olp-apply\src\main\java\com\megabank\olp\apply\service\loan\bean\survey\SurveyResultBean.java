package com.megabank.olp.apply.service.loan.bean.survey;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class SurveyResultBean extends BaseBean
{
	private BigDecimal loanRequestAmt;

	private Integer loanPeriod;

	private BigDecimal loanRate;

	public SurveyResultBean()
	{}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public BigDecimal getLoanRate()
	{
		return loanRate;
	}

	public BigDecimal getLoanRequestAmt()
	{
		return loanRequestAmt;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanRate( BigDecimal loanRate )
	{
		this.loanRate = loanRate;
	}

	public void setLoanRequestAmt( BigDecimal loanRequestAmt )
	{
		this.loanRequestAmt = loanRequestAmt;
	}

}
