package com.megabank.olp.apply.controller.management.bean.personalloan;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class PersonalLoanDetailGetterArgBean extends BaseBean
{
	@NotNull
	@JsonProperty( "id" )
	private Long loanId;

	public PersonalLoanDetailGetterArgBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return loanId
	 */
	public Long getLoanId()
	{
		return loanId;
	}

	/**
	 *
	 * @param loanId
	 */
	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}
}
