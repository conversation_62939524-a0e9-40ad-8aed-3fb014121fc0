package com.megabank.olp.api.service.iloan;

import com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoCreateParamBean;
import com.megabank.olp.api.controller.eloan.bean.signing.RateDataBean;
import com.megabank.olp.api.controller.eloan.bean.signing.RepaymentBean;
import com.megabank.olp.api.service.eloan.bean.*;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.*;
import com.megabank.olp.client.sender.micro.apply.management.signing.iloan.ContractPaymentInfoILoanClient;
import com.megabank.olp.client.sender.micro.apply.management.signing.iloan.SigningContractCreatedILoanClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.megabank.olp.api.utility.BaseApiService;
import com.megabank.olp.client.sender.micro.JwtArgBean;

import com.megabank.olp.client.sender.micro.apply.management.signing.bean.SigningContractDiscardArgBean;
import com.megabank.olp.client.sender.micro.apply.management.signing.iloan.SigningContractInvalidILoanClient;

import java.util.ArrayList;
import java.util.List;

@Service
public class SigningContractILoanService extends BaseApiService
{
	@Autowired
	private SigningContractInvalidILoanClient signingContractInvalidILoanClient;

	@Autowired
	private SigningContractCreatedILoanClient contractCreatedClient;

	@Autowired
	private ContractPaymentInfoILoanClient contractPaymentInfoClient;

	public String discardContract( String contractNo )
	{
		String result = "";

		SigningContractDiscardArgBean argBean = new SigningContractDiscardArgBean();
		argBean.setContractNo( contractNo );

		signingContractInvalidILoanClient.send( argBean, new JwtArgBean() );

		return result;
	}

	public String createContract( SigningContractCreatedParamBean paramBean )
	{
		String result = "";

		contractCreatedClient.send( mapSigningContractCreatedArgBean( paramBean ), new JwtArgBean() );

		return result;
	}

	public String createPaymentInfo( PaymentInfoCreateParamBean paramBean )
	{
		String result = "";

		contractPaymentInfoClient.send( mapPaymentInfoClientArgBean( paramBean ), new JwtArgBean() );

		return result;
	}

	private SigningContractCreatedArgBean mapSigningContractCreatedArgBean(SigningContractCreatedParamBean paramBean )
	{
		SigningContractCreatedArgBean argBean = new SigningContractCreatedArgBean();
		argBean.setBranchCode( paramBean.getBranchCode() );
		argBean.setBorrowerBirthDate( paramBean.getBorrowerBirthDate() );
		argBean.setBorrowerId( paramBean.getBorrowerId() );
		argBean.setBorrowerMobileNumber( paramBean.getBorrowerMobileNumber() );
		argBean.setBorrowerName( paramBean.getBorrowerName() );
		argBean.setBorrowerEmail( paramBean.getBorrowerEmail() );
		argBean.setIsBorrowerYouth( paramBean.getIsBorrowerYouth() == null ? null : "Y".equals( paramBean.getIsBorrowerYouth() ));
		argBean.setContractNo( paramBean.getContractNo() );
		argBean.setContractVersion( paramBean.getContractVersion() );
		argBean.setCourtName( paramBean.getCourtName() );
		argBean.setExpiredDate( paramBean.getExpiredDate() );
		argBean.setProductCode( paramBean.getProductCode() );
		argBean.setLoanType( LoanTypeEnum.PERSONAL_LOAN.getContext() );
		argBean.setLoanConditionInfoBean( mapLoanConditionInfoBean( paramBean.getLoanConditionDataBean() ) );

		GuaranteeDataBean guaranteeDataBean = paramBean.getGuaranteeDataBean();
		argBean.setGuaranteeAmt( guaranteeDataBean.getGuaranteeAmt() );
		argBean.setGeneralGuaranteePlan( guaranteeDataBean.getGeneralGuaranteePlan() );
		argBean.setGeneralGuaranteePlanInfo( setDescHtml( guaranteeDataBean.getGeneralGuaranteePlanInfo() ) );
		argBean.setJointGuaranteePlan( guaranteeDataBean.getJointGuaranteePlan() );
		argBean.setJointGuaranteePlanInfo( setDescHtml( guaranteeDataBean.getJointGuaranteePlanInfo() ) );

		if( StringUtils.isNotBlank( paramBean.getRelatedPersonType() ) )
		{
			argBean.setRelatedPersonBirthDate( paramBean.getRelatedPersonBirthDate() );
			argBean.setRelatedPersonId( paramBean.getRelatedPersonId() );
			argBean.setRelatedPersonMobileNumber( paramBean.getRelatedPersonMobileNumber() );
			argBean.setRelatedPersonName( paramBean.getRelatedPersonName() );
			argBean.setRelatedPersonType( paramBean.getRelatedPersonType() );
			argBean.setRelatedPersonEmail( paramBean.getRelatedPersonEmail() );
		}

		argBean.setLoanAccts( mapLoanAccts( paramBean.getBankAccountDataBeans() ) );
		argBean.setLoanPlan( paramBean.getLoanPlan() );
		argBean.setGrpCntrNo( paramBean.getGrpCntrNo() );
		argBean.setGivenApprBegDate( paramBean.getGivenApprBegDate() );
		argBean.setGivenApprEndDate( paramBean.getGivenApprEndDate() );
		argBean.setPayeeBankCode( paramBean.getPayeeBankCode() );
		argBean.setPayeeBankAccountNo( paramBean.getPayeeBankAccountNo() );
		argBean.setPayeeBankAccountName( paramBean.getPayeeBankAccountName() );
		argBean.setPayeeTotalAmt( paramBean.getPayeeTotalAmt() );
		argBean.setPayeeRemittance( paramBean.getPayeeRemittance() );
		argBean.setPayeeSelfProvide( paramBean.getPayeeSelfProvide() );
		argBean.setBaseRate( paramBean.getBaseRate() );
		argBean.setRateList( mapRateList( paramBean.getRateList() ) );
		argBean.setIsRepayment( "Y".equals( paramBean.getIsRepayment() ) );
		argBean.setRepaymentList( paramBean.getRepaymentList().size() > 0 ? mapRepaymentList( paramBean.getRepaymentList() ) : new ArrayList<>() );
		argBean.setStaffRule( false );
		argBean.setRefSystemId( paramBean.getRefSystemId() );

		return argBean;
	}

	private PaymentInfoCreateArgBean mapPaymentInfoClientArgBean(PaymentInfoCreateParamBean paramBean )
	{
		PaymentInfoCreateArgBean argBean = new PaymentInfoCreateArgBean();
		argBean.setContractNo( paramBean.getContractNo() );
		argBean.setPreliminaryFee( paramBean.getPreliminaryFee() );
		argBean.setCrChkFee( paramBean.getCrChkFee() );
		argBean.setPaymentInfoList( paramBean.getPaymentInfoList().size() > 0 ? mapPaymentList( paramBean.getPaymentInfoList() )
				: new ArrayList<>() );

		return argBean;
	}

	private List<PaymentInfoBean> mapPaymentList(List<com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoBean> paymentInfoBeans )
	{
		List<com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean> resBean = new ArrayList<>();

		for( com.megabank.olp.api.controller.eloan.bean.signing.PaymentInfoBean bean : paymentInfoBeans )
		{
			com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean infoBean =
					new com.megabank.olp.client.sender.micro.apply.management.signing.bean.PaymentInfoBean();
			infoBean.setBankCode( bean.getBankCode() );
			infoBean.setBankName( bean.getBankName() );
			infoBean.setRepaymentProductType( bean.getRepaymentProductType() );
			infoBean.setRepaymentProduct( bean.getRepaymentProduct() );
			infoBean.setBankAcctNo( bean.getBankAcctNo() );
			infoBean.setRepaymentAmt( bean.getRepaymentAmt() );
			infoBean.setAccountName( bean.getAccountName() );
			resBean.add( infoBean );
		}

		return resBean;
	}

	private List<RateInfoBean> mapRateList(List<RateDataBean> rateInfoBeans )
	{
		List<RateInfoBean> rateLists = new ArrayList<>();

		for( RateDataBean rateInfoBean : rateInfoBeans )
		{
			RateInfoBean infoBean = new RateInfoBean();
			infoBean.setRate( rateInfoBean.getRate() );
			infoBean.setRate_Type( rateInfoBean.getRate_Type() );
			infoBean.setRate_Bgn( rateInfoBean.getRate_Bgn() );
			infoBean.setRate_End( rateInfoBean.getRate_End() );
			rateLists.add( infoBean );
		}

		return rateLists;
	}

	private String setDescHtml( List<String> descList )
	{
		if( descList == null || descList.isEmpty() )
			return "";

		StringBuilder builder = new StringBuilder();
		for( String desc : descList )
		{
			if( builder.length() > 0 )
				builder.append( "<br>" );

			builder.append( StringUtils.replaceAll( desc, "\n", "<br>" ) );
		}

		return builder.toString();
	}

	private LoanConditionInfoBean mapLoanConditionInfoBean( LoanConditionDataBean data )
	{
		LoanConditionInfoBean infoBean = new LoanConditionInfoBean();
		infoBean.setCreditCheckFee( data.getCreditCheckFee() );
		infoBean.setDrawDownType( data.getDrawDownType() );
		infoBean.setLendingPlan( data.getLendingPlan() );
		infoBean.setLoanAmt( data.getLoanAmt() );
		infoBean.setLoanPeriod( data.getLoanPeriod() );
		infoBean.setLoanPurposeInfoBeans( mapLoanPurposeInfoBeans( data.getLoanPurposeDataBeans() ) );
		infoBean.setOneTimeFee( data.getOneTimeFee() );
		infoBean.setPreliminaryFee( data.getPreliminaryFee() );
		infoBean.setRepaymentMethod( data.getRepaymentMethod() );

		LendingPlanDataBean lendingPlanDataBean = data.getLendingPlanDataBean();
		infoBean.setAdvancedRedemptionTitle( lendingPlanDataBean.getAdvancedRedemptionTitle() );
		infoBean.setAdvancedRedemptionDesc( setDescHtml( lendingPlanDataBean.getAdvancedRedemptionDesc() ) );
		infoBean.setAdvancedRateTitle( lendingPlanDataBean.getAdvancedRateTitle() );
		infoBean.setAdvancedRateDesc( setDescHtml( lendingPlanDataBean.getAdvancedRateDesc() ) );
		infoBean.setAdvancedApr( lendingPlanDataBean.getAdvancedApr() );
		infoBean.setLimitedRedemptionTitle( lendingPlanDataBean.getLimitedRedemptionTitle() );
		infoBean.setLimitedRedemptionDesc( setDescHtml( lendingPlanDataBean.getLimitedRedemptionDesc() ) );
		infoBean.setLimitedRateTitle( lendingPlanDataBean.getLimitedRateTitle() );
		infoBean.setLimitedRateDesc( setDescHtml( lendingPlanDataBean.getLimitedRateDesc() ) );
		infoBean.setLimitedApr( lendingPlanDataBean.getLimitedApr() );
		infoBean.setOtherInfoDesc( setDescHtml( lendingPlanDataBean.getOtherInfoDesc() ) );
		infoBean.setOtherInfoTitle( lendingPlanDataBean.getOtherInfoTitle() );
		infoBean.setShowOption( lendingPlanDataBean.getShowOption() );

		return infoBean;
	}

	private List<String> mapLoanAccts( List<BankAccountDataBean> bankAccountDataBeans )
	{
		List<String> acctDatas = new ArrayList<>();

		for( BankAccountDataBean bankAccountDataBean : bankAccountDataBeans )
			acctDatas.add( bankAccountDataBean.getAccount() );

		return acctDatas;
	}

	private List<com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean> mapRepaymentList( List<RepaymentBean> repaymentList )
	{
		List<com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean> resBean = new ArrayList<>();

		for( RepaymentBean repaymentBean : repaymentList )
		{
			com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean bean =
					new com.megabank.olp.client.sender.micro.apply.management.signing.bean.RepaymentBean();
			bean.setBankCode( repaymentBean.getBankCode() );
			bean.setBankName( repaymentBean.getBankName() );
			bean.setRepaymentProductType( repaymentBean.getRepaymentProductType() );
			bean.setOriginalAmt( repaymentBean.getOriginalAmt() );
			resBean.add( bean );
		}

		return resBean;
	}

	private List<LoanPurposeInfoBean> mapLoanPurposeInfoBeans( List<LoanPurposeDataBean> loanPurposeDataBeans )
	{
		List<LoanPurposeInfoBean> infoBeans = new ArrayList<>();

		for( LoanPurposeDataBean loanPurposeDataBean : loanPurposeDataBeans )
		{
			LoanPurposeInfoBean infoBean = new LoanPurposeInfoBean();
			infoBean.setLoanPurpose( loanPurposeDataBean.getLoanPurposeName() );
			infoBean.setIsChecked( "Y".equals( loanPurposeDataBean.getIsChecked() ) );

			infoBeans.add( infoBean );
		}

		return infoBeans;
	}

}
