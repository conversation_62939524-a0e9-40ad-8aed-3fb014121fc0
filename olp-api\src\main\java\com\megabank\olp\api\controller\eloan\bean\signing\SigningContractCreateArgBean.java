/**
 *
 */
package com.megabank.olp.api.controller.eloan.bean.signing;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SigningContractCreateArgBean extends BaseBean
{
	@NotBlank
	private String contractNo;

	@NotBlank
	private String contractVersion;

	@NotBlank
	private String courtName;

	@NotBlank
	private String branchCode;

	@NotBlank
	private String borrowerName;

	@NotBlank
	private String borrowerId;

	@NotNull
	private Date borrowerBirthDate;

	@NotBlank
	private String borrowerMobileNumber;

	@NotBlank
	private String borrowerEmail;

	private String isBorrowerYouth;

	private String relatedPersonType;

	private String relatedPersonName;

	private String relatedPersonId;

	private Date relatedPersonBirthDate;

	private String relatedPersonMobileNumber;

	private String relatedPersonEmail;

	@NotBlank
	private String productCode;

	@NotNull
	private Date expiredDate;

	@JsonProperty( "loanConditionInfo" )
	@NotNull
	@Valid
	private LoanConditionInfoBean loanConditionInfoBean;

	@JsonProperty( "guaranteeInfo" )
	@NotNull
	@Valid
	private GuaranteeInfoBean guaranteeInfoBean;

	@JsonProperty( "loanAcct" )
	private List<BankAccountInfoBean> bankAccountInfoBeans;

	private String loanPlan;

	private String grpCntrNo;

	private Date givenApprBegDate;

	private Date givenApprEndDate;

	private String payeeBankCode;

	private String payeeBankAccountNo;

	private String payeeBankAccountName;

	private Integer payeeTotalAmt;

	private Integer payeeRemittance;

	private Integer payeeSelfProvide;

	private BigDecimal baseRate;

	private List<RateDataBean> rateList;

	private String isRepayment;

	private List<RepaymentBean> repaymentList;

	private String staffRule;

	private PayeeInfoBean payeeInfo;

	private ExpireInfoBean expireInfo;

	private RepaymentInfoBean repaymentInfo;

	private InterestInfoBean interestInfo;

	private BigDecimal guaranteeType;

	private String witness;

	private String brNoTel;

	private String brNoFax;

	private String prodKind;

	private String lnDate;

	private String consentVer;

	@JsonProperty( "collateralBuildingAddr_1" )
	private String collateralBuildingAddr1;

	@JsonProperty( "collateralBuildingAddr_2" )
	private String collateralBuildingAddr2;

	@JsonProperty( "mortgageMaxAmt_1" )
	private Integer mortgageMaxAmt1;

	@JsonProperty( "mortgageMaxAmt_2" )
	private Integer mortgageMaxAmt2;

	@JsonProperty( "firstLoanDate_year" )
	private String firstLoanDateYear;

	@JsonProperty( "firstLoanDate_mth" )
	private String firstLoanDateMth;

	@JsonProperty( "firstLoanDate_day" )
	private String firstLoanDateDay;

	@JsonProperty( "firstLoanAmt_1" )
	private Integer firstLoanAmt1;

	@JsonProperty( "firstLoanAmt_2" )
	private Integer firstLoanAmt2;

	private List<String> collateralContractTerms;

	private String unregisteredBuildingDesc;

	private String houseLoanContractNo;

	private String coTarget;

	private String cbAfftTerms;

	@JsonProperty( "cbAfft1Content" )
	private CbAfft1ContentBean cbAfft1ContentBean;

	@JsonProperty( "cbAfft2Content" )
	private CbAfft2ContentBean cbAfft2ContentBean;

	@JsonProperty( "cbAfft3Content" )
	private CbAfft3ContentBean cbAfft3ContentBean;

	@JsonProperty( "cbAfft4Content" )
	private CbAfft4ContentBean cbAfft4ContentBean;

	@JsonProperty( "cbAfft5Content" )
	private CbAfft5ContentBean cbAfft5ContentBean;

	@NotBlank
	private String cbAfftVersion;

	public SigningContractCreateArgBean()
	{}

	public List<BankAccountInfoBean> getBankAccountInfoBeans()
	{
		return bankAccountInfoBeans;
	}

	public BigDecimal getBaseRate()
	{
		return baseRate;
	}

	public Date getBorrowerBirthDate()
	{
		return borrowerBirthDate;
	}

	public String getBorrowerEmail()
	{
		return borrowerEmail;
	}

	public String getBorrowerId()
	{
		return borrowerId;
	}

	public String getBorrowerMobileNumber()
	{
		return borrowerMobileNumber;
	}

	public String getBorrowerName()
	{
		return borrowerName;
	}

	public String getBranchCode()
	{
		return branchCode;
	}

	public String getBrNoFax()
	{
		return brNoFax;
	}

	public String getBrNoTel()
	{
		return brNoTel;
	}

	public CbAfft1ContentBean getCbAfft1ContentBean()
	{
		return cbAfft1ContentBean;
	}

	public CbAfft2ContentBean getCbAfft2ContentBean()
	{
		return cbAfft2ContentBean;
	}

	public CbAfft3ContentBean getCbAfft3ContentBean()
	{
		return cbAfft3ContentBean;
	}

	public CbAfft4ContentBean getCbAfft4ContentBean()
	{
		return cbAfft4ContentBean;
	}

	public CbAfft5ContentBean getCbAfft5ContentBean()
	{
		return cbAfft5ContentBean;
	}

	public String getCbAfftTerms()
	{
		return cbAfftTerms;
	}

	public String getCbAfftVersion()
	{
		return cbAfftVersion;
	}

	public String getCollateralBuildingAddr1()
	{
		return collateralBuildingAddr1;
	}

	public String getCollateralBuildingAddr2()
	{
		return collateralBuildingAddr2;
	}

	public List<String> getCollateralContractTerms()
	{
		return collateralContractTerms;
	}

	public String getConsentVer()
	{
		return consentVer;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getContractVersion()
	{
		return contractVersion;
	}

	public String getCoTarget()
	{
		return coTarget;
	}

	public String getCourtName()
	{
		return courtName;
	}

	public Date getExpiredDate()
	{
		return expiredDate;
	}

	public ExpireInfoBean getExpireInfo()
	{
		return expireInfo;
	}

	public Integer getFirstLoanAmt1()
	{
		return firstLoanAmt1;
	}

	public Integer getFirstLoanAmt2()
	{
		return firstLoanAmt2;
	}

	public String getFirstLoanDateDay()
	{
		return firstLoanDateDay;
	}

	public String getFirstLoanDateMth()
	{
		return firstLoanDateMth;
	}

	public String getFirstLoanDateYear()
	{
		return firstLoanDateYear;
	}

	public Date getGivenApprBegDate()
	{
		return givenApprBegDate;
	}

	public Date getGivenApprEndDate()
	{
		return givenApprEndDate;
	}

	public String getGrpCntrNo()
	{
		return grpCntrNo;
	}

	public GuaranteeInfoBean getGuaranteeInfoBean()
	{
		return guaranteeInfoBean;
	}

	public BigDecimal getGuaranteeType()
	{
		return guaranteeType;
	}

	public String getHouseLoanContractNo()
	{
		return houseLoanContractNo;
	}

	public InterestInfoBean getInterestInfo()
	{
		return interestInfo;
	}

	public String getIsBorrowerYouth()
	{
		return isBorrowerYouth;
	}

	public String getIsRepayment()
	{
		return isRepayment;
	}

	public String getLnDate()
	{
		return lnDate;
	}

	public LoanConditionInfoBean getLoanConditionInfoBean()
	{
		return loanConditionInfoBean;
	}

	public String getLoanPlan()
	{
		return loanPlan;
	}

	public Integer getMortgageMaxAmt1()
	{
		return mortgageMaxAmt1;
	}

	public Integer getMortgageMaxAmt2()
	{
		return mortgageMaxAmt2;
	}

	public String getPayeeBankAccountName()
	{
		return payeeBankAccountName;
	}

	public String getPayeeBankAccountNo()
	{
		return payeeBankAccountNo;
	}

	public String getPayeeBankCode()
	{
		return payeeBankCode;
	}

	public PayeeInfoBean getPayeeInfo()
	{
		return payeeInfo;
	}

	public Integer getPayeeRemittance()
	{
		return payeeRemittance;
	}

	public Integer getPayeeSelfProvide()
	{
		return payeeSelfProvide;
	}

	public Integer getPayeeTotalAmt()
	{
		return payeeTotalAmt;
	}

	public String getProdKind()
	{
		return prodKind;
	}

	public String getProductCode()
	{
		return productCode;
	}

	public List<RateDataBean> getRateList()
	{
		return rateList;
	}

	public Date getRelatedPersonBirthDate()
	{
		return relatedPersonBirthDate;
	}

	public String getRelatedPersonEmail()
	{
		return relatedPersonEmail;
	}

	public String getRelatedPersonId()
	{
		return relatedPersonId;
	}

	public String getRelatedPersonMobileNumber()
	{
		return relatedPersonMobileNumber;
	}

	public String getRelatedPersonName()
	{
		return relatedPersonName;
	}

	public String getRelatedPersonType()
	{
		return relatedPersonType;
	}

	public RepaymentInfoBean getRepaymentInfo()
	{
		return repaymentInfo;
	}

	public List<RepaymentBean> getRepaymentList()
	{
		return repaymentList;
	}

	public String getStaffRule()
	{
		return staffRule;
	}

	public String getUnregisteredBuildingDesc()
	{
		return unregisteredBuildingDesc;
	}

	public String getWitness()
	{
		return witness;
	}

	public void setBankAccountInfoBeans( List<BankAccountInfoBean> bankAccountInfoBeans )
	{
		this.bankAccountInfoBeans = bankAccountInfoBeans;
	}

	public void setBaseRate( BigDecimal baseRate )
	{
		this.baseRate = baseRate;
	}

	public void setBorrowerBirthDate( Date borrowerBirthDate )
	{
		this.borrowerBirthDate = borrowerBirthDate;
	}

	public void setBorrowerEmail( String borrowerEmail )
	{
		this.borrowerEmail = borrowerEmail;
	}

	public void setBorrowerId( String borrowerId )
	{
		this.borrowerId = borrowerId;
	}

	public void setBorrowerMobileNumber( String borrowerMobileNumber )
	{
		this.borrowerMobileNumber = borrowerMobileNumber;
	}

	public void setBorrowerName( String borrowerName )
	{
		this.borrowerName = borrowerName;
	}

	public void setBranchCode( String branchCode )
	{
		this.branchCode = branchCode;
	}

	public void setBrNoFax( String brNoFax )
	{
		this.brNoFax = brNoFax;
	}

	public void setBrNoTel( String brNoTel )
	{
		this.brNoTel = brNoTel;
	}

	public void setCbAfft1ContentBean( CbAfft1ContentBean cbAfft1ContentBean )
	{
		this.cbAfft1ContentBean = cbAfft1ContentBean;
	}

	public void setCbAfft2ContentBean( CbAfft2ContentBean cbAfft2ContentBean )
	{
		this.cbAfft2ContentBean = cbAfft2ContentBean;
	}

	public void setCbAfft3ContentBean( CbAfft3ContentBean cbAfft3ContentBean )
	{
		this.cbAfft3ContentBean = cbAfft3ContentBean;
	}

	public void setCbAfft4ContentBean( CbAfft4ContentBean cbAfft4ContentBean )
	{
		this.cbAfft4ContentBean = cbAfft4ContentBean;
	}

	public void setCbAfft5ContentBean( CbAfft5ContentBean cbAfft5ContentBean )
	{
		this.cbAfft5ContentBean = cbAfft5ContentBean;
	}

	public void setCbAfftTerms( String cbAfftTerms )
	{
		this.cbAfftTerms = cbAfftTerms;
	}

	public void setCbAfftVersion( String cbAfftVersion )
	{
		this.cbAfftVersion = cbAfftVersion;
	}

	public void setCollateralBuildingAddr1( String collateralBuildingAddr1 )
	{
		this.collateralBuildingAddr1 = collateralBuildingAddr1;
	}

	public void setCollateralBuildingAddr2( String collateralBuildingAddr2 )
	{
		this.collateralBuildingAddr2 = collateralBuildingAddr2;
	}

	public void setCollateralContractTerms( List<String> collateralContractTerms )
	{
		this.collateralContractTerms = collateralContractTerms;
	}

	public void setConsentVer( String consentVer )
	{
		this.consentVer = consentVer;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setContractVersion( String contractVersion )
	{
		this.contractVersion = contractVersion;
	}

	public void setCoTarget( String coTarget )
	{
		this.coTarget = coTarget;
	}

	public void setCourtName( String courtName )
	{
		this.courtName = courtName;
	}

	public void setExpiredDate( Date expiredDate )
	{
		this.expiredDate = expiredDate;
	}

	public void setExpireInfo( ExpireInfoBean expireInfo )
	{
		this.expireInfo = expireInfo;
	}

	public void setFirstLoanAmt1( Integer firstLoanAmt1 )
	{
		this.firstLoanAmt1 = firstLoanAmt1;
	}

	public void setFirstLoanAmt2( Integer firstLoanAmt2 )
	{
		this.firstLoanAmt2 = firstLoanAmt2;
	}

	public void setFirstLoanDateDay( String firstLoanDateDay )
	{
		this.firstLoanDateDay = firstLoanDateDay;
	}

	public void setFirstLoanDateMth( String firstLoanDateMth )
	{
		this.firstLoanDateMth = firstLoanDateMth;
	}

	public void setFirstLoanDateYear( String firstLoanDateYear )
	{
		this.firstLoanDateYear = firstLoanDateYear;
	}

	public void setGivenApprBegDate( Date givenApprBegDate )
	{
		this.givenApprBegDate = givenApprBegDate;
	}

	public void setGivenApprEndDate( Date givenApprEndDate )
	{
		this.givenApprEndDate = givenApprEndDate;
	}

	public void setGrpCntrNo( String grpCntrNo )
	{
		this.grpCntrNo = grpCntrNo;
	}

	public void setGuaranteeInfoBean( GuaranteeInfoBean guaranteeInfoBean )
	{
		this.guaranteeInfoBean = guaranteeInfoBean;
	}

	public void setGuaranteeType( BigDecimal guaranteeType )
	{
		this.guaranteeType = guaranteeType;
	}

	public void setHouseLoanContractNo( String houseLoanContractNo )
	{
		this.houseLoanContractNo = houseLoanContractNo;
	}

	public void setInterestInfo( InterestInfoBean interestInfo )
	{
		this.interestInfo = interestInfo;
	}

	public void setIsBorrowerYouth( String isBorrowerYouth )
	{
		this.isBorrowerYouth = isBorrowerYouth;
	}

	public void setIsRepayment( String isRepayment )
	{
		this.isRepayment = isRepayment;
	}

	public void setLnDate( String lnDate )
	{
		this.lnDate = lnDate;
	}

	public void setLoanConditionInfoBean( LoanConditionInfoBean loanConditionInfoBean )
	{
		this.loanConditionInfoBean = loanConditionInfoBean;
	}

	public void setLoanPlan( String loanPlan )
	{
		this.loanPlan = loanPlan;
	}

	public void setMortgageMaxAmt1( Integer mortgageMaxAmt1 )
	{
		this.mortgageMaxAmt1 = mortgageMaxAmt1;
	}

	public void setMortgageMaxAmt2( Integer mortgageMaxAmt2 )
	{
		this.mortgageMaxAmt2 = mortgageMaxAmt2;
	}

	public void setPayeeBankAccountName( String payeeBankAccountName )
	{
		this.payeeBankAccountName = payeeBankAccountName;
	}

	public void setPayeeBankAccountNo( String payeeBankAccountNo )
	{
		this.payeeBankAccountNo = payeeBankAccountNo;
	}

	public void setPayeeBankCode( String payeeBankCode )
	{
		this.payeeBankCode = payeeBankCode;
	}

	public void setPayeeInfo( PayeeInfoBean payeeInfo )
	{
		this.payeeInfo = payeeInfo;
	}

	public void setPayeeRemittance( Integer payeeRemittance )
	{
		this.payeeRemittance = payeeRemittance;
	}

	public void setPayeeSelfProvide( Integer payeeSelfProvide )
	{
		this.payeeSelfProvide = payeeSelfProvide;
	}

	public void setPayeeTotalAmt( Integer payeeTotalAmt )
	{
		this.payeeTotalAmt = payeeTotalAmt;
	}

	public void setProdKind( String prodKind )
	{
		this.prodKind = prodKind;
	}

	public void setProductCode( String productCode )
	{
		this.productCode = productCode;
	}

	public void setRateList( List<RateDataBean> rateList )
	{
		this.rateList = rateList;
	}

	public void setRelatedPersonBirthDate( Date relatedPersonBirthDate )
	{
		this.relatedPersonBirthDate = relatedPersonBirthDate;
	}

	public void setRelatedPersonEmail( String relatedPersonEmail )
	{
		this.relatedPersonEmail = relatedPersonEmail;
	}

	public void setRelatedPersonId( String relatedPersonId )
	{
		this.relatedPersonId = relatedPersonId;
	}

	public void setRelatedPersonMobileNumber( String relatedPersonMobileNumber )
	{
		this.relatedPersonMobileNumber = relatedPersonMobileNumber;
	}

	public void setRelatedPersonName( String relatedPersonName )
	{
		this.relatedPersonName = relatedPersonName;
	}

	public void setRelatedPersonType( String relatedPersonType )
	{
		this.relatedPersonType = relatedPersonType;
	}

	public void setRepamentList( List<RepaymentBean> repaymentList )
	{
		this.repaymentList = repaymentList;
	}

	public void setRepaymentInfo( RepaymentInfoBean repaymentInfo )
	{
		this.repaymentInfo = repaymentInfo;
	}

	public void setRepaymentList( List<RepaymentBean> repaymentList )
	{
		this.repaymentList = repaymentList;
	}

	public void setStaffRule( String staffRule )
	{
		this.staffRule = staffRule;
	}

	public void setUnregisteredBuildingDesc( String unregisteredBuildingDesc )
	{
		this.unregisteredBuildingDesc = unregisteredBuildingDesc;
	}

	public void setWitness( String witness )
	{
		this.witness = witness;
	}
}
