package com.megabank.olp.apply.persistence.pojo.apply.loan;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import com.megabank.olp.apply.persistence.pojo.code.CodeEducationLevel;
import com.megabank.olp.apply.persistence.pojo.code.CodeMarriageStatus;
import com.megabank.olp.apply.persistence.pojo.code.CodeNationality;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The ApplyLoanBasic is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_loan_basic" )
public class ApplyLoanBasic extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_loan_basic";

	public static final String LOAN_ID_CONSTANT = "loanId";

	public static final String APPLY_LOAN_CONSTANT = "applyLoan";

	public static final String CODE_EDUCATION_LEVEL_CONSTANT = "codeEducationLevel";

	public static final String CODE_MARRIAGE_STATUS_CONSTANT = "codeMarriageStatus";

	public static final String CODE_NATIONALITY_CONSTANT = "codeNationality";

	public static final String NAME_CONSTANT = "name";

	public static final String ENG_FIRST_NAME_CONSTANT = "engFirstName";

	public static final String ENG_LAST_NAME_CONSTANT = "engLastName";

	public static final String ID_NO_CONSTANT = "idNo";

	public static final String BIRTH_DATE_CONSTANT = "birthDate";

	public static final String CHILDREN_COUNT_CONSTANT = "childrenCount";

	public static final String NOT_US_TAXPAYER_CONSTANT = "notUsTaxpayer";

	public static final String NOT_OUTTW_TAXPAYER_CONSTANT = "notOuttwTaxpayer";

	public static final String RATE_ADJ_NOTIFY_CONSTANT = "rateAdjNotify";

	public static final String ENG_NAME_FG_CONSTANT = "engNameFg";

	public static final String ENG_NAME_CONSTANT = "engName";

	public static final String CROSS_MARKETING_CONSTANT = "crossMarketing";

	private long loanId;

	private transient ApplyLoan applyLoan;

	private transient CodeEducationLevel codeEducationLevel;

	private transient CodeMarriageStatus codeMarriageStatus;

	private transient CodeNationality codeNationality;

	private String name;

	private String engFirstName;

	private String engLastName;

	private String idNo;

	private Date birthDate;

	private int childrenCount;

	private Boolean notUsTaxpayer; // J-110-0373 中鋼消貸線上申請暨對保作業

	private Boolean notOuttwTaxpayer;

	private String rateAdjNotify;

	private String engNameFg; // 若 engNameFg=="Y" 表示 英文姓名 無法拆分{姓/名} engLastName/engFirstName， 只能去抓 engName

	private String engName;

	private Boolean crossMarketing;

	public ApplyLoanBasic()
	{}

	public ApplyLoanBasic( ApplyLoan applyLoan, String idNo, Date birthDate, int childrenCount )
	{
		this.applyLoan = applyLoan;
		this.idNo = idNo;
		this.birthDate = birthDate;
		this.childrenCount = childrenCount;
	}

	public ApplyLoanBasic( Long loanId )
	{
		this.loanId = loanId;
	}

	@OneToOne( fetch = FetchType.LAZY )
	@PrimaryKeyJoinColumn
	public ApplyLoan getApplyLoan()
	{
		return applyLoan;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "birth_date", nullable = false, length = 10 )
	public Date getBirthDate()
	{
		return birthDate;
	}

	@Column( name = "children_count", nullable = false, precision = 5, scale = 0 )
	public int getChildrenCount()
	{
		return childrenCount;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "education_level_code" )
	public CodeEducationLevel getCodeEducationLevel()
	{
		return codeEducationLevel;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "marriage_status_code" )
	public CodeMarriageStatus getCodeMarriageStatus()
	{
		return codeMarriageStatus;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "nationality_code" )
	public CodeNationality getCodeNationality()
	{
		return codeNationality;
	}

	@Column( name = "cross_marketing", precision = 1, scale = 0 )
	public Boolean getCrossMarketing()
	{
		return crossMarketing;
	}

	@Column( name = "eng_first_name", length = 100 )
	public String getEngFirstName()
	{
		return engFirstName;
	}

	@Column( name = "eng_last_name", length = 100 )
	public String getEngLastName()
	{
		return engLastName;
	}

	@Column( name = "eng_name", length = 100 )
	public String getEngName()
	{
		return engName;
	}

	@Column( name = "eng_name_fg", length = 1 )
	public String getEngNameFg()
	{
		return engNameFg;
	}

	@Column( name = "id_no", nullable = false, length = 10 )
	public String getIdNo()
	{
		return idNo;
	}

	@GenericGenerator( name = "generator", strategy = "foreign", parameters = @Parameter( name = "property", value = "applyLoan" ) )
	@Id
	@GeneratedValue( generator = "generator" )
	@Column( name = "loan_id", unique = true, nullable = false )
	public long getLoanId()
	{
		return loanId;
	}

	@Column( name = "name" )
	public String getName()
	{
		return name;
	}

	@Column( name = "not_outtw_taxpayer", precision = 1, scale = 0 )
	public Boolean getNotOuttwTaxpayer()
	{
		return notOuttwTaxpayer;
	}

	@Column( name = "not_us_taxpayer", precision = 1, scale = 0 )
	public Boolean getNotUsTaxpayer()
	{
		return notUsTaxpayer;
	}

	@Column( name = "rate_adj_notify", length = 1 )
	public String getRateAdjNotify()
	{
		return rateAdjNotify;
	}

	public void setApplyLoan( ApplyLoan applyLoan )
	{
		this.applyLoan = applyLoan;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setChildrenCount( int childrenCount )
	{
		this.childrenCount = childrenCount;
	}

	public void setCodeEducationLevel( CodeEducationLevel codeEducationLevel )
	{
		this.codeEducationLevel = codeEducationLevel;
	}

	public void setCodeMarriageStatus( CodeMarriageStatus codeMarriageStatus )
	{
		this.codeMarriageStatus = codeMarriageStatus;
	}

	public void setCodeNationality( CodeNationality codeNationality )
	{
		this.codeNationality = codeNationality;
	}

	public void setCrossMarketing( Boolean crossMarketing )
	{
		this.crossMarketing = crossMarketing;
	}

	public void setEngFirstName( String engFirstName )
	{
		this.engFirstName = engFirstName;
	}

	public void setEngLastName( String engLastName )
	{
		this.engLastName = engLastName;
	}

	public void setEngName( String engName )
	{
		this.engName = engName;
	}

	public void setEngNameFg( String engNameFg )
	{
		this.engNameFg = engNameFg;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanId( long loanId )
	{
		this.loanId = loanId;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotOuttwTaxpayer( Boolean notOuttwTaxpayer )
	{
		this.notOuttwTaxpayer = notOuttwTaxpayer;
	}

	public void setNotUsTaxpayer( Boolean notUsTaxpayer )
	{
		this.notUsTaxpayer = notUsTaxpayer;
	}

	public void setRateAdjNotify( String rateAdjNotify )
	{
		this.rateAdjNotify = rateAdjNotify;
	}
}