package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanContentUpdatedParamBean extends BaseBean
{
	private Long loanId;

	private Integer loanRequestAmt;

	private Long loanPurposeId;

	private String otherPurpose;

	private String loanPeriod;

	private Long collateralAddressId;

	private String gracePeriodCode;

	private String notificationCode;

	private String mortgageType;

	private String nonPrivateUsageType;

	private String nonPrivateUsageSubType;

	private String privateUsageType;

	private Boolean isIncreasingLoan;

	private String appnBankCode;

	private String appnDpAcct;

	private String caseSourceCode;

	private String urlToIdentifyFraud;

	public ApplyLoanContentUpdatedParamBean()
	{}

	public String getAppnBankCode()
	{
		return appnBankCode;
	}

	public String getAppnDpAcct()
	{
		return appnDpAcct;
	}

	public Long getCollateralAddressId()
	{
		return collateralAddressId;
	}

	public String getGracePeriodCode()
	{
		return gracePeriodCode;
	}

	public Boolean getIsIncreasingLoan()
	{
		return isIncreasingLoan;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getLoanPeriod()
	{
		return loanPeriod;
	}

	public Long getLoanPurposeId()
	{
		return loanPurposeId;
	}

	public Integer getLoanRequestAmt()
	{
		return loanRequestAmt;
	}

	public String getMortgageType()
	{
		return mortgageType;
	}

	public String getNonPrivateUsageSubType()
	{
		return nonPrivateUsageSubType;
	}

	public String getNonPrivateUsageType()
	{
		return nonPrivateUsageType;
	}

	public String getNotificationCode()
	{
		return notificationCode;
	}

	public String getOtherPurpose()
	{
		return otherPurpose;
	}

	public String getPrivateUsageType()
	{
		return privateUsageType;
	}

	public void setAppnBankCode( String appnBankCode )
	{
		this.appnBankCode = appnBankCode;
	}

	public void setAppnDpAcct( String appnDpAcct )
	{
		this.appnDpAcct = appnDpAcct;
	}

	public void setCollateralAddressId( Long collateralAddressId )
	{
		this.collateralAddressId = collateralAddressId;
	}

	public void setGracePeriodCode( String gracePeriodCode )
	{
		this.gracePeriodCode = gracePeriodCode;
	}

	public void setIsIncreasingLoan( Boolean isIncreasingLoan )
	{
		this.isIncreasingLoan = isIncreasingLoan;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setLoanPeriod( String loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanPurposeId( Long loanPurposeId )
	{
		this.loanPurposeId = loanPurposeId;
	}

	public void setLoanRequestAmt( Integer loanRequestAmt )
	{
		this.loanRequestAmt = loanRequestAmt;
	}

	public void setMortgageType( String mortgageType )
	{
		this.mortgageType = mortgageType;
	}

	public void setNonPrivateUsageSubType( String nonPrivateUsageSubType )
	{
		this.nonPrivateUsageSubType = nonPrivateUsageSubType;
	}

	public void setNonPrivateUsageType( String nonPrivateUsageType )
	{
		this.nonPrivateUsageType = nonPrivateUsageType;
	}

	public void setNotificationCode( String notificationCode )
	{
		this.notificationCode = notificationCode;
	}

	public void setOtherPurpose( String otherPurpose )
	{
		this.otherPurpose = otherPurpose;
	}

	public void setPrivateUsageType( String privateUsageType )
	{
		this.privateUsageType = privateUsageType;
	}

	public String getCaseSourceCode()
	{
		return caseSourceCode;
	}

	public void setCaseSourceCode( String caseSourceCode )
	{
		this.caseSourceCode = caseSourceCode;
	}

	public String getUrlToIdentifyFraud()
	{
		return urlToIdentifyFraud;
	}

	public void setUrlToIdentifyFraud( String urlToIdentifyFraud )
	{
		this.urlToIdentifyFraud = urlToIdentifyFraud;
	}
}
