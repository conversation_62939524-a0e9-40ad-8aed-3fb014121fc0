package com.megabank.olp.apply.persistence.dao.mixed;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.mixed.HouseLoanTrialListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.HouseLoanTrialListDTO;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class HouseLoanTrialDAO extends BaseDAO
{
	private static final String CASE_NO_CONSTANT = "caseNo";

	private static final String DATE_END_CONSTANT = "dateEnd";

	private static final String DATE_START_CONSTANT = "dateStart";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String EMAIL_CONSTANT = "email";

	private static final String PROCESS_CODE_CONSTANT = "processCode";

	private static final String PROCESS_STATUS_CONSTANT = "processStatus";

	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String NOT_NOTIFIED_NAME_CONSTANT = "notNotifiedName";

	private static final String NOTIFIED_NAME_CONSTANT = "notifiedName";

	private static final String LOAN_TRIAL_INFO_ID_CONSTANT = "loanTrialInfoId";

	private static final String CREATED_DATE_CONSTANT = "createdDate";

	private static final String BRANCH_BANK_CONSTANT = "branchBank";

	private static final String NOTIFICATION_STATUS_CONSTANT = "notificationStatus";

	public List<HouseLoanTrialListDTO> getList( HouseLoanTrialListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "houseloantrial.getList" );
		nativeQuery.setParameter( EMAIL_CONSTANT, paramBean.getEmail(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getCreatedDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getCreatedDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, paramBean.getBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.addScalar( LOAN_TRIAL_INFO_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( EMAIL_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( HouseLoanTrialListDTO.class ) );

		return nativeQuery.getResultList();
	}

	public PagingBean<HouseLoanTrialListDTO> getPaging( HouseLoanTrialListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "houseloantrial.getList" );
		nativeQuery.setParameter( EMAIL_CONSTANT, paramBean.getEmail(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getCreatedDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getCreatedDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, paramBean.getBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.addScalar( LOAN_TRIAL_INFO_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( EMAIL_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );

		NativeQuery countQuery = getNamedSQLQueryByCount( "houseloantrial.getList.count" );
		countQuery.setParameter( EMAIL_CONSTANT, paramBean.getEmail(), String.class );
		countQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		countQuery.setParameter( DATE_START_CONSTANT, paramBean.getCreatedDateStart(), Date.class );
		countQuery.setParameter( DATE_END_CONSTANT, paramBean.getCreatedDateEnd(), Date.class );
		countQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		countQuery.setParameter( BRANCH_BANK_ID_CONSTANT, paramBean.getBranchBankId(), Long.class );
		countQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( HouseLoanTrialListDTO.class ) );

		return processPagination( nativeQuery, countQuery );

	}

}
