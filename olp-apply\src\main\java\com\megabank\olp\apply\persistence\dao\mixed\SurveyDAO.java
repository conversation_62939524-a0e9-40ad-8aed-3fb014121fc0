package com.megabank.olp.apply.persistence.dao.mixed;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;

import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.mixed.SurveyListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.SurveyListDTO;
import com.megabank.olp.apply.persistence.pojo.apply.survey.ApplySurveyContact;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BaseDAO;

@Repository
public class SurveyDAO extends BaseDAO
{
	private static final String CASE_NO_CONSTANT = "caseNo";

	private static final String DATE_END_CONSTANT = "dateEnd";

	private static final String DATE_START_CONSTANT = "dateStart";

	private static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	private static final String NAME_CONSTANT = "name";

	private static final String SURVEY_ID_CONSTANT = "surveyId";

	private static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	private static final String PROCESS_CODE_CONSTANT = "processCode";

	private static final String PROCESS_STATUS_CONSTANT = "processStatus";

	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String CONTACT_TIME_CODE_CONSTANT = "contactTimeCode";

	private static final String NOT_NOTIFIED_NAME_CONSTANT = "notNotifiedName";

	private static final String NOTIFIED_NAME_CONSTANT = "notifiedName";

	private static final String CREATED_DATE_CONSTANT = "createdDate";

	private static final String BRANCH_BANK_CONSTANT = "branchBank";

	private static final String NOTIFICATION_STATUS_CONSTANT = "notificationStatus";

	private static final String CONTACT_TIME_CONSTANT = "contactTime";

	private static final String LOAN_PLAN_CODE_CONSTANT = "loanPlanCode";

	private static final String LOAN_PLAN_NAME_CONSTANT = "loanPlanName";

	public ApplySurveyContact getLatestContactData( Long validatedIdentityId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "survey.getLatestContactData" );
		nativeQuery.setParameter( VALIDATED_IDENTITY_ID_CONSTANT, validatedIdentityId, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplySurveyContact.class );

		return ( ApplySurveyContact )nativeQuery.uniqueResult();
	}

	public List<SurveyListDTO> getList( SurveyListGetterParamBean paramBean )
	{

		NativeQuery nativeQuery = getNamedNativeQuery( "survey.getList" );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( CONTACT_TIME_CODE_CONSTANT, paramBean.getContactTimeCode(), String.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.addScalar( SURVEY_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( CONTACT_TIME_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_CODE_CONSTANT, String.class );
		nativeQuery.addScalar( LOAN_PLAN_NAME_CONSTANT, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( SurveyListDTO.class ) );

		return nativeQuery.getResultList();
	}

	public PagingBean<SurveyListDTO> getPaging( SurveyListGetterParamBean paramBean )
	{
		NativeQuery nativeQuery = getNamedNativeQuery( "survey.getList" );
		nativeQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		nativeQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		nativeQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		nativeQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		nativeQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		nativeQuery.setParameter( CONTACT_TIME_CODE_CONSTANT, paramBean.getContactTimeCode(), String.class );

		nativeQuery.setParameter( NOT_NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getName(), String.class );
		nativeQuery.setParameter( NOTIFIED_NAME_CONSTANT, NotificationStatusEnum.NOTIFIED.getName(), String.class );

		nativeQuery.addScalar( SURVEY_ID_CONSTANT, Long.class );
		nativeQuery.addScalar( CASE_NO_CONSTANT, String.class );
		nativeQuery.addScalar( NAME_CONSTANT, String.class );
		nativeQuery.addScalar( MOBILE_NUMBER_CONSTANT, String.class );
		nativeQuery.addScalar( CREATED_DATE_CONSTANT, Timestamp.class );
		nativeQuery.addScalar( PROCESS_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( BRANCH_BANK_CONSTANT, String.class );
		nativeQuery.addScalar( NOTIFICATION_STATUS_CONSTANT, String.class );
		nativeQuery.addScalar( CONTACT_TIME_CONSTANT, String.class );

		NativeQuery countQuery = getNamedSQLQueryByCount( "survey.getList.count" );
		countQuery.setParameter( NAME_CONSTANT, paramBean.getName(), String.class );
		countQuery.setParameter( MOBILE_NUMBER_CONSTANT, paramBean.getMobileNumber(), String.class );
		countQuery.setParameter( DATE_START_CONSTANT, paramBean.getDateStart(), Date.class );
		countQuery.setParameter( DATE_END_CONSTANT, paramBean.getDateEnd(), Date.class );
		countQuery.setParameter( PROCESS_CODE_CONSTANT, paramBean.getProcessCode(), String.class );
		countQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, paramBean.getFinalBranchBankId(), Long.class );
		countQuery.setParameter( NOTIFIED_CONSTANT, paramBean.getNotified(), Integer.class );
		countQuery.setParameter( CONTACT_TIME_CODE_CONSTANT, paramBean.getContactTimeCode(), String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( SurveyListDTO.class ) );

		return processPagination( nativeQuery, countQuery );

	}

}
