/**
 *
 */
package com.megabank.olp.api.service.estimation.bean;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class CalculateLoanDataBean extends BaseBean
{
	private BigDecimal topLoanCredit;

	private BigDecimal rate;

	public BigDecimal getRate()
	{
		return rate;
	}

	public BigDecimal getTopLoanCredit()
	{
		return topLoanCredit;
	}

	public void setRate( BigDecimal rate )
	{
		this.rate = rate;
	}

	public void setTopLoanCredit( BigDecimal topLoanCredit )
	{
		this.topLoanCredit = topLoanCredit;
	}
}
