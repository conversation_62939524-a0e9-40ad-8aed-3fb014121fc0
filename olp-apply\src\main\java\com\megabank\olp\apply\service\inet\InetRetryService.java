package com.megabank.olp.apply.service.inet;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplyCollateralContractAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.signing.ApplySigningContractDAO;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplyCollateralContractAttachment;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.service.inet.bean.PackagedInetResBean;
import com.megabank.olp.apply.service.loan.DeliverService;
import com.megabank.olp.apply.service.management.ContractService;
import com.megabank.olp.apply.utility.BaseApplyService;
import com.megabank.olp.apply.utility.enums.InetResponseStatusEnum;
import com.megabank.olp.apply.utility.enums.InetReturnTypeEnum;
import com.megabank.olp.apply.utility.enums.InetRptTemplateEnum;
import com.megabank.olp.apply.utility.enums.SigningContractSendStatusEnum;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.enums.ProductCodeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.utility.CommonUtil;
import com.megabank.olp.client.utility.enums.InetReportCodeEnum;

@Service
@Transactional
public class InetRetryService extends BaseApplyService
{
	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	@Autowired
	private InetService inetService;

	@Autowired
	private DeliverService deliverService;

	@Autowired
	private ContractService contractService;

	@Autowired
	private ApplyCollateralContractAttachmentDAO applyCollateralContractAttachmentDAO;

	public synchronized void retryGetCollateralContractAttachmentPdf() throws MyRuntimeException
	{
		List<ApplyCollateralContractAttachment> attachmentList = applyCollateralContractAttachmentDAO
					.getPojosByInetResponseStatus( InetResponseStatusEnum.FAIL.getCode() );

		for( ApplyCollateralContractAttachment attachment : attachmentList )
		{
			PackagedInetResBean inetResBean = inetService
						.getPdf( attachment.getInetRptName(), InetReturnTypeEnum.PDF.getContext(),
								 Collections.singletonList( attachment.getApplySigningContract().getSigningContractId() ) );

			boolean isInetSuccess = InetResponseStatusEnum.SUCCESS.getCode().equals( inetResBean.getStat() );
			if( isInetSuccess )
			{
				applyCollateralContractAttachmentDAO.updatePdfContentViaInet( attachment.getAttachmentId(), inetResBean.getPdfContent().getData(),
																			  InetResponseStatusEnum.SUCCESS.getCode() );

				attachment.setFileContent( inetResBean.getPdfContent() );
				boolean isSubmitSuccess = deliverService.submitCollateralContractAttachment( attachment );
				String transmissionStatusCode = isSubmitSuccess ? TransmissionStatusEnum.COMPLETED.getContext()
																: TransmissionStatusEnum.EXCEPTION.getContext();
				applyCollateralContractAttachmentDAO.updateTransmissionStatus( attachment.getAttachmentId(), transmissionStatusCode );

			}

		}
	}

	public synchronized void retryGetLoanSigningContract( String code ) throws MyRuntimeException
	{
		List<ApplySigningContract> contracts = null;
		if( InetReportCodeEnum.HOUSE_LOAN_SIGNING_CONTRACT.getCode().equals( code ) )
			contracts = applySigningContractDAO.getPojoByProductCodeAndInetResponseStat( ProductCodeEnum.HOUSE_LOAN.getContext(),
																						 InetResponseStatusEnum.FAIL.getCode() );
		else if( InetReportCodeEnum.PERSONAL_LOAN_SIGNING_CONTRACT.getCode().equals( code ) )
			contracts = applySigningContractDAO.getPojoByProductCodeAndInetResponseStat( ProductCodeEnum.PERSONAL_LOAN.getContext(),
																						 InetResponseStatusEnum.FAIL.getCode() );

		for( ApplySigningContract contract : contracts )
		{
			byte[] pdfContent = getInetPdf( contract, code );

			if( contract.getInetResponseStatus() == InetResponseStatusEnum.SUCCESS.getCode() )
			{
				applySigningContractDAO.updatePdfContent( contract.getContractNo(), pdfContent );
				contract.setPdfContent( new ImmutableByteArray( pdfContent ) );

				boolean submitSuccess = false;
				// 未撥款
				if( !contract.getIsAppropiration() )
					submitSuccess = deliverService.submitSigningContract( contract, CommonUtil.getByteArray( contract.getPdfContent() ) );
				// 已撥款
				else
				{
					submitSuccess = deliverService.submitSigningContractPdf( contract );
					contractService.informCustomerAppropriated( contract, true );
				}

				String sendStatusCode = submitSuccess ? SigningContractSendStatusEnum.COMPLETED.getContext()
													  : SigningContractSendStatusEnum.EXCEPTION.getContext();

				applySigningContractDAO.updateSendStatus( contract.getSigningContractId(), sendStatusCode );
			}
		}
	}

	private byte[] getInetPdf( ApplySigningContract contract, String code )
	{
		PackagedInetResBean resBean = null;
		if( InetReportCodeEnum.HOUSE_LOAN_SIGNING_CONTRACT.getCode().equals( code ) )
			resBean = inetService.getPdf( InetRptTemplateEnum.HOUSE_LOAN_CONTRACT.getContext(), InetReturnTypeEnum.PDF.getContext(),
										  Arrays.asList( contract.getSigningContractId() ) );
		else if( InetReportCodeEnum.PERSONAL_LOAN_SIGNING_CONTRACT.getCode().equals( code ) )
			resBean = inetService.getPdf( InetRptTemplateEnum.PERSONAL_LOAN_CONTRACT.getContext(), InetReturnTypeEnum.PDF.getContext(),
										  Arrays.asList( contract.getSigningContractId() ) );

		Integer stat = resBean.getStat();
		applySigningContractDAO.updateInetResponseStat( contract.getContractNo(), stat );
		contract.setInetResponseStatus( stat );

		return resBean.getPdfContent().getData();
	}
}
