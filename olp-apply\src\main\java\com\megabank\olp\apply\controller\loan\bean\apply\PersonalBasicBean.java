package com.megabank.olp.apply.controller.loan.bean.apply;

import java.util.Date;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.megabank.olp.base.bean.BaseBean;

public class PersonalBasicBean extends BaseBean
{
	@NotBlank
	private String idNo;

	@NotBlank
	private String name;

	private String engFirstName;

	private String engLastName;

	@NotNull
	private Date birthDate;

	@NotBlank
	private String marriageStatusCode;

	@NotBlank
	private String educationLevelCode;

	@NotNull
	@Min( 0 )
	private Integer childrenCount;

	private String nationalityCode;

	public PersonalBasicBean()
	{
		// default constructor
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public Integer getChildrenCount()
	{
		return childrenCount;
	}

	public String getEducationLevelCode()
	{
		return educationLevelCode;
	}

	public String getEngFirstName()
	{
		return engFirstName;
	}

	public String getEngLastName()
	{
		return engLastName;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMarriageStatusCode()
	{
		return marriageStatusCode;
	}

	public String getName()
	{
		return name;
	}

	public String getNationalityCode()
	{
		return nationalityCode;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setChildrenCount( Integer childrenCount )
	{
		this.childrenCount = childrenCount;
	}

	public void setEducationLevelCode( String educationLevelCode )
	{
		this.educationLevelCode = educationLevelCode;
	}

	public void setEngFirstName( String engFirstName )
	{
		this.engFirstName = engFirstName;
	}

	public void setEngLastName( String engLastName )
	{
		this.engLastName = engLastName;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMarriageStatusCode( String marriageStatusCode )
	{
		this.marriageStatusCode = marriageStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNationalityCode( String nationalityCode )
	{
		this.nationalityCode = nationalityCode;
	}

}
