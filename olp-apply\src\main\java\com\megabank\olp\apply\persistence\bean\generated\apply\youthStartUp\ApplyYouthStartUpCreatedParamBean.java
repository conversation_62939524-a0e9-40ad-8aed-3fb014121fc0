package com.megabank.olp.apply.persistence.bean.generated.apply.youthStartUp;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyYouthStartUpCreatedParamBean extends BaseBean
{
	private Long validatedIdentityId;

	private String caseNo;

	private String finalBranchBankCode;

	public ApplyYouthStartUpCreatedParamBean()
	{
		// default constructor
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

	public String getFinalBranchBankCode()
	{
		return finalBranchBankCode;
	}

	public void setFinalBranchBankCode( String finalBranchBankCode )
	{
		this.finalBranchBankCode = finalBranchBankCode;
	}
}
