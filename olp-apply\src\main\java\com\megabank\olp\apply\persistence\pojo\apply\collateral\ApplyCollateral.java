package com.megabank.olp.apply.persistence.pojo.apply.collateral;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeTransmissionStatus;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The ApplyCollateral is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "apply_collateral" )
public class ApplyCollateral extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "apply_collateral";

	public static final String COLLATERAL_ID_CONSTANT = "collateralId";

	public static final String CODE_BRANCH_BANK_CONSTANT = "codeBranchBank";

	public static final String CODE_TRANSMISSION_STATUS_CONSTANT = "codeTransmissionStatus";

	public static final String CONTRACT_NO_CONSTANT = "contractNo";

	public static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	public static final String PROVIDER_NAME_CONSTANT = "providerName";

	public static final String BORROWER_NAME_CONSTANT = "borrowerName";

	public static final String RESEND_CONSTANT = "resend";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String PDF_CONTENT_CONSTANT = "pdfContent";

	public static final String COMPLETED_DATE_CONSTANT = "completedDate";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String APPLY_COLLATERAL_AGREEMENT_CONSTANT = "applyCollateralAgreement";

	private Long collateralId;

	private transient CodeBranchBank codeBranchBank;

	private transient CodeTransmissionStatus codeTransmissionStatus;

	private String contractNo;

	private long validatedIdentityId;

	private String providerName;

	private String borrowerName;

	private int resend;

	private boolean notified;

	private transient ImmutableByteArray pdfContent;

	private Date completedDate;

	private Date updatedDate;

	private Date createdDate;

	private transient ApplyCollateralAgreement applyCollateralAgreement;

	public ApplyCollateral()
	{}

	public ApplyCollateral( CodeBranchBank codeBranchBank, CodeTransmissionStatus codeTransmissionStatus, String contractNo, long validatedIdentityId,
							String providerName, String borrowerName, int resend, boolean notified, Date updatedDate, Date createdDate )
	{
		this.codeBranchBank = codeBranchBank;
		this.codeTransmissionStatus = codeTransmissionStatus;
		this.contractNo = contractNo;
		this.validatedIdentityId = validatedIdentityId;
		this.providerName = providerName;
		this.borrowerName = borrowerName;
		this.resend = resend;
		this.notified = notified;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
	}

	public ApplyCollateral( Long collateralId )
	{
		this.collateralId = collateralId;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "applyCollateral" )
	public ApplyCollateralAgreement getApplyCollateralAgreement()
	{
		return applyCollateralAgreement;
	}

	@Column( name = "borrower_name", nullable = false )
	public String getBorrowerName()
	{
		return borrowerName;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "branch_bank_id", nullable = false )
	public CodeBranchBank getCodeBranchBank()
	{
		return codeBranchBank;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "transmission_status_code", nullable = false )
	public CodeTransmissionStatus getCodeTransmissionStatus()
	{
		return codeTransmissionStatus;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "collateral_id", unique = true, nullable = false )
	public Long getCollateralId()
	{
		return collateralId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "completed_date", length = 23 )
	public Date getCompletedDate()
	{
		return completedDate;
	}

	@Column( name = "contract_no", nullable = false, length = 20 )
	public String getContractNo()
	{
		return contractNo;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "pdf_content" ) ) } )
	public ImmutableByteArray getPdfContent()
	{
		return pdfContent;
	}

	@Column( name = "provider_name", nullable = false )
	public String getProviderName()
	{
		return providerName;
	}

	@Column( name = "resend", nullable = false, precision = 5, scale = 0 )
	public int getResend()
	{
		return resend;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "validated_identity_id", nullable = false )
	public long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	public void setApplyCollateralAgreement( ApplyCollateralAgreement applyCollateralAgreement )
	{
		this.applyCollateralAgreement = applyCollateralAgreement;
	}

	public void setBorrowerName( String borrowerName )
	{
		this.borrowerName = borrowerName;
	}

	public void setCodeBranchBank( CodeBranchBank codeBranchBank )
	{
		this.codeBranchBank = codeBranchBank;
	}

	public void setCodeTransmissionStatus( CodeTransmissionStatus codeTransmissionStatus )
	{
		this.codeTransmissionStatus = codeTransmissionStatus;
	}

	public void setCollateralId( Long collateralId )
	{
		this.collateralId = collateralId;
	}

	public void setCompletedDate( Date completedDate )
	{
		this.completedDate = completedDate;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setPdfContent( ImmutableByteArray pdfContent )
	{
		this.pdfContent = pdfContent;
	}

	public void setProviderName( String providerName )
	{
		this.providerName = providerName;
	}

	public void setResend( int resend )
	{
		this.resend = resend;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

	public void setValidatedIdentityId( long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}
}