package com.megabank.olp.apply.persistence.dao.mixed;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.persistence.bean.mixed.SurveyListGetterParamBean;
import com.megabank.olp.apply.persistence.dto.SurveyListDTO;
import com.megabank.olp.apply.persistence.pojo.apply.survey.ApplySurveyContact;
import com.megabank.olp.base.bean.PagingBean;
import com.megabank.olp.base.bean.threadlocal.PagingThreadLocalBean;
import com.megabank.olp.base.threadlocal.PagingThreadLocal;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class SurveyDAOIntegration
{
	@Autowired
	private PagingThreadLocal pagingThreadLocal;
	
	@Autowired
	private SurveyDAO dao;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void getLatestContactData()
	{
		Long otpTokenId = 1L;

		ApplySurveyContact pojo = dao.getLatestContactData( otpTokenId );

		logger.info( "pojo:{}", pojo );
	}

	@Test
	public void getList()
	{
		SurveyListGetterParamBean paramBean = new SurveyListGetterParamBean();

		List<SurveyListDTO> dtos = dao.getList( paramBean );

		logger.info( "dtos:{}", dtos );
	}

	@Test
	public void getPaging()
	{
		PagingThreadLocalBean localBean = new PagingThreadLocalBean();
		localBean.setStart( 0 );
		localBean.setLength( 10 );
		localBean.setSortColumn( "createdDate" );
		localBean.setSortDirection( "asc" );
		pagingThreadLocal.set( localBean );

		SurveyListGetterParamBean paramBean = new SurveyListGetterParamBean();

		PagingBean<SurveyListDTO> result = dao.getPaging( paramBean );

		logger.info( "data:{}", result.getData() );
		logger.info( "recordsFiltered:{}", result.getRecordsFiltered() );
		logger.info( "recordsTotal:{}", result.getRecordsTotal() );

	}

}
