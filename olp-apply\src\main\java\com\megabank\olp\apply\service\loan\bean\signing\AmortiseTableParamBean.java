package com.megabank.olp.apply.service.loan.bean.signing;

import java.util.ArrayList;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

//攤還表
public class AmortiseTableParamBean extends BaseBean
{
	//剩餘金額
	List<Double> loanAmt = new ArrayList<Double>();
	//本利和
	List<Double> compoundAmt = new ArrayList<Double>();
	//本金
	List<Double> principalAmt = new ArrayList<Double>();
	//利息
	List<Double> interestAmt = new ArrayList<Double>();
	//期數
	List<String> period = new ArrayList<String>();
	//總費用年百分率
	String totalCostYearRate;

	public List<Double> getLoanAmt() {
		return loanAmt;
	}

	public void setLoanAmt(List<Double> loanAmt) {
		this.loanAmt = loanAmt;
	}

	public List<Double> getCompoundAmt() {
		return compoundAmt;
	}

	public void setCompoundAmt(List<Double> compoundAmt) {
		this.compoundAmt = compoundAmt;
	}

	public List<Double> getPrincipalAmt() {
		return principalAmt;
	}

	public void setPrincipalAmt(List<Double> principalAmt) {
		this.principalAmt = principalAmt;
	}

	public List<Double> getInterestAmt() {
		return interestAmt;
	}

	public void setInterestAmt(List<Double> interestAmt) {
		this.interestAmt = interestAmt;
	}

	public List<String> getPeriod() {
		return period;
	}

	public void setPeriod(List<String> period) {
		this.period = period;
	}
	
	public String getTotalCostYearRate() {
		return totalCostYearRate;
	}

	public void setTotalCostYearRate(String totalCostYearRate) {
		this.totalCostYearRate = totalCostYearRate;
	}
}
