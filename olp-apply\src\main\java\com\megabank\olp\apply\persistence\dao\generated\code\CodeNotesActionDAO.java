package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeNotesAction;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeNotesActionDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeNotesActionDAO extends BasePojoDAO<CodeNotesAction, String>
{
	/**
	 *
	 * @param notesActionCode
	 * @return
	 */
	public CodeNotesAction read( String notesActionCode )
	{
		Validate.notNull( notesActionCode );
		return getPojoByPK( notesActionCode, CodeNotesAction.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeNotesAction> getPojoClass()
	{
		return CodeNotesAction.class;
	}
}
