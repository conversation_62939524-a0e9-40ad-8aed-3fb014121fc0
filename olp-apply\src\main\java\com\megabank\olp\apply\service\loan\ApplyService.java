package com.megabank.olp.apply.service.loan;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.megabank.olp.system.service.ConfigPropService;
import com.megabank.olp.system.utility.enums.ConfigPropEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.persistence.bean.generated.apply.address.ApplyAddrTextParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.address.ApplyAddressParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.attachment.ApplyAttachmentCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicUpdatedApplyCtrParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicUpdatedEngNameParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanBasicUpdatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContactInfoCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContactInfoUpdatedAddrTextParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContactInfoUpdatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContentCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanContentUpdatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanGuaranteeInfoCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanGuaranteeInfoUpdatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanOccupationCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanOccupationUpdatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanRelationCreatedParamBean;
import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanServedCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.apply.address.ApplyAddrTextDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.address.ApplyAddressDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.agreed.ApplyAgreedDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.agreed.ApplyAgreedItemDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.attachment.ApplyAttachmentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanBasicDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanContactInfoDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanContentDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanCounterDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanGuaranteeInfoDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanOccupationDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanRelationDAO;
import com.megabank.olp.apply.persistence.dao.generated.apply.loan.ApplyLoanServedDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankLoanDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeEducationLevelDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeJobSubTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeJobTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeListDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanPeriodDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanPlanDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanPurposeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanVersionDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeMarriageStatusDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeNationalityDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeResidenceStatusDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTitleTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeUserSubTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.identity.IdentityIloanWhitelistDAO;
import com.megabank.olp.apply.persistence.dao.mixed.AgreedDAO;
import com.megabank.olp.apply.persistence.dao.mixed.LoanDAO;
import com.megabank.olp.apply.persistence.dto.ApplyListDTO;
import com.megabank.olp.apply.persistence.pojo.apply.address.ApplyAddress;
import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreed;
import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreedItem;
import com.megabank.olp.apply.persistence.pojo.apply.attachment.ApplyAttachment;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanBasic;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContactInfo;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanContent;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanGuaranteeInfo;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanOccupation;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanRelation;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanServed;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBankLoan;
import com.megabank.olp.apply.persistence.pojo.code.CodeList;
import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPeriod;
import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPlan;
import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPurpose;
import com.megabank.olp.apply.persistence.pojo.code.CodeRecipientSystem;
import com.megabank.olp.apply.persistence.pojo.code.CodeUserSubType;
import com.megabank.olp.apply.service.loan.bean.apply.AgreedBean;
import com.megabank.olp.apply.service.loan.bean.apply.AgreedItemBean;
import com.megabank.olp.apply.service.loan.bean.apply.AgreementResBean;
import com.megabank.olp.apply.service.loan.bean.apply.BorrowerInfoResBean;
import com.megabank.olp.apply.service.loan.bean.apply.BranchBankResBean;
import com.megabank.olp.apply.service.loan.bean.apply.CreateResBean;
import com.megabank.olp.apply.service.loan.bean.apply.IxmlMydataDisplayedBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanApplyResBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanApplySubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanContentParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanContentResBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanRelationParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanRelationResBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanServedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.LoanServedResBean;
import com.megabank.olp.apply.service.loan.bean.apply.MessageResBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalBasicParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalBasicResBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalContactParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalContactResBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalGuaranteeParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalGuaranteeResBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalInfoSubmittedParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalJobParamBean;
import com.megabank.olp.apply.service.loan.bean.apply.PersonalJobResBean;
import com.megabank.olp.apply.utility.ApplyLoanUtils;
import com.megabank.olp.apply.utility.BaseApplyService;
import com.megabank.olp.apply.utility.enums.ApplyErrorEnum;
import com.megabank.olp.apply.utility.enums.ApplyStatusEnum;
import com.megabank.olp.apply.utility.enums.BillhunterMailTypeEnum;
import com.megabank.olp.apply.utility.enums.IdentityFlagEnum;
import com.megabank.olp.apply.utility.enums.ProcessStatusEnum;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.enums.CommonErrorEnum;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.enums.LoanPlanEnum;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.RecipientSystemEnum;
import com.megabank.olp.base.enums.UserSubTypeEnum;
import com.megabank.olp.base.enums.UserTypeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.base.utility.date.CommonDateUtils;
import com.megabank.olp.base.utility.date.CommonTimeUtils;
import com.megabank.olp.base.utility.text.CommonBase64Utils;
import com.megabank.olp.client.sender.billhunter.bean.AttachmentFileBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderArgBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderResultBean;
import com.megabank.olp.client.sender.billhunter.bean.ToMailInfoBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoResultBean;
import com.megabank.olp.client.sender.micro.identity.IdentityAttachmentsClient;
import com.megabank.olp.client.sender.micro.identity.bean.IdentityAttachmentResultBean;
import com.megabank.olp.client.sender.micro.identity.bean.IdentityAttachmentsArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalCustInfoResultBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalPbAccountResultBean;
import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoResultBean;
import com.megabank.olp.client.service.billhunter.BillhunterSenderService;
import com.megabank.olp.client.service.common.UserClientService;
import com.megabank.olp.client.service.eloan.EloanSenderService;
import com.megabank.olp.client.service.iloan.SenderILoanService;
import com.megabank.olp.client.service.otherdatabase.OtherDataBaseClientService;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Transactional
public class ApplyService extends BaseApplyService
{

	private static final String APPLY_DATE_PATTERN = "yyyy年MM月dd日HH時mm分";

	private static final String APPLY_DATE_PATTERN_ONLY_DATE_1 = "yyyy年MM月dd日";

	private static final String APPLY_DATE_PATTERN_ONLY_DATE_2 = "yyyy/MM/dd";

	private static final String APPLY_DATE_PATTERN_ONLY_DATE_3 = "yyyy/MM/dd HH:mm";

	private static final int DAY_DIFF_VALUE = 180;

	@Autowired
	private SystemService systemService;

	@Autowired
	private UserClientService userClientService;

	@Autowired
	private GenerateService generateService;

	@Autowired
	private DeliverService deliverService;

	@Autowired
	private IdentityAttachmentsClient identityAttachmentsClient;

	@Autowired
	private BillhunterSenderService billhunterSenderService;

	@Autowired
	private OtherDataBaseClientService otherDataBaseClientService;

	@Autowired
	private EloanSenderService eloanSenderService;

	@Autowired
	private SenderILoanService iloanSenderService;

	@Autowired
	private LoanDAO loanDAO;

	@Autowired
	private AgreedDAO agreedDAO;

	@Autowired
	private ApplyAddressDAO applyAddressDAO;

	@Autowired
	private ApplyAddrTextDAO applyAddrTextDAO;

	@Autowired
	private ApplyAgreedDAO applyAgreedDAO;

	@Autowired
	private ApplyAgreedItemDAO applyAgreedItemDAO;

	@Autowired
	private ApplyAttachmentDAO applyAttachmentDAO;

	@Autowired
	private ApplyLoanDAO applyLoanDAO;

	@Autowired
	private ApplyLoanBasicDAO applyLoanBasicDAO;

	@Autowired
	private ApplyLoanContactInfoDAO applyLoanContactInfoDAO;

	@Autowired
	private ApplyLoanGuaranteeInfoDAO applyLoanGuaranteeInfoDAO;

	@Autowired
	private ApplyLoanOccupationDAO applyLoanOccupationDAO;

	@Autowired
	private ApplyLoanContentDAO applyLoanContentDAO;

	@Autowired
	private ApplyLoanCounterDAO applyLoanCounterDAO;

	@Autowired
	private ApplyLoanRelationDAO applyLoanRelationDAO;

	@Autowired
	private ApplyLoanServedDAO applyLoanServedDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeBranchBankLoanDAO codeBranchBankLoanDAO;

	@Autowired
	private CodeLoanPeriodDAO codeLoanPeriodDAO;

	@Autowired
	private CodeLoanPlanDAO codeLoanPlanDAO;

	@Autowired
	private CodeLoanPurposeDAO codeLoanPurposeDAO;

	@Autowired
	private CodeLoanVersionDAO codeLoanVersionDAO;

	@Autowired
	private CodeJobTypeDAO codeJobTypeDAO;

	@Autowired
	private CodeJobSubTypeDAO codeJobSubTypeDAO;

	@Autowired
	private CodeUserSubTypeDAO codeUserSubTypeDAO;

	@Autowired
	private CodeMarriageStatusDAO codeMarriageStatusDAO;

	@Autowired
	private CodeEducationLevelDAO codeEducationLevelDAO;

	@Autowired
	private CodeNationalityDAO codeNationalityDAO;

	@Autowired
	private CodeResidenceStatusDAO codeResidenceStatusDAO;

	@Autowired
	private CodeTitleTypeDAO codeTitleTypeDAO;

	@Autowired
	private CodeListDAO codeListDAO;

	@Autowired
	private IdentityIloanWhitelistDAO identityIloanWhitelistDAO;

	@Autowired
	private ConfigPropService configPropService;

	@Value( "${all.api.server}" )
	private String whichAPIServer;

	@Value( "${iloan.ixml.hidden}" )
	private boolean iloanIxmlHidden;

	@Value( "${iloan.mydata.hidden}" )
	private boolean iloanMydataHidden;

	@Value( "${iloan.whitelist.enable}" )
	private boolean iloanWhitelistEnable;

	/**
	 * 回到基本申請頁狀態
	 *
	 * @param loanType
	 * @return
	 */
	public Long backToPersonalInfo( String loanType )
	{
		ApplyLoan applyLoan = getApplyLoan( loanType );

		checkStepIsCorrect( applyLoan.getCodeApplyStatus().getApplyStatusCode(),
							Arrays.asList( ApplyStatusEnum.COMPLETE_BASIC.getContext(), ApplyStatusEnum.COMPLETE_LOAN.getContext() ) );

		String applyStatus = ApplyStatusEnum.COMPLETE_AGREED.getContext();

		return applyLoanDAO.updateApplyStatus( applyLoan.getLoanId(), applyStatus );
	}

	/**
	 * 檢查是否可申請 ( T:可申請/ F:不可申請)
	 *
	 * @param loanType
	 * @return
	 */
	public boolean checkApplyCount( String loanType )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		String userType = identityInfo.getUserType();

		return checkApplyCount( getIdNo(), getBirthDate(), loanType, userType );
	}

	/**
	 * 檢查案件是否提供保證人申請
	 *
	 * @param caseNo
	 * @param loanType
	 * @return
	 */
	public boolean checkGuarantorAvailable( String caseNo )
	{
		List<ApplyLoan> applyLoan = loanDAO.getPojosByBorrowerInfo( caseNo, null, null, null, null );

		if( applyLoan.isEmpty() )
			throw new MyRuntimeException( ApplyErrorEnum.NO_APPLY_RESULT );

		// iloan 信貸件無法提供保證人申請
		if( !Objects.isNull( applyLoan.get( 0 ).getCodeRecipientSystem() ) && applyLoan.get( 0 ).getCodeRecipientSystem().getSystemId() == 2 )
			return false;

		return true;
	}

	/**
	 * 檢查是否有借款案件 ( T:有案件/ F:無案件)
	 *
	 * @param caseNo
	 * @param name
	 * @param idNo
	 * @param mobileNumber
	 * @return
	 */
	public boolean checkLoanApplyExisted( String caseNo, String name, String idNo, String mobileNumber, String loanType )
	{
		List<ApplyLoan> applyLoan = loanDAO.getPojosByBorrowerInfo( caseNo, name, idNo, mobileNumber, loanType );

		if( applyLoan.isEmpty() )
			return false;

		return applyLoan.get( 0 ).getApplyLoanContent() != null;
	}

	/**
	 * 檢查 plan 的狀態 {0:有效的行銷方案, 1:失效的行銷方案, -1:不存在的行銷方案} <br/>
	 * 或是用 throw new MyRuntimeException( ApplyErrorEnum.NOT_ACTIVE_LOAN_PLAN );
	 *
	 * @param plan
	 * @return
	 */
	public int checkLoanPlanCode_cmp_endTs( String plan )
	{
		if( StringUtils.isBlank( plan ) )
			return 0;

		CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( plan );

		if( codeLoanPlan == null )
			throw new MyRuntimeException( ApplyErrorEnum.NOT_ACTIVE_LOAN_PLAN );

		Timestamp currentTs = new java.sql.Timestamp( new Date().getTime() );
		if( currentTs.after( codeLoanPlan.getEndTs() ) )
			if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( plan ) )
			throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01017_LOAN_PLAN_EXCEED_ENDTS,
										  new String[]{ build_msg_APPLY01017_ChinaSteelGroup_BatchPersonalLoan( codeLoanPlan ) } );
			else
			throw new MyRuntimeException( ApplyErrorEnum.LOAN_PLAN_EXCEED_ENDTS, new String[]{ codeLoanPlan.getName() } );

		return 0;
	}

	/**
	 * 確認同意事項
	 *
	 * @param loanType
	 * @param itemIds
	 * @return
	 */
	public Long confirmAgreement( String loanType, List<Long> itemIds )
	{
		return confirmAgreement( loanType, itemIds, null, null, null, null );
	}

	public Long confirmAgreement( String loanType, List<Long> itemIds, Boolean notUsTaxpayer, Boolean notOuttwTaxpayer, String rateAdjNotify,
								  Boolean crossMarketing )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		String userType = identityInfo.getUserType();
		String identityType = identityInfo.getIdentityType();

		ApplyLoan applyLoan = getApplyLoan( identityInfo.getValidatedIdentityId(), loanType );

		checkStepIsCorrect( applyLoan.getCodeApplyStatus().getApplyStatusCode(), Arrays.asList( ApplyStatusEnum.COMPLETE_CREATED.getContext() ) );

		checkItemsChecked( userType, identityType, loanType, itemIds );

		String loanVersion = codeLoanVersionDAO.read( loanType ).getLoanVersion();

		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( applyLoan.getLoanPlanCode() ) )
		{
			ApplyLoanBasicUpdatedApplyCtrParamBean applyCtrParamBean = new ApplyLoanBasicUpdatedApplyCtrParamBean();
			applyCtrParamBean.setLoanId( applyLoan.getLoanId() );
			applyCtrParamBean.setNotUsTaxpayer( notUsTaxpayer );
			applyCtrParamBean.setNotOuttwTaxpayer( notOuttwTaxpayer );
			applyCtrParamBean.setRateAdjNotify( rateAdjNotify );
			applyCtrParamBean.setCrossMarketing( crossMarketing );
			applyLoanBasicDAO.updateWhenHasApplyCtr( applyCtrParamBean );
		}

		return applyLoanDAO.updateAgreedDate( applyLoan.getLoanId(), ApplyStatusEnum.COMPLETE_AGREED.getContext(), loanVersion );
	}

	/**
	 * 確認完成申請
	 *
	 * @param loanType
	 * @return
	 * @throws IOException
	 */
	public void confirmFilledInfo( String loanType )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();

		ApplyLoan applyLoan = getApplyLoan( identityInfo.getValidatedIdentityId(), loanType );

		Long loanId = applyLoan.getLoanId();

		if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			if( StringUtils.equals( applyLoan.getLoanPlanCode(), LoanPlanEnum.C001.getContext() ) )
			{
				// 中鋼消貸總公司員工只會在第1頁勾完，就送出
				if( applyLoan.getAgreedDate() == null || StringUtils.isBlank( applyLoan.getLoanVersion() ) )
					throw new MyRuntimeException( ApplyErrorEnum.APPLY_STEP_ERROR );
			}
			else
				checkStepIsCorrect( applyLoan.getCodeApplyStatus().getApplyStatusCode(),
									Arrays.asList( ApplyStatusEnum.COMPLETE_LOAN.getContext() ) );
		else
			checkStepIsCorrect( applyLoan.getCodeApplyStatus().getApplyStatusCode(), Arrays.asList( ApplyStatusEnum.COMPLETE_BASIC.getContext() ) );

		checkIsSameUser( applyLoan.getApplyLoanBasic().getIdNo(), applyLoan.getApplyLoanBasic().getBirthDate() );

		checkCanApply( loanType, identityInfo.getUserType() );

		String loanPlanCode = applyLoan.getLoanPlanCode();
		if( StringUtils.isNotBlank( loanPlanCode ) && applyLoan.getRefBorrowerApplyLoan() == null )
		{
			// 客戶通過身分驗證之後，若在操作畫面停留了10分鐘才按下「送出」，一樣是逾越「行銷方案」的期限
			// 在最後送出的時候，再檢核一次期限
			if( checkLoanPlanCode_cmp_endTs( applyLoan.getLoanPlanCode() ) != 0 )
				throw new MyRuntimeException( ApplyErrorEnum.NOT_ACTIVE_LOAN_PLAN );
			// ==========================================
			ApplyLoanOccupation applyLoanOccupation = applyLoan.getApplyLoanOccupation();
			String occupationTaxNo = StringUtils.trim( applyLoanOccupation.getTaxNo() );
			if( ApplyLoanUtils.validate_loanPlan_OccupationTaxNo( loanPlanCode, occupationTaxNo ) == false )
				throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01019_DESCRIPTION,
											  new String[]{ "服務單位統一編號(" + occupationTaxNo + ") 與行銷方案(" + loanPlanCode + ")檢核失敗" } );
			// ===========
			if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
			{

			}
			else if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode ) )
			{

				ApplyLoanContent applyLoanContent = applyLoan.getApplyLoanContent();
				if( StringUtils.isBlank( applyLoanContent.getAppnDpAcct() ) )
					throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01019_DESCRIPTION, new String[]{ "申貸內容>撥款帳戶 不可空白" } );

				if( applyLoanContent.getRestrictContr() == null )
					throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01019_DESCRIPTION, new String[]{ "申貸內容>是否綁約 不可空白" } );
			}

		}

		if( IdentityTypeEnum.OTHER_BANK.getContext().equals( getCurrentIdentityType() ) )
			saveIdentityAttachment( applyLoan.getLoanId(), identityInfo.getRefIdentityId(), identityInfo.getValidatedIdentityId() );

		// =========================
		// 將 dbo.apply_loan_contact_info.branch_bank_id 的值，寫入
		// dbo.apply_loan.final_branch_bank_id
		// 之後可能「改派」其它分行
		// 用 LoanApplyService.java 裡的 updateBranchBank( Long loanId, Long branchBankId,
		// String employeeId, String employeeName )
		Long finalBranchBankId = getFinalBranchBankId( applyLoan );

		if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( applyLoan.getCodeLoanType().getLoanType() ) )
		{
			Long chose_branchBankId = finalBranchBankId;

			// J-110-0543 消貸 {主借人} 進件都統一派到 229-大安分行, {保證人}進件時則跟著主借人
			if( applyLoan.getRefBorrowerApplyLoan() == null )
			{
				boolean concentrated_to_229_br = true;
				if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
					concentrated_to_229_br = false;
				else if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode ) )
					concentrated_to_229_br = false;
				else if( ApplyLoanUtils.is_co80601119_D001_BatchPersonalLoan( loanPlanCode ) )
					concentrated_to_229_br = false; // J-111-0246 采鈺科技認股案，直接派到 020分行

				if( concentrated_to_229_br )
				{
					CodeBranchBank codeBranchBank_229 = codeBranchBankDAO.getPojoByBankCode( "229" );
					if( codeBranchBank_229 != null )
						chose_branchBankId = codeBranchBank_229.getBranchBankId();
				}
			}
			applyLoanDAO.updateFinalBranchBank_and_introduceBrNo( applyLoan.getLoanId(), chose_branchBankId,
																  choose_PersonalLoan_introduceBrNo( applyLoan, finalBranchBankId ) );
		}
		else
			applyLoanDAO.updateFinalBranchBank( applyLoan.getLoanId(), finalBranchBankId );

		applyLoanDAO.updateApplyCompletedDate( loanId, new Date() );

	}

	/**
	 * 建立新申請案件
	 *
	 * @param loanType
	 * @param refCaseNo
	 * @return
	 */
	public CreateResBean createLoanApply( String loanType, String refCaseNo, String plan, String introduceBrNo )
	{
		CreateResBean createResBean = new CreateResBean();
		checkCitizen( getIdNo() );

		checkLegalAge( getBirthDate() );

		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		String userType = identityInfo.getUserType();

		checkUserType( userType );

		checkCanApply( loanType, userType );

		if( codeBranchBankDAO.getPojoByBankCode( introduceBrNo, true ) != null )
			codeBranchBankDAO.getPojoByBankCode( introduceBrNo );
		else
			introduceBrNo = "943";

		EloanCustInfoResultBean iloanCustInfo = null;

		if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) && UserTypeEnum.BORROWER.getContext().equals( userType ) )
		{
			iloanCustInfo = getIloanCustInfo( getIdNo(), getBirthDate() );

			if( !Objects.isNull( iloanCustInfo )
				&& ( !Objects.isNull( iloanCustInfo.getAllowApplyILoan() ) && iloanCustInfo.getAllowApplyILoan() == false ) )
			{
				String latestApplyDate =
									   getLatestApplyDateIn30Days( LoanTypeEnum.PERSONAL_LOAN.getContext(), RecipientSystemEnum.ILOAN.getSystemId() );
				createResBean.setProcessing( true );
				createResBean.setApplyCompletedDate( latestApplyDate );
				return createResBean;
			}
		}

		if( UserTypeEnum.BORROWER.getContext().equals( userType ) )
			// 檢核
			checkLoanPlanCode( getIdNo(), getBirthDate(), loanType, plan, iloanCustInfo );

		if( !UserTypeEnum.BORROWER.getContext().equals( userType ) )
			checkCaseIsValid( refCaseNo );

		Long loanId = createLoanData( identityInfo, loanType, refCaseNo, plan, introduceBrNo, iloanCustInfo );
		createResBean.setProcessing( false );
		createResBean.setLoanId( loanId );
		return createResBean;
	}

	/**
	 * 上傳檔案送eloan
	 *
	 * @param applyAttachments
	 */
	public void deliverAttachment( Set<ApplyAttachment> applyAttachments )
	{
		for( ApplyAttachment applyAttachment : applyAttachments )
		{
			boolean submitSuccess = deliverService.submitAttachment( applyAttachment );

			String transmissionStatusCode = submitSuccess ? TransmissionStatusEnum.COMPLETED.getContext()
														  : TransmissionStatusEnum.EXCEPTION.getContext();

			applyAttachmentDAO.updateTransmissionStatus( applyAttachment.getAttachmentId(), transmissionStatusCode );
		}
	}

	/**
	 * 申請案件送 eloan or iloan
	 *
	 * @param applyLoan
	 */
	public void deliverLoan( ApplyLoan applyLoan )
	{
		boolean submitSuccess = LoanTypeEnum.PERSONAL_LOAN.getContext().equals( applyLoan.getCodeLoanType().getLoanType() ) ? deliverService
					.submitPersonalLoanApply( applyLoan ) : deliverService.submitHouseLoanApply( applyLoan );

		String transmissionStatusCode = submitSuccess ? TransmissionStatusEnum.COMPLETED.getContext() : TransmissionStatusEnum.EXCEPTION.getContext();

		applyLoanDAO.updateTransmissionStatus( applyLoan.getLoanId(), transmissionStatusCode );
	}

	/**
	 * ilaon 驗證未過，轉送案件至 eloan
	 *
	 * @param applyLoan
	 */
	public void deliverLoan2Eloan( ApplyLoan applyLoan )
	{
		boolean submitSuccess = LoanTypeEnum.PERSONAL_LOAN.getContext().equals( applyLoan.getCodeLoanType().getLoanType() ) ? deliverService
					.submitPersonalLoanApply2Eloan( applyLoan ) : deliverService.submitHouseLoanApply( applyLoan );

		String transmissionStatusCode = submitSuccess ? TransmissionStatusEnum.COMPLETED.getContext() : TransmissionStatusEnum.EXCEPTION.getContext();

		applyLoanDAO.updateTransmissionStatus( applyLoan.getLoanId(), transmissionStatusCode );
	}

	/**
	 * 取得同意事項
	 *
	 * @param loanType
	 * @return
	 */
	public AgreementResBean getAgreement( String loanType )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		String identityType = identityInfo.getIdentityType();
		String userType = identityInfo.getUserType();

		checkUserType( userType );

		List<ApplyAgreed> agreedList = applyAgreedDAO.getApplyServiceAgreeds( userType, identityType, loanType );

		return mapAgreementResBean( agreedList );
	}

	/**
	 * 取得借款人資訊
	 *
	 * @param loanType
	 * @return
	 */
	public BorrowerInfoResBean getBorrowerInfo( String loanType )
	{
		ApplyLoan applyLoan = getApplyLoan( loanType );

		ApplyLoan refBorrowerApplyLoan = applyLoan.getRefBorrowerApplyLoan();

		BorrowerInfoResBean resBean = new BorrowerInfoResBean();

		if( refBorrowerApplyLoan == null )
			return resBean;

		resBean.setPersonalBasicResBean( mapPersonalBasicInfo( refBorrowerApplyLoan, refBorrowerApplyLoan.getApplyLoanBasic() ) );
		resBean.setPersonalContactResBean( mapLoanContactInfo( refBorrowerApplyLoan.getApplyLoanContactInfo(), null ) );
		resBean.setPersonalJobResBean( mapLoanJobInfo( refBorrowerApplyLoan.getApplyLoanOccupation() ) );
		resBean.setLoanContentResBean( mapLoanApplyContent( refBorrowerApplyLoan.getApplyLoanContent() ) );
		resBean.setLoanRelationResBeans( mapLoanApplyRelationResBean( refBorrowerApplyLoan.getApplyLoanContent().getApplyLoanRelations() ) );
		resBean.setLoanServedResBeans( mapLoanApplyServedResBean( refBorrowerApplyLoan.getApplyLoanContent().getApplyLoanServeds() ) );

		return resBean;
	}

	/**
	 * 取得貸款戶派案分行
	 *
	 * @return
	 */
	public List<BranchBankResBean> getBranchBankList()
	{
		List<String> loaneeBankCodes = getLoaneeBankCode( getIdNo() );

		List<BranchBankResBean> resBeans = new ArrayList<>();

		if( loaneeBankCodes.isEmpty() )
			return resBeans;

		List<CodeBranchBank> codeBranchBanks = codeBranchBankDAO.getAllPojos();

		List<String> bankCodes = codeBranchBanks.stream().map( CodeBranchBank::getBankCode ).collect( Collectors.toList() );

		if( !bankCodes.containsAll( loaneeBankCodes ) )
			throw new MyRuntimeException( ApplyErrorEnum.LOANEE_BANK_CODE_ERROR );

		for( CodeBranchBank codeBranchBank : codeBranchBanks )
		{
			String code = codeBranchBank.getBankCode();
			if( loaneeBankCodes.contains( code ) )
			{
				BranchBankResBean resBean = new BranchBankResBean();
				resBean.setCode( codeBranchBank.getBranchBankId().toString() );
				resBean.setName( codeBranchBank.getName() );
				resBean.setAddress( codeBranchBank.getAddress() );

				resBeans.add( resBean );
			}
		}

		return resBeans;
	}

	/**
	 * 取得最近完成申請日期
	 *
	 * @param loanType
	 * @return
	 */
	public String getLatestApplyDate( String loanType )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		String idNo = identityInfo.getIdNo();
		Date birthDate = identityInfo.getBirthDate();

		List<Long> identityIds = loanDAO.getCompletedIdentityIdsIn7Days( idNo, birthDate, loanType );

		if( identityIds.isEmpty() )
			return null;

		List<String> userTypes = userClientService.getUserTypesResult( identityIds );

		if( !userTypes.contains( identityInfo.getUserType() ) )
			return null;

		ApplyLoan applyLoan = applyLoanDAO.getLatestPojoIn7Days( identityIds, loanType );

		if( applyLoan == null )
			return null;

		return CommonDateStringUtils.transDate2String( applyLoan.getApplyCompletedDate(), APPLY_DATE_PATTERN );
	}

	/**
	 * 取得最近完成申請日期
	 *
	 * @param loanType
	 * @param loanRecipientId
	 * @return
	 */
	public String getLatestApplyDateIn30Days( String loanType, Integer loanRecipientId )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		String idNo = identityInfo.getIdNo();
		Date birthDate = identityInfo.getBirthDate();

		List<Long> identityIds = loanDAO.getCompletedIdentityIdIn30Days( idNo, birthDate, loanType, loanRecipientId );

		if( identityIds.isEmpty() )
			return null;

		List<String> userTypes = userClientService.getUserTypesResult( identityIds );

		if( !userTypes.contains( identityInfo.getUserType() ) )
			return null;

		ApplyLoan applyLoan = applyLoanDAO.getLatestPojo( identityIds, loanType, loanRecipientId );

		if( applyLoan == null )
			return null;

		return CommonDateStringUtils.transDate2String( applyLoan.getApplyCompletedDate(), APPLY_DATE_PATTERN );
	}

	/**
	 * 取得申貸頁內容
	 *
	 * @param loanType
	 * @return
	 */
	public LoanApplyResBean getLoanApplyInfo( String loanType )
	{
		ApplyLoan applyLoan = getApplyLoan( loanType );

		ApplyLoanContent content = applyLoan.getApplyLoanContent();

		LoanApplyResBean resBean = new LoanApplyResBean();
		resBean.setLoanContentResBean( mapLoanApplyContent( content ) );

		if( content != null )
		{
			resBean.setLoanRelationResBeans( mapLoanApplyRelationResBean( content.getApplyLoanRelations() ) );
			resBean.setLoanServedResBeans( mapLoanApplyServedResBean( content.getApplyLoanServeds() ) );
		}

		boolean fetch_PbAccountList = false;
		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( applyLoan.getLoanPlanCode() )
			&& !StringUtils.equals( applyLoan.getLoanPlanCode(), LoanPlanEnum.C001.getContext() ) )
			fetch_PbAccountList = true;

		if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( applyLoan.getLoanPlanCode() ) )
			fetch_PbAccountList = true;

		if( fetch_PbAccountList )
		{
			IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
			String idNo = identityInfo.getIdNo();
			Date birthDate = identityInfo.getBirthDate();

			// 中鋼消貸，子公司只限{撥款}到{實帳戶}
			String include_currTWD = "Y";
			String include_currNotTWD = "N";
			String include_digitalAccount_promoted = "Y";
			String include_digitalAccount_unpromoted = "N";

			InternalPbAccountResultBean pbAccountResultBean = otherDataBaseClientService
						.getPbAccount( idNo, birthDate, include_currTWD, include_currNotTWD, include_digitalAccount_promoted,
									   include_digitalAccount_unpromoted );
			List<String> src_list = pbAccountResultBean.getAccountList();
			Set<String> exclude_cond_arr = new HashSet<>();
			exclude_cond_arr.add( "09" );
			exclude_cond_arr.add( "64" );

			Set<String> include_cond_arr = new HashSet<>();
			include_cond_arr.add( "07" );
			include_cond_arr.add( "08" );
			include_cond_arr.add( "10" );
			include_cond_arr.add( "13" );
			include_cond_arr.add( "60" );
			include_cond_arr.add( "61" );
			include_cond_arr.add( "62" );
			include_cond_arr.add( "63" );
			include_cond_arr.add( "65" );
			resBean.setPbAccountList( filter_account_include( filter_account_exclude( src_list, exclude_cond_arr ), include_cond_arr ) );
		}
		return resBean;
	}

	public Long getLoanId( String loanType )
	{
		Long loanId = getLatestLoanIdIn6Months( getIdNo(), getBirthDate(), loanType );

		if( loanId == null )
			throw new MyRuntimeException( ApplyErrorEnum.NO_RESULT_TO_UPLOAD );

		return loanId;
	}

	public String getLoanPlanDesc( String plan )
	{
		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( plan ) )
		{
			CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( plan );

			if( codeLoanPlan != null )
			{
				String display_name = codeLoanPlan.getCompanyName();
				if( StringUtils.isNotBlank( codeLoanPlan.getPlanDesc() ) )
					display_name = codeLoanPlan.getPlanDesc();
				// ==================
				return display_name + "申貸期間 " + CommonDateStringUtils.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 )
					+ " ~ " + CommonDateStringUtils.transDate2String( codeLoanPlan.getEndTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 );
			}
		}
		else
		{
			HashSet<String> check_loanPlanCode = new HashSet<>();
			check_loanPlanCode.add( LoanPlanEnum.C101.getContext() );
			check_loanPlanCode.add( LoanPlanEnum.D001.getContext() );

			if( check_loanPlanCode.contains( plan ) )
			{
				CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( plan );

				if( codeLoanPlan != null )
				{
					String display_name = codeLoanPlan.getCompanyName();
					if( StringUtils.isNotBlank( codeLoanPlan.getPlanDesc() ) )
						display_name = codeLoanPlan.getPlanDesc();
					// ==================
					return display_name + "申貸期間 " + CommonDateStringUtils.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 )
						+ " ~ " + CommonDateStringUtils.transDate2String( codeLoanPlan.getEndTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 );
				}
			}

		}

		return "";

	}

	public List<String> getLoanPlanDescList( String plan )
	{
		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( plan ) )
		{
			CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( plan );

			if( codeLoanPlan != null )
			{
				String display_name = codeLoanPlan.getCompanyName();
				if( StringUtils.isNotBlank( codeLoanPlan.getPlanDesc() ) )
					display_name = codeLoanPlan.getPlanDesc();
				// ==================
				String line1 = display_name + " 消費性貸款專案";
				String line2 = "申貸期間:" + CommonDateStringUtils.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_3 ) + " ~ "
					+ CommonDateStringUtils.transDate2String( codeLoanPlan.getEndTs(), APPLY_DATE_PATTERN_ONLY_DATE_3 );
				return _returnList( line1, line2 );
			}
		}
		else if( StringUtils.isNotBlank( plan ) )
		{
			CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( plan );

			if( codeLoanPlan != null )
			{
				String display_name = codeLoanPlan.getCompanyName();
				if( StringUtils.isNotBlank( codeLoanPlan.getPlanDesc() ) )
					display_name = codeLoanPlan.getPlanDesc();
				// ==================
				String line1 = display_name;
				String line2 = "申貸期間:" + CommonDateStringUtils.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 ) + " ~ "
					+ CommonDateStringUtils.transDate2String( codeLoanPlan.getEndTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 );
				return _returnList( line1, line2 );
			}
		}

		return _returnList( "", "" );

	}

	/**
	 * 取得個人基本資訊內容
	 *
	 * @param loanType
	 * @return
	 */
	public PersonalBasicResBean getPersonalBasicInfo( String loanType )
	{
		ApplyLoan applyLoan = getApplyLoan( loanType );

		PersonalBasicResBean resBean = mapPersonalBasicInfo( applyLoan, applyLoan.getApplyLoanBasic() );
		resBean.setIdentities( getIdentities( applyLoan.getIdentityFlag() ) );

		return resBean;
	}

	/**
	 * 取得個人聯絡資訊內容
	 *
	 * @param loanType
	 * @return
	 */
	public PersonalContactResBean getPersonalContactInfo( String loanType )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();

		ApplyLoan applyLoan = getApplyLoan( identityInfo.getValidatedIdentityId(), loanType );

		PersonalContactResBean resBean = applyLoan.getApplyLoanContactInfo() == null ? mapLoanContactInfo( identityInfo.getMobileNumber() )
																					 : mapLoanContactInfo( applyLoan.getApplyLoanContactInfo(),
																										   applyLoan.getIntroduceBr1st() );

		if( applyLoan.getRefBorrowerApplyLoan() != null && StringUtils.isBlank( resBean.getBranchBankCode() ) )
			setGuaranteeBranchBank( resBean, applyLoan.getRefBorrowerApplyLoan() );

		return resBean;
	}

	/**
	 * 取得保證人填寫內容
	 *
	 * @param loanType
	 * @return
	 */
	public PersonalGuaranteeResBean getPersonalGuaranteeInfo( String loanType )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();

		ApplyLoan applyLoan = getApplyLoan( identityInfo.getValidatedIdentityId(), loanType );

		return mapGuaranteeInfo( applyLoan.getApplyLoanGuaranteeInfo(), identityInfo );
	}

	/**
	 * 取得個人職業資訊內容
	 *
	 * @param loanType
	 * @return
	 */
	public PersonalJobResBean getPersonalJobInfo( String loanType )
	{
		ApplyLoan applyLoan = getApplyLoan( loanType );

		return mapLoanJobInfo( applyLoan.getApplyLoanOccupation() );
	}

	/**
	 * 取得感謝頁內容
	 *
	 * @param loanType
	 * @return
	 */
	public MessageResBean getThankyouMessage( String loanType )
	{
		MessageResBean resBean = new MessageResBean();
		resBean.setTitle( "感謝您的申請" );
		resBean.setContent( getThankyouContent( loanType ) );

		return resBean;
	}

	/**
	 * 是否隱藏ixml、mydata選項
	 *
	 * @param loanType
	 * @return
	 */
	public IxmlMydataDisplayedBean isIxmlMydataDisplayed( String loanType )
	{
		// 若為 iloan 件且開關開啟，則隱藏 ixml 或 mydata
		boolean ixmlDisplayed = true;
		boolean mydataDisplayed = true;
		if( LoanTypeEnum.YOUTH_START_UP.getContext().equals( loanType ) )
		{
			ixmlDisplayed = false;
			mydataDisplayed = false;
			IxmlMydataDisplayedBean resBean = new IxmlMydataDisplayedBean();
			resBean.setIxmlDisplayed( ixmlDisplayed );
			resBean.setMydataDisplayed( mydataDisplayed );
			return resBean;
		}
		else
		{
			IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
			ApplyLoan applyLoan = applyLoanDAO.getPojoByIdentityAndLoanType( identityInfo.getValidatedIdentityId(), loanType );

			if( applyLoan == null )
			{
				Long loanId = getLoanId( loanType );
				applyLoan = applyLoanDAO.getPojoByPkAndLoanType( loanId, loanType );
			}

			if( iloanIxmlHidden && !Objects.isNull( applyLoan.getCodeRecipientSystem() )
				&& applyLoan.getCodeRecipientSystem().getSystemId() == RecipientSystemEnum.ILOAN.getSystemId() )
				ixmlDisplayed = false;

			if( iloanMydataHidden && !Objects.isNull( applyLoan.getCodeRecipientSystem() )
				&& applyLoan.getCodeRecipientSystem().getSystemId() == RecipientSystemEnum.ILOAN.getSystemId() )
				mydataDisplayed = false;

			IxmlMydataDisplayedBean resBean = new IxmlMydataDisplayedBean();
			resBean.setIxmlDisplayed( ixmlDisplayed );
			resBean.setMydataDisplayed( mydataDisplayed );

			return resBean;
		}
	}

	public Long submitConfirmFilledInfo( String loanType ) throws IOException
	{

		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		ApplyLoan applyLoan = getApplyLoan( identityInfo.getValidatedIdentityId(), loanType );

		Long loanId = applyLoan.getLoanId();
		Date apply_completed_date = applyLoan.getApplyCompletedDate();

		updatePdfContent( applyLoan, apply_completed_date );

		sendThankyouMail( applyLoan, loanId );

		String applyStatus = ApplyStatusEnum.COMPLETE_CONFIRMED.getContext();
		applyLoanDAO.updateApplyCompleted( applyLoan.getLoanId(), applyStatus, apply_completed_date );

		// ======================
		// 僅有信貸需要決定送往 eloan 或 ilaon
		// 分件邏輯為：iloan 白名單 > 送件系統開關 > 貸款內容（大於300萬、團貸案件、行員）
		if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( applyLoan.getCodeLoanType().getLoanType() ) )
			// 借款人根據分件邏輯判斷收件系統，非借款人依循借款人的收件系統
			if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			setLoanRecipient( applyLoan, identityInfo );
			else if( !Objects.isNull( applyLoan.getRefBorrowerApplyLoan() ) )
		{
			CodeRecipientSystem recipientSystem = applyLoan.getRefBorrowerApplyLoan().getCodeRecipientSystem();
			if( RecipientSystemEnum.ILOAN.getSystemId() == recipientSystem.getSystemId() )
				return applyLoan.getLoanId(); // iloan 不收保證人案件 (跳過 deliverLoan, deliverAttachment)
			else
			{
				applyLoan.setLoanRecipient( recipientSystem );
				applyLoan.setApplyLoanContent( applyLoan.getRefBorrowerApplyLoan().getApplyLoanContent() );
			}
		}

		deliverLoan( applyLoan );

		deliverAttachment( applyLoan.getApplyAttachments() );

		return applyLoan.getLoanId();

	}

	/**
	 * 送出申貸頁內容
	 *
	 * @param paramBean
	 * @return
	 */
	public Long submitLoanApplyInfo( LoanApplySubmittedParamBean paramBean )
	{
		Validate.isTrue( checkLoanApplyInfoParamBean( paramBean.getLoanType(), paramBean ) );

		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();

		ApplyLoan applyLoan = getApplyLoan( identityInfo.getValidatedIdentityId(), paramBean.getLoanType() );

		if( !UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			throw new MyRuntimeException( ApplyErrorEnum.APPLY_STEP_ERROR );

		checkStepIsCorrect( applyLoan.getCodeApplyStatus().getApplyStatusCode(), Arrays.asList( ApplyStatusEnum.COMPLETE_BASIC.getContext() ) );

		createLoanContent( applyLoan.getLoanId(), paramBean.getLoanContentParamBean() );

		createLoanRelation( applyLoan.getLoanId(), checkLoanRelationUnique( paramBean.getLoanRelationParamBeans() ) );

		createLoanServed( applyLoan.getLoanId(), checkLoanServedUnique( paramBean.getLoanServedParamBeans() ) );

		String applyStatus = ApplyStatusEnum.COMPLETE_LOAN.getContext();

		return applyLoanDAO.updateApplyStatus( applyLoan.getLoanId(), applyStatus );
	}

	/**
	 * 送出個人資訊頁內容
	 *
	 * @param paramBean
	 * @return
	 */
	public Long submitPersonalInfo( PersonalInfoSubmittedParamBean paramBean )
	{
		checkCitizen( paramBean.getPersonalBasicParamBean().getIdNo() );

		checkLegalAge( paramBean.getPersonalBasicParamBean().getBirthDate() );

		checkIsSameUser( paramBean.getPersonalBasicParamBean().getIdNo(), paramBean.getPersonalBasicParamBean().getBirthDate() );

		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();

		ApplyLoan applyLoan = getApplyLoan( identityInfo.getValidatedIdentityId(), paramBean.getLoanType() );

		checkStepIsCorrect( applyLoan.getCodeApplyStatus().getApplyStatusCode(), Arrays.asList( ApplyStatusEnum.COMPLETE_AGREED.getContext() ) );

		updateApplyLoanBasic( applyLoan.getLoanId(), paramBean.getPersonalBasicParamBean() );

		updateApplyLoanContactInfo( applyLoan.getLoanId(), paramBean.getPersonalContactParamBean() );

		updateApplyLoanOccupation( applyLoan.getLoanId(), paramBean.getPersonalJobParamBean() );

		if( UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			applyLoanDAO.updateIntroduceBr1st( applyLoan.getLoanId(), paramBean.getPersonalContactParamBean().getServiceAssociateBranchCode() );

		String userSubType = paramBean.getPersonalGuaranteeParamBean().getUserSubType();

		if( !UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			createLoanGuaranteeInfo( applyLoan.getLoanId(), paramBean.getPersonalGuaranteeParamBean(), userSubType );

		if( UserTypeEnum.GUARANTOR.getContext().equals( identityInfo.getUserType() ) )
			userClientService.updateUserSubType( userSubType );

		String applyStatus = ApplyStatusEnum.COMPLETE_BASIC.getContext();

		return applyLoanDAO.updateApplyStatus( applyLoan.getLoanId(), applyStatus );
	}

	private List<String> _returnList( String line1, String line2 )
	{
		List<String> list = new ArrayList<>();
		list.add( line1 );
		list.add( line2 );
		return list;
	}

	private String build_ChinaSteelGroup_msg()
	{// "您未於中鋼EIP系統申請時間內(11/10-11/14)選擇線上申貸，建議於11/16-11/17(廠區)及11/18(總部)紙本申貸"
	 // 時間定義在CodeList，依序為 ChinaSteel_EIP_Time/ChinaSteel_fac_Time/ChinaSteel_head_Time
		StringBuilder msg = new StringBuilder( "您未於中鋼EIP系統申請時間內" );

		List<CodeList> eip_timeList = codeListDAO.getPojosByCodeType( "ChinaSteel_EIP_Time" );
		if( eip_timeList != null && eip_timeList.size() > 0 )
		{
			CodeList eip_time = eip_timeList.get( 0 );
			if( StringUtils.isNotBlank( eip_time.getCodeValue() ) )
				msg.append( "(" + eip_time.getCodeValue() + ")" );
		}
		msg.append( "選擇線上申貸，建議於" );

		List<CodeList> fac_TimeList = codeListDAO.getPojosByCodeType( "ChinaSteel_fac_Time" );
		if( fac_TimeList != null && fac_TimeList.size() > 0 )
		{
			CodeList fac_time = fac_TimeList.get( 0 );
			if( StringUtils.isNotBlank( fac_time.getCodeValue() ) )
				msg.append( fac_time.getCodeValue() );
		}
		msg.append( "(廠區)及" );
		List<CodeList> head_TimeList = codeListDAO.getPojosByCodeType( "ChinaSteel_head_Time" );
		if( head_TimeList != null && head_TimeList.size() > 0 )
		{
			CodeList head_time = head_TimeList.get( 0 );
			if( StringUtils.isNotBlank( head_time.getCodeValue() ) )
				msg.append( head_time.getCodeValue() );
		}
		msg.append( "(總部)紙本申貸" );
		return msg.toString();
	}

	private String build_msg_APPLY01017_ChinaSteelGroup_BatchPersonalLoan( CodeLoanPlan codeLoanPlan )
	{
		return "中鋼集團-" + codeLoanPlan.getCompanyName() + "申貸時間已逾期" + "(" + "申貸期間 "
			+ CommonDateStringUtils.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 ) + " ~ "
			+ CommonDateStringUtils.transDate2String( codeLoanPlan.getEndTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 ) + ")";
	}

	private String build_msg_APPLY01018_ChinaSteelGroup_BatchPersonalLoan( CodeLoanPlan codeLoanPlan )
	{
		return "中鋼集團-" + codeLoanPlan.getCompanyName() + "申貸時間尚未開始" + "(" + "申貸期間 "
			+ CommonDateStringUtils.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 ) + " ~ "
			+ CommonDateStringUtils.transDate2String( codeLoanPlan.getEndTs(), APPLY_DATE_PATTERN_ONLY_DATE_2 ) + ")";

	}

	private int calculateAge( Date birthDate )
	{
		Calendar birthCalendar = DateUtils.toCalendar( birthDate );
		Calendar currentCalendar = Calendar.getInstance();

		int age = currentCalendar.get( Calendar.YEAR ) - birthCalendar.get( Calendar.YEAR );

		if( ( currentCalendar.get( Calendar.MONTH ) - birthCalendar.get( Calendar.MONTH ) ) < 0
			|| ( ( currentCalendar.get( Calendar.MONTH ) - birthCalendar.get( Calendar.MONTH ) ) == 0
				&& ( currentCalendar.get( Calendar.DATE ) - birthCalendar.get( Calendar.DATE ) ) < 0 ) )
			age -= 1;

		return age;
	}

	private boolean checkApplyCount( String idNo, Date birthDate, String loanType, String userType )
	{
		List<Long> identityIds = loanDAO.getCompletedIdentityIdsIn7Days( idNo, birthDate, loanType );

		if( identityIds.isEmpty() )
			return true;

		List<String> userTypes = userClientService.getUserTypesResult( identityIds );

		return !userTypes.contains( userType );
	}

	private void checkCanApply( String loanType, String userType )
	{
		boolean canApply = checkApplyCount( getIdNo(), getBirthDate(), loanType, userType );

		if( !canApply )
			throw new MyRuntimeException( ApplyErrorEnum.EXCEED_APPLY_COUNT_LIMIT );

	}

	private void checkCaseIsValid( String refCaseNo )
	{
		ApplyLoan applyLoan = applyLoanDAO.getPojoByCaseNo( refCaseNo );

		if( getIdNo().equals( applyLoan.getApplyLoanBasic().getIdNo() )
			&& getBirthDate().getTime() == applyLoan.getApplyLoanBasic().getBirthDate().getTime() )
			throw new MyRuntimeException( ApplyErrorEnum.CANNOT_SELF_GUARANTEE );

		if( !ApplyStatusEnum.COMPLETE_CONFIRMED.getContext().equals( applyLoan.getCodeApplyStatus().getApplyStatusCode() ) )
			throw new MyRuntimeException( ApplyErrorEnum.CANNOT_GUARANTEE_CASE );

		IdentityInfoResultBean identityInfo = userClientService.getIdentityInfoResult( applyLoan.getValidatedIdentityId() );

		if( !UserTypeEnum.BORROWER.getContext().equals( identityInfo.getUserType() ) )
			throw new MyRuntimeException( ApplyErrorEnum.CANNOT_GUARANTEE_CASE );
	}

	/**
	 * 檢查是否為本國人
	 *
	 * @param idNo
	 */
	private void checkCitizen( String idNo )
	{
		if( idNo.matches( "[A-Za-z][8-9][0-9]{8}" ) )
			throw new MyRuntimeException( ApplyErrorEnum.NON_CITIZEN );
	}

	/**
	 * 檢查是否為本行行員
	 *
	 * @param idNo
	 * @return
	 */
	private Boolean checkIsEmployee( String idNo )
	{
		try
		{
			return otherDataBaseClientService.checkIsEmployee( idNo );
		}
		catch( Exception ex )
		{
			return false;
		}
	}

	/**
	 * 檢查是否曾為本行貸款戶
	 *
	 * @param idNo
	 * @return
	 */
	private Boolean checkIsLoanee( String idNo )
	{
		try
		{
			List<String> loaneeBankCodes = getLoaneeBankCode( idNo );

			return !loaneeBankCodes.isEmpty();
		}
		catch( Exception ex )
		{
			return false;
		}
	}

	/**
	 * 檢查是否為同一使用者
	 *
	 * @param idNo
	 * @param birthDate
	 */
	private void checkIsSameUser( String idNo, Date birthDate )
	{
		if( !( getIdNo().equals( idNo ) && getBirthDate().getTime() == birthDate.getTime() ) )
			throw new MyRuntimeException( ApplyErrorEnum.ILLEGAL_USER );
	}

	/**
	 * 檢查同意事項必填選項是否填選
	 *
	 * @param userType
	 * @param identityType
	 * @param loanType
	 * @param itemIds
	 */
	private void checkItemsChecked( String userType, String identityType, String loanType, List<Long> itemIds )
	{
		List<Long> requiredItemIds = agreedDAO.getNeedToCheckItemIds( userType, identityType, loanType );

		if( !itemIds.containsAll( requiredItemIds ) )
			throw new MyRuntimeException( ApplyErrorEnum.NEED_TO_CHECK );

	}

	/**
	 * 檢查是否未滿20歲
	 *
	 * @param birthDate
	 */
	private void checkLegalAge( Date birthDate )
	{
		if( calculateAge( birthDate ) < 18 )
			throw new MyRuntimeException( ApplyErrorEnum.UNDER_THE_LEGAL_AGE );
	}

	private boolean checkLoanApplyInfoParamBean( String loanType, LoanApplySubmittedParamBean paramBean )
	{
		if( LoanTypeEnum.HOUSE_LOAN.getContext().equals( loanType ) )
			return !StringUtils.isAnyBlank( paramBean.getLoanContentParamBean().getCollateralAddressTownCode(),
											paramBean.getLoanContentParamBean().getCollateralAddressStreet(),
											paramBean.getLoanContentParamBean().getGracePeriodCode() );
		else if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) )
			return !StringUtils.isAnyBlank( paramBean.getLoanContentParamBean().getCaseSourceCode() );

		return true;

	}

	private void checkLoanPlanCode( String idNo, Date birthDate, String loanType, String loanPlanCode, EloanCustInfoResultBean iloanCustInfo )
	{
		if( StringUtils.isEmpty( loanPlanCode ) )
			return;
		// ========================================================================
		int checkLoanPlanCode_result = checkLoanPlanCode_active( loanPlanCode );

		// 檢核{-1:代碼不存在}
		if( checkLoanPlanCode_result == -1 )
			throw new MyRuntimeException( ApplyErrorEnum.NOT_ACTIVE_LOAN_PLAN );

		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
		{
			if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) )
				checkLoanPlanCode_ChinaSteelGroup_BatchPersonalLoan( idNo, birthDate, loanPlanCode, checkLoanPlanCode_result, iloanCustInfo );
			else
				// 親愛的兆豐客戶您好，您非此行銷方案的適用對象
				throw new MyRuntimeException( ApplyErrorEnum.NOT_QUALIFIED_FOR_LOAN_PLAN );
		}
		else if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode )
			|| ApplyLoanUtils.is_co80601119_D001_BatchPersonalLoan( loanPlanCode ) )
		{
			if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) )
				checkLoanPlanCode_BatchPersonalLoan_hasGrpCntrNo_in_whiteList( idNo, birthDate, loanPlanCode, checkLoanPlanCode_result,
																			   iloanCustInfo );
			else
				// 親愛的兆豐客戶您好，您非此行銷方案的適用對象
				throw new MyRuntimeException( ApplyErrorEnum.NOT_QUALIFIED_FOR_LOAN_PLAN );
		}
		else
			checkLoanPlanCode_General( idNo, birthDate, loanPlanCode, checkLoanPlanCode_result );

	}

	/**
	 * 檢查 plan 的狀態
	 *
	 * @param plan
	 * @return {0:可用, -1:代碼不存在, 1:已逾期}
	 */
	private int checkLoanPlanCode_active( String plan )
	{
		CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( plan );

		if( codeLoanPlan == null )
			return -1;
		else if( CommonTimeUtils.in_given_timestamp_range( codeLoanPlan.getBegTs(), codeLoanPlan.getEndTs() ) )
			return 0;
		else
			return 1;
	}

	private void checkLoanPlanCode_BatchPersonalLoan_hasGrpCntrNo_in_whiteList( String idNo, Date birthDate, String loanPlanCode,
																				int checkLoanPlanCode_result, EloanCustInfoResultBean iloanCustInfo )
	{
		EloanCustInfoResultBean eloanCustInfoResultBean = getCustInfo( idNo, birthDate, iloanCustInfo );
		if( eloanCustInfoResultBean != null
			&& StringUtils.equals( ApplyLoanUtils.get_eloan_return_specGroup_grpCntrNo(), eloanCustInfoResultBean.getGrpCntrNo() ) )
			return; // 不必做後續的檢核，不論 plan 的狀態是{可用/逾期}

		if( checkLoanPlanCode_result == 1 )
		{
			CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( loanPlanCode );
			if( codeLoanPlan != null )
			{
				Timestamp currentTs = new java.sql.Timestamp( new Date().getTime() );
				// ~~~~~~~~~~~~~~~~~~
				// 行銷方案尚未開始 => {0}申貸期間為{1}
				if( currentTs.before( codeLoanPlan.getBegTs() ) )
					throw new MyRuntimeException( ApplyErrorEnum.LOAN_PLAN_NOT_BEGIN, new String[]{ codeLoanPlan.getName(), CommonDateStringUtils
								.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_1 ) } );
				// ~~~~~~~~~~~~~~~~~~
				// 行銷方案已逾期 => 已逾{0}申貸時間
				if( currentTs.after( codeLoanPlan.getEndTs() ) )
					throw new MyRuntimeException( ApplyErrorEnum.LOAN_PLAN_EXCEED_ENDTS, new String[]{ codeLoanPlan.getName() } );
			}

			throw new MyRuntimeException( ApplyErrorEnum.NOT_ACTIVE_LOAN_PLAN );
		}
		// ========================================================================
		// 已通過 loanPlanCode 的初步檢核
		if( true )
		{
			// 若N個月內有(未discard)且申貸成功的案件 => 阻擋不能重複申貸
			Date cmpDate = CommonDateUtils.addMonth( new Date(), -1 );
			boolean cond_discard = false;
			List<ApplyListDTO> apply_list = loanDAO.getApplyList( idNo, birthDate, null, cond_discard );
			for( ApplyListDTO dto : apply_list )
				if( dto.getApplyDate() != null && CommonDateUtils.cmpDate( dto.getApplyDate(), ">=", cmpDate ) )
					throw new MyRuntimeException( ApplyErrorEnum.ALREADY_FINISH_APPLY,
												  new String[]{ CommonDateStringUtils.transDate2String( dto.getApplyDate(), APPLY_DATE_PATTERN ) } );

		}

		if( eloanCustInfoResultBean != null && StringUtils.isNotBlank( eloanCustInfoResultBean.getGrpCntrNo() ) )
		{
			// 已存在 lms.c122m01e 控管名單
		}
		else
		{
			HashSet<String> check_loanPlanCode = new HashSet<>();
			check_loanPlanCode.add( LoanPlanEnum.C101.getContext() );
			check_loanPlanCode.add( LoanPlanEnum.D001.getContext() );

			if( check_loanPlanCode.contains( loanPlanCode ) )
				// 存在於 list 裡面 idNo 才能承做
				throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01019_DESCRIPTION, new String[]{ "您非行銷方案(" + loanPlanCode + ")的可申貸人員" } );
		}

	}

	private void checkLoanPlanCode_ChinaSteelGroup_BatchPersonalLoan( String idNo, Date birthDate, String loanPlanCode, int checkLoanPlanCode_result,
																	  EloanCustInfoResultBean iloanCustInfo )
	{
		EloanCustInfoResultBean eloanCustInfoResultBean = getCustInfo( idNo, birthDate, iloanCustInfo );
		if( eloanCustInfoResultBean != null
			&& StringUtils.equals( ApplyLoanUtils.get_eloan_return_specGroup_grpCntrNo(), eloanCustInfoResultBean.getGrpCntrNo() ) )
			return; // 不必做後續的檢核，不論 plan 的狀態是{可用/逾期}

		if( checkLoanPlanCode_result == 1 )
		{
			CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( loanPlanCode );
			if( codeLoanPlan != null )
			{
				Timestamp currentTs = new java.sql.Timestamp( new Date().getTime() );
				// ~~~~~~~~~~~~~~~~~~
				// 行銷方案尚未開始 => {0}申貸期間為{1}
				if( currentTs.before( codeLoanPlan.getBegTs() ) )
					if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
					throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01018_LOAN_PLAN_NOT_BEGIN,
												  new String[]{ build_msg_APPLY01018_ChinaSteelGroup_BatchPersonalLoan( codeLoanPlan ) } );
					else
					throw new MyRuntimeException( ApplyErrorEnum.LOAN_PLAN_NOT_BEGIN, new String[]{ codeLoanPlan.getName(), CommonDateStringUtils
								.transDate2String( codeLoanPlan.getBegTs(), APPLY_DATE_PATTERN_ONLY_DATE_1 ) } );
				// ~~~~~~~~~~~~~~~~~~
				// 行銷方案已逾期 => 已逾{0}申貸時間
				if( currentTs.after( codeLoanPlan.getEndTs() ) )
					if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
					throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01017_LOAN_PLAN_EXCEED_ENDTS,
												  new String[]{ build_msg_APPLY01017_ChinaSteelGroup_BatchPersonalLoan( codeLoanPlan ) } );
					else
					throw new MyRuntimeException( ApplyErrorEnum.LOAN_PLAN_EXCEED_ENDTS, new String[]{ codeLoanPlan.getName() } );
			}

			throw new MyRuntimeException( ApplyErrorEnum.NOT_ACTIVE_LOAN_PLAN );
		}
		// ========================================================================
		// 已通過 loanPlanCode 的初步檢核
		if( true )
		{
			// 中鋼消貸{不論總公司或子公司}應該一年只會申請乙次而已，若N個月內有(未discard)且申貸成功的案件 => 阻擋不能重複申貸
			Date cmpDate = CommonDateUtils.addMonth( new Date(), -6 );
			boolean cond_discard = false;
			List<ApplyListDTO> apply_list = loanDAO.getApplyList( idNo, birthDate, null, cond_discard );
			for( ApplyListDTO dto : apply_list )
				if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( dto.getLoanPlanCode() ) && dto.getApplyDate() != null
					&& CommonDateUtils.cmpDate( dto.getApplyDate(), ">=", cmpDate ) )
					throw new MyRuntimeException( ApplyErrorEnum.ALREADY_FINISH_APPLY,
												  new String[]{ CommonDateStringUtils.transDate2String( dto.getApplyDate(), APPLY_DATE_PATTERN ) } );

		}

		if( StringUtils.equals( loanPlanCode, LoanPlanEnum.C001.getContext() ) )
			if( eloanCustInfoResultBean != null && StringUtils.isNotBlank( eloanCustInfoResultBean.getGrpCntrNo() ) )
		{
			// 已存在中鋼{總公司}控管名單
		}
			else
			// 中鋼{總公司}的方案，要由 002分行上傳 => 存在於 list 裡面 idNo 才能承做
			// "您未於中鋼EIP系統申請時間內(11/10-11/14)選擇線上申貸，建議於11/16-11/17(廠區)及11/18(總部)紙本申貸"
			throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01019_DESCRIPTION, new String[]{ build_ChinaSteelGroup_msg() } );

	}

	private void checkLoanPlanCode_General( String idNo, Date birthDate, String loanPlanCode, int checkLoanPlanCode_result )
	{
		if( checkLoanPlanCode_result == 1 )
			throw new MyRuntimeException( ApplyErrorEnum.NOT_ACTIVE_LOAN_PLAN );
	}

	/**
	 * 檢查relation 中屬性relationIdNo沒有重複
	 *
	 * @param List<LoanRelationParamBean>
	 * @return List<LoanRelationParamBean>
	 */
	private List<LoanRelationParamBean> checkLoanRelationUnique( List<LoanRelationParamBean> loanRelationParamBean )
	{

		if( loanRelationParamBean.isEmpty() )
			return loanRelationParamBean;
		List<LoanRelationParamBean> newOne = loanRelationParamBean.stream()
					.collect( Collectors.toMap( item -> item.getRelationIdNo(), Function.identity(), ( existing, replacement ) -> existing ) )
					.values().stream().collect( Collectors.toList() );

		if( newOne.size() == loanRelationParamBean.size() )
			return loanRelationParamBean;
		else
			throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01019_DESCRIPTION, "配偶及二等親以內血親，檢核相同【關係人身分證字號】只能填寫一筆。" );
	}

	/**
	 * 檢查served 中屬性TaxNo沒有重複
	 *
	 * @param List<LoanServedParamBean>
	 * @return List<LoanServedParamBean>
	 */
	private List<LoanServedParamBean> checkLoanServedUnique( List<LoanServedParamBean> loanServedParamBeans )
	{

		if( loanServedParamBeans.isEmpty() )
			return loanServedParamBeans;
		List<LoanServedParamBean> newOne = loanServedParamBeans.stream()
					.collect( Collectors.toMap( item -> item.getTaxNo(), Function.identity(), ( existing, replacement ) -> existing ) ).values()
					.stream().collect( Collectors.toList() );
		if( newOne.size() == loanServedParamBeans.size() )
			return loanServedParamBeans;
		else
			throw new MyRuntimeException( ApplyErrorEnum.FREETEXT_01019_DESCRIPTION, "本人或配偶擔任負責人之企業資料，檢核相同【統一編號】只能填寫一筆。" );
	}

	/**
	 * 檢查申請步驟是否正確
	 *
	 * @param currentStatus
	 * @param allowedStatus
	 */
	private void checkStepIsCorrect( String currentStatus, List<String> allowedStatus )
	{
		if( !allowedStatus.contains( currentStatus ) )
			throw new MyRuntimeException( ApplyErrorEnum.APPLY_STEP_ERROR );
	}

	private void checkUserType( String userType )
	{
		if( StringUtils.isBlank( userType ) )
			throw new MyRuntimeException( ApplyErrorEnum.USER_TYPE_REQUIRED );

		if( !UserTypeEnum.BORROWER.getContext().equals( userType ) && !UserTypeEnum.GUARANTOR.getContext().equals( userType ) )
			throw new MyRuntimeException( ApplyErrorEnum.ILLEGAL_USER );

	}

	/*
	 * 前端的 CommonStepBasicInFormation.js
	 * personalBasicInfo.data.result.identities.includes("employee")
	 *
	 * if (personalBasicInfo.data.result.identities.includes("loanee")) {
	 *
	 * 對應到 /loan/apply/getPersonalBasicInfo
	 */
	private boolean chg_by_borrowser_PersonalLoan_introduceBrNo( ApplyLoan applyLoan, Long finalBranchBankId )
	{

		if( IdentityFlagEnum.NONE.getContext() == applyLoan.getIdentityFlag() )
			return false;

		List<String> identity_flag_list = getIdentities( applyLoan.getIdentityFlag() );
		boolean isEMPLOYEE = false;
		boolean isLOANEE = false;
		for( String identity_flag : identity_flag_list )
		{
			if( StringUtils.equals( IdentityFlagEnum.EMPLOYEE.getName(), identity_flag ) )
				isEMPLOYEE = true;

			if( StringUtils.equals( IdentityFlagEnum.LOANEE.getName(), identity_flag ) )
				isLOANEE = true;

			if( StringUtils.equals( IdentityFlagEnum.EMPLOYEE_AND_LOANEE.getName(), identity_flag ) )
			{
				isEMPLOYEE = true;
				isLOANEE = true;
			}
		}

		if( isEMPLOYEE )
			return true;

		/*
		 * 既有貸款戶、非行員：除「貸款分行」之外，信貸只可選擇 229BR 既有貸款戶、行員：可選任一分行
		 */
		if( isEMPLOYEE == false && isLOANEE
			&& getLoaneeBankCode( applyLoan.getApplyLoanBasic().getIdNo() ).contains( codeBranchBankDAO.read( finalBranchBankId ).getBankCode() ) )
			return true;

		return false;
	}

	private String choose_PersonalLoan_introduceBrNo( ApplyLoan applyLoan, Long finalBranchBankId )
	{
		if( StringUtils.isNotBlank( applyLoan.getIntroduceBrNo() ) )
			return applyLoan.getIntroduceBrNo();

		if( applyLoan.getRefBorrowerApplyLoan() == null )
		{
			if( StringUtils.isNotBlank( applyLoan.getIntroduceBr1st() ) )
				return applyLoan.getIntroduceBr1st();

			// {行員、既有貸款戶}可以自行選擇{特定分行}
			// 若行員申請信貸，但他沒有掃QR-Code，而是自己在 React 的 UI 指定某個分行
			if( chg_by_borrowser_PersonalLoan_introduceBrNo( applyLoan, finalBranchBankId ) )
				return codeBranchBankDAO.read( finalBranchBankId ).getBankCode();
		}
		else
			return applyLoan.getRefBorrowerApplyLoan().getIntroduceBrNo();

		// 信貸預設 IntroduceBrNo {第1順位: QR-Code}{第2順位: 943}
		CodeBranchBank codeBranchBank_943 = codeBranchBankDAO.getPojoByHeadOffice( true );
		if( codeBranchBank_943 != null )
			return codeBranchBank_943.getBankCode();

		// ===============
		return "";
	}

	/**
	 * 比較戶籍地址與通訊地址是否相同
	 *
	 * @param homeAddressTownCode
	 * @param homeAddressStreet
	 * @param mailingAddressTownCode
	 * @param mailingAddressStreet
	 * @return
	 */
	private boolean compareIsMailingAddressSameToHome( ApplyAddress homeAddress, ApplyAddress mailingAddress )
	{
		if( homeAddress == null || mailingAddress == null )
			return true;

		if( homeAddress.getAddressId().equals( mailingAddress.getAddressId() ) )
			return true;

		return ( ( Objects.equals( homeAddress.getCodeTown(), mailingAddress.getCodeTown() ) )
			&& ( Objects.equals( homeAddress.getVillage(), mailingAddress.getVillage() ) )
			&& ( Objects.equals( homeAddress.getNeighborhood(), mailingAddress.getNeighborhood() ) )
			&& ( Objects.equals( homeAddress.getStreet(), mailingAddress.getStreet() ) )
			&& ( Objects.equals( homeAddress.getSection(), mailingAddress.getSection() ) )
			&& ( Objects.equals( homeAddress.getLane(), mailingAddress.getLane() ) )
			&& ( Objects.equals( homeAddress.getAlley(), mailingAddress.getAlley() ) )
			&& ( Objects.equals( homeAddress.getNo(), mailingAddress.getNo() ) )
			&& ( Objects.equals( homeAddress.getFloor(), mailingAddress.getFloor() ) )
			&& ( Objects.equals( homeAddress.getRoom(), mailingAddress.getRoom() ) ) );
	}

	private boolean compareIsMailingAddressSameToHome( PersonalContactParamBean contactBean )
	{
		return ( ( Objects.equals( contactBean.getHomeAddressTownCode(), contactBean.getMailingAddressTownCode() ) )
			&& ( Objects.equals( contactBean.getHomeAddressVillage(), contactBean.getMailingAddressVillage() ) )
			&& ( Objects.equals( contactBean.getHomeAddressNeighborhood(), contactBean.getMailingAddressNeighborhood() ) )
			&& ( Objects.equals( contactBean.getHomeAddressStreet(), contactBean.getMailingAddressStreet() ) )
			&& ( Objects.equals( contactBean.getHomeAddressSection(), contactBean.getMailingAddressSection() ) )
			&& ( Objects.equals( contactBean.getHomeAddressLane(), contactBean.getMailingAddressLane() ) )
			&& ( Objects.equals( contactBean.getHomeAddressAlley(), contactBean.getMailingAddressAlley() ) )
			&& ( Objects.equals( contactBean.getHomeAddressNo(), contactBean.getMailingAddressNo() ) )
			&& ( Objects.equals( contactBean.getHomeAddressFloor(), contactBean.getMailingAddressFloor() ) )
			&& ( Objects.equals( contactBean.getHomeAddressRoom(), contactBean.getMailingAddressRoom() ) ) );
	}

	private void createApplyAttachment( Long loanId, Long validatedIdentityId, IdentityAttachmentResultBean identityAttachment )
	{
		String transmissionStatusCode = TransmissionStatusEnum.NO.getContext();

		ApplyAttachmentCreatedParamBean paramBean = new ApplyAttachmentCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setValidatedIdentityId( validatedIdentityId );
		paramBean.setTransmissionStatusCode( transmissionStatusCode );
		paramBean.setAttachmentType( identityAttachment.getAttachmentType() );
		paramBean.setFileName( identityAttachment.getFileName() );
		paramBean.setFileSize( identityAttachment.getFileSize() );
		paramBean.setFileContent( new ImmutableByteArray( CommonBase64Utils.base64Decoder( identityAttachment.getFileContent() ) ) );
		paramBean.setCompressFileContent( new ImmutableByteArray( CommonBase64Utils.base64Decoder( identityAttachment.getCompressFileContent() ) ) );

		applyAttachmentDAO.create( paramBean );
	}

	private Long createApplyLoan( Long validatedIdentityId, String loanType, String refCaseNo, String loanPlanCode, String introduceBrNo )
	{
		String caseNo = getCaseNo( loanType );
		String applyStatusCode = ApplyStatusEnum.COMPLETE_CREATED.getContext();
		String transmissionStatusCode = TransmissionStatusEnum.NO.getContext();
		String processCode = ProcessStatusEnum.UNPROCESSED.getContext();

		ApplyLoanCreatedParamBean paramBean = new ApplyLoanCreatedParamBean();
		paramBean.setValidatedIdentityId( validatedIdentityId );
		paramBean.setCaseNo( caseNo );
		paramBean.setApplyStatusCode( applyStatusCode );
		paramBean.setTransmissionStatusCode( transmissionStatusCode );
		paramBean.setLoanType( loanType );
		paramBean.setProcessCode( processCode );
		paramBean.setRefCaseNo( refCaseNo );
		paramBean.setIdentityFlag( getIdentityFlag( getIdNo() ) );
		paramBean.setLoanPlanCode( loanPlanCode );
		paramBean.setIntroduceBrNo( introduceBrNo );
		return applyLoanDAO.create( paramBean );
	}

	/**
	 * 建立個人基本資訊
	 *
	 * @param loanId
	 * @param basicBean
	 */
	private void createApplyLoanBasic( Long loanId )
	{
		ApplyLoanBasicCreatedParamBean paramBean = new ApplyLoanBasicCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setIdNo( getIdNo() );
		paramBean.setBirthDate( getBirthDate() );
		paramBean.setChildrenCount( 0 );

		applyLoanBasicDAO.create( paramBean );

	}

	/**
	 * 建立個人聯絡資訊
	 *
	 * @param loanId
	 * @param loanType
	 * @param contactBean
	 */
	private void createApplyLoanContactInfo( Long loanId )
	{
		ApplyLoanContactInfoCreatedParamBean paramBean = new ApplyLoanContactInfoCreatedParamBean();
		paramBean.setLoanId( loanId );

		applyLoanContactInfoDAO.create( paramBean );
	}

	private void createApplyLoanContent( Long loanId, LoanContentParamBean loanContentParamBean )
	{
		ApplyLoanContentCreatedParamBean paramBean = new ApplyLoanContentCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setLoanRequestAmt( loanContentParamBean.getLoanRequestAmt() );
		paramBean.setLoanPurposeId( Long.parseLong( loanContentParamBean.getLoanPurpose() ) );
		paramBean.setOtherPurpose( loanContentParamBean.getOtherPurpose() );
		paramBean.setLoanPeriod( loanContentParamBean.getLoanPeriod() );
		paramBean.setCollateralAddressId( getCollateralAddressId( null, loanContentParamBean ) );
		paramBean.setGracePeriodCode( loanContentParamBean.getGracePeriodCode() );
		paramBean.setNotificationCode( loanContentParamBean.getNotificationCode() );
		paramBean.setMortgageType( loanContentParamBean.getMortgageType() );
		paramBean.setNonPrivateUsageType( loanContentParamBean.getNonPrivateUsageType() );
		paramBean.setNonPrivateUsageSubType( loanContentParamBean.getNonPrivateUsageSubType() );
		paramBean.setPrivateUsageType( loanContentParamBean.getPrivateUsageType() );
		paramBean.setIsIncreasingLoan( loanContentParamBean.getIsIncreasingLoan() );
		paramBean.setAppnBankCode( loanContentParamBean.getAppnBankCode() );
		paramBean.setAppnDpAcct( loanContentParamBean.getAppnDpAcct() );
		paramBean.setCaseSourceCode( loanContentParamBean.getCaseSourceCode() );
		paramBean.setUrlToIdentifyFraud( loanContentParamBean.getUrlToIdentifyFraud() );
		applyLoanContentDAO.create( paramBean );

	}

	private void createApplyLoanGuaranteeInfo( Long loanId, String userSubType, PersonalGuaranteeParamBean guaranteeParamBean )
	{
		ApplyLoanGuaranteeInfoCreatedParamBean paramBean = new ApplyLoanGuaranteeInfoCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setRelationBorrowerType( guaranteeParamBean.getRelationBorrowerType() );
		paramBean.setCohabitationFlag( guaranteeParamBean.getIsCohabiting() );

		List<String> requiredGuarantyReason = Arrays.asList( UserSubTypeEnum.N_GUARANTOR.getContext(), UserSubTypeEnum.G_GUARANTOR.getContext() );
		if( requiredGuarantyReason.contains( userSubType ) )
		{
			paramBean.setGuarantyReasonCode( guaranteeParamBean.getGuarantyReasonCode() );
			paramBean.setOtherGuarantyReason( guaranteeParamBean.getOtherGuarantyReason() );
		}

		applyLoanGuaranteeInfoDAO.create( paramBean );
	}

	/**
	 * 建立個人職業資訊
	 *
	 * @param loanId
	 * @param jobBean
	 */
	private void createApplyLoanOccupation( Long loanId )
	{
		ApplyLoanOccupationCreatedParamBean paramBean = new ApplyLoanOccupationCreatedParamBean();
		paramBean.setLoanId( loanId );

		applyLoanOccupationDAO.create( paramBean );
	}

	private void createApplyLoanRelation( Long loanId, LoanRelationParamBean loanRelationParamBean )
	{
		ApplyLoanRelationCreatedParamBean paramBean = new ApplyLoanRelationCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setRelationName( loanRelationParamBean.getRelationName() );
		paramBean.setRelationIdNo( loanRelationParamBean.getRelationIdNo() );
		paramBean.setRelationType( loanRelationParamBean.getRelationType() );

		applyLoanRelationDAO.create( paramBean );
	}

	private void createApplyLoanServed( Long loanId, LoanServedParamBean loanServedParamBean )
	{
		ApplyLoanServedCreatedParamBean paramBean = new ApplyLoanServedCreatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setCompanyName( loanServedParamBean.getCompanyName() );
		paramBean.setServedTitle( loanServedParamBean.getServedTitle() );
		paramBean.setTaxNo( loanServedParamBean.getTaxNo() );
		paramBean.setComment( loanServedParamBean.getComment() );
		paramBean.setRepresentativeType( loanServedParamBean.getRepresentativeType() );

		applyLoanServedDAO.create( paramBean );
	}

	/**
	 * 建立申貸內容
	 *
	 * @param loanId
	 * @param loanType
	 * @param loanContentParamBean
	 */
	private void createLoanContent( Long loanId, LoanContentParamBean loanContentParamBean )
	{
		ApplyLoanContent content = applyLoanContentDAO.readToNull( loanId );

		if( content == null )
			createApplyLoanContent( loanId, loanContentParamBean );
		else
			updateApplyLoanContent( loanId, loanContentParamBean );

	}

	/**
	 * 建立貸款案件
	 *
	 * @param identityInfo
	 * @param userType
	 * @param loanType
	 * @return
	 */
	private Long createLoanData( IdentityInfoResultBean identityInfo, String loanType, String refCaseNo, String loanPlanCode, String introduceBrNo,
								 EloanCustInfoResultBean iloanCustInfo )
	{
		Long validatedIdentityId = identityInfo.getValidatedIdentityId();

		ApplyLoan applyLoan = applyLoanDAO.getPojoByIdentityAndLoanType( validatedIdentityId, loanType );

		if( applyLoan != null )
			throw new MyRuntimeException( ApplyErrorEnum.APPLY_STEP_ERROR );

		Long loanId = createApplyLoan( validatedIdentityId, loanType, refCaseNo, loanPlanCode, introduceBrNo );

		createApplyLoanBasic( loanId );
		createApplyLoanContactInfo( loanId );
		createApplyLoanOccupation( loanId );

		updateExistedData( loanId, identityInfo, loanType, loanPlanCode, iloanCustInfo );

		return loanId;
	}

	/**
	 * 建立保證人資訊
	 *
	 * @param loanId
	 * @param guaranteeParamBean
	 * @param userSubType
	 */
	private void createLoanGuaranteeInfo( Long loanId, PersonalGuaranteeParamBean guaranteeParamBean, String userSubType )
	{
		ApplyLoanGuaranteeInfo guaranteeInfo = applyLoanGuaranteeInfoDAO.readToNull( loanId );

		if( guaranteeInfo == null )
			createApplyLoanGuaranteeInfo( loanId, userSubType, guaranteeParamBean );
		else
			updateApplyLoanGuaranteeInfo( loanId, userSubType, guaranteeParamBean );

	}

	/**
	 * 建立關係人資訊
	 *
	 * @param loanId
	 * @param loanRelationParamBeans
	 */
	private void createLoanRelation( Long loanId, List<LoanRelationParamBean> loanRelationParamBeans )
	{
		applyLoanRelationDAO.deletePojosByLoanId( loanId );

		for( LoanRelationParamBean loanRelationParamBean : loanRelationParamBeans )
			if( StringUtils.isNotBlank( loanRelationParamBean.getRelationIdNo() )
				&& StringUtils.isNotBlank( loanRelationParamBean.getRelationName() ) )
				createApplyLoanRelation( loanId, loanRelationParamBean );
	}

	/**
	 * 建立負責企業資訊
	 *
	 * @param loanId
	 * @param loanServedParamBeans
	 */
	private void createLoanServed( Long loanId, List<LoanServedParamBean> loanServedParamBeans )
	{
		applyLoanServedDAO.deletePojosByLoanId( loanId );

		for( LoanServedParamBean loanServedParamBean : loanServedParamBeans )
			createApplyLoanServed( loanId, loanServedParamBean );

	}

	private List<String> filter_account_exclude( List<String> src_list, Set<String> exclude_cond_arr )
	{
		List<String> result = new ArrayList<>();
		for( String accountNo : src_list )
			if( !exclude_cond_arr.contains( StringUtils.substring( accountNo, 3, 5 ) ) )
				result.add( accountNo );

		return result;
	}

	private List<String> filter_account_include( List<String> src_list, Set<String> include_cond_arr )
	{
		List<String> result = new ArrayList<>();
		for( String accountNo : src_list )
			if( include_cond_arr.contains( StringUtils.substring( accountNo, 3, 5 ) ) )
				result.add( accountNo );

		return result;
	}

	private Long gen_AddrText_PK( String addrPostalCode, String addr )
	{
		if( StringUtils.isNotBlank( addr ) )
			return applyAddrTextDAO.create( new ApplyAddrTextParamBean( addrPostalCode, addr ) );

		return null;
	}

	/**
	 * 取得同意事項選項
	 *
	 * @param agreedId
	 * @return
	 */
	private List<AgreedItemBean> getAgreedItems( Long agreedId )
	{
		List<ApplyAgreedItem> itemList = applyAgreedItemDAO.getPojosByAgreedId( agreedId );

		List<AgreedItemBean> itemBeans = new ArrayList<>();

		for( ApplyAgreedItem item : itemList )
		{
			AgreedItemBean itemBean = new AgreedItemBean();
			itemBean.setItemId( item.getAgreedItemId() );
			itemBean.setText( item.getContent() );
			itemBean.setRequired( item.isNeedToCheck() );

			itemBeans.add( itemBean );
		}

		return itemBeans;
	}

	/**
	 * 取得貸款案件
	 *
	 * @param validatedIdentityId
	 * @param loanType
	 * @return
	 */
	private ApplyLoan getApplyLoan( Long validatedIdentityId, String loanType )
	{
		ApplyLoan applyLoan = applyLoanDAO.getPojoByIdentityAndLoanType( validatedIdentityId, loanType );

		if( applyLoan == null )
			throw new MyRuntimeException( CommonErrorEnum.DATA_NOT_FOUND_WITH, new String[]{ "貸款申請" } );

		return applyLoan;
	}

	private ApplyLoan getApplyLoan( String loanType )
	{
		IdentityInfoResultBean identityInfo = userClientService.getCurrentIdentityInfoResult();
		Long validatedIdentityId = identityInfo.getValidatedIdentityId();

		return getApplyLoan( validatedIdentityId, loanType );
	}

	private Long getBranchBankId( Long loanId, String branchBankCode )
	{
		ApplyLoan applyLoan = applyLoanDAO.read( loanId );

		if( applyLoan.getRefBorrowerApplyLoan() != null )
			return applyLoan.getRefBorrowerApplyLoan().getApplyLoanContactInfo().getCodeBranchBank().getBranchBankId();

		return Long.parseLong( branchBankCode );
	}

	/**
	 * 取得案件編號
	 *
	 * @param loanType
	 *
	 * @return
	 */
	private String getCaseNo( String loanType )
	{
		Long counter = applyLoanCounterDAO.create();
		String title = LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) ? "PA" : "HA";

		StringBuilder builder = new StringBuilder();
		builder.append( title );
		builder.append( new SimpleDateFormat( "yyyyMMdd" ).format( new Date() ) );
		builder.append( StringUtils.leftPad( String.valueOf( counter % 1000000 ), 6, "0" ) );

		return builder.toString();

	}

	private Long getCollateralAddressId( Long loanId, LoanContentParamBean contentBean )
	{
		if( contentBean.getCollateralAddressTownCode() == null )
			return null;

		ApplyAddressParamBean paramBean = new ApplyAddressParamBean();
		paramBean.setTownCode( contentBean.getCollateralAddressTownCode() );
		paramBean.setVillage( contentBean.getCollateralAddressVillage() );
		paramBean.setNeighborhood( contentBean.getCollateralAddressNeighborhood() );
		paramBean.setStreet( contentBean.getCollateralAddressStreet() );
		paramBean.setSection( contentBean.getCollateralAddressSection() );
		paramBean.setLane( contentBean.getCollateralAddressLane() );
		paramBean.setAlley( contentBean.getCollateralAddressAlley() );
		paramBean.setNo( contentBean.getCollateralAddressNo() );
		paramBean.setFloor( contentBean.getCollateralAddressFloor() );
		paramBean.setRoom( contentBean.getCollateralAddressRoom() );

		if( loanId == null )
			return applyAddressDAO.create( paramBean );

		ApplyAddress collateralAddress = applyLoanDAO.read( loanId ).getApplyLoanContent().getApplyAddress();

		if( collateralAddress == null )
			return applyAddressDAO.create( paramBean );

		return applyAddressDAO.update( collateralAddress.getAddressId(), paramBean );
	}

	private ApplyAddressParamBean getCompanyAddressParamBean( PersonalJobParamBean jobBean )
	{
		ApplyAddressParamBean paramBean = new ApplyAddressParamBean();
		paramBean.setTownCode( jobBean.getCompanyAddressTownCode() );
		paramBean.setVillage( jobBean.getCompanyAddressVillage() );
		paramBean.setNeighborhood( jobBean.getCompanyAddressNeighborhood() );
		paramBean.setStreet( jobBean.getCompanyAddressStreet() );
		paramBean.setSection( jobBean.getCompanyAddressSection() );
		paramBean.setLane( jobBean.getCompanyAddressLane() );
		paramBean.setAlley( jobBean.getCompanyAddressAlley() );
		paramBean.setNo( jobBean.getCompanyAddressNo() );
		paramBean.setFloor( jobBean.getCompanyAddressFloor() );
		paramBean.setRoom( jobBean.getCompanyAddressRoom() );

		return paramBean;
	}

	private EloanCustInfoResultBean getCustInfo( String idNo, Date birthDate, EloanCustInfoResultBean iloanCustInfo )
	{
		try
		{
			EloanCustInfoResultBean custInfo = null;
			if( "eloan".equals( whichAPIServer ) )
				custInfo = eloanSenderService.getEloanCustInfo( idNo, birthDate );
			else if( "iloan".equals( whichAPIServer ) )
				custInfo = iloanCustInfo;
			else if( "byConditions".equals( whichAPIServer ) )
			{
				EloanCustInfoResultBean eloanCustInfo = getEloanCustInfo( idNo, birthDate );
				custInfo = getLastestCustInfo( eloanCustInfo, iloanCustInfo );
			}

			if( StringUtils.isNotBlank( custInfo.getEducationLevel() ) && codeEducationLevelDAO.readToNull( custInfo.getEducationLevel() ) == null )
				custInfo.setEducationLevel( null );

			if( StringUtils.isNotBlank( custInfo.getMarriageStatus() ) && codeMarriageStatusDAO.readToNull( custInfo.getMarriageStatus() ) == null )
				custInfo.setMarriageStatus( null );

			if( StringUtils.isNotBlank( custInfo.getEducationLevel() ) && codeEducationLevelDAO.readToNull( custInfo.getEducationLevel() ) == null )
				custInfo.setEducationLevel( null );

			if( StringUtils.isNotBlank( custInfo.getNationality() ) && codeNationalityDAO.readToNull( custInfo.getNationality() ) == null )
				custInfo.setNationality( null );

			if( StringUtils.isNotBlank( custInfo.getResidenceStatus() )
				&& codeResidenceStatusDAO.readToNull( custInfo.getResidenceStatus() ) == null )
				custInfo.setResidenceStatus( null );

			if( StringUtils.isNotBlank( custInfo.getJobType() ) && codeJobTypeDAO.readToNull( custInfo.getJobType() ) == null )
				custInfo.setJobType( null );

			if( ( StringUtils.isNotBlank( custInfo.getJobType() ) && StringUtils.isNotBlank( custInfo.getJobSubType() ) )
				&& codeJobSubTypeDAO.getPojoByTypes( custInfo.getJobType(), custInfo.getJobSubType() ) == null )
				custInfo.setJobSubType( null );

			return custInfo;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-apply", "取得顧客資料-連結 eloan / iloan 系統錯誤" );
		}

		return new EloanCustInfoResultBean();

	}

	private EloanCustInfoResultBean getEloanCustInfo( String idNo, Date birthDate )
	{
		try
		{
			return eloanSenderService.getEloanCustInfo( idNo, birthDate );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-apply", "取得顧客資料-連結 eloan 系統錯誤" );
			return null;
		}
	}

	private Long getFinalBranchBankId( ApplyLoan applyLoan )
	{
		if( applyLoan.getRefBorrowerApplyLoan() != null )
			return applyLoan.getRefBorrowerApplyLoan().getCodeBranchBank().getBranchBankId();

		String loanType = applyLoan.getCodeLoanType().getLoanType();
		Long branchBankId = applyLoan.getApplyLoanContactInfo().getCodeBranchBank().getBranchBankId();

		CodeBranchBankLoan codeBranchBankLoan = codeBranchBankLoanDAO.getPojoByLoanType( branchBankId, loanType );

		if( codeBranchBankLoan != null )
			return branchBankId;

		// select * from code_Branch_Bank where head_office=1
		// 會找出 943-消金處
		CodeBranchBank codeBranchBank = codeBranchBankDAO.getPojoByHeadOffice( true );

		return codeBranchBank.getBranchBankId();

	}

	private ApplyAddressParamBean getHomeAddressParamBean( PersonalContactParamBean contactBean )
	{
		ApplyAddressParamBean paramBean = new ApplyAddressParamBean();
		paramBean.setTownCode( contactBean.getHomeAddressTownCode() );
		paramBean.setVillage( contactBean.getHomeAddressVillage() );
		paramBean.setNeighborhood( contactBean.getHomeAddressNeighborhood() );
		paramBean.setStreet( contactBean.getHomeAddressStreet() );
		paramBean.setSection( contactBean.getHomeAddressSection() );
		paramBean.setLane( contactBean.getHomeAddressLane() );
		paramBean.setAlley( contactBean.getHomeAddressAlley() );
		paramBean.setNo( contactBean.getHomeAddressNo() );
		paramBean.setFloor( contactBean.getHomeAddressFloor() );
		paramBean.setRoom( contactBean.getHomeAddressRoom() );

		return paramBean;
	}

	private List<String> getIdentities( int identityFlag )
	{
		List<String> result = new ArrayList<>();

		if( identityFlag == 0 )
			return result;

		if( identityFlag == 1 )
			result.add( IdentityFlagEnum.EMPLOYEE.getName() );

		if( identityFlag == 2 )
			result.add( IdentityFlagEnum.LOANEE.getName() );

		if( identityFlag == 3 )
			result.add( IdentityFlagEnum.EMPLOYEE_AND_LOANEE.getName() );

		return result;

	}

	private Integer getIdentityFlag( String idNo )
	{
		boolean isEmployee = checkIsEmployee( idNo );
		boolean isLoanee = checkIsLoanee( idNo );

		if( BooleanUtils.isTrue( isEmployee ) && BooleanUtils.isTrue( isLoanee ) )
			return ( IdentityFlagEnum.EMPLOYEE_AND_LOANEE.getContext() );

		if( BooleanUtils.isTrue( isEmployee ) )
			return IdentityFlagEnum.EMPLOYEE.getContext();

		if( BooleanUtils.isTrue( isLoanee ) )
			return IdentityFlagEnum.LOANEE.getContext();

		return IdentityFlagEnum.NONE.getContext();
	}

	private EloanCustInfoResultBean getIloanCustInfo( String idNo, Date birthDate )
	{
		try
		{
			return iloanSenderService.getILoanCustInfo( idNo, birthDate );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-apply", "取得顧客資料-連結 iloan 系統錯誤" );
			return null;
		}
	}

	private InternalCustInfoResultBean getInternalCustInfo( String idNo )
	{
		try
		{
			InternalCustInfoResultBean internalCustInfo = otherDataBaseClientService.getInternalCustInfo( idNo );

			if( StringUtils.isNotBlank( internalCustInfo.getTitleType() ) && codeTitleTypeDAO.readToNull( internalCustInfo.getTitleType() ) == null )
				internalCustInfo.setTitleType( null );

			return internalCustInfo;
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-apply", "取得顧客資料-連結 dw 錯誤" );
		}

		return new InternalCustInfoResultBean();
	}

	private EloanCustInfoResultBean getLastestCustInfo( EloanCustInfoResultBean eloanResult, EloanCustInfoResultBean iloanResult )
	{
		if( !Objects.isNull( eloanResult ) && Objects.isNull( iloanResult ) )
			return eloanResult;
		if( Objects.isNull( eloanResult ) && !Objects.isNull( iloanResult ) )
			return iloanResult;

		if( Objects.isNull( eloanResult.getUpdatedDate() ) || Objects.isNull( iloanResult.getUpdatedDate() ) )
			return eloanResult;

		return ( eloanResult.getUpdatedDate() - iloanResult.getUpdatedDate() ) >= 0 ? eloanResult : iloanResult;
	}

	private Long getLatestLoanIdIn6Months( String idNo, Date birthDate, String loanType )
	{
		String identityType = getCurrentIdentityType();

		if( identityType.equals( IdentityTypeEnum.OTP.getContext() ) )
			return loanDAO.getLatestLoanIdInDaysByOtp( idNo, birthDate, getMobileNumber(), loanType, DAY_DIFF_VALUE );

		return loanDAO.getLatestLoanIdInDays( idNo, birthDate, null, loanType, DAY_DIFF_VALUE );
	}

	private List<String> getLoaneeBankCode( String idNo )
	{
		return otherDataBaseClientService.getLoaneeBankCode( idNo );
	}

	private ApplyAddressParamBean getMailingAddressParamBean( PersonalContactParamBean contactBean )
	{
		ApplyAddressParamBean paramBean = new ApplyAddressParamBean();
		paramBean.setTownCode( contactBean.getMailingAddressTownCode() );
		paramBean.setVillage( contactBean.getMailingAddressVillage() );
		paramBean.setNeighborhood( contactBean.getMailingAddressNeighborhood() );
		paramBean.setStreet( contactBean.getMailingAddressStreet() );
		paramBean.setSection( contactBean.getMailingAddressSection() );
		paramBean.setLane( contactBean.getMailingAddressLane() );
		paramBean.setAlley( contactBean.getMailingAddressAlley() );
		paramBean.setNo( contactBean.getMailingAddressNo() );
		paramBean.setFloor( contactBean.getMailingAddressFloor() );
		paramBean.setRoom( contactBean.getMailingAddressRoom() );

		return paramBean;
	}

	private String getMailMessage( String loanType, String caseNo )
	{
		String uploadUrl = propertyBean.getUploadUrl();
		String downloadUrl = propertyBean.getDownloadUrl();

		String loanStr = "信用貸款";
		String attachmentStr = "1.<strong>身分證正反面</strong><br />2.<strong>財力文件</strong>(如薪轉存摺封面與內頁、薪資扣繳憑單或報稅所得等)<br />";

		if( LoanTypeEnum.HOUSE_LOAN.getContext().equals( loanType ) )
		{
			loanStr = "房屋貸款";
			attachmentStr += "3.<strong>其他文件</strong> (如買賣契約書、土地及建物所有權狀等)<br />";
		}

		return "<p><span style=\"font-size:16px;\">感謝您於 " + CommonDateStringUtils.transDate2String( new Date(), APPLY_DATE_PATTERN ) + " 線上申請兆豐銀行"
			+ loanStr + "！<br />" + "您的案件編號為" + caseNo + "<br />建議立即點選<a href=\"" + uploadUrl
			+ "\" rel=\"noopener\" target=\"_blank\">上傳文件</a>，提供您的<br /><br />" + attachmentStr + "<br />我們將於收到完整資料後盡速為您審核！<br /><br /></span></p>"
			+ "<p><span style=\"font-size: 16px;\">註1：本郵件為填寫資料送出後提供您留底，若您已上傳文件可免重新上傳。<br />註2：本郵件所附申請書檔案開啟密碼設定為您的身分證字號，第一個字母請大寫，<br />"
			+ "或可透過<a href=\"" + downloadUrl + "\">調閱貸款申請書</a>功能線上查閱您的申請書內容。</span></p>";

	}

	private String getMailTitle( String loanType )
	{
		if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) )
			return "兆豐銀行個人信用貸款線上申請書";

		return "兆豐銀行房屋貸款線上申請書";
	}

	private BillhunterMailTypeEnum getMailType( String loanType )
	{
		if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) )
			return BillhunterMailTypeEnum.PERSONAL_LOAN_APPLY_COMPLETED;

		return BillhunterMailTypeEnum.HOUSE_LOAN_APPLY_COMPLETED;
	}

	private String getMobileNumber()
	{
		return userClientService.getCurrentIdentityInfoResult().getMobileNumber();
	}

	/**
	 * 取得感謝頁訊息內容
	 *
	 * @param loanType
	 * @return
	 */
	private String getThankyouContent( String loanType )
	{
		String identityType = getCurrentIdentityType();
		// ==================
		String idNo = getIdNo();
		Date birthDate = getBirthDate();
		int DAY_DIFF_VALUE = 180;
		Long loanId = loanDAO.getLatestLoanIdInDays( idNo, birthDate, null, loanType, DAY_DIFF_VALUE );

		ApplyLoan applyLoan = applyLoanDAO.readToNull( loanId );
		String loanPlanCode = "";
		if( applyLoan != null )
			loanPlanCode = StringUtils.trim( applyLoan.getLoanPlanCode() );
		// ==================
		if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType ) )
			return generateService.getPersonalLoanMessage( identityType, loanPlanCode );

		if( LoanTypeEnum.HOUSE_LOAN.getContext().equals( loanType ) )
			return generateService.getHouseLoanMessage( identityType );

		return null;

	}

	private boolean isGroupLoan( String loanPlanCode )
	{
		if( loanPlanCode.length() == 4 && ( loanPlanCode.startsWith( "E7" ) || loanPlanCode.startsWith( "E8" ) || loanPlanCode.startsWith( "E9" )
			|| loanPlanCode.equals( "E014" ) || loanPlanCode.equals( "E015" ) || loanPlanCode.equals( "M003" ) ) )
			return true;

		if( loanPlanCode.length() == 4 && loanPlanCode.matches( "^C[0-9]{3}$" ) )
		{
			Integer code = Integer.parseInt( loanPlanCode.substring( 1 ) );
			return 1 <= code && code <= 27;
		}

		return false;
	}

	private AgreementResBean mapAgreementResBean( List<ApplyAgreed> agreedList )
	{
		List<AgreedBean> agreedBeans = new ArrayList<>();

		for( ApplyAgreed agreed : agreedList )
		{
			AgreedBean agreedBean = new AgreedBean();
			agreedBean.setTitle( agreed.getTitle() );
			agreedBean.setContent( agreed.getContent() );
			agreedBean.setAgreedItemBeans( getAgreedItems( agreed.getAgreedId() ) );

			agreedBeans.add( agreedBean );
		}

		AgreementResBean resBean = new AgreementResBean();
		resBean.setAgreedBeans( agreedBeans );

		return resBean;
	}

	private PersonalGuaranteeResBean mapGuaranteeInfo( ApplyLoanGuaranteeInfo guaranteeInfo, IdentityInfoResultBean identityInfo )
	{
		PersonalGuaranteeResBean resBean = new PersonalGuaranteeResBean();

		if( guaranteeInfo == null )
			return resBean;

		CodeUserSubType codeUserSubType = codeUserSubTypeDAO.read( identityInfo.getUserSubType() );

		resBean.setUserSubType( codeUserSubType.getUserSubType() );
		resBean.setUserSubTypeName( codeUserSubType.getName() );

		if( guaranteeInfo.getCodeGuarantyReason() != null )
		{
			resBean.setGuarantyReasonCode( guaranteeInfo.getCodeGuarantyReason().getGuarantyReasonCode() );
			resBean.setGuarantyReasonName( guaranteeInfo.getCodeGuarantyReason().getName() );
		}

		if( guaranteeInfo.getCodeRelationBorrowerType() != null )
		{
			resBean.setRelationBorrowerType( guaranteeInfo.getCodeRelationBorrowerType().getRelationBorrowerType() );
			resBean.setRelationBorrowerTypeName( guaranteeInfo.getCodeRelationBorrowerType().getName() );
		}

		resBean.setOtherGuarantyReason( guaranteeInfo.getOtherGuarantyReason() );
		resBean.setIsCohabiting( guaranteeInfo.isCohabitationFlag() );

		return resBean;
	}

	private LoanContentResBean mapLoanApplyContent( ApplyLoanContent content )
	{
		LoanContentResBean resBean = new LoanContentResBean();

		if( content == null )
			return resBean;

		resBean.setLoanRequestAmt( content.getLoanRequestAmt() );
		resBean.setLoanPurposeCode( content.getCodeLoanPurpose().getLoanPurposeId().toString() );
		resBean.setLoanPurposeName( content.getCodeLoanPurpose().getName() );
		resBean.setOtherPurpose( content.getOtherPurpose() );
		resBean.setLoanPeriodCode( content.getCodeLoanPeriod().getLoadPeriodCode() );
		resBean.setLoanPeriodName( content.getCodeLoanPeriod().getName() + "年" );
		resBean.setNotificationCode( content.getCodeNotification().getNotificationCode() );
		resBean.setNotificationName( content.getCodeNotification().getName() );
		resBean.setIsIncreasingLoan( content.getIncreasingLoan() );
		resBean.setAppnBankCode( content.getAppnBankCode() );
		resBean.setAppnDpAcct( content.getAppnDpAcct() );
		resBean.setRestrictContr( content.getRestrictContr() );
		resBean.setNonPrivateUsageSubType( content.getNonPrivateUsageSubType() );
		resBean.setUrlToIdentifyFraud( content.getUrlToIdentifyFraud() );
		if( !Objects.isNull( content.getCodeCaseSource() ) )
			resBean.setCaseSourceName( content.getCodeCaseSource().getName() );

		if( content.getCodeMortgageType() != null )
		{
			resBean.setMortgageType( content.getCodeMortgageType().getMortgageType() );
			resBean.setMortgageTypeName( content.getCodeMortgageType().getName() );
		}

		if( content.getCodeNonPrivateUsageType() != null )
		{
			resBean.setNonPrivateUsageType( content.getCodeNonPrivateUsageType().getNonPrivateUsageType() );
			resBean.setNonPrivateUsageTypeName( content.getCodeNonPrivateUsageType().getName() );
		}

		if( content.getCodePrivateUsageType() != null )
		{
			resBean.setPrivateUsageType( content.getCodePrivateUsageType().getPrivateUsageType() );
			resBean.setPrivateUsageTypeName( content.getCodePrivateUsageType().getName() );
		}

		if( content.getCodeGracePeriod() != null )
		{
			resBean.setGracePeriodCode( content.getCodeGracePeriod().getGracePeriodCode() );
			resBean.setGracePeriodName( content.getCodeGracePeriod().getName() + "年" );
		}

		if( content.getCodeCaseSource() != null )
			resBean.setCaseSourceCode( content.getCodeCaseSource().getCaseSourceCode() );

		ApplyAddress collateralAddress = content.getApplyAddress();
		if( collateralAddress != null )
		{
			resBean.setCollateralAddressCityCode( collateralAddress.getCodeTown().getCodeCity().getCityCode() );
			resBean.setCollateralAddressCityName( collateralAddress.getCodeTown().getCodeCity().getName() );
			resBean.setCollateralAddressTownCode( collateralAddress.getCodeTown().getTownCode() );
			resBean.setCollateralAddressTownName( collateralAddress.getCodeTown().getName() );
			resBean.setCollateralAddressVillage( collateralAddress.getVillage() );
			resBean.setCollateralAddressNeighborhood( collateralAddress.getNeighborhood() );
			resBean.setCollateralAddressStreet( collateralAddress.getStreet() );
			resBean.setCollateralAddressSection( collateralAddress.getSection() );
			resBean.setCollateralAddressLane( collateralAddress.getLane() );
			resBean.setCollateralAddressAlley( collateralAddress.getAlley() );
			resBean.setCollateralAddressNo( collateralAddress.getNo() );
			resBean.setCollateralAddressFloor( collateralAddress.getFloor() );
			resBean.setCollateralAddressRoom( collateralAddress.getRoom() );
		}

		return resBean;
	}

	private List<LoanRelationResBean> mapLoanApplyRelationResBean( Set<ApplyLoanRelation> applyLoanRelations )
	{
		List<LoanRelationResBean> resBeans = new ArrayList<>();

		for( ApplyLoanRelation relation : applyLoanRelations.stream().collect( Collectors.toList() ) )
		{
			LoanRelationResBean resBean = new LoanRelationResBean();
			resBean.setRelationName( relation.getRelationName() );
			resBean.setRelationIdNo( relation.getRelationIdNo() );
			resBean.setRelationType( relation.getCodeRelationType().getRelationType() );
			resBean.setRelationTypeName( relation.getCodeRelationType().getName() );

			resBeans.add( resBean );
		}

		return resBeans;
	}

	private List<LoanServedResBean> mapLoanApplyServedResBean( Set<ApplyLoanServed> applyLoanServeds )
	{
		List<LoanServedResBean> resBeans = new ArrayList<>();
		for( ApplyLoanServed served : applyLoanServeds.stream().collect( Collectors.toList() ) )
		{
			LoanServedResBean resBean = new LoanServedResBean();
			resBean.setCompanyName( served.getCompanyName() );
			resBean.setServedTitle( served.getServedTitle() );
			resBean.setTaxNo( served.getTaxNo() );
			resBean.setComment( served.getComment() );
			resBean.setRepresentativeType( served.getCodeRepresentativeType().getRepresentativeType() );
			resBean.setRepresentativeTypeName( served.getCodeRepresentativeType().getName() );

			resBeans.add( resBean );
		}

		return resBeans;
	}

	private PersonalContactResBean mapLoanContactInfo( ApplyLoanContactInfo contactInfo, String introduceBr1st )
	{
		PersonalContactResBean resBean = new PersonalContactResBean();
		resBean.setMailingAddressSameToHome( true );

		if( contactInfo == null )
			return resBean;

		resBean.setEmail( contactInfo.getEmail() );
		resBean.setHasHomePhone( !StringUtils.isAnyBlank( contactInfo.getHomePhoneCode(), contactInfo.getHomePhoneNumber() ) );
		resBean.setHomePhoneCode( contactInfo.getHomePhoneCode() );
		resBean.setHomePhoneNumber( contactInfo.getHomePhoneNumber() );
		resBean.setMobileNumber( contactInfo.getMobileNumber() );

		if( contactInfo.getCodeResidenceStatus() != null )
		{
			resBean.setResidenceStatusCode( contactInfo.getCodeResidenceStatus().getResidenceStatusCode() );
			resBean.setResidenceStatusName( contactInfo.getCodeResidenceStatus().getName() );
		}

		ApplyAddress homeAddress = contactInfo.getHomeAddress();
		if( homeAddress != null )
		{
			resBean.setHomeAddressCityCode( homeAddress.getCodeTown().getCodeCity().getCityCode() );
			resBean.setHomeAddressCityName( homeAddress.getCodeTown().getCodeCity().getName() );
			resBean.setHomeAddressTownCode( homeAddress.getCodeTown().getTownCode() );
			resBean.setHomeAddressTownName( homeAddress.getCodeTown().getName() );
			resBean.setHomeAddressVillage( homeAddress.getVillage() );
			resBean.setHomeAddressNeighborhood( homeAddress.getNeighborhood() );
			resBean.setHomeAddressStreet( homeAddress.getStreet() );
			resBean.setHomeAddressSection( homeAddress.getSection() );
			resBean.setHomeAddressLane( homeAddress.getLane() );
			resBean.setHomeAddressAlley( homeAddress.getAlley() );
			resBean.setHomeAddressNo( homeAddress.getNo() );
			resBean.setHomeAddressFloor( homeAddress.getFloor() );
			resBean.setHomeAddressRoom( homeAddress.getRoom() );
		}

		ApplyAddress mailingAddress = contactInfo.getMailingAddress();
		if( mailingAddress != null )
		{
			resBean.setMailingAddressCityCode( mailingAddress.getCodeTown().getCodeCity().getCityCode() );
			resBean.setMailingAddressCityName( mailingAddress.getCodeTown().getCodeCity().getName() );
			resBean.setMailingAddressTownCode( mailingAddress.getCodeTown().getTownCode() );
			resBean.setMailingAddressTownName( mailingAddress.getCodeTown().getName() );
			resBean.setMailingAddressVillage( mailingAddress.getVillage() );
			resBean.setMailingAddressNeighborhood( mailingAddress.getNeighborhood() );
			resBean.setMailingAddressStreet( mailingAddress.getStreet() );
			resBean.setMailingAddressSection( mailingAddress.getSection() );
			resBean.setMailingAddressLane( mailingAddress.getLane() );
			resBean.setMailingAddressAlley( mailingAddress.getAlley() );
			resBean.setMailingAddressNo( mailingAddress.getNo() );
			resBean.setMailingAddressFloor( mailingAddress.getFloor() );
			resBean.setMailingAddressRoom( mailingAddress.getRoom() );
		}

		resBean.setMailingAddressSameToHome( compareIsMailingAddressSameToHome( homeAddress, mailingAddress ) );

		resBean.setRent( contactInfo.getRent() );

		if( contactInfo.getCodeServiceAssociateDept() != null )
		{
			resBean.setServiceAssociateDeptCode( contactInfo.getCodeServiceAssociateDept().getServiceAssociateDeptCode() );
			resBean.setServiceAssociateDeptName( contactInfo.getCodeServiceAssociateDept().getName() );
			resBean.setServiceAssociate( contactInfo.getServiceAssociate() );
		}

		if( contactInfo.getCodeBranchBank() != null )
		{
			resBean.setBranchBankCode( contactInfo.getCodeBranchBank().getBranchBankId().toString() );
			resBean.setBranchBankName( contactInfo.getCodeBranchBank().getName() );
			resBean.setBranchBankAddress( contactInfo.getCodeBranchBank().getAddress() );
			resBean.setBranchBankCityCode( contactInfo.getCodeBranchBank().getCodeTown().getCodeCity().getCityCode() );
			resBean.setBranchBankCityName( contactInfo.getCodeBranchBank().getCodeTown().getCodeCity().getName() );
			resBean.setBranchBankTownCode( contactInfo.getCodeBranchBank().getCodeTown().getTownCode() );
			resBean.setBranchBankTownName( contactInfo.getCodeBranchBank().getCodeTown().getName() );
		}

		if( !Objects.isNull( introduceBr1st ) && StringUtils.isNoneBlank( introduceBr1st ) )
		{
			resBean.setServiceAssociateBranchCode( introduceBr1st );
			if( codeBranchBankDAO.getPojoByBankCode( introduceBr1st, true ) != null )
				resBean.setServiceAssociateBranchName( codeBranchBankDAO.getPojoByBankCode( introduceBr1st ).getName() );
			else
				resBean.setServiceAssociateBranchName( "" );
		}

		return resBean;
	}

	private PersonalContactResBean mapLoanContactInfo( String mobileNumber )
	{
		PersonalContactResBean resBean = new PersonalContactResBean();
		resBean.setMobileNumber( mobileNumber );
		resBean.setMailingAddressSameToHome( true );

		return resBean;
	}

	private PersonalJobResBean mapLoanJobInfo( ApplyLoanOccupation occupation )
	{
		PersonalJobResBean resBean = new PersonalJobResBean();

		if( occupation == null )
			return resBean;

		resBean.setCompanyName( occupation.getCompanyName() );
		resBean.setAnnualIncome( occupation.getAnnualIncome() );
		resBean.setSeniorityYear( occupation.getSeniorityYear() );
		resBean.setSeniorityMonth( occupation.getSeniorityMonth() );
		resBean.setCompanyTaxNo( occupation.getTaxNo() );
		resBean.setCompanyPhoneCode( occupation.getCompanyPhoneCode() );
		resBean.setCompanyPhoneNumber( occupation.getCompanyPhoneNumber() );
		resBean.setCompanyPhoneExt( occupation.getCompanyPhoneExt() );

		if( occupation.getCodeJobSubType() != null )
		{
			resBean.setJobType( occupation.getCodeJobSubType().getCodeJobType().getJobType() );
			resBean.setJobTypeName( occupation.getCodeJobSubType().getCodeJobType().getName() );
			resBean.setJobSubType( occupation.getCodeJobSubType().getJobSubTypeId().toString() );
			resBean.setJobSubTypeName( occupation.getCodeJobSubType().getName() );
		}

		if( occupation.getCodeTitleType() != null )
		{
			resBean.setTitleType( occupation.getCodeTitleType().getTitleType() );
			resBean.setTitleTypeName( occupation.getCodeTitleType().getName() );
		}

		ApplyAddress companyAddress = occupation.getApplyAddress();
		if( companyAddress != null )
		{
			resBean.setCompanyAddressCityCode( companyAddress.getCodeTown().getCodeCity().getCityCode() );
			resBean.setCompanyAddressCityName( companyAddress.getCodeTown().getCodeCity().getName() );
			resBean.setCompanyAddressTownCode( companyAddress.getCodeTown().getTownCode() );
			resBean.setCompanyAddressTownName( companyAddress.getCodeTown().getName() );
			resBean.setCompanyAddressVillage( companyAddress.getVillage() );
			resBean.setCompanyAddressNeighborhood( companyAddress.getNeighborhood() );
			resBean.setCompanyAddressStreet( companyAddress.getStreet() );
			resBean.setCompanyAddressSection( companyAddress.getSection() );
			resBean.setCompanyAddressLane( companyAddress.getLane() );
			resBean.setCompanyAddressAlley( companyAddress.getAlley() );
			resBean.setCompanyAddressNo( companyAddress.getNo() );
			resBean.setCompanyAddressFloor( companyAddress.getFloor() );
			resBean.setCompanyAddressRoom( companyAddress.getRoom() );
		}

		if( occupation.getCodeAmountPerMonth() != null )
		{
			resBean.setAmountPerMonthCode( occupation.getCodeAmountPerMonth().getAmountPerMonthCode() );
			resBean.setAmountPerMonthName( occupation.getCodeAmountPerMonth().getName() );
		}
		resBean.setEmpNo( occupation.getEmpNo() );

		return resBean;
	}

	private PersonalBasicResBean mapPersonalBasicInfo( ApplyLoan applyLoan, ApplyLoanBasic basicInfo )
	{
		PersonalBasicResBean resBean = new PersonalBasicResBean();

		if( basicInfo == null )
		{
			resBean.setIdNo( getIdNo() );
			resBean.setBirthDate( CommonDateStringUtils.transDate2String( getBirthDate() ) );

			return resBean;
		}

		resBean.setIdNo( basicInfo.getIdNo() );
		resBean.setBirthDate( CommonDateStringUtils.transDate2String( basicInfo.getBirthDate() ) );
		resBean.setName( basicInfo.getName() );
		resBean.setEngFirstName( basicInfo.getEngFirstName() );
		resBean.setEngLastName( basicInfo.getEngLastName() );

		if( basicInfo.getCodeEducationLevel() != null )
		{
			resBean.setEducationLevelCode( basicInfo.getCodeEducationLevel().getEducationLevelCode() );
			resBean.setEducationLevelName( basicInfo.getCodeEducationLevel().getName() );
		}

		if( basicInfo.getCodeMarriageStatus() != null )
		{
			resBean.setMarriageStatusCode( basicInfo.getCodeMarriageStatus().getMarriageStatusCode() );
			resBean.setMarriageStatusName( basicInfo.getCodeMarriageStatus().getName() );
		}

		resBean.setChildrenCount( ApplyLoanUtils.get_ApplyLoanBasic_childrenCount( applyLoan.getLoanPlanCode(), basicInfo ) );

		boolean hasOtherNationality = basicInfo.getCodeNationality() != null;
		resBean.setHasOtherNationality( hasOtherNationality );

		if( hasOtherNationality )
		{
			resBean.setNationalityCode( basicInfo.getCodeNationality().getNationalityCode() );
			resBean.setNationalityName( basicInfo.getCodeNationality().getName() );
		}

		return resBean;
	}

	private void saveIdentityAttachment( Long loanId, Long otherBankId, Long validatedIdentityId )
	{
		IdentityAttachmentsArgBean argBean = new IdentityAttachmentsArgBean();
		argBean.setOtherBankId( otherBankId );

		List<IdentityAttachmentResultBean> identityAttachments = identityAttachmentsClient.send( argBean );

		for( IdentityAttachmentResultBean identityAttachment : identityAttachments )
			createApplyAttachment( loanId, validatedIdentityId, identityAttachment );

	}

	/**
	 * 寄送感謝信
	 *
	 * @param applyLoan
	 */
	private void sendThankyouMail( ApplyLoan applyLoan, Long loanId )
	{
		String loanType = applyLoan.getCodeLoanType().getLoanType();

		BillhunterMailTypeEnum mailTypeEnum = getMailType( loanType );

		String subject = mailTypeEnum.getSubject();
		String category = mailTypeEnum.getCategory();
		String message = getMailMessage( loanType, applyLoan.getCaseNo() );
		String caseNo = applyLoan.getCaseNo();

		ToMailInfoBean toMailInfoBean = new ToMailInfoBean();
		toMailInfoBean.setToUserName( applyLoan.getApplyLoanBasic().getName() );
		toMailInfoBean.setToUserId( applyLoan.getApplyLoanBasic().getIdNo() );
		toMailInfoBean.setToMail( applyLoan.getApplyLoanContactInfo().getEmail() );

		String title = getMailTitle( loanType );
		String fileName = title + "-" + caseNo + "-" + new SimpleDateFormat( "yyyyMMdd" ).format( new Date() ) + ".pdf";

		String fileContent = CommonBase64Utils.base64Encoder( applyLoan.getPdfContent() );

		AttachmentFileBean attachmentFileBean = new AttachmentFileBean();
		attachmentFileBean.setFileName( fileName );
		attachmentFileBean.setFileContent( fileContent );

		BillhunterSenderArgBean argBean = new BillhunterSenderArgBean();
		argBean.setSubject( subject );
		argBean.setCategory( category );
		argBean.setTextFormat( true );
		argBean.setMessage( message );
		argBean.setToMailInfoBeans( Arrays.asList( toMailInfoBean ) );
		argBean.setAttachmentFileBeans( Arrays.asList( attachmentFileBean ) );
		argBean.setCaseNo( caseNo );

		BillhunterSenderResultBean resultBean = billhunterSenderService.send( argBean );

		if( !resultBean.isSuccess() )
		{
			applyLoanDAO.updateEraseApplyCompletedDate( loanId );
			throw new MyRuntimeException( ApplyErrorEnum.SEND_MAIL_ERROR );
		}

	}

	private void setGuaranteeBranchBank( PersonalContactResBean resBean, ApplyLoan refBorrowerApplyLoan )
	{
		CodeBranchBank codeBranchBank = refBorrowerApplyLoan.getApplyLoanContactInfo().getCodeBranchBank();
		resBean.setBranchBankCode( codeBranchBank.getBranchBankId().toString() );
		resBean.setBranchBankName( codeBranchBank.getName() );
		resBean.setBranchBankAddress( codeBranchBank.getAddress() );
		resBean.setBranchBankCityCode( codeBranchBank.getCodeTown().getCodeCity().getCityCode() );
		resBean.setBranchBankCityName( codeBranchBank.getCodeTown().getCodeCity().getName() );
		resBean.setBranchBankTownCode( codeBranchBank.getCodeTown().getTownCode() );
		resBean.setBranchBankTownName( codeBranchBank.getCodeTown().getName() );
	}

	private void setLoanRecipient( ApplyLoan applyLoan, IdentityInfoResultBean identityInfo )
	{
		int eLoanAmountLimit = Integer.parseInt(configPropService.getValue(ConfigPropEnum.MIN_PERSONAL_APPLY_AMT_TO_ELOAN));

		// iloan 白名單開關開啟時，只要在白名單裡的身分證字號，無條件進件 iloan
		if( iloanWhitelistEnable && !Objects.isNull( identityIloanWhitelistDAO.getPojoByIdNo( identityInfo.getIdNo() ) ) )
			applyLoan.setLoanRecipient( new CodeRecipientSystem( 2, "iloan" ) );
		else // 根據收件系統開關，決定送往 eloan、iloan，還是 byConditions (根據貸款內容)
		if( "eloan".equals( whichAPIServer ) )
			applyLoan.setLoanRecipient( new CodeRecipientSystem( 1, "eloan" ) );
		else if( "iloan".equals( whichAPIServer ) )
			applyLoan.setLoanRecipient( new CodeRecipientSystem( 2, "iloan" ) );
		else if( "byConditions".equals( whichAPIServer ) ) // 根據貸款內容，大於送往 eLoan 信貸申貸金額下限、團貸案件、行員送往 eloan，其他送往 iloan
			if( applyLoan.getApplyLoanContent().getLoanRequestAmt() > eLoanAmountLimit
				|| Set.of( IdentityFlagEnum.EMPLOYEE.getContext(), IdentityFlagEnum.EMPLOYEE_AND_LOANEE.getContext() )
							.contains( applyLoan.getIdentityFlag() )
				|| isGroupLoan( applyLoan.getLoanPlanCode() ) ) {
				applyLoan.setLoanRecipient( new CodeRecipientSystem( 1, "eloan" ) );
			} else {
				applyLoan.setLoanRecipient( new CodeRecipientSystem( 2, "iloan" ) );
			}
	}

	private void updateApplyLoanBasic( Long loanId, EloanCustInfoResultBean eloanCustInfo, InternalCustInfoResultBean internalCustInfo,
									   String loanPlanCode )
	{
		String name = StringUtils.isNotBlank( internalCustInfo.getName() ) ? internalCustInfo.getName() : eloanCustInfo.getName();
		String nationalityCode = "TW".equals( eloanCustInfo.getNationality() ) ? null : eloanCustInfo.getNationality();

		ApplyLoanBasicUpdatedParamBean paramBean = new ApplyLoanBasicUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setIdNo( getIdNo() );
		paramBean.setBirthDate( getBirthDate() );
		paramBean.setName( name );
		paramBean.setMarriageStatusCode( eloanCustInfo.getMarriageStatus() );
		paramBean.setEducationLevelCode( eloanCustInfo.getEducationLevel() );
		paramBean.setChildrenCount( eloanCustInfo.getChildrenCount() == null ? 0 : eloanCustInfo.getChildrenCount() );
		paramBean.setNationalityCode( nationalityCode );
		if( StringUtils.equals( loanPlanCode, LoanPlanEnum.C001.getContext() ) )
		{
			paramBean.setName( eloanCustInfo.getName() );
			paramBean.setMarriageStatusCode( null );
			paramBean.setEducationLevelCode( null );
			paramBean.setChildrenCount( 0 );
			paramBean.setNationalityCode( null );
		}
		applyLoanBasicDAO.update( paramBean );

		if( StringUtils.equals( loanPlanCode, LoanPlanEnum.C001.getContext() ) )
		{
			ApplyLoanBasicUpdatedEngNameParamBean engNameParamBean = new ApplyLoanBasicUpdatedEngNameParamBean();
			engNameParamBean.setLoanId( loanId );
			engNameParamBean.setEngNameFg( "Y" );
			engNameParamBean.setEngName( eloanCustInfo.getEngName() );
			applyLoanBasicDAO.updateWhenHasEngName( engNameParamBean );
		}
	}

	private void updateApplyLoanBasic( Long loanId, PersonalBasicParamBean basicBean )
	{
		ApplyLoanBasicUpdatedParamBean paramBean = new ApplyLoanBasicUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setIdNo( basicBean.getIdNo() );
		paramBean.setName( basicBean.getName() );
		paramBean.setEngFirstName( basicBean.getEngFirstName() );
		paramBean.setEngLastName( basicBean.getEngLastName() );
		paramBean.setBirthDate( basicBean.getBirthDate() );
		paramBean.setMarriageStatusCode( basicBean.getMarriageStatus() );
		paramBean.setEducationLevelCode( basicBean.getEducationLevel() );
		paramBean.setChildrenCount( basicBean.getChildrenCount() );
		paramBean.setNationalityCode( basicBean.getNationality() );

		applyLoanBasicDAO.update( paramBean );
	}

	private void updateApplyLoanContactInfo( Long loanId, EloanCustInfoResultBean eloanCustInfo, String mobileNumber, String userType,
											 String loanPlanCode )
	{
		ApplyLoanContactInfoUpdatedParamBean paramBean = new ApplyLoanContactInfoUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setMobileNumber( mobileNumber );
		paramBean.setResidenceStatusCode( eloanCustInfo.getResidenceStatus() );
		paramBean.setHomePhoneCode( eloanCustInfo.getHomePhoneCode() );
		paramBean.setHomePhoneNumber( eloanCustInfo.getHomePhone() );
		paramBean.setEmail( eloanCustInfo.getEmail() ); // 中鋼消貸總公司員工，其e-mail是必填欄位(為了讓 BillHunter 寄送 申請書PDF檔)，會由中鋼的EIP系統提供

		if( StringUtils.equals( loanPlanCode, LoanPlanEnum.C001.getContext() ) )
			paramBean.setResidenceStatusCode( "" );

		if( UserTypeEnum.BORROWER.getContext().equals( userType ) && StringUtils.isNotBlank( loanPlanCode ) )
		{
			String assign_brNo = "";

			if( StringUtils.isBlank( assign_brNo ) && ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
				assign_brNo = ApplyLoanUtils.get_ChinaSteelGroup_BatchPersonalLoan_brNo();

			if( StringUtils.isBlank( assign_brNo ) && ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode ) )
				assign_brNo = ApplyLoanUtils.get_co70647919_C101_BatchPersonalLoan_brNo();

			if( StringUtils.isBlank( assign_brNo ) && ApplyLoanUtils.is_co80601119_D001_BatchPersonalLoan( loanPlanCode ) )
				assign_brNo = ApplyLoanUtils.get_co80601119_D001_BatchPersonalLoan_brNo();

			if( StringUtils.isBlank( assign_brNo ) && eloanCustInfo != null && StringUtils.isNotBlank( eloanCustInfo.getGrpOwnBrId() ) )
				assign_brNo = eloanCustInfo.getGrpOwnBrId();

			if( StringUtils.isNotBlank( assign_brNo ) )
			{
				// 若已有指派分行
				CodeBranchBank codeBranchBank_assign_brNo = codeBranchBankDAO.getPojoByBankCode( assign_brNo );
				if( codeBranchBank_assign_brNo != null )
					paramBean.setBranchBankId( codeBranchBank_assign_brNo.getBranchBankId() );
			}
		}

		applyLoanContactInfoDAO.update( paramBean );

		// ======================================
		if( StringUtils.equals( loanPlanCode, LoanPlanEnum.C001.getContext() ) )
		{
			Long homeAddrTextId = gen_AddrText_PK( eloanCustInfo.getfAddrPostalCode(), eloanCustInfo.getfAddr() );
			Long mailingAddrTextId = gen_AddrText_PK( eloanCustInfo.getCoAddrPostalCode(), eloanCustInfo.getCoAddr() );
			// ~~~~~~
			ApplyLoanContactInfoUpdatedAddrTextParamBean paramAddrTextBean = new ApplyLoanContactInfoUpdatedAddrTextParamBean();
			paramAddrTextBean.setLoanId( loanId );
			paramAddrTextBean.setHomeAddrTextId( homeAddrTextId );
			paramAddrTextBean.setMailingAddrTextId( mailingAddrTextId );
			applyLoanContactInfoDAO.update( paramAddrTextBean );
		}
	}

	private void updateApplyLoanContactInfo( Long loanId, PersonalContactParamBean contactBean )
	{
		boolean isMailingAddressSameToHome = compareIsMailingAddressSameToHome( contactBean );

		ApplyAddress homeAddress = applyLoanDAO.read( loanId ).getApplyLoanContactInfo().getHomeAddress();
		Long homeAddressId = null;
		if( homeAddress == null )
			homeAddressId = applyAddressDAO.create( getHomeAddressParamBean( contactBean ) );
		else
			homeAddressId = applyAddressDAO.update( homeAddress.getAddressId(), getHomeAddressParamBean( contactBean ) );

		ApplyAddress mailingAddress = applyLoanDAO.read( loanId ).getApplyLoanContactInfo().getMailingAddress();
		Long mailingAddressId = null;
		if( isMailingAddressSameToHome )
			mailingAddressId = homeAddressId;
		else if( mailingAddress == null || mailingAddress.getAddressId().equals( homeAddressId ) )
			mailingAddressId = applyAddressDAO.create( getMailingAddressParamBean( contactBean ) );
		else
			mailingAddressId = applyAddressDAO.update( mailingAddress.getAddressId(), getMailingAddressParamBean( contactBean ) );

		ApplyLoanContactInfoUpdatedParamBean paramBean = new ApplyLoanContactInfoUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setHomeAddressId( homeAddressId );
		paramBean.setMailingAddressId( mailingAddressId );
		paramBean.setResidenceStatusCode( contactBean.getResidenceStatus() );
		paramBean.setHouseStatusCode( contactBean.getHouseStatus() );
		paramBean.setHomePhoneCode( contactBean.getHomePhoneCode() );
		paramBean.setHomePhoneNumber( contactBean.getHomePhoneNumber() );
		paramBean.setEmail( contactBean.getEmail() );
		paramBean.setMobileNumber( contactBean.getMobileNumber() );
		paramBean.setBranchBankId( getBranchBankId( loanId, contactBean.getBranchBankCode() ) );
		if( true ) // 即使客戶在「其它分行」已有貸款，只要行銷方案是中鋼消貸，也要指派到 002 分行
		{
			ApplyLoan applyLoan = applyLoanDAO.read( loanId );
			String loanPlanCode = applyLoan.getLoanPlanCode();
			String assign_brNo = "";
			if( StringUtils.isNotBlank( loanPlanCode ) )
				if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
				assign_brNo = ApplyLoanUtils.get_ChinaSteelGroup_BatchPersonalLoan_brNo();
				else if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode ) )
				assign_brNo = ApplyLoanUtils.get_co70647919_C101_BatchPersonalLoan_brNo();
				else if( ApplyLoanUtils.is_co80601119_D001_BatchPersonalLoan( loanPlanCode ) )
				assign_brNo = ApplyLoanUtils.get_co80601119_D001_BatchPersonalLoan_brNo();
				else
				assign_brNo = ""; // 像 M001 就沒有指定的承辦分行
			// =======================
			if( StringUtils.isNotBlank( assign_brNo ) )
			{
				CodeBranchBank codeBranchBank_assign_brNo = codeBranchBankDAO.getPojoByBankCode( assign_brNo );
				if( codeBranchBank_assign_brNo != null )
					paramBean.setBranchBankId( codeBranchBank_assign_brNo.getBranchBankId() );
			}
		}
		paramBean.setServiceAssociateDeptCode( contactBean.getServiceAssociateDeptCode() );
		paramBean.setServiceAssociate( contactBean.getServiceAssociate() );
		paramBean.setRent( contactBean.getRent() );

		applyLoanContactInfoDAO.update( paramBean );
	}

	private void updateApplyLoanContent( Long loanId, LoanContentParamBean loanContentParamBean )
	{
		ApplyLoanContentUpdatedParamBean paramBean = new ApplyLoanContentUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setLoanRequestAmt( loanContentParamBean.getLoanRequestAmt() );
		paramBean.setLoanPurposeId( Long.parseLong( loanContentParamBean.getLoanPurpose() ) );
		paramBean.setOtherPurpose( loanContentParamBean.getOtherPurpose() );
		paramBean.setLoanPeriod( loanContentParamBean.getLoanPeriod() );
		paramBean.setCollateralAddressId( getCollateralAddressId( loanId, loanContentParamBean ) );
		paramBean.setGracePeriodCode( loanContentParamBean.getGracePeriodCode() );
		paramBean.setNotificationCode( loanContentParamBean.getNotificationCode() );
		paramBean.setMortgageType( loanContentParamBean.getMortgageType() );
		paramBean.setNonPrivateUsageType( loanContentParamBean.getNonPrivateUsageType() );
		paramBean.setNonPrivateUsageSubType( loanContentParamBean.getNonPrivateUsageSubType() );
		paramBean.setPrivateUsageType( loanContentParamBean.getPrivateUsageType() );
		paramBean.setIsIncreasingLoan( loanContentParamBean.getIsIncreasingLoan() );
		paramBean.setAppnBankCode( loanContentParamBean.getAppnBankCode() );
		paramBean.setAppnDpAcct( loanContentParamBean.getAppnDpAcct() );
		paramBean.setCaseSourceCode( loanContentParamBean.getCaseSourceCode() );
		paramBean.setUrlToIdentifyFraud( loanContentParamBean.getUrlToIdentifyFraud() );
		applyLoanContentDAO.update( paramBean );

	}

	private void updateApplyLoanGuaranteeInfo( Long loanId, String userSubType, PersonalGuaranteeParamBean guaranteeParamBean )
	{
		ApplyLoanGuaranteeInfoUpdatedParamBean paramBean = new ApplyLoanGuaranteeInfoUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setRelationBorrowerType( guaranteeParamBean.getRelationBorrowerType() );
		paramBean.setCohabitationFlag( guaranteeParamBean.getIsCohabiting() );

		List<String> requiredGuarantyReason = Arrays.asList( UserSubTypeEnum.N_GUARANTOR.getContext(), UserSubTypeEnum.G_GUARANTOR.getContext() );
		if( requiredGuarantyReason.contains( userSubType ) )
		{
			paramBean.setGuarantyReasonCode( guaranteeParamBean.getGuarantyReasonCode() );
			paramBean.setOtherGuarantyReason( guaranteeParamBean.getOtherGuarantyReason() );
		}

		applyLoanGuaranteeInfoDAO.update( paramBean );
	}

	private void updateApplyLoanOccupation( Long loanId, EloanCustInfoResultBean eloanCustInfo, InternalCustInfoResultBean internalCustInfo,
											String loanPlanCode )
	{

		Long jobSubTypeId = StringUtils.isAnyBlank( eloanCustInfo.getJobType(),
													eloanCustInfo.getJobSubType() )
																					? null
																					: codeJobSubTypeDAO
																								.getPojoByTypes( eloanCustInfo.getJobType(),
																												 eloanCustInfo.getJobSubType() )
																								.getJobSubTypeId();

		ApplyLoanOccupationUpdatedParamBean paramBean = new ApplyLoanOccupationUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setTitleType( internalCustInfo.getTitleType() );
		paramBean.setJobSubTypeId( jobSubTypeId );
		paramBean.setCompanyName( eloanCustInfo.getCompanyName() );
		paramBean.setCompanyTaxNo( eloanCustInfo.getCompanyTaxNo() );
		paramBean.setCompanyPhoneCode( eloanCustInfo.getCompanyPhoneCode() );
		paramBean.setCompanyPhoneNumber( eloanCustInfo.getCompanyPhone() );
		paramBean.setCompanyPhoneExt( eloanCustInfo.getCompanyPhoneExt() );

		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
		{
			paramBean.setCompanyName( null );
			paramBean.setCompanyTaxNo( null );
			// ======================
			CodeLoanPlan codeLoanPlan = codeLoanPlanDAO.read( loanPlanCode );
			if( codeLoanPlan != null )
			{
				paramBean.setCompanyName( codeLoanPlan.getCompanyName() );
				paramBean.setCompanyTaxNo( codeLoanPlan.getTaxno() );
			}

			if( StringUtils.equals( loanPlanCode, LoanPlanEnum.C001.getContext() ) )
			{
				paramBean.setTitleType( null );
				paramBean.setJobSubTypeId( null );
				paramBean.setEmpNo( eloanCustInfo.getEmpNo() );
				paramBean.setJobPosition( eloanCustInfo.getJobPosition() );
				BigDecimal bd_AnnualIncome = ApplyLoanUtils.get_floorValue_divideWAN( eloanCustInfo.getPayAmt() );
				paramBean.setAnnualIncome( bd_AnnualIncome != null ? bd_AnnualIncome.intValue() : null );
				paramBean.setSeniorityYear( eloanCustInfo.getSnrY() );
				paramBean.setSeniorityMonth( eloanCustInfo.getSnrM() );
			}
		}
		else if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode )
			|| ApplyLoanUtils.is_co80601119_D001_BatchPersonalLoan( loanPlanCode ) )
		{
			String companyId = StringUtils.trim( eloanCustInfo.getCompanyId() );
			if( StringUtils.isNotBlank( companyId ) )
			{
				paramBean.setCompanyTaxNo( companyId );
				Map<String, String> taxNoAndName = ApplyLoanUtils.get_loanPlan_allow_TaxNoAndName( loanPlanCode );

				if( taxNoAndName.containsKey( companyId ) )
					paramBean.setCompanyName( taxNoAndName.get( companyId ) );
			}
		}

		applyLoanOccupationDAO.update( paramBean );

	}

	private void updateApplyLoanOccupation( Long loanId, PersonalJobParamBean jobBean )
	{
		ApplyLoanOccupation applyLoanOccupation = applyLoanDAO.read( loanId ).getApplyLoanOccupation();
		ApplyAddress companyAddress = applyLoanOccupation == null ? null : applyLoanOccupation.getApplyAddress();

		Long companyAddressId = null;
		if( companyAddress == null )
			companyAddressId = applyAddressDAO.create( getCompanyAddressParamBean( jobBean ) );
		else
			companyAddressId = applyAddressDAO.update( companyAddress.getAddressId(), getCompanyAddressParamBean( jobBean ) );

		ApplyLoanOccupationUpdatedParamBean paramBean = new ApplyLoanOccupationUpdatedParamBean();
		paramBean.setLoanId( loanId );
		paramBean.setJobSubTypeId( jobBean.getJobSubType() == null ? null : Long.parseLong( jobBean.getJobSubType() ) );
		paramBean.setCompanyName( jobBean.getCompanyName() );
		paramBean.setAnnualIncome( jobBean.getAnnualIncome() );
		paramBean.setSeniorityYear( jobBean.getSeniorityYear() );
		paramBean.setSeniorityMonth( jobBean.getSeniorityMonth() );
		paramBean.setTitleType( jobBean.getTitleType() );
		paramBean.setCompanyTaxNo( jobBean.getCompanyTaxNo() );
		paramBean.setCompanyPhoneCode( jobBean.getCompanyPhoneCode() );
		paramBean.setCompanyPhoneNumber( jobBean.getCompanyPhoneNumber() );
		paramBean.setCompanyPhoneExt( jobBean.getCompanyPhoneExt() );
		paramBean.setCompanyAddressId( companyAddressId );
		paramBean.setAmountPerMonthCode( jobBean.getAmountPerMonthCode() );
		paramBean.setEmpNo( jobBean.getEmpNo() );
		paramBean.setJobPosition( applyLoanOccupation.getJobPosition() );

		applyLoanOccupationDAO.update( paramBean );
	}

	private void updateExistedData( Long loanId, IdentityInfoResultBean identityInfo, String loanType, String loanPlanCode,
									EloanCustInfoResultBean iloanCustInfo )
	{
		EloanCustInfoResultBean eloanCustInfo = getCustInfo( getIdNo(), getBirthDate(), iloanCustInfo );

		InternalCustInfoResultBean internalCustInfo = getInternalCustInfo( getIdNo() );

		String userType = identityInfo.getUserType();

		updateApplyLoanBasic( loanId, eloanCustInfo, internalCustInfo, loanPlanCode );
		updateApplyLoanContactInfo( loanId, eloanCustInfo, identityInfo.getMobileNumber(), userType, loanPlanCode );
		updateApplyLoanOccupation( loanId, eloanCustInfo, internalCustInfo, loanPlanCode );

		if( UserTypeEnum.BORROWER.getContext().equals( userType ) )
		{
			// 組出 dbo.apply_loan_content
			if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
			{
				// 鎖定「週轉」 select * from dbo.code_loan_purpose
				CodeLoanPurpose codeLoanPurpose = codeLoanPurposeDAO.getPojoByLoanTypeAndPurposeCode( loanType, "N" );

				CodeLoanPeriod codeLoanPeriod = codeLoanPeriodDAO.getPojoByName( ApplyLoanUtils.get_ChinaSteelGroup_BatchPersonalLoan_loanPeriod() );

				if( StringUtils.equals( loanPlanCode, LoanPlanEnum.C001.getContext() ) )
				{
					LoanContentParamBean loanContentParamBean = new LoanContentParamBean();
					BigDecimal grpApplyAmtWAN = ApplyLoanUtils.get_floorValue_divideWAN( eloanCustInfo.getGrpApplyAmt() );
					loanContentParamBean.setLoanRequestAmt( grpApplyAmtWAN == null ? 0 : grpApplyAmtWAN.intValue() );
					loanContentParamBean.setLoanPurpose( String.valueOf( codeLoanPurpose.getLoanPurposeId() ) );
					loanContentParamBean.setLoanPeriod( codeLoanPeriod.getLoadPeriodCode() ); // 鎖定「7年」 select * from
					// dbo.code_loan_period
					loanContentParamBean.setAppnBankCode( eloanCustInfo.getAppnBankCode() );
					loanContentParamBean.setAppnDpAcct( eloanCustInfo.getDpAcct() );
					/*
					 * 貸款利息收據通知方式 {1:書面寄送(通訊地址) , 2:電子郵件, 3:不通知} select * from dbo.code_notification
					 *
					 * 利率變動通知方式{對保} dbo.apply_signing_appropriation 欄位
					 * rate_adjustment_notificaiton_code 其值域 {2:書面, 3:電子郵件} select * from
					 * dbo.code_rate_adjustment_notification where disabled=0
					 */
					loanContentParamBean.setNotificationCode( "3" );
					// ~~~
					createLoanContent( loanId, loanContentParamBean );
				}
				else
				{
					// 中鋼子公司
				}
			}
		}
		else
		{
			// 申貸金額/年限/用途，只有「主借人」需要填寫
		}
	}

	/**
	 * 更新案件pdf內容
	 *
	 * @param applyLoan
	 * @throws IOException
	 */
	private void updatePdfContent( ApplyLoan applyLoan, Date passed_applyTs ) throws IOException
	{
		byte[] pdfContent = generateService.generateLoanApplyPdf( applyLoan );

		if( pdfContent.length > 0 )
			applyLoanDAO.updatePdfContent( applyLoan.getLoanId(), pdfContent );
	}

}
