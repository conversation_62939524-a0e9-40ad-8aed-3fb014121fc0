package com.megabank.olp.apply.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeSigningSendStatus;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeSigningSendStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeSigningSendStatusDAO extends BasePojoDAO<CodeSigningSendStatus, String>
{
	public CodeSigningSendStatus read( String sendStatusCode )
	{
		Validate.notBlank( sendStatusCode );

		return getPojoByPK( sendStatusCode, CodeSigningSendStatus.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeSigningSendStatus> getPojoClass()
	{
		return CodeSigningSendStatus.class;
	}
}
