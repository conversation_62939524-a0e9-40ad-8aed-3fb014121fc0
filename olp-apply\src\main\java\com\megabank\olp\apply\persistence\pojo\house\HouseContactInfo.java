package com.megabank.olp.apply.persistence.pojo.house;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.megabank.olp.apply.persistence.pojo.code.CodeLoanPlan;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;

import com.megabank.olp.apply.persistence.pojo.apply.note.ApplyNote;
import com.megabank.olp.apply.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.apply.persistence.pojo.code.CodeProcess;
import com.megabank.olp.base.bean.BaseBean;

/**
 * The HouseContactInfo is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "house_contact_info", uniqueConstraints = @UniqueConstraint( columnNames = "case_no" ) )
public class HouseContactInfo extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "house_contact_info";

	public static final String CONTACT_INFO_ID_CONSTANT = "contactInfoId";

	public static final String CODE_BRANCH_BANK_CONSTANT = "codeBranchBank";

	public static final String CODE_PROCESS_CONSTANT = "codeProcess";

	public static final String CASE_NO_CONSTANT = "caseNo";

	public static final String MOBILE_NUMBER_CONSTANT = "mobileNumber";

	public static final String NOTIFIED_CONSTANT = "notified";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String APPLY_NOTES_CONSTANT = "applyNotes";

	public static final String HOUSE_CONTACT_LOAN_INFO_CONSTANT = "houseContactLoanInfo";

	public static final String HOUSE_CONTACT_BASIC_INFO_CONSTANT = "houseContactBasicInfo";

	private Long contactInfoId;

	private transient CodeBranchBank codeBranchBank;

	private transient CodeProcess codeProcess;

	private String caseNo;

	private String mobileNumber;

	private boolean notified;

	private Date updatedDate;

	private Date createdDate;

	private String otherMsg;

	private String loanPlanCode;

	private transient Set<ApplyNote> applyNotes = new HashSet<>( 0 );

	private transient HouseContactLoanInfo houseContactLoanInfo;

	private transient HouseContactBasicInfo houseContactBasicInfo;

	public HouseContactInfo()
	{}

	public HouseContactInfo( CodeBranchBank codeBranchBank, CodeProcess codeProcess, String caseNo, String mobileNumber, boolean notified,
							 Date updatedDate, Date createdDate )
	{
		this.codeBranchBank = codeBranchBank;
		this.codeProcess = codeProcess;
		this.caseNo = caseNo;
		this.mobileNumber = mobileNumber;
		this.notified = notified;
		this.updatedDate = updatedDate;
		this.createdDate = createdDate;
	}

	public HouseContactInfo( Long contactInfoId )
	{
		this.contactInfoId = contactInfoId;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "houseContactInfo" )
	public Set<ApplyNote> getApplyNotes()
	{
		return applyNotes;
	}

	@Column( name = "case_no", unique = true, nullable = false, length = 20 )
	public String getCaseNo()
	{
		return caseNo;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "branch_bank_id", nullable = false )
	public CodeBranchBank getCodeBranchBank()
	{
		return codeBranchBank;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "process_code", nullable = false )
	public CodeProcess getCodeProcess()
	{
		return codeProcess;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "contact_info_id", unique = true, nullable = false )
	public Long getContactInfoId()
	{
		return contactInfoId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "houseContactInfo" )
	public HouseContactBasicInfo getHouseContactBasicInfo()
	{
		return houseContactBasicInfo;
	}

	@OneToOne( fetch = FetchType.LAZY, mappedBy = "houseContactInfo" )
	public HouseContactLoanInfo getHouseContactLoanInfo()
	{
		return houseContactLoanInfo;
	}

	@Column( name = "mobile_number", nullable = false, length = 10 )
	public String getMobileNumber()
	{
		return mobileNumber;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "notified", nullable = false, precision = 1, scale = 0 )
	public boolean isNotified()
	{
		return notified;
	}

	@Column( name = "loan_plan_code", length = 20 )
	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	@Column( name = "other_msg", length = 1000 )
	public String getOtherMsg()
	{
		return otherMsg;
	}

	public void setApplyNotes( Set<ApplyNote> applyNotes )
	{
		this.applyNotes = applyNotes;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCodeBranchBank( CodeBranchBank codeBranchBank )
	{
		this.codeBranchBank = codeBranchBank;
	}

	public void setCodeProcess( CodeProcess codeProcess )
	{
		this.codeProcess = codeProcess;
	}

	public void setContactInfoId( Long contactInfoId )
	{
		this.contactInfoId = contactInfoId;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setHouseContactBasicInfo( HouseContactBasicInfo houseContactBasicInfo )
	{
		this.houseContactBasicInfo = houseContactBasicInfo;
	}

	public void setHouseContactLoanInfo( HouseContactLoanInfo houseContactLoanInfo )
	{
		this.houseContactLoanInfo = houseContactLoanInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setNotified( boolean notified )
	{
		this.notified = notified;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}
}