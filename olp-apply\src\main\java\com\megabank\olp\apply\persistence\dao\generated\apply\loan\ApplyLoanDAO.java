package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeApplyStatusDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeBranchBankDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeLoanTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeProcessDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeRecipientSystemDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.dto.LoanCountDTO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoan;
import com.megabank.olp.apply.utility.ApplyLoanUtils;
import com.megabank.olp.apply.utility.enums.ApplyStatusEnum;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanDAO extends BasePojoDAO<ApplyLoan, Long>
{
	private static final String APPLY_STATUS_CODE_CONSTANT = "applyStatusCode";

	private static final String CURRENT_TIME_CONSTANT = "currentTime";

	private static final String DISCARD_CONSTANT = "discard";

	private static final String LOAN_ID_CONSTANT = "loanId";

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String FINAL_BRANCH_BANK_ID_CONSTANT = "finalBranchBankId";

	private static final String TRANSMISSION_STATUS_CONSTANT = "transmissionStatusCode";

	private static final String VALIDATED_IDENTITY_ID_CONSTANT = "validatedIdentityId";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	private static final String SECOND_DIFF_CONSTANT = "secondDiff";

	private static final String LOAN_RECIPIENT_ID_CONSTANT = "loanRecipientId";

	@Autowired
	private CodeApplyStatusDAO codeApplyStatusDAO;

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@Autowired
	private CodeLoanTypeDAO codeLoanTypeDAO;

	@Autowired
	private CodeProcessDAO codeProcessDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeRecipientSystemDAO codeRecipientSystemDAO;

	public Long create( ApplyLoanCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getIdentityFlag() );
		Validate.notBlank( paramBean.getCaseNo() );
		Validate.notBlank( paramBean.getApplyStatusCode() );
		Validate.notBlank( paramBean.getLoanType() );
		Validate.notBlank( paramBean.getTransmissionStatusCode() );
		Validate.notBlank( paramBean.getProcessCode() );

		String loanPlanCode = paramBean.getLoanPlanCode();
		String introduceBrNo = paramBean.getIntroduceBrNo();

		ApplyLoan pojo = new ApplyLoan();
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setCaseNo( paramBean.getCaseNo() );
		pojo.setCodeLoanType( codeLoanTypeDAO.read( paramBean.getLoanType() ) );
		pojo.setCodeApplyStatus( codeApplyStatusDAO.read( paramBean.getApplyStatusCode() ) );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( paramBean.getTransmissionStatusCode() ) );
		pojo.setNotified( false );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );
		pojo.setCodeProcess( codeProcessDAO.read( paramBean.getProcessCode() ) );
		pojo.setRefBorrowerApplyLoan( StringUtils.isBlank( paramBean.getRefCaseNo() ) ? null : getPojoByCaseNo( paramBean.getRefCaseNo() ) );
		pojo.setIdentityFlag( paramBean.getIdentityFlag() );
		pojo.setLoanPlanCode( loanPlanCode );
		pojo.setIntroduceBr1st( introduceBrNo );
		// 主借人 才設定引介分行
		if( StringUtils.isBlank( paramBean.getRefCaseNo() ) )
			if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
			pojo.setIntroduceBrNo( ApplyLoanUtils.get_ChinaSteelGroup_BatchPersonalLoan_brNo() );
			else if( ApplyLoanUtils.is_co70647919_C101_BatchPersonalLoan( loanPlanCode ) )
			pojo.setIntroduceBrNo( ApplyLoanUtils.get_co70647919_C101_BatchPersonalLoan_brNo() );
			else if( ApplyLoanUtils.is_co80601119_D001_BatchPersonalLoan( loanPlanCode ) )
			pojo.setIntroduceBrNo( ApplyLoanUtils.get_co80601119_D001_BatchPersonalLoan_brNo() );
			else if( StringUtils.isNotBlank( introduceBrNo ) )
				pojo.setIntroduceBrNo( introduceBrNo );

		pojo.setNotCompletedUpdatedDate( new Date() );

		// ====================
		// 因中鋼消貸進件的量太大，不應按{目前機制}通知分行經辦 => applyLoanDAO.************************( ?, ?, LOAN_INTERRUPTED_INTERVAL ); 進件中斷
		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
			pojo.setNotified( true );

		return super.createPojo( pojo );
	}

	public ApplyLoan getLatestPojo( List<Long> validatedIdentityId, String loanType, Integer loanRecipientId )
	{
		Validate.notEmpty( validatedIdentityId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getLatestPojo" );
		nativeQuery.setParameterList( VALIDATED_IDENTITY_ID_CONSTANT, validatedIdentityId, Long.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext() );
		nativeQuery.setParameter( LOAN_RECIPIENT_ID_CONSTANT, loanRecipientId );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyLoan.class );

		return ( ApplyLoan )nativeQuery.uniqueResult();
	}

	@SuppressWarnings( "unchecked" )
	public ApplyLoan getLatestPojoIn7Days( List<Long> validatedIdentityId, String loanType )
	{
		Validate.notEmpty( validatedIdentityId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getLatestPojoIn7Days" );
		nativeQuery.setParameterList( VALIDATED_IDENTITY_ID_CONSTANT, validatedIdentityId, Long.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext(), String.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyLoan.class );

		return ( ApplyLoan )nativeQuery.uniqueResult();
	}

	public List<Long> ************************( List<String> applyStatusCode, String loanType, int secondDiff )
	{
		Validate.notEmpty( applyStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getBranchBankIds" );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, applyStatusCode, String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( SECOND_DIFF_CONSTANT, secondDiff, Integer.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		return nativeQuery.getResultList();
	}

	public List<LoanCountDTO> getNeedToNotifiedLoanCountByBranch( List<String> applyStatusCode, String loanType, Long finalBranchBankId,
																  int secondDiff )
	{
		Validate.notEmpty( applyStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getLoanIds" );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, applyStatusCode, String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( FINAL_BRANCH_BANK_ID_CONSTANT, finalBranchBankId, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( SECOND_DIFF_CONSTANT, secondDiff, Integer.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).setResultTransformer( Transformers.aliasToBean( LoanCountDTO.class ) );

		return nativeQuery.getResultList();
	}

	public ApplyLoan getPojoByCaseNo( String caseNo )
	{
		Validate.notBlank( caseNo );

		NameValueBean condition = new NameValueBean( ApplyLoan.CASE_NO_CONSTANT, caseNo );

		return getUniquePojoByProperty( condition, ApplyLoan.TABLENAME_CONSTANT );
	}

	public ApplyLoan getPojoByIdentityAndLoanType( Long validatedIdentityId, String loanType )
	{
		Validate.notNull( validatedIdentityId );
		Validate.notBlank( loanType );

		NameValueBean userValiatedIdentity = new NameValueBean( ApplyLoan.VALIDATED_IDENTITY_ID_CONSTANT, validatedIdentityId );
		NameValueBean codeLoanType = new NameValueBean( ApplyLoan.CODE_LOAN_TYPE_CONSTANT, codeLoanTypeDAO.read( loanType ) );
		NameValueBean[] conditions = new NameValueBean[]{ userValiatedIdentity, codeLoanType };

		return getUniquePojoByProperties( conditions );
	}

	public ApplyLoan getPojoByPkAndLoanType( Long loanId, String loanType )
	{
		Validate.notNull( loanId );
		Validate.notBlank( loanType );

		NameValueBean pk = new NameValueBean( ApplyLoan.LOAN_ID_CONSTANT, loanId );
		NameValueBean codeLoanType = new NameValueBean( ApplyLoan.CODE_LOAN_TYPE_CONSTANT, codeLoanTypeDAO.read( loanType ) );
		NameValueBean[] conditions = new NameValueBean[]{ pk, codeLoanType };

		return getUniquePojoByProperties( conditions, ApplyLoan.TABLENAME_CONSTANT );
	}

	public List<ApplyLoan> getPojosByApplyStatus( List<String> applyStatusCode, String loanType, Integer secondDiff )
	{
		Validate.notEmpty( applyStatusCode );
		Validate.notNull( secondDiff );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getPojosByApplyStatus" );
		nativeQuery.setParameterList( APPLY_STATUS_CODE_CONSTANT, applyStatusCode, String.class );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOT_NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( SECOND_DIFF_CONSTANT, secondDiff, Integer.class );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyLoan.class );

		return nativeQuery.getResultList();
	}

	public List<ApplyLoan> getPojosByRefCaseNo( String refCaseNo, String applyStatusCode )
	{
		Validate.notBlank( refCaseNo );
		Validate.notBlank( applyStatusCode );

		NameValueBean refBorrowerApplyLoan = new NameValueBean( ApplyLoan.REF_BORROWER_APPLY_LOAN_CONSTANT, getPojoByCaseNo( refCaseNo ) );
		NameValueBean codeApplyStatus = new NameValueBean( ApplyLoan.CODE_APPLY_STATUS_CONSTANT, codeApplyStatusDAO.read( applyStatusCode ) );
		NameValueBean[] conditions = new NameValueBean[]{ refBorrowerApplyLoan, codeApplyStatus };

		return this.getPojosByProperties( conditions );
	}

	public List<ApplyLoan> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( TRANSMISSION_STATUS_CONSTANT, transmissionStatusCode, String.class );
		nativeQuery.setParameter( APPLY_STATUS_CODE_CONSTANT, ApplyStatusEnum.COMPLETE_CONFIRMED.getContext(), String.class );
		nativeQuery.setParameter( DISCARD_CONSTANT, false, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyLoan.class );

		return nativeQuery.getResultList();
	}

	public Long initializeResendCount( Long loanId )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setResendCount( 0 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public ApplyLoan read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, ApplyLoan.TABLENAME_CONSTANT );
	}

	public ApplyLoan readToNull( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId );
	}

	public Long updateAgreedDate( Long loanId, String applyStatusCode, String loanVersion )
	{
		Validate.notNull( loanId );
		Validate.notBlank( applyStatusCode );

		ApplyLoan pojo = read( loanId );
		pojo.setCodeApplyStatus( codeApplyStatusDAO.read( applyStatusCode ) );
		pojo.setLoanVersion( loanVersion );
		pojo.setAgreedDate( new Date() );
		pojo.setUpdatedDate( new Date() );
		pojo.setNotCompletedUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateApplyCompleted( Long loanId, String applyStatusCode, Date apply_completed_date )
	{
		Validate.notNull( loanId );
		Validate.notBlank( applyStatusCode );

		ApplyLoan pojo = read( loanId );
		pojo.setCodeApplyStatus( codeApplyStatusDAO.read( applyStatusCode ) );
		pojo.setApplyCompletedDate( apply_completed_date );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateApplyCompletedDate( Long loanId, Date apply_completed_date )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setApplyCompletedDate( apply_completed_date );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateApplyStatus( Long loanId, String applyStatus )
	{
		Validate.notNull( loanId );
		Validate.notBlank( applyStatus );

		ApplyLoan pojo = read( loanId );
		pojo.setCodeApplyStatus( codeApplyStatusDAO.read( applyStatus ) );
		pojo.setNotCompletedUpdatedDate( new Date() );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateDiscard( Long loanId )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setDiscard( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateEraseApplyCompletedDate( Long loanId )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setApplyCompletedDate( null );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateFinalBranchBank( Long loanId, Long branchBankId )
	{
		Validate.notNull( loanId );
		Validate.notNull( branchBankId );

		ApplyLoan pojo = read( loanId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( branchBankId ) );
		pojo.setNotified( false );
		// ================
		String loanPlanCode = pojo.getLoanPlanCode();
		// 因中鋼消貸進件的量太大，不應按{目前機制}通知分行經辦 => applyLoanDAO.************************( ?, ?, LOAN_COMPLETED_INTERVAL ); 進件完成
		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
			pojo.setNotified( true );

		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();

	}

	public Long updateFinalBranchBank_and_introduceBrNo( Long loanId, Long branchBankId, String introduceBrNo )
	{
		Validate.notNull( loanId );
		Validate.notNull( branchBankId );

		ApplyLoan pojo = read( loanId );
		pojo.setCodeBranchBank( codeBranchBankDAO.read( branchBankId ) );
		pojo.setIntroduceBrNo( introduceBrNo );
		pojo.setNotified( false );
		// ================
		String loanPlanCode = pojo.getLoanPlanCode();
		// 因中鋼消貸進件的量太大，不應按{目前機制}通知分行經辦 => applyLoanDAO.************************( ?, ?, LOAN_COMPLETED_INTERVAL ); 進件完成
		if( ApplyLoanUtils.is_ChinaSteelGroup_BatchPersonalLoan( loanPlanCode ) )
			pojo.setNotified( true );

		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateIntroduceBr1st( Long loanId, String introduceBr1stCode )
	{
		Validate.notNull( loanId );
		Validate.notBlank( introduceBr1stCode );

		ApplyLoan pojo = read( loanId );
		pojo.setIntroduceBr1st( introduceBr1stCode );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateLoanRecipientId( Long loanId, Integer systemId )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setLoanRecipient( codeRecipientSystemDAO.read( systemId ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public int updateNotified( List<Long> loanIds )
	{
		Validate.notEmpty( loanIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "loan.updateNotified" );
		nativeQuery.setParameterList( LOAN_ID_CONSTANT, loanIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long loanId )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updatePdfContent( Long loanId, byte[] pdfContent )
	{
		Validate.notNull( loanId );
		Validate.notNull( pdfContent );

		ApplyLoan pojo = read( loanId );
		pojo.setPdfContent( new ImmutableByteArray( pdfContent ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateProcess( Long loanId, String processCode )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setCodeProcess( codeProcessDAO.read( processCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateRefCase( Long loanId, String refCaseNo )
	{
		Validate.notNull( loanId );
		Validate.notBlank( refCaseNo );

		ApplyLoan pojo = read( loanId );
		pojo.setRefBorrowerApplyLoan( getPojoByCaseNo( refCaseNo ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateResendCount( Long loanId )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setResendCount( pojo.getResendCount() + 1 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateTransmissionStatus( Long loanId, String transmissionStatusCode )
	{
		Validate.notNull( loanId );

		ApplyLoan pojo = read( loanId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	@Override
	protected Class<ApplyLoan> getPojoClass()
	{
		return ApplyLoan.class;
	}
}
