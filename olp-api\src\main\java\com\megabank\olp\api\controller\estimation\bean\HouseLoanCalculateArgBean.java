/**
 *
 */
package com.megabank.olp.api.controller.estimation.bean;

import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseLoanCalculateArgBean extends BaseBean
{
	@NotBlank
	private String caseID;

	private String mobileNumber;

	private String email;

	@NotNull
	private Date createdDate;

	@JsonProperty( "contactBranchCode" )
	@NotBlank
	private String branchBankCode;

	@Valid
	private CalculateBasicInfoBean basicInfo;

	@Valid
	private CalculateLoanInfoBean loanInfo;

	public CalculateBasicInfoBean getBasicInfo()
	{
		return basicInfo;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getCaseID()
	{
		return caseID;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getEmail()
	{
		return email;
	}

	public CalculateLoanInfoBean getLoanInfo()
	{
		return loanInfo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBasicInfo( CalculateBasicInfoBean basicInfo )
	{
		this.basicInfo = basicInfo;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCaseID( String caseID )
	{
		this.caseID = caseID;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setLoanInfo( CalculateLoanInfoBean loanInfo )
	{
		this.loanInfo = loanInfo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}
}
