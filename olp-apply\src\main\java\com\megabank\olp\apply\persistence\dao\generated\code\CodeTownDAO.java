package com.megabank.olp.apply.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.code.CodeTown;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The CodeTownDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeTownDAO extends BasePojoDAO<CodeTown, String>
{
	@Autowired
	private CodeCityDAO codeCityDAO;

	public List<CodeTown> getPojosByCityCode( String cityCode )
	{
		Validate.notBlank( cityCode );

		NameValueBean condition = new NameValueBean( "codeCity", codeCityDAO.read( cityCode ) );

		return getPojosByProperty( condition );
	}

	public CodeTown read( String townCode )
	{
		Validate.notBlank( townCode );

		return getPojoByPK( townCode, CodeTown.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeTown> getPojoClass()
	{
		return CodeTown.class;
	}
}
