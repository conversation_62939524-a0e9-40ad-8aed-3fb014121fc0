package com.megabank.olp.apply.controller.management.bean.notification;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class AbortContractNotificationArgBean extends BaseBean
{
	@NotBlank
	private String contractNo;

	public AbortContractNotificationArgBean()
	{}

	public String getContractNo()
	{
		return contractNo;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}
}
