package com.megabank.olp.apply.persistence.dao.generated.ixml.attachment;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.pojo.ixml.attachment.IxmlAttachment;
import com.megabank.olp.apply.utility.enums.TransmissionStatusEnum;
import com.megabank.olp.base.bean.ImmutableByteArray;
import com.megabank.olp.base.layer.BasePojoDAO;

@Repository
public class IxmlAttachmentDAO extends BasePojoDAO<IxmlAttachment, Long>
{
	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@SuppressWarnings( "unchecked" )
	public List<IxmlAttachment> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "ixml.pdf.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( IxmlAttachment.class );

		return nativeQuery.getResultList();
	}

	public IxmlAttachment read( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId, IxmlAttachment.TABLENAME_CONSTANT );
	}

	public IxmlAttachment readToNull( Long loanId )
	{
		Validate.notNull( loanId );

		return getPojoByPK( loanId );
	}

	@Transactional( propagation = Propagation.REQUIRES_NEW )
	public Long savePdf( Long loanId, byte[] pdfContent )
	{
		Validate.notNull( loanId );
		Validate.notNull( pdfContent );

		IxmlAttachment pojo = readToNull( loanId );

		if( pojo == null )
		{
			pojo = new IxmlAttachment();
			pojo.setLoanId( loanId );
			pojo.setPdfContent( new ImmutableByteArray( pdfContent ) );
			pojo.setApproved( false );
			pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( TransmissionStatusEnum.NO.getContext() ) );
			pojo.setResend( 0 );
			pojo.setUpdatedDate( new Date() );
			pojo.setCreatedDate( new Date() );
			return super.createPojo( pojo );
		}
		else
		{
			pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( TransmissionStatusEnum.NO.getContext() ) );
			pojo.setResend( 0 );
			pojo.setPdfContent( new ImmutableByteArray( pdfContent ) );
			pojo.setUpdatedDate( new Date() );
			return pojo.getLoanId();
		}
	}

	public Long setApproved( Long loanId )
	{
		Validate.notNull( loanId );

		IxmlAttachment pojo = read( loanId );

		pojo.setApproved( true );

		return pojo.getLoanId();
	}

	public Long updateInetResponseStat( Long loanId, Integer stat )
	{
		Validate.notNull( loanId );
		Validate.notNull( stat );

		IxmlAttachment pojo = read( loanId );
		pojo.setInetResponseStatus( stat );
		pojo.setUpdatedDate( new Date() );

		return pojo.getLoanId();
	}

	public Long updateResend( Long loanId )
	{
		Validate.notNull( loanId );

		IxmlAttachment pojo = read( loanId );
		pojo.setResend( pojo.getResend() + 1 );

		return pojo.getLoanId();
	}

	public Long updateTransmissionStatus( Long loanId, String transmissionStatusCode )
	{
		Validate.notNull( loanId );

		IxmlAttachment pojo = read( loanId );

		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );

		return pojo.getLoanId();
	}

	@Override
	protected Class<IxmlAttachment> getPojoClass()
	{
		return IxmlAttachment.class;
	}

}
