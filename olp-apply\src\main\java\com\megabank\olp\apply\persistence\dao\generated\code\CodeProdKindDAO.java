package com.megabank.olp.apply.persistence.dao.generated.code;

import com.megabank.olp.apply.persistence.pojo.code.CodeProdKind;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CodeProdKindDAO extends BasePojoDAO<CodeProdKind, String>
{
	public CodeProdKind read( String prodKindId )
	{
		Validate.notNull( prodKindId );

		return getPojoByPK( prodKindId, CodeProdKind.TABLENAME_CONSTANT );
	}

	public List<CodeProdKind> getPojosByCaseType(String caseType )
	{
		NameValueBean condition = new NameValueBean( CodeProdKind.CASE_TYPE, caseType );

		return getPojosByProperty( condition );
	}

	@Override
	protected Class<CodeProdKind> getPojoClass()
	{
		return CodeProdKind.class;
	}
}
