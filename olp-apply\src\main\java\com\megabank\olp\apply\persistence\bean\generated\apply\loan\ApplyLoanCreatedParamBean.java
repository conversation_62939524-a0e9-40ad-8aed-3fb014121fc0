package com.megabank.olp.apply.persistence.bean.generated.apply.loan;

import com.megabank.olp.base.bean.BaseBean;

public class ApplyLoanCreatedParamBean extends BaseBean
{
	private Long validatedIdentityId;

	private String caseNo;

	private String applyStatusCode;

	private String transmissionStatusCode;

	private String loanType;

	private String processCode;

	private String refCaseNo;

	private Integer identityFlag;

	private String loanPlanCode;

	private String introduceBrNo;

	public ApplyLoanCreatedParamBean()
	{
		// default constructor
	}

	public String getApplyStatusCode()
	{
		return applyStatusCode;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Integer getIdentityFlag()
	{
		return identityFlag;
	}

	public String getIntroduceBrNo()
	{
		return introduceBrNo;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getProcessCode()
	{
		return processCode;
	}

	public String getRefCaseNo()
	{
		return refCaseNo;
	}

	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setApplyStatusCode( String applyStatusCode )
	{
		this.applyStatusCode = applyStatusCode;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setIdentityFlag( Integer identityFlag )
	{
		this.identityFlag = identityFlag;
	}

	public void setIntroduceBrNo( String introduceBrNo )
	{
		this.introduceBrNo = introduceBrNo;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setProcessCode( String processCode )
	{
		this.processCode = processCode;
	}

	public void setRefCaseNo( String refCaseNo )
	{
		this.refCaseNo = refCaseNo;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

}
