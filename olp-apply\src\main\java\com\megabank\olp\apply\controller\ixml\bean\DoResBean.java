package com.megabank.olp.apply.controller.ixml.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class DoResBean extends BaseBean
{
	@JsonProperty( "BusinessNo" )
	private String businessNo;

	@JsonProperty( "ApiVersion" )
	private String apiVersion;

	@JsonProperty( "HashKeyNo" )
	private String hashKeyNo;

	@JsonProperty( "VerifyNo" )
	private String verifyNo;

	@JsonProperty( "MemberNoMapping" )
	private String memberNoMapping;

	@JsonProperty( "ReturnParams" )
	private String returnParams;

	@JsonProperty( "Token" )
	private String token;

	@JsonProperty( "CAType" )
	private String caType;

	@JsonProperty( "ResultCode" )
	private String resultCode;

	@JsonProperty( "ReturnCode" )
	private String returnCode;

	@JsonProperty( "ReturnCodeDesc" )
	private String returnCodeDesc;

	@JsonProperty( "IdentifyNo" )
	private String identifyNo;

	public DoResBean()
	{}

	public String getApiVersion()
	{
		return apiVersion;
	}

	public String getBusinessNo()
	{
		return businessNo;
	}

	public String getCaType()
	{
		return caType;
	}

	public String getHashKeyNo()
	{
		return hashKeyNo;
	}

	public String getIdentifyNo()
	{
		return identifyNo;
	}

	public String getMemberNoMapping()
	{
		return memberNoMapping;
	}

	public String getResultCode()
	{
		return resultCode;
	}

	public String getReturnCode()
	{
		return returnCode;
	}

	public String getReturnCodeDesc()
	{
		return returnCodeDesc;
	}

	public String getReturnParams()
	{
		return returnParams;
	}

	public String getToken()
	{
		return token;
	}

	public String getVerifyNo()
	{
		return verifyNo;
	}

	public void setApiVersion( String apiVersion )
	{
		this.apiVersion = apiVersion;
	}

	public void setBusinessNo( String businessNo )
	{
		this.businessNo = businessNo;
	}

	public void setCaType( String caType )
	{
		this.caType = caType;
	}

	public void setHashKeyNo( String hashKeyNo )
	{
		this.hashKeyNo = hashKeyNo;
	}

	public void setIdentifyNo( String identifyNo )
	{
		this.identifyNo = identifyNo;
	}

	public void setMemberNoMapping( String memberNoMapping )
	{
		this.memberNoMapping = memberNoMapping;
	}

	public void setResultCode( String resultCode )
	{
		this.resultCode = resultCode;
	}

	public void setReturnCode( String returnCode )
	{
		this.returnCode = returnCode;
	}

	public void setReturnCodeDesc( String returnCodeDesc )
	{
		this.returnCodeDesc = returnCodeDesc;
	}

	public void setReturnParams( String returnParams )
	{
		this.returnParams = returnParams;
	}

	public void setToken( String token )
	{
		this.token = token;
	}

	public void setVerifyNo( String verifyNo )
	{
		this.verifyNo = verifyNo;
	}

}
