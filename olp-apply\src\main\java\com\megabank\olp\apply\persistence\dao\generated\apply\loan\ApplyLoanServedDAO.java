package com.megabank.olp.apply.persistence.dao.generated.apply.loan;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.bean.generated.apply.loan.ApplyLoanServedCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeRepresentativeTypeDAO;
import com.megabank.olp.apply.persistence.pojo.apply.loan.ApplyLoanServed;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyLoanServedDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyLoanServedDAO extends BasePojoDAO<ApplyLoanServed, Long>
{
	@Autowired
	private ApplyLoanContentDAO applyLoanContentDAO;

	@Autowired
	private CodeRepresentativeTypeDAO codeRepresentativeTypeDAO;

	public Long create( ApplyLoanServedCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getLoanId() );
		Validate.notBlank( paramBean.getCompanyName() );
		Validate.notBlank( paramBean.getServedTitle() );
		Validate.notBlank( paramBean.getRepresentativeType() );

		ApplyLoanServed pojo = new ApplyLoanServed();
		pojo.setApplyLoanContent( applyLoanContentDAO.read( paramBean.getLoanId() ) );
		pojo.setCompanyName( paramBean.getCompanyName() );
		pojo.setServedTitle( paramBean.getServedTitle() );
		pojo.setTaxNo( paramBean.getTaxNo() );
		pojo.setComment( paramBean.getComment() );
		pojo.setCodeRepresentativeType( codeRepresentativeTypeDAO.read( paramBean.getRepresentativeType() ) );

		return super.createPojo( pojo );
	}

	public void deletePojosByLoanId( Long loanId )
	{
		List<ApplyLoanServed> pojos = getPojosByLoanId( loanId );

		super.deletePojos( pojos );
	}

	public List<ApplyLoanServed> getPojosByLoanId( Long loanId )
	{
		Validate.notNull( loanId );

		NameValueBean condition = new NameValueBean( ApplyLoanServed.APPLY_LOAN_CONTENT_CONSTANT, applyLoanContentDAO.read( loanId ) );

		return getPojosByProperty( condition );
	}

	@Override
	protected Class<ApplyLoanServed> getPojoClass()
	{
		return ApplyLoanServed.class;
	}
}
