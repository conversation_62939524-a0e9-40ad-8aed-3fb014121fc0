package com.megabank.olp.apply.persistence.dao.generated.apply.signing;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningBankAccount;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningContract;
import com.megabank.olp.apply.persistence.pojo.apply.signing.ApplySigningUser;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplySigningBankAccountDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplySigningBankAccountDAO extends BasePojoDAO<ApplySigningBankAccount, Long>
{
	private static final String CONTRACTID_CONSTANT = "contractId";

	@Autowired
	private ApplySigningContractDAO applySigningContractDAO;

	public Long create( Long contractId, String account )
	{
		Validate.notNull( contractId );
		Validate.notNull( account );
		Validate.notBlank( account );

		ApplySigningBankAccount pojo = new ApplySigningBankAccount();
		pojo.setApplySigningContract( applySigningContractDAO.read( contractId ) );
		pojo.setBankAccount( account );
		pojo.setBankCode( "017" );
		pojo.setDiscard( false );

		return super.createPojo( pojo );
	}

	public Long createForContractCtrTypeC( Long contractId, String code, String account )
	{
		Validate.notNull( contractId );
		Validate.notNull( account );
		// Validate.notBlank( account );

		ApplySigningBankAccount pojo = new ApplySigningBankAccount();
		pojo.setApplySigningContract( applySigningContractDAO.read( contractId ) );
		pojo.setBankAccount( account );
		pojo.setBankCode( code );
		pojo.setDiscard( false );

		return super.createPojo( pojo );
	}

	public Long createOtherBankAccount( Long contractId, String bankCode, String bankBranchCode, String bankAccount )
	{

		Validate.notNull( contractId );
		Validate.notNull( bankCode );
		Validate.notNull( bankBranchCode );
		Validate.notNull( bankAccount );

		ApplySigningBankAccount pojo = new ApplySigningBankAccount();
		pojo.setApplySigningContract( applySigningContractDAO.read( contractId ) );
		pojo.setBankCode( bankCode );
		pojo.setBankBranchCode( bankBranchCode );
		pojo.setBankAccount( bankAccount );
		pojo.setDiscard( false );
		return super.createPojo( pojo );

	}

	/**
	 * 作廢他行帳號
	 *
	 * @param contractId
	 * @return
	 */
	public int discardOtherBankAccount( Long contractId )
	{

		Validate.notNull( contractId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "applysigningbank.discardOtherBankAccount" );

		nativeQuery.setParameter( CONTRACTID_CONSTANT, contractId, Long.class );

		return nativeQuery.executeUpdate();

	}

	public List<ApplySigningBankAccount> getContractBankAccounts( Long contractId )
	{
		Validate.notNull( contractId );

		ApplySigningContract applySigningContract = applySigningContractDAO.read( contractId );

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( ApplySigningUser.APPLY_SIGNING_CONTRACT_CONSTANT, applySigningContract ),
														  new NameValueBean( ApplySigningContract.DISCARD_CONSTANT, false ) };

		return getPojosByPropertiesOrderBy( conditions, new OrderBean[ 0 ] );
	}

	/**
	 * 取得指定簽約案件的本行帳號
	 *
	 * @param contractId
	 * @return
	 */
	public List<ApplySigningBankAccount> getContractInnerBankAccount( Long contractId )
	{

		Validate.notNull( contractId );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "applysigningbank.getInnerBankAccount" );

		nativeQuery.setParameter( CONTRACTID_CONSTANT, contractId, Long.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplySigningBankAccount.class );

		return nativeQuery.getResultList();

	}

	public ApplySigningBankAccount read( Long contractId, String account )
	{
		Validate.notNull( contractId );
		Validate.notBlank( account );

		ApplySigningContract applySigningContract = applySigningContractDAO.read( contractId );

		NameValueBean[] conditions = new NameValueBean[]{
														  new NameValueBean( ApplySigningBankAccount.APPLY_SIGNING_CONTRACT_CONSTANT,
																			 applySigningContract ),
														  new NameValueBean( ApplySigningBankAccount.BANK_ACCOUNT_CONSTANT, account ),
														  new NameValueBean( ApplySigningContract.DISCARD_CONSTANT, false ) };

		return getUniquePojoByProperties( conditions );
	}

	public ApplySigningBankAccount readForContractCtrTypeC( Long contractId, String account )
	{
		Validate.notNull( contractId );
		Validate.notNull( account );

		ApplySigningContract applySigningContract = applySigningContractDAO.read( contractId );

		NameValueBean[] conditions = new NameValueBean[]{
														  new NameValueBean( ApplySigningBankAccount.APPLY_SIGNING_CONTRACT_CONSTANT,
																			 applySigningContract ),
														  new NameValueBean( ApplySigningBankAccount.BANK_ACCOUNT_CONSTANT, account ) };

		return getUniquePojoByProperties( conditions );
	}

	@Override
	protected Class<ApplySigningBankAccount> getPojoClass()
	{
		return ApplySigningBankAccount.class;
	}
}
