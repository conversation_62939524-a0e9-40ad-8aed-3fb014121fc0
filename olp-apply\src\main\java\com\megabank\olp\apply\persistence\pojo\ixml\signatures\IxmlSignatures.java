package com.megabank.olp.apply.persistence.pojo.ixml.signatures;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import com.megabank.olp.apply.persistence.pojo.code.CodeTransmissionStatus;
import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

@Entity
@Table( name = "ixml_signatures" )
public class IxmlSignatures extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "ixml_signatures";

	private long signatureId;

	private long loanId;

	private String idNo;

	private Date createdDate;

	private transient CodeTransmissionStatus codeTransmissionStatus;

	private int resend;

	private String recivedSignature;

	private transient ImmutableByteArray sendContent;

	public IxmlSignatures()
	{}

	public IxmlSignatures( Long loanId, String idNo, Date createdDate, CodeTransmissionStatus codeTransmissionStatus, int resend,
						   String recivedSignature, ImmutableByteArray sendContent )
	{
		this.loanId = loanId;
		this.idNo = idNo;
		this.createdDate = createdDate;
		this.codeTransmissionStatus = codeTransmissionStatus;
		this.resend = resend;
		this.recivedSignature = recivedSignature;
		this.sendContent = sendContent;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "transmission_status_code", nullable = false )
	public CodeTransmissionStatus getCodeTransmissionStatus()
	{
		return codeTransmissionStatus;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", unique = true, nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Column( name = "id_no" )
	public String getIdNo()
	{
		return idNo;
	}

	@Column( name = "loan_id", unique = true, nullable = false )
	public long getLoanId()
	{
		return loanId;
	}

	@Column( name = "recived_signature" )
	public String getRecivedSignature()
	{
		return recivedSignature;
	}

	@Column( name = "resend", nullable = false, precision = 5, scale = 0 )
	public int getResend()
	{
		return resend;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "send_content" ) ) } )
	public ImmutableByteArray getSendContent()
	{
		return sendContent;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "signature_id", unique = true, nullable = false )
	public long getSignatureId()
	{
		return signatureId;
	}

	public void setCodeTransmissionStatus( CodeTransmissionStatus codeTransmissionStatus )
	{
		this.codeTransmissionStatus = codeTransmissionStatus;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanId( long loanId )
	{
		this.loanId = loanId;
	}

	public void setRecivedSignature( String recivedSignature )
	{
		this.recivedSignature = recivedSignature;
	}

	public void setResend( int resend )
	{
		this.resend = resend;
	}

	public void setSendContent( ImmutableByteArray sendContent )
	{
		this.sendContent = sendContent;
	}

	public void setSignatureId( Long signatureId )
	{
		this.signatureId = signatureId;
	}
}
