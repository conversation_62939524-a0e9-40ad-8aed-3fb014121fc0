/**
 *
 */
package com.megabank.olp.apply.persistence.bean.generated.house;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class HouseInfoCreatedParamBean extends BaseBean
{
	private Long housePricingInfoId;

	private String county;

	private String district;

	private String addr;

	private String houseType;

	private Integer bAge;

	private BigDecimal bArea;

	private Integer floors;

	private Integer levelSelect;

	private Integer level;

	private String houseParkingCode;

	private Integer parkingGty;

	private BigDecimal parkingP;

	public String getAddr()
	{
		return addr;
	}

	public Integer getbAge()
	{
		return bAge;
	}

	public BigDecimal getbArea()
	{
		return bArea;
	}

	public String getCounty()
	{
		return county;
	}

	public String getDistrict()
	{
		return district;
	}

	public Integer getFloors()
	{
		return floors;
	}

	public String getHouseParkingCode()
	{
		return houseParkingCode;
	}

	public Long getHousePricingInfoId()
	{
		return housePricingInfoId;
	}

	public String getHouseType()
	{
		return houseType;
	}

	public Integer getLevel()
	{
		return level;
	}

	public Integer getLevelSelect()
	{
		return levelSelect;
	}

	public Integer getParkingGty()
	{
		return parkingGty;
	}

	public BigDecimal getParkingP()
	{
		return parkingP;
	}

	public void setAddr( String addr )
	{
		this.addr = addr;
	}

	public void setbAge( Integer bAge )
	{
		this.bAge = bAge;
	}

	public void setbArea( BigDecimal bArea )
	{
		this.bArea = bArea;
	}

	public void setCounty( String county )
	{
		this.county = county;
	}

	public void setDistrict( String district )
	{
		this.district = district;
	}

	public void setFloors( Integer floors )
	{
		this.floors = floors;
	}

	public void setHouseParkingCode( String houseParkingCode )
	{
		this.houseParkingCode = houseParkingCode;
	}

	public void setHousePricingInfoId( Long housePricingInfoId )
	{
		this.housePricingInfoId = housePricingInfoId;
	}

	public void setHouseType( String houseType )
	{
		this.houseType = houseType;
	}

	public void setLevel( Integer level )
	{
		this.level = level;
	}

	public void setLevelSelect( Integer levelSelect )
	{
		this.levelSelect = levelSelect;
	}

	public void setParkingGty( Integer parkingGty )
	{
		this.parkingGty = parkingGty;
	}

	public void setParkingP( BigDecimal parkingP )
	{
		this.parkingP = parkingP;
	}
}
