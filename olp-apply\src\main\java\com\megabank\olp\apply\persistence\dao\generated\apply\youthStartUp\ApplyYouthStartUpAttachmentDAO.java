package com.megabank.olp.apply.persistence.dao.generated.apply.youthStartUp;

import com.megabank.olp.apply.persistence.bean.generated.apply.youthStartUp.ApplyYouthStartUpAttachmentCreatedParamBean;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeAttachmentTypeDAO;
import com.megabank.olp.apply.persistence.dao.generated.code.CodeTransmissionStatusDAO;
import com.megabank.olp.apply.persistence.pojo.apply.youthStartUp.ApplyYouthStartUpAttachment;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.layer.BasePojoDAO;
import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.hibernate.query.sql.internal.NativeQueryImpl;

import java.util.Date;
import java.util.List;

@Repository
public class ApplyYouthStartUpAttachmentDAO extends BasePojoDAO<ApplyYouthStartUpAttachment, Long>
{
	private static final String ATTACHMENT_ID_CONSTANT = "attachmentId";

	private static final String NOTIFIED_CONSTANT = "notified";

	private static final String UPDATED_DATE_CONSTANT = "updatedDate";

	@Autowired
	private ApplyYouthStartUpDAO applyYouthStartUpDAO;

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@Autowired
	private CodeAttachmentTypeDAO codeAttachmentTypeDAO;

	public Long create( ApplyYouthStartUpAttachmentCreatedParamBean paramBean )
	{
		Validate.notNull( paramBean.getYouthStartUpId() );
		Validate.notNull( paramBean.getValidatedIdentityId() );
		Validate.notNull( paramBean.getFileSize() );
		Validate.notNull( paramBean.getFileContent() );
		Validate.notBlank( paramBean.getAttachmentType() );
		Validate.notBlank( paramBean.getFileName() );
		Validate.notBlank( paramBean.getTransmissionStatusCode() );

		ApplyYouthStartUpAttachment pojo = new ApplyYouthStartUpAttachment();
		pojo.setApplyYouthStartUp( applyYouthStartUpDAO.read( paramBean.getYouthStartUpId() ) );
		pojo.setValidatedIdentityId( paramBean.getValidatedIdentityId() );
		pojo.setCodeAttachmentType( codeAttachmentTypeDAO.read( paramBean.getAttachmentType() ) );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( paramBean.getTransmissionStatusCode() ) );
		pojo.setCompressFileContent( paramBean.getCompressFileContent() );
		pojo.setFileName( paramBean.getFileName() );
		pojo.setFileSize( paramBean.getFileSize() );
		pojo.setFileContent( paramBean.getFileContent() );
		pojo.setUpdatedDate( new Date() );
		pojo.setCreatedDate( new Date() );

		return super.createPojo( pojo );
	}

	public List<ApplyYouthStartUpAttachment> getPojosByYouthStartUpId( Long youthStartUpId )
	{
		NameValueBean condition = new NameValueBean( ApplyYouthStartUpAttachment.APPLY_YOUTH_START_UP_CONSTANT, applyYouthStartUpDAO.read( youthStartUpId ) );

		return getPojosByProperty( condition );
	}

	@SuppressWarnings( "unchecked" )
	public List<ApplyYouthStartUpAttachment> getPojosByTransmissionStatus( String transmissionStatusCode )
	{
		Validate.notBlank( transmissionStatusCode );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "youthStartUpAttachment.getPojosByTransmissionStatus" );
		nativeQuery.setParameter( "transmissionStatusCode", transmissionStatusCode, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( ApplyYouthStartUpAttachment.class );

		return nativeQuery.getResultList();
	}

	public ApplyYouthStartUpAttachment read( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		return getPojoByPK( attachmentId, ApplyYouthStartUpAttachment.TABLENAME_CONSTANT );
	}

	public int updateNotified( List<Long> attachmentIds )
	{
		Validate.notEmpty( attachmentIds );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "youthStartUpAttachment.updateNotified" );
		nativeQuery.setParameterList( ATTACHMENT_ID_CONSTANT, attachmentIds, Long.class );
		nativeQuery.setParameter( NOTIFIED_CONSTANT, NotificationStatusEnum.NOTIFIED.getContext(), Integer.class );
		nativeQuery.setParameter( UPDATED_DATE_CONSTANT, new Date(), Date.class );

		return nativeQuery.executeUpdate();
	}

	public Long updateNotified( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		ApplyYouthStartUpAttachment pojo = read( attachmentId );
		pojo.setNotified( true );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long updateResend( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		ApplyYouthStartUpAttachment pojo = read( attachmentId );
		pojo.setResend( pojo.getResend() + 1 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long initializeResend( Long attachmentId )
	{
		Validate.notNull( attachmentId );

		ApplyYouthStartUpAttachment pojo = read( attachmentId );
		pojo.setResend( 0 );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	public Long updateTransmissionStatus( Long attachmentId, String transmissionStatusCode )
	{
		Validate.notNull( attachmentId );

		ApplyYouthStartUpAttachment pojo = read( attachmentId );
		pojo.setCodeTransmissionStatus( codeTransmissionStatusDAO.read( transmissionStatusCode ) );
		pojo.setUpdatedDate( new Date() );

		return pojo.getAttachmentId();
	}

	@Override
	protected Class<ApplyYouthStartUpAttachment> getPojoClass()
	{
		return ApplyYouthStartUpAttachment.class;
	}
}
