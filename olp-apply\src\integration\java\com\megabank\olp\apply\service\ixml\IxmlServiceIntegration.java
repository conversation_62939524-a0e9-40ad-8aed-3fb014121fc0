package com.megabank.olp.apply.service.ixml;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.megabank.olp.apply.config.ApplyConfig;
import com.megabank.olp.apply.controller.ixml.bean.DoResBean;
import com.megabank.olp.apply.service.ixml.bean.IxmlResBean;
import com.megabank.olp.apply.utility.EncryptorUtils;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginArgBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginInputParamsBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginJWSInputParamsBean;

@SpringBootTest
@ContextConfiguration( classes = ApplyConfig.class )
public class IxmlServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private IxmlService service;

	@Disabled
	@Test
	public void escapeChar()
	{
		String s =
				 "{\\\"header\\\": {\\\"mainCode\\\": \\\"017\\\",\\\"bankCode\\\": \\\"0179438\\\",\\\"type\\\": \\\"C01\\\",\\\"form\\\": \\\"E政府介接資料授權書\\\",\\\"serial\\\": \\\"*********\\\",\\\"signEnc\\\": \\\"Sign\\\",\\\"issueTime\\\": \\\"2023-05-02-12:00:00 \\\"},\\\"context\\\": {\\\"idnBan\\\": \\\"N272962731\\\",\\\"name\\\": \\\"王大大\\\",\\\"purpose\\\": \\\"A,B,C\\\",\\\"startDate\\\": \\\"2023-05-02\\\",\\\"endDate\\\": \\\"2023-08-01\\\",\\\"ver\\\": \\\"1.0\\\",\\\"agrStatement\\\": \\\"立書人王大大(N272962731)為辦理授信貸款所需，茲瞭解並同意兆豐國際商業銀行(局、社、農漁會信用部、公司、信用保證基金等)(機構代號0179438)、金融監督管理委員會(以下簡稱金管會)、財團法人金融聯合徵信中心(以下簡稱管理機構)、經金管會授權之管理機構會員金融機構及其他經提供介接資料之公務機關(以下簡稱介接機關)同意使用介接資料之介接使用單位，及前揭機構授權之人員，得依本授權書及個人資料保護法或相關法規，於授信業務特定目的範圍內，查調、蒐集、處理及利用財政部、勞動部、交通部等介接機關提供之立書人一切業務往來之介接資料，惟該特定目的消失時，在合理作業期間內應停止處理及利用該資料。金管會、管理機構、介接使用單位及其授權人員對介接資料之使用，如違反個人資料保護法及稅捐稽徵法等規定，致當事人權益受損害者，應負損害賠償責任(個人資料保護法第28條、第29條及稅捐稽徵法第33條等規定)。另涉及刑事責任者，依稅捐稽徵法及個人資料保護法之相關規定，移送司法機關辦理。授權起始日為2019-01-01至結束日2019-03-01為止，授權項目為A,B,C。\\\",\\\"sourceIp\\\": \\\"***********\\\"}}";
		List<String> sList = new ArrayList<>();
		sList.add( s );

		IxmlLoginArgBean argBean = new IxmlLoginArgBean();
		IxmlLoginInputParamsBean bean = new IxmlLoginInputParamsBean();
		IxmlLoginJWSInputParamsBean jwsInputParams = new IxmlLoginJWSInputParamsBean();
		argBean.setInputParams( bean );
		bean.setJWSInputParams( jwsInputParams );
		jwsInputParams.setContents( sList );

		logger.info( "argBean:{}", argBean );
	}

	@Disabled
	@Test
	public void ixmlSHA256Encrypt()
	{
		String t =
				 "037059031.01 6cd79e9b40 LoginCERTpersonalloan{\"MemberNo\":\"F235353398\",\"Action\":\"CERT\",\"Plaintext\":\"F235353398\",\"AssignCertPassword\":\"Wy+TeEhB+Sw1m77UbZuv6A==\"}";

		String s = EncryptorUtils.ixmlSHA256Encrypt( t );

		logger.info( "s:{}", s );
	}

	@Disabled
	@Test
	public void jcicContent()
	{
		// List<String> slist = service.getJCICContents();
		// String s = slist.get( 0 );

		// Pattern pattern = Pattern.compile( "\\{(\\w+)\\}" );
		// Matcher matcher = pattern.matcher( s );

		// Map<String, String> repalceMap = new HashMap<>();
		// repalceMap.put( "serial", "001" );
		// repalceMap.put( "issueTime", "002" );
		// repalceMap.put( "idnBan", "003" );
		// repalceMap.put( "name", "004" );
		// repalceMap.put( "startDate", "005" );
		// repalceMap.put( "endDate", "006" );
		// repalceMap.put( "sourceIp", "007" );

		// while( matcher.find() )
		// if( repalceMap.containsKey( matcher.group( 1 ) ) )
		// s = s.replace( matcher.group(), repalceMap.get( matcher.group( 1 ) ) );

		// logger.info( "s:{}", s );
	}

	// @Disabled
	@Test
	public void login()
	{
		IxmlResBean res = service.login( "F235353398", "CERT", "personalloan", "B", "" );

		logger.info( "res:{}", res );
	}

	@Disabled
	@Test
	public void loginAssignCertPasswordEncrypt()
	{
		String AssignCertPasswordEncrypt = EncryptorUtils.ixmlAssignCertPasswordEncrypt( "F235353398", "" );

		logger.info( "AssignCertPasswordEncrypt:{}", AssignCertPasswordEncrypt );
	}

	@Disabled
	@Test
	public void queryCert()
	{
		String result = service.queryCert( "R124766395", "127.0.0.1" );

		logger.info( "result:{}", result );
	}

	@Disabled
	@Test
	public void queryVerifyResult() throws UnsupportedEncodingException
	{
		service.queryVerifyResult( "0dd1c84b-fe1c-4f6e-a16f-9b25cb1b4496", "R124766395", "cfdd6a129e994574b7871dce86e77e214b4f2ac7", "a",
								   "127.0.0.1" );
	}

	@Disabled
	@Test
	public void revokeCert()
	{
		service.revokeCert( "R124766395", "127.0.0.1" );
	}

	@Disabled
	@Test
	public void urlEncodedMapper() throws URISyntaxException
	{
		String url = "http://localhost:9010/ixml/doCallback";

		String uri =
				   "BusinessNo=03705903&ApiVersion=1.0&HashKeyNo=1&VerifyNo=edf4b0a0-e27f-4fdd-bfd0-829435d6e39a&ReturnParams=&Token=706537a07bac49ffa4b3a1caca2421598b4dcf39&CAType=&MemberNoMapping=1&ResultCode=S&ReturnCode=0&ReturnCodeDesc=%E6%88%90%E5%8A%9F&IdentifyNo=cac4ef5fe8efb9c11b18f7900d930d683fc8c8c0739cead1e1b28af3b7b0e1c3";

		List<NameValuePair> params = URLEncodedUtils.parse( new URI( url + "/?" + uri ), StandardCharsets.UTF_8 );

		Map<String, String> map = params.stream().collect( Collectors.toMap( NameValuePair::getName, NameValuePair::getValue ) );

		DoResBean bean = new ObjectMapper().convertValue( map, DoResBean.class );

		logger.info( "bean:{}", bean );
	}
}
