/**
 *
 */
package com.megabank.olp.api.service.estimation.bean;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class EvaluateHouseDataBean extends BaseBean
{
	/**
	 * 縣市
	 */
	private String county;

	/**
	 * 鄉鎮
	 */
	private String district;

	/**
	 * 街道
	 */
	private String addr;

	/**
	 * 房屋類型 ( 1: 無電梯公寓 2: 別墅 / 透天厝 3: 店面 (店鋪) 4: 辦公商業大樓 6: 電梯大樓 / 華廈 7: 套房 .)
	 */
	private String bTypeInt;

	/**
	 * 屋齡
	 */
	private Integer bAge;

	/**
	 * 坪數
	 */
	private BigDecimal bAreaP;

	/**
	 * 總樓層
	 */
	@JsonProperty( "Floors" )
	private Integer floors;

	/**
	 * 樓層類別(地上/地下)
	 */
	private String levelSelect;

	/**
	 * 所在樓層
	 */
	private Integer level;

	/**
	 * 車位狀態(1: 無車位 2: 機械車位 3: 非機械車位.)
	 */
	private String parking;

	/**
	 * 車位個數
	 */
	private Integer parkingGTY;

	/**
	 * 車位面積
	 */
	private BigDecimal parkingP;

	public String getAddr()
	{
		return addr;
	}

	public Integer getbAge()
	{
		return bAge;
	}

	public BigDecimal getbAreaP()
	{
		return bAreaP;
	}

	public String getbTypeInt()
	{
		return bTypeInt;
	}

	public String getCounty()
	{
		return county;
	}

	public String getDistrict()
	{
		return district;
	}

	public Integer getFloors()
	{
		return floors;
	}

	public Integer getLevel()
	{
		return level;
	}

	public String getLevelSelect()
	{
		return levelSelect;
	}

	public String getParking()
	{
		return parking;
	}

	public Integer getParkingGTY()
	{
		return parkingGTY;
	}

	public BigDecimal getParkingP()
	{
		return parkingP;
	}

	public void setAddr( String addr )
	{
		this.addr = addr;
	}

	public void setbAge( Integer bAge )
	{
		this.bAge = bAge;
	}

	public void setbAreaP( BigDecimal bAreaP )
	{
		this.bAreaP = bAreaP;
	}

	public void setbTypeInt( String bTypeInt )
	{
		this.bTypeInt = bTypeInt;
	}

	public void setCounty( String county )
	{
		this.county = county;
	}

	public void setDistrict( String district )
	{
		this.district = district;
	}

	public void setFloors( Integer floors )
	{
		this.floors = floors;
	}

	public void setLevel( Integer level )
	{
		this.level = level;
	}

	public void setLevelSelect( String levelSelect )
	{
		this.levelSelect = levelSelect;
	}

	public void setParking( String parking )
	{
		this.parking = parking;
	}

	public void setParkingGTY( Integer parkingGTY )
	{
		this.parkingGTY = parkingGTY;
	}

	public void setParkingP( BigDecimal parkingP )
	{
		this.parkingP = parkingP;
	}

}
