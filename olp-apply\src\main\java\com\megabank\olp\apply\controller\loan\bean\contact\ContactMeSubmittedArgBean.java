package com.megabank.olp.apply.controller.loan.bean.contact;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class ContactMeSubmittedArgBean extends BaseBean
{

	@NotBlank
	private String name;

	@NotBlank
	private String mobileNumber;

	private String email;

	@NotBlank
	private String contactTimeCode;

	private String otherMsg;

	@NotBlank
	private String sexCode;

	private String loanPlanCode;

	private Long introducerBranchBankId;

	private String introducerEmpId;

	public ContactMeSubmittedArgBean()
	{
		// default constructor
	}

	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getSexCode()
	{
		return sexCode;
	}

	public String getLoanPlanCode()
	{
		return loanPlanCode;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setSexCode( String sexCode )
	{
		this.sexCode = sexCode;
	}

	public void setLoanPlanCode( String loanPlanCode )
	{
		this.loanPlanCode = loanPlanCode;
	}

	public Long getIntroducerBranchBankId()
	{
		return introducerBranchBankId;
	}

	public void setIntroducerBranchBankId( Long introducerBranchBankId )
	{
		this.introducerBranchBankId = introducerBranchBankId;
	}

	public String getIntroducerEmpId()
	{
		return introducerEmpId;
	}

	public void setIntroducerEmpId( String introducerEmpId )
	{
		this.introducerEmpId = introducerEmpId;
	}
}
