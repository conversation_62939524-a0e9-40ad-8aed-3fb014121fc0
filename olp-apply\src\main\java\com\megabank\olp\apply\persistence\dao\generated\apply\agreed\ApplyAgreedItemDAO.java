package com.megabank.olp.apply.persistence.dao.generated.apply.agreed;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.apply.persistence.pojo.apply.agreed.ApplyAgreedItem;
import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;

/**
 * The ApplyAgreedItemDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class ApplyAgreedItemDAO extends BasePojoDAO<ApplyAgreedItem, Long>
{
	@Autowired
	private ApplyAgreedDAO applyAgreedDAO;

	public List<ApplyAgreedItem> getPojosByAgreedId( Long agreedId )
	{
		Validate.notNull( agreedId );

		NameValueBean condition = new NameValueBean( "applyAgreed", applyAgreedDAO.read( agreedId ) );
		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( "displayOrder" ) };

		return super.getPojosByPropertyOrderBy( condition, orderBeans );
	}

	@Override
	protected Class<ApplyAgreedItem> getPojoClass()
	{
		return ApplyAgreedItem.class;
	}
}
